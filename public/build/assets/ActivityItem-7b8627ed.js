import{D as o}from"./datetime-8ddd27a0.js";import{e as l,l as d,i as u,o as m,c as p,w as y,a as t,t as s}from"./app-f0078ddb.js";/* empty css            */const v={class:"w-full rounded-lg h-12 flex flex-row items-center px-2 gap-4 cursor-pointer hover:bg-gray-100"},f={class:"flex-auto overflow-hidden flex flex-col"},h={class:"text-sm font-medium truncate"},_={class:"text-xs text-gray-600 truncate"},x={class:"w-12 grow py-2"},w={class:"text-xs text-gray-600 text-right"},I={__name:"ActivityItem",props:{activity:{type:Object,required:!0}},setup(r){const e=r;let a=l(o.fromSQL(e.activity.updated_at).toLocal());const c=d(()=>{switch(a.value.toISODate()){case o.now().toLocal().toISODate():return"Today";case o.now().minus({days:1}).toLocal().toISODate():return"Yesterday";default:return a.value.toISODate()<o.now().minus({years:1}).toLocal().toISODate()?a.value.toFormat("MMM d, yyyy"):a.value.toFormat("MMM d")}});return(i,g)=>{const n=u("Link");return m(),p(n,{href:e.activity.type=="vocab"?i.route("practice.vocabulary.attempt.id",r.activity.id):i.route("practice.grammar.attempt.id",r.activity.id)},{default:y(()=>[t("div",v,[t("div",f,[t("h5",h,s(e.activity.title),1),t("p",_,s(e.activity.description),1)]),t("div",x,[t("p",w,s(c.value),1)])])]),_:1},8,["href"])}}};export{I as default};
