import{V as de,e as f,l as W,k as v,A as Fe,i as Qe,o,c as x,w as m,b as d,a as r,j as me,u as e,d as n,t as a,F as P,h as B,n as I,g,f as u,T as M,W as D,a3 as pe}from"./app-f0078ddb.js";import{_ as qe}from"./AppLayout-33f062bc.js";import{_ as <PERSON>}from"./Breadcrumbs-c96e9207.js";import Ne from"./Options-8208fa44.js";import{_ as $}from"./ButtonItem-718c0517.js";import{_ as fe}from"./ProgressBar-b7203293.js";import $e from"./OptionsSidebar-14dacea6.js";import Re from"./Drag-9e816807.js";import Ue from"./MultipleChoice-080711a4.js";import Oe from"./Type-dfa282d2.js";import We from"./Matching-1875e265.js";import{r as Ae}from"./replaceMacra-3b9666ed.js";import{g as Te}from"./index-794e919d.js";import{T as ve}from"./easytimer-3d932146.js";import De from"./Finish-09791c16.js";import{_ as Ke}from"./Footer-0988dcd8.js";import{_ as Xe}from"./MobileSidebar-5e21b4cd.js";import{_ as oe}from"./AssignmentModule-fc2620bb.js";import{P as Je}from"./Promotion-3eee0057.js";import{r as Ge}from"./InformationCircleIcon-716f3ffb.js";import{r as Ve}from"./PlusIcon-4ea782f1.js";import{r as ze,a as Me}from"./PlayIcon-8c672cba.js";import{r as Se}from"./StarIcon-155a2a28.js";import{r as je}from"./CheckCircleIcon-d86d1232.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./OptionsMode-109debf5.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";import"./OptionsWords-ece543ea.js";import"./ToggleItem-94c3ab1e.js";import"./OptionsDifficulty-1a9565c6.js";import"./vuedraggable.umd-aab17b5c.js";import"./NextButtonSmall-9e6ffefc.js";import"./XCircleIcon-63af2b2a.js";import"./lodash-631955d9.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-6a201aa1.js";import"./ArrowPathIcon-f74cb8d6.js";const Ye={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Ze={class:"px-4 py-8 sm:px-8"},et={class:"flex flex-row items-center justify-between"},tt={class:"mt-8"},st={key:0,class:"mt-16"},ot={class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},rt={class:"mt-2 mb-8"},nt={class:"text-center text-xs font-bold text-gray-500 uppercase"},at={class:"mb-8"},it={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},lt={key:0,class:"mb-8 text-left"},ct={class:"text-sm font-bold text-gray-500 uppercase"},ut={key:0},dt={key:1},mt={key:0,class:"mt-2 flex flex-row items-center"},pt={key:1,class:"w-full"},ft={key:0,class:"mx-auto"},vt={key:1,class:"mx-auto"},gt={key:0},yt={key:1},xt={key:0,class:"mx-auto"},ht={key:1,class:"mx-auto"},wt={key:1},bt={key:0,class:"lg:hidden"},kt={class:"mt-2 text-left text-sm text-gray-800"},_t={key:0},Lt={class:"mx-auto mt-8 text-center lg:hidden"},Ct={key:0,class:"mx-auto"},$t={key:1,class:"mx-auto"},At={key:0},Tt={key:0,class:"mx-auto"},Vt={key:1,class:"mx-auto"},zt={key:3,class:"mt-2 text-center text-sm text-red-600"},Mt={key:0},St={key:1,class:"mt-2"},jt={class:"text-center text-xs font-bold text-gray-500 uppercase"},Et={key:2},Pt={key:1,class:"mt-8 grid grid-cols-1 gap-8"},Bt={key:0},It={key:1},Ft={class:"mt-2 rounded-lg bg-slate-100"},Qt={key:0,class:"mt-2 flex flex-row items-center"},qt=["src"],Ht={class:"ml-2 flex grow flex-col"},Nt={class:"font-intro text-lg font-bold text-gray-900"},Rt={key:0},Ut={key:1},Ot={class:"text-xs text-gray-600"},Wt={key:1,class:"my-2 flex flex-row items-center"},Dt={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},Kt={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},Xt={class:"ml-2 flex grow flex-col"},Jt={class:"font-intro text-lg font-bold text-gray-900"},Gt={class:"text-xs text-gray-600"},Yt={key:0,class:"mt-4"},Zt={class:"mt-2 mr-2 inline-block rounded-full bg-slate-200 px-2 py-1 text-xs font-bold text-slate-500"},es={"aria-labelledby":"userStats"},ts={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},ss={class:"text-sm leading-6 font-medium text-gray-500"},os={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},rs={key:0},ns={key:0,class:"ml-1 text-sm font-medium text-gray-500"},as={key:1},is={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},ls={key:0,class:"mb-8"},cs={key:2,class:"mt-2"},us={class:"text-center text-xs font-bold text-gray-500 uppercase"},ds={key:3},ms={key:0,class:"mt-8"},ps={key:1},fs={class:"mt-2 text-left text-sm text-gray-800"},vs={key:0},gs={class:"mx-auto mt-8 text-center"},ys={key:0,class:"mx-auto"},xs={key:1,class:"mx-auto"},hs={key:0},ws={key:0,class:"mx-auto"},bs={key:1,class:"mx-auto"},ks={key:3,class:"mt-2 text-center text-sm text-red-600"},_s={key:4,class:"mt-8 grid grid-cols-1 gap-8"},Ls={key:0},Cs={class:"mt-2 rounded-lg bg-white shadow-sm"},$s={key:0,class:"mt-2 flex flex-row items-center"},As=["src"],Ts={class:"ml-2 flex grow flex-col"},Vs={class:"font-intro text-lg font-bold text-gray-900"},zs={key:0},Ms={key:1},Ss={class:"text-xs text-gray-600"},js={key:1,class:"my-2 flex flex-row items-center"},Es={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},Ps={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},Bs={class:"ml-2 flex grow flex-col"},Is={class:"font-intro text-lg font-bold text-gray-900"},Fs={class:"text-xs text-gray-600"},Qs={key:0,class:"mt-4"},qs={class:"mt-2 mr-2 inline-block rounded-full bg-slate-200 px-2 py-1 text-xs font-bold text-slate-500"},Hs={key:1},Ns={key:2,class:"text-left"},Rs={class:"text-sm font-bold text-gray-500 uppercase"},Us={key:0},Os={key:1},Ws={key:0,class:"mt-2 flex flex-row items-center"},Ds={"aria-labelledby":"userStats"},Ks={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},Xs={class:"text-sm leading-6 font-medium text-gray-500"},Js={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},Gs={key:0},Ys={key:0,class:"ml-1 text-sm font-medium text-gray-500"},Zs={key:1},eo={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},to={key:3,class:"mt-8 w-full"},so={key:0,class:"mx-auto"},oo={key:1,class:"mx-auto"},vr={__name:"Attempt",props:{session:{type:Object,required:!1},question:{type:Object,required:!1},sections:{type:Object,required:!0},initialQuestion:{type:Object,required:!1},filters:{type:Array,required:!1},assignment:{type:Object,required:!1}},setup(c){const y=c,Ee=[{name:"Practice",href:"/practice",current:!1},{name:"Vocabulary",href:"/practice/vocabulary",current:!1},{name:"Attempt",href:"#",current:!0}];let L=de({words:"all",time:0,difficulty:0,toggleLearned:!1,toggleNames:!1}),G=f(y.sections.word_count);const ge=W(()=>G.value<5);let re=f(v().props.authenticated?v().props.user.xp:0),S=v().props.authenticated?f(v().props.user.xp):0,A=v().props.authenticated?f(v().props.user.level):f(0),Pe=f(v().props.authenticated?v().props.user.level.next_level_max:null),j=v().props.authenticated?f(v().props.user.level.max-v().props.user.xp+1):f(0),K=f(0),q=f(!1),ne=f(!1),H=f(!1),p=de({correct:0,incorrect:0}),w=f(!1),C=f(),k=de([]),i=f(),ae=f(),Be=f([]),b=f(),E=f(!0),N=f(0),T=f(!1);function Y(){w.value=!0,D.reload({only:["assignment"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{w.value=!1}})}const Z=W(()=>p.correct+p.incorrect==0?-1:p.correct+p.incorrect<10?p.correct/10:Math.round(100*p.correct/(p.correct+p.incorrect)));function X(l=null){i.value=ae.value,R.value=!1,D.reload({only:["question"],preserveState:!0,preserveScroll:!0,replace:!0,data:{disregard_id:l},onSuccess:()=>{ae.value=y.question,R.value=!0}}),N.value++,q.value=!1}let F=f(0),h=new ve,V=new ve;const ye=(l=!1)=>{D.reload({only:["session","assignment"],data:{reattempt:!!l},preserveState:!0,preserveScroll:!0,onSuccess:()=>{p.correct=y.session.correct,p.incorrect=y.session.incorrect,F.value=y.session.streak,k=y.session.words_seen?y.session.words_seen:[],ie.value=y.session.xp_earned,pe.post("/api/practice/vocabulary/update-session-options",{session:y.session.id,settings:L,filters:y.filters}).then(()=>{setTimeout(()=>{V.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),L.time>0?(h.start({countdown:!0,startValues:{seconds:L.time}}),b.value=O(h.getTimeValues()),h.addEventListener("secondsUpdated",function(){b.value=O(h.getTimeValues())}),h.addEventListener("targetAchieved",function(){J()})):(b.value=O(V.getTimeValues()),V.addEventListener("secondsUpdated",function(){b.value=O(V.getTimeValues())})),D.reload({method:"get",only:["initialQuestion"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{i.value=y.initialQuestion.first,ae.value=y.initialQuestion.second,H.value=!0}})},Ie(200,500))})}})};let R=f(!0),ie=f(y.session?y.session.xp_earned:0);const ee=l=>{if(E.value=!0,!w.value){if(Ae(l.answer).toUpperCase()==Ae(i.value.key).toUpperCase()){C.value=!0;let _={value:p.correct};F.value<1?F.value=1:F.value++,Te.to(_,{value:p.correct+1,duration:.3,onUpdate:()=>{p.correct=Math.round(_.value)}});let s={value:re.value};Te.to(s,{value:re.value+U(),duration:.3,onUpdate:()=>{re.value=Math.round(s.value)}}),ie.value+=U()}else C.value=!1,F.value=0,p.incorrect++;if(k.find(_=>_.id===i.value.id)){var t=k.findIndex(_=>_.id===i.value.id);k[t].correct=k[t].correct+C.value,k[t].total=k[t].total+1}else k.push({id:i.value.id,word:i.value.word,pos:i.value.pos,definition:i.value.short_definition,core:i.value.core,correct:C.value?1:0,gender:i.value.gender,total:1,lexicon_id:i.value.lexicon_id});q.value=!0,C.value?E.value=!1:setTimeout(()=>{E.value=!1},2e3),S.value=S.value+U(),j.value<=U()?(A.value.level=A.value.level+1,j.value=Pe.value-S.value+1):j.value=j.value-U(),K.value=V.getTotalTimeValues().seconds,pe.post("/api/practice/vocabulary/add-attempt",{session:y.session.id,token_id:i.value.id,word_id:i.value.word_id,type:i.value.type,correct:C.value,xp:U(),streak:F.value,time:V.getTotalTimeValues().secondTenths}).then(_=>{S.value=_.data.xp,A.value=_.data.level,j.value=_.data.next_level_xp})}},te=()=>{if(w.value)return;Be.value.push(i.value.word_id);let l=i.value.word_id;pe.post("/api/practice/vocabulary/add-known-word",{word_id:l}),E.value=!1,X(i.value.id)},le=W(()=>{let l=A.value.max-A.value.min+1;return(S.value-A.value.min)/l*100}),U=()=>{if(C.value)switch(i.value.type){case"drag":switch(L.difficulty){case 1:return 3;case 2:return 4;default:return 2}case"type":switch(L.difficulty){case 1:return 4;case 2:return 5;default:return 3}default:return 1}return 0},Ie=(l,t)=>Math.floor(Math.random()*(t-l+1))+l,xe=W(()=>{var l=[];return y.sections&&y.sections.sectionList.forEach(t=>{t.vocabList?l.push(t.vocabList.id):l.push(t.sectionList.book_id+":"+parseInt(t.sectionList.start)+":"+parseInt(t.sectionList.end))}),l}),J=()=>{w.value=!0,D.post("/api/practice/vocabulary/finish",{session:y.session.id})},he=W(()=>p.correct+p.incorrect==0?-1:p.correct+p.incorrect<10?p.correct:Math.round(10*p.correct/(p.correct+p.incorrect))),we=l=>{switch(!0){case!l:return null;case Math.round(l/10)<15:return"A few seconds spent";case Math.round(l/10)<30:return"Less than 30 seconds  spent";case Math.round(l/10)<60:return"Less than a minute  spent";case Math.round(l/10)<90:return"About a minute  spent";case Math.round(l/10)<150:return"About 2 minutes spent";case Math.round(l/10)<210:return"About 3 minutes spent";case Math.round(l/10)<270:return"About 4 minutes spent";case Math.round(l/10)<330:return"About 5 minutes spent";default:return"About "+Math.round(l/600)+" minutes spent"}},be=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],ke=W(()=>[{name:"Correct",value:p.correct,unit:"out of "+(p.correct+p.incorrect)},{name:"Current Streak",value:F.value,unit:"in a row"},{name:"XP Earned",value:ie.value},{name:"Words Seen",value:k.length}]),O=l=>l.days>1?"more than "+l.days+" days":l.days>0?"more than "+l.days+" day":l.hours>1?"more than "+l.hours+" hours":l.hours>0?"more than "+l.hours+" hour":l.seconds<=9?l.minutes+":0"+l.seconds:l.minutes+":"+l.seconds;let Q=f(!1);const _e=()=>{L.time>0?h.isRunning()?(h.pause(),Q.value=!0):(h.start(),Q.value=!1):V.isRunning()?(V.pause(),Q.value=!0):(V.start(),Q.value=!1)},Le=()=>{let l=h.getTimeValues();l.minutes+=1,h.removeAllEventListeners("targetAchieved"),h=new ve,h.start({countdown:!0,startValues:{minutes:l.minutes,seconds:l.seconds}}),Q.value&&h.pause(),b.value=O(h.getTimeValues()),h.addEventListener("secondsUpdated",function(){b.value=O(h.getTimeValues())}),h.addEventListener("targetAchieved",function(){J()})},se=()=>{v().props.authenticated?(ne.value=!0,ye()):D.get(`/login?redirect=/practice/vocabulary/attempt${window.location.search}`)};let z=f(!1);Fe(()=>{y.assignment&&(z.value=y.assignment.completed==0)});let ce=f(!1);const Ce=()=>{ce.value=!0,ye(!0)};return(l,t)=>{const _=Qe("Head");return o(),x(qe,null,{default:m(()=>[d(_,null,{default:m(()=>t[33]||(t[33]=[r("title",null,"Vocabulary Attempt",-1)])),_:1}),r("main",Ye,[r("div",Ze,[r("div",et,[d(He,{class:"lg:col-span-9 xl:grid-cols-10",pages:Ee}),d(e(Ge),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=s=>me(T)?T.value=!e(T):T=!e(T))})]),r("div",tt,[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:m(()=>[e(H)?(o(),n("div",st,[r("div",ot,[d(fe,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(A).level,"post-text":e(S)+" XP",progress:le.value},null,8,["pre-text","post-text","progress"]),r("div",rt,[r("p",nt,a(e(j))+" xp to the next level ",1)]),r("section",at,[t[34]||(t[34]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),r("div",it,[(o(),n(P,null,B(be,s=>r("div",{class:I(["h-4 w-full rounded-sm shadow-sm",he.value>=s.value?s.color:"bg-gray-300"]),key:s.value},null,2)),64))])]),e(b)?(o(),n("section",lt,[r("h5",ct,[t[35]||(t[35]=g(" Time ")),e(L).time>0?(o(),n("span",ut,"Remaining")):(o(),n("span",dt,"Elapsed"))]),e(b)?(o(),n("div",mt,[r("div",{class:I(["grow text-left font-medium text-gray-900",e(b).length>10?"text-2xl":"text-4xl"])},a(e(b)),3),e(L).time>0?(o(),n("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[1]||(t[1]=s=>Le())},[d(e(Ve),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):u("",!0),r("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[2]||(t[2]=s=>_e())},[e(Q)?(o(),x(e(ze),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(o(),x(e(Me),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):u("",!0)])):u("",!0),e(H)?(o(),n("div",pt,[d($,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(w),onClick:t[3]||(t[3]=s=>J())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(w)?(o(),n("span",ft,t[36]||(t[36]=[r("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Finishing ...",-1)]))):(o(),n("span",vt,"Finish"))]),_:1})]),_:1},8,["disabled"])])):u("",!0)]),d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:m(()=>[e(i)&&e(i).type==="mc"?(o(),x(Ue,{key:`${e(N)}-mc`,options:e(i).options,"question-key":e(i).key,"is-correct":e(C),"is-answer":e(q),attempts:e(p).correct+e(p).incorrect,disabled:e(E)||e(w)||!e(R),"is-vocabulary":!0,onSubmit:t[4]||(t[4]=s=>ee(s)),onIKnowThis:t[5]||(t[5]=s=>te()),onNextQuestion:t[6]||(t[6]=s=>X())},{stem:m(()=>[g(a(e(i).stem),1)]),instructions:m(()=>[g(a(e(i).instructions),1)]),_:1},8,["options","question-key","is-correct","is-answer","attempts","disabled"])):e(i)&&e(i).type==="match"?(o(),x(We,{key:`${e(N)}-match`,options:e(i).options,"is-answer":e(q),"is-correct":e(C),"user-attempt":l.attemptValue,disabled:e(E)||e(w)||!e(R),"is-vocabulary":!0,onSubmit:t[7]||(t[7]=s=>ee(s)),onIKnowThis:t[8]||(t[8]=s=>te()),onNextQuestion:t[9]||(t[9]=s=>X())},{instructions:m(()=>[g(a(e(i).instructions),1)]),_:1},8,["options","is-answer","is-correct","user-attempt","disabled"])):e(i)&&e(i).type==="type"?(o(),x(Oe,{key:`${e(N)}-type`,options:e(i).key,"is-answer":e(q),"is-correct":e(C),difficulty:e(i).difficulty,disabled:e(E)||e(w)||!e(R),"is-vocabulary":!0,onSubmit:t[10]||(t[10]=s=>ee(s)),onIKnowThis:t[11]||(t[11]=s=>te()),onNextQuestion:t[12]||(t[12]=s=>X())},{stem:m(()=>[g(a(e(i).stem),1)]),instructions:m(()=>[g(a(e(i).instructions),1)]),_:1},8,["options","is-answer","is-correct","difficulty","disabled"])):e(i)&&e(i).type==="drag"?(o(),x(Re,{key:`${e(N)}-drag`,options:e(i).options,"question-key":e(i).key,syllabalized:e(i).syllabized,stem:Array.isArray(e(i).stem)?e(i).stem[1]:"","is-answer":e(q),"is-correct":e(C),difficulty:e(i).difficulty,disabled:e(E)||e(w)||!e(R),"is-vocabulary":!0,onSubmit:t[13]||(t[13]=s=>ee(s)),onIKnowThis:t[14]||(t[14]=s=>te()),onNextQuestion:t[15]||(t[15]=s=>X())},{stem:m(()=>[Array.isArray(e(i).stem)?(o(),n("span",gt,a(e(i).stem[0]),1)):(o(),n("span",yt,a(e(i).stem),1))]),instructions:m(()=>[g(a(e(i).instructions),1)]),_:1},8,["options","question-key","syllabalized","stem","is-answer","is-correct","difficulty","disabled"])):(o(),x(De,{key:`${e(N)}-finish`},{default:m(()=>[d($,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(w),onClick:t[16]||(t[16]=s=>J())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(w)?(o(),n("span",xt,t[37]||(t[37]=[r("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Finishing ...",-1)]))):(o(),n("span",ht,"Finish"))]),_:1})]),_:1},8,["disabled"])]),_:1}))]),_:1})])):(o(),n("div",wt,[d(Ne,{words:c.sections.words,learned:c.sections.learned,names:c.sections.names,sections:c.sections.sectionList,"onUpdate:options":t[17]||(t[17]=s=>Object.assign(e(L),s)),"onUpdate:totalWords":t[18]||(t[18]=s=>me(G)?G.value=s:G=s)},null,8,["words","learned","names","sections"]),e(z)&&c.assignment.attempt?(o(),n("div",bt,[t[38]||(t[38]=r("h4",{class:"mt-8 text-left text-sm font-bold text-gray-500 uppercase"}," Assignment in Progress ",-1)),r("p",kt,[g(a(c.assignment.attempt.correct)+" out of "+a(c.assignment.attempt.attempts)+" correct ",1),c.assignment.attempt.attempts>0?(o(),n("span",_t," ("+a(Math.round(100*c.assignment.attempt.correct)/c.assignment.attempt.attempts)+"% accuracy)",1)):u("",!0),g(" | "+a(we(c.assignment.attempt.time)),1)])])):u("",!0),r("div",Lt,[e(v)().props.user&&e(v)().props.user.membership.subscribed?(o(),n("div",{key:0,class:I(["grid",e(z)?"grid-cols-1 gap-4 sm:grid-cols-2":"grid-cols-1"])},[d($,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:t[19]||(t[19]=s=>se())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(ne)?(o(),n("span",Ct,t[39]||(t[39]=[r("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),n("span",$t,[t[40]||(t[40]=g("Continue")),e(z)?(o(),n("span",At," Attempt")):u("",!0)]))]),_:1})]),_:1}),e(z)?(o(),x($,{key:0,color:"white",size:"md",class:"inline-flex w-full disabled:bg-gray-200",onClick:t[20]||(t[20]=s=>Ce())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(ce)?(o(),n("span",Tt,t[41]||(t[41]=[r("svg",{class:"inline h-5 w-5 animate-spin text-gray-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),n("span",Vt,"Restart"))]),_:1})]),_:1})):u("",!0)],2)):e(v)().props.authenticated?(o(),x($,{key:1,color:"indigo",size:"md",class:"flex w-full items-center justify-center",link:"/subscribe"},{default:m(()=>t[42]||(t[42]=[g("Join LatinTutorial Pro to Attempt")])),_:1})):(o(),x($,{key:2,color:"indigo",size:"md",class:"flex w-full items-center justify-center",onClick:t[21]||(t[21]=s=>se())},{default:m(()=>t[43]||(t[43]=[g("Log in to Continue")])),_:1})),ge.value&&e(v)().props.authenticated?(o(),n("p",zt," This set needs more than 5 words to continue. ")):u("",!0)])]))]),_:1})])]),d(Ke)]),d(Xe,{class:"lg:hidden",show:e(T),onClose:t[24]||(t[24]=s=>me(T)?T.value=!1:T=!1)},{default:m(()=>[e(H)?(o(),n("div",Pt,[c.assignment?(o(),n("section",Bt,[d(oe,{assignment:c.assignment,"words-seen":e(k).length,"words-correct":e(p).correct,time:e(K),accuracy:Z.value,onCompleted:t[23]||(t[23]=s=>Y())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):u("",!0),c.sections?(o(),n("section",It,[t[46]||(t[46]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Sections",-1)),r("div",Ft,[(o(!0),n(P,null,B(c.sections.sectionList,(s,ue)=>(o(),n("div",{key:ue,class:"h-20 w-full px-4 py-2"},[s.sectionList?(o(),n("div",Qt,[r("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.sectionList.image_name,class:"h-12 w-12"},null,8,qt),r("div",Ht,[r("h4",Nt,[g(a(s.sectionList.work)+" "+a(s.sectionList.l1)+" "+a(s.sectionList.book)+".",1),s.sectionList.start===s.sectionList.end?(o(),n("span",Rt,a(s.sectionList.start),1)):(o(),n("span",Ut,a(s.sectionList.start)+"-"+a(s.sectionList.end),1))]),r("p",Ot,a(s.sectionList.word_count)+" words | "+a(s.sectionList.core_count)+" core, "+a(s.sectionList.word_count-s.sectionList.core_count)+" not core ",1)])])):u("",!0),s.vocabList?(o(),n("div",Wt,[t[44]||(t[44]=r("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),s.vocabList.id=="star"?(o(),n("div",Dt,[d(e(Se),{class:"h-8 w-8 stroke-2"})])):s.vocabList.id=="learn"?(o(),n("div",Kt,[d(e(je),{class:"h-8 w-8 stroke-2"})])):(o(),n("div",{key:2,class:I(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${s.vocabList.icon_color}-300 text-${s.vocabList.icon_color}-700`])},a(s.vocabList.abbreviation),3)),r("div",Xt,[r("h4",Jt,a(s.vocabList.name),1),r("p",Gt,a(s.vocabList.word_count)+" words | "+a(s.vocabList.core_count)+" core, "+a(s.vocabList.word_count-s.vocabList.core_count)+" not core ",1)])])):u("",!0)]))),128))]),c.filters?(o(),n("div",Yt,[t[45]||(t[45]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Filters",-1)),(o(!0),n(P,null,B(c.filters,s=>(o(),n("div",Zt,a(s),1))),256))])):u("",!0)])):u("",!0),r("section",es,[r("div",null,[r("dl",ts,[(o(!0),n(P,null,B(ke.value,s=>(o(),n("div",{key:s.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[r("dt",ss,a(s.name),1),r("dd",os,[s.value?(o(),n("span",rs,[g(a(s.value)+" ",1),s.unit?(o(),n("span",ns,a(s.unit),1)):u("",!0)])):(o(),n("span",as,"–"))])]))),128))])])])])):(o(),n("div",Mt,[e(v)().props.authenticated?(o(),x(fe,{key:0,class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(A).level,"post-text":e(S)+" XP",progress:le.value},null,8,["pre-text","post-text","progress"])):u("",!0),e(v)().props.authenticated?(o(),n("div",St,[r("p",jt,a(e(j))+" xp to the next level ",1)])):u("",!0),c.assignment?(o(),n("section",Et,[d(oe,{assignment:c.assignment,"words-seen":e(k).length,"words-correct":e(p).correct,time:e(K),accuracy:Z.value,onCompleted:t[22]||(t[22]=s=>Y())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):u("",!0),e(v)().props.authenticated?(o(),x($e,{key:3,sections:c.sections.sectionList,"current-list":xe.value,filters:c.filters},null,8,["sections","current-list","filters"])):u("",!0)]))]),_:1},8,["show"]),r("aside",is,[e(v)().props.authenticated?u("",!0):(o(),n("section",ls,[d(Je)])),e(v)().props.authenticated?(o(),x(fe,{key:1,class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(A).level,"post-text":e(S)+" XP",progress:le.value},null,8,["pre-text","post-text","progress"])):u("",!0),e(v)().props.authenticated?(o(),n("div",cs,[r("p",us,a(e(j))+" xp to the next level ",1)])):u("",!0),e(H)?(o(),n("div",_s,[c.sections?(o(),n("section",Ls,[t[55]||(t[55]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Sections",-1)),r("div",Cs,[(o(!0),n(P,null,B(c.sections.sectionList,(s,ue)=>(o(),n("div",{key:ue,class:"h-20 w-full px-4 py-2"},[s.sectionList?(o(),n("div",$s,[r("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.sectionList.image_name,class:"h-12 w-12"},null,8,As),r("div",Ts,[r("h4",Vs,[g(a(s.sectionList.work)+" "+a(s.sectionList.l1)+" "+a(s.sectionList.book)+".",1),s.sectionList.start===s.sectionList.end?(o(),n("span",zs,a(s.sectionList.start),1)):(o(),n("span",Ms,a(s.sectionList.start)+"-"+a(s.sectionList.end),1))]),r("p",Ss,a(s.sectionList.word_count)+" words | "+a(s.sectionList.core_count)+" core, "+a(s.sectionList.word_count-s.sectionList.core_count)+" not core ",1)])])):u("",!0),s.vocabList?(o(),n("div",js,[t[53]||(t[53]=r("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),s.vocabList.id=="star"?(o(),n("div",Es,[d(e(Se),{class:"h-8 w-8 stroke-2"})])):s.vocabList.id=="learn"?(o(),n("div",Ps,[d(e(je),{class:"h-8 w-8 stroke-2"})])):(o(),n("div",{key:2,class:I(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${s.vocabList.icon_color}-300 text-${s.vocabList.icon_color}-700`])},a(s.vocabList.abbreviation),3)),r("div",Bs,[r("h4",Is,a(s.vocabList.name),1),r("p",Fs,a(s.vocabList.word_count)+" words | "+a(s.vocabList.core_count)+" core, "+a(s.vocabList.word_count-s.vocabList.core_count)+" not core ",1)])])):u("",!0)]))),128))]),c.filters?(o(),n("div",Qs,[t[54]||(t[54]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Filters",-1)),(o(!0),n(P,null,B(c.filters,s=>(o(),n("div",qs,a(s),1))),256))])):u("",!0)])):u("",!0),c.assignment?(o(),n("section",Hs,[d(oe,{assignment:c.assignment,"words-seen":e(k).length,"words-correct":e(p).correct,time:e(K),accuracy:Z.value,onCompleted:t[29]||(t[29]=s=>Y())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):u("",!0),e(b)?(o(),n("section",Ns,[r("h5",Rs,[t[56]||(t[56]=g(" Time ")),e(L).time>0?(o(),n("span",Us,"Remaining")):(o(),n("span",Os,"Elapsed"))]),e(b)?(o(),n("div",Ws,[r("div",{class:I(["grow text-left font-medium text-gray-900",e(b).length>10?"text-2xl":"text-4xl"])},a(e(b)),3),e(L).time>0?(o(),n("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[30]||(t[30]=s=>Le())},[d(e(Ve),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):u("",!0),r("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[31]||(t[31]=s=>_e())},[e(Q)?(o(),x(e(ze),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(o(),x(e(Me),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):u("",!0)])):u("",!0),r("section",Ds,[r("div",null,[r("dl",Ks,[(o(!0),n(P,null,B(ke.value,s=>(o(),n("div",{key:s.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[r("dt",Xs,a(s.name),1),r("dd",Js,[s.value?(o(),n("span",Gs,[g(a(s.value)+" ",1),s.unit?(o(),n("span",Ys,a(s.unit),1)):u("",!0)])):(o(),n("span",Zs,"–"))])]))),128))])])]),r("section",null,[t[57]||(t[57]=r("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Performance",-1)),r("div",eo,[(o(),n(P,null,B(be,s=>r("div",{class:I(["h-4 w-full rounded-sm shadow-sm",he.value>=s.value?s.color:"bg-gray-300"]),key:s.value},null,2)),64))])]),e(H)?(o(),n("div",to,[d($,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(w),onClick:t[32]||(t[32]=s=>J())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(w)?(o(),n("span",so,t[58]||(t[58]=[r("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Finishing ...",-1)]))):(o(),n("span",oo,"Finish"))]),_:1})]),_:1},8,["disabled"])])):u("",!0)])):(o(),n("div",ds,[d($e,{sections:c.sections.sectionList,"current-list":xe.value,filters:c.filters},{default:m(()=>[c.assignment?(o(),n("section",ms,[d(oe,{assignment:c.assignment,"words-seen":e(k).length,"words-correct":e(p).correct,time:e(K),accuracy:Z.value,onCompleted:t[25]||(t[25]=s=>Y())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):u("",!0),e(z)&&c.assignment.attempt?(o(),n("div",ps,[t[47]||(t[47]=r("h4",{class:"mt-8 text-left text-sm font-bold text-gray-500 uppercase"}," Assignment in Progress ",-1)),r("p",fs,[g(a(c.assignment.attempt.correct)+" out of "+a(c.assignment.attempt.attempts)+" correct",1),c.assignment.attempt.attempts>0?(o(),n("span",vs," ("+a(Math.round(100*c.assignment.attempt.correct)/c.assignment.attempt.attempts)+"% accuracy)",1)):u("",!0),g(" | "+a(we(c.assignment.attempt.time)),1)])])):u("",!0),r("div",gs,[e(v)().props.user&&e(v)().props.user.membership.subscribed?(o(),n("div",{key:0,class:I(["grid",e(z)?"grid-cols-2 gap-4":"grid-cols-1"])},[d($,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:t[26]||(t[26]=s=>se())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(ne)?(o(),n("span",ys,t[48]||(t[48]=[r("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),n("span",xs,[t[49]||(t[49]=g("Continue")),e(z)?(o(),n("span",hs," Attempt")):u("",!0)]))]),_:1})]),_:1}),e(z)?(o(),x($,{key:0,color:"white",size:"md",class:"inline-flex w-full disabled:bg-gray-200",onClick:t[27]||(t[27]=s=>Ce())},{default:m(()=>[d(M,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:m(()=>[e(ce)?(o(),n("span",ws,t[50]||(t[50]=[r("svg",{class:"inline h-5 w-5 animate-spin text-gray-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),n("span",bs,"Restart"))]),_:1})]),_:1})):u("",!0)],2)):e(v)().props.authenticated?(o(),x($,{key:1,color:"indigo",size:"md",class:"flex w-full items-center justify-center",link:"/subscribe"},{default:m(()=>t[51]||(t[51]=[g("Join LatinTutorial Pro to Attempt")])),_:1})):(o(),x($,{key:2,color:"indigo",size:"md",class:"flex w-full items-center justify-center",onClick:t[28]||(t[28]=s=>se())},{default:m(()=>t[52]||(t[52]=[g("Log in to Continue")])),_:1})),ge.value&&e(v)().props.authenticated?(o(),n("p",ks," This set needs more than 5 words to continue. ")):u("",!0)])]),_:1},8,["sections","current-list","filters"])]))])]),_:1})}}};export{vr as default};
