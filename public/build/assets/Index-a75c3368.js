import{_ as h,a as b}from"./AppLayout-33f062bc.js";import{_ as v}from"./Breadcrumbs-c96e9207.js";import{i as w,o as s,c as A,w as p,b as i,a as t,u as c,d as n,h as k,k as l,t as m,f as D,F as B}from"./app-f0078ddb.js";import{D as d}from"./datetime-8ddd27a0.js";import{_ as C}from"./Footer-0988dcd8.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";/* empty css            */const E={class:"p-8 pb-16"},V={class:"mt-8 w-full","aria-labelledby":"Content"},$={class:"grid grid-cols-4 gap-x-8 gap-y-16"},L={class:"text-lg text-gray-900 font-bold mt-4 font-intro leading-5 text-center"},N={class:"text-base text-gray-600 font-medium mt-4 font-intro leading-5 text-center"},S={key:0,class:"text-xs text-gray-600 font-medium mt-2 font-intro leading-5 text-center"},ft={__name:"Index",props:{achievements:{type:Array,required:!0}},setup(u){let f=[{name:"Achievements",href:"#",current:!0}];const _=o=>l().props.user.achievements.filter(e=>o===e.id).length>0,g=o=>{const e=l().props.user.achievements.filter(a=>o===a.id)[0].pivot.created_at;return d.fromISO(e).toLocaleString(d.DATE_MED)};return(o,e)=>{const a=w("Head");return s(),A(h,null,{default:p(()=>[i(a,null,{default:p(()=>e[0]||(e[0]=[t("title",null,"Achievements",-1)])),_:1}),t("main",null,[t("div",E,[i(v,{class:"lg:col-span-9 xl:grid-cols-10",pages:c(f)},null,8,["pages"]),e[1]||(e[1]=t("section",{class:"mt-8 w-full","aria-labelledby":"Description"},[t("h1",{class:"text-4xl text-gray-900 font-bold"}," Achievements "),t("p",{class:"text-base font-normal text-gray-600 mt-4"}," View all of your achievements on LatinTutorial. ")],-1)),t("section",V,[t("div",$,[(s(!0),n(B,null,k(u.achievements,(r,x)=>(s(),n("div",{key:x,class:"flex flex-col items-center justify-start"},[i(b,{size:"lg",icon:r.icon,level:r.level,"is-earned":c(l)().props.user.achievements.filter(y=>r.id===y.id).length>0},null,8,["icon","level","is-earned"]),t("h3",L,m(r.name),1),t("p",N,m(r.description),1),_(r.id)?(s(),n("p",S," Awarded on "+m(g(r.id)),1)):D("",!0)]))),128))])])]),i(C)])]),_:1})}}};export{ft as default};
