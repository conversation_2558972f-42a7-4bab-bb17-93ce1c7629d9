import{l as v,e as f,p as B,i as j,o as r,d as a,a as t,F as d,h as m,b as y,w as N,f as l,t as i,u as g,j as O,r as R,W as x,g as h,n as F,c as T}from"./app-f0078ddb.js";import{_ as $}from"./DropdownGeneral-ce7a4558.js";import E from"./VideoItemSmall-c82820c8.js";/* empty css            */import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./keyboard-982fc047.js";import"./open-closed-7f51e238.js";const P={class:"grid grid-cols-1 gap-8"},U={key:0},W={class:"mt-2 rounded-lg bg-white shadow-sm"},z={key:0,class:"mt-2 flex flex-row items-center"},D=["src"],G={class:"ml-2 flex grow flex-col"},I={class:"text-lg font-bold text-gray-900 font-intro"},J={key:0},X={key:1},q={class:"text-xs text-gray-600"},H={key:1},K={class:"font-intro text-2xl font-bold"},Q={class:"mt-2 font-sans text-sm text-gray-600 font-medium"},Y={key:0,class:"content-left text-left"},Z=["src"],tt={class:"ml-2 text-base font-medium text-gray-900"},et={key:1,class:"mt-2 text-left text-sm font-medium text-gray-500"},st={key:2,class:"mt-2 content-left text-sm text-left font-bold text-amber-600"},rt={class:"grid grid-cols-10 gap-2 opacity-75 mt-4"},at={"aria-labelledby":"userStats"},ot={class:"grid grid-cols-1 gap-px bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2 lg:shadow-sm rounded-lg overflow-hidden"},nt={class:"text-sm font-medium leading-6 text-gray-500"},it={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},lt={key:0},ut={key:0,class:"ml-1 text-sm font-medium text-gray-500"},ct={key:1},dt={key:0},Vt={__name:"ReviewSidebar",props:{sections:{type:Array,required:!1},currentList:Array,description:String,stats:{type:Object,required:!1},sort:String,activity:Object,videos:Array},emits:["update:sort"],setup(n,{emit:b}){let s=n;const k=b;function w(){s.activity.id?x.get("/practice/grammar/a/"+s.activity.id):x.get("/practice/grammar/c/attempt",{sections:s.currentList})}const _=v(()=>{if(s.stats)switch(!0){case!s.stats.time:return null;case Math.round(s.stats.time/10)<15:return"Just a few seconds spent";case Math.round(s.stats.time/10)<30:return"Less than 30 seconds spent";case Math.round(s.stats.time/10)<60:return"Less than a minute spent";case Math.round(s.stats.time/10)<90:return"About a minute spent";case Math.round(s.stats.time/10)<150:return"About 2 minutes spent";case Math.round(s.stats.time/10)<210:return"About 3 minutes spent";case Math.round(s.stats.time/10)<270:return"About 4 minutes spent";case Math.round(s.stats.time/10)<330:return"About 5 minutes spent";default:return"About "+Math.round(s.stats.time/600)+" minutes spent"}return null}),A=v(()=>s.stats.attempts==0?0:s.stats.attempts<10?s.stats.correct:Math.round(10*s.stats.correct/s.stats.attempts)<1?1:Math.round(10*s.stats.correct/s.stats.attempts)),M=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}];let S=f([{name:"Correct",value:s.stats.correct,unit:"out of "+s.stats.attempts},{name:"Best Streak",value:s.stats.streak,unit:"in a row"},{name:"XP Earned",value:s.stats.xp_earned},{name:"Accuracy",value:s.stats.accuracy,unit:"%"}]),p=[{name:"Attempt Order",value:"order"},{name:"Correct",value:"correct"},{name:"Incorrect",value:"incorrect"}];const L=c=>p.find(o=>o.value==c);let u=f(s.sort?L(s.sort):p[0]);return B(()=>u,c=>{k("update:sort",c.value.value)},{deep:!0}),(c,o)=>{const C=j("Link");return r(),a("div",null,[t("div",P,[t("section",null,[n.sections?(r(),a("div",U,[o[3]||(o[3]=t("h2",{class:"font-intro text-2xl font-bold"},"Custom Grammar Review",-1)),o[4]||(o[4]=t("h5",{class:"text-sm font-bold uppercase text-gray-500 mt-2"}," Sections ",-1)),t("div",W,[(r(!0),a(d,null,m(n.sections,(e,V)=>(r(),a("div",{key:V,class:"w-full px-4 py-2"},[e?(r(),a("div",z,[t("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+e.image_name,class:"h-12 w-12"},null,8,D),t("div",G,[t("h4",I,[h(i(e.work)+" "+i(e.l1)+" "+i(e.book)+".",1),e.start===e.end?(r(),a("span",J,i(e.start),1)):(r(),a("span",X,i(e.start)+"-"+i(e.end),1))]),t("p",q,i(e.word_count)+" nouns and verbs ",1)])])):l("",!0)]))),128)),y(C,{href:"/practice/grammar/c/attempt",data:{sections:n.currentList}},{default:N(()=>o[2]||(o[2]=[t("div",{class:"py-2 px-4 bg-gray-100 group hover:bg-gray-200 rounded-b-lg cursor-pointer transition duration-150 ease-in-out"},[t("div",{class:"cursor-pointer text-sm text-gray-600 group-hover:text-gray-700 text-center font-semibold transition duration-150 ease-in-out"}," Try Again ")],-1)])),_:1},8,["data"])])])):l("",!0),n.activity?(r(),a("div",H,[t("h2",K,i(n.activity.name),1),t("h4",Q,i(n.activity.description),1),t("div",{class:"mt-4 py-2 px-4 bg-gray-100 group hover:bg-gray-200 rounded-lg cursor-pointer transition duration-150 ease-in-out",onClick:o[0]||(o[0]=e=>w())},o[5]||(o[5]=[t("div",{class:"cursor-pointer text-sm text-gray-600 group-hover:text-gray-700 text-center font-semibold transition duration-150 ease-in-out"}," Try Again ",-1)]))])):l("",!0)]),t("section",null,[n.stats?(r(),a("p",Y,[t("img",{src:n.stats.user_avatar,class:"inline h-6 w-6 rounded-full"},null,8,Z),t("span",tt,i(n.stats.user),1)])):l("",!0),n.description?(r(),a("p",et,i(n.description),1)):l("",!0),n.stats?(r(),a("p",st,i(_.value),1)):l("",!0)]),t("section",null,[y($,{modelValue:g(u),"onUpdate:modelValue":o[1]||(o[1]=e=>O(u)?u.value=e:u=e),list:g(p),current:g(u),title:"Sort"},null,8,["modelValue","list","current"])]),t("section",null,[o[6]||(o[6]=t("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Performance",-1)),t("div",rt,[(r(),a(d,null,m(M,e=>t("div",{class:F(["rounded-sm w-full h-4 shadow-sm",A.value>=e.value?e.color:"bg-gray-300"]),key:e.value},null,2)),64))])]),t("section",at,[t("div",null,[t("dl",ot,[(r(!0),a(d,null,m(g(S),e=>(r(),a("div",{key:e.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[t("dt",nt,i(e.name),1),t("dd",it,[e.value?(r(),a("span",lt,[h(i(e.value)+" ",1),e.unit?(r(),a("span",ut,i(e.unit),1)):l("",!0)])):(r(),a("span",ct,"–"))])]))),128))])])]),t("section",null,[n.activity&&n.activity.videos.length>0?(r(),a("div",dt,[o[7]||(o[7]=t("h5",{class:"text-sm font-bold uppercase text-gray-500"}," Video Review ",-1)),t("div",null,[(r(!0),a(d,null,m(n.activity.videos,e=>(r(),T(E,{key:e.id,item:e},null,8,["item"]))),128))])])):l("",!0)]),R(c.$slots,"default")])])}}};export{Vt as default};
