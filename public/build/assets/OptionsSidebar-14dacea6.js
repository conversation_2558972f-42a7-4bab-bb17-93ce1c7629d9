import{i as w,o as r,d as n,a as s,g as a,b as c,w as L,F as d,h as f,f as u,r as _,W as x,t as e,u as b,n as h}from"./app-f0078ddb.js";import{r as k}from"./StarIcon-155a2a28.js";import{r as C}from"./CheckCircleIcon-d86d1232.js";/* empty css            */const S={class:"mt-8"},$={class:"mt-4 text-sm text-blue-600 hover:underline"},E={class:"mt-2 rounded-lg bg-white shadow-sm"},N={key:0,class:"mt-2 flex flex-row items-center"},O=["src"],V={class:"ml-2 flex grow flex-col"},F={class:"font-intro text-lg font-bold text-gray-900"},W={key:0},j={key:1},A={class:"text-xs text-gray-600"},B={key:1,class:"my-2 flex flex-row items-center"},R={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},T={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},q={class:"ml-2 flex grow flex-col"},z={class:"font-intro text-lg font-bold text-gray-900"},D={class:"text-xs text-gray-600"},I={class:"grid grid-cols-2 divide-x divide-gray-200"},M={key:0,class:"mt-4"},G={class:"mr-2 mt-2 inline-block rounded-full bg-slate-200 px-2 py-1 text-xs font-bold text-slate-500"},Q={__name:"OptionsSidebar",props:{sections:Array,currentList:Array,filters:Array},setup(i){let l=i;const g=()=>{x.get("/practice/vocabulary",{sections:l.currentList})},p=()=>{x.get("/words/c/list",{sections:l.currentList,filters:l.filters})};return(v,o)=>{const y=w("Link");return r(),n(d,null,[s("div",S,[o[3]||(o[3]=s("p",{class:"text-sm font-medium text-gray-600"},[s("span",{class:"font-bold"},"CORE"),a(" words are the 1,000 most common words found in Latin literature. Most of the time, these words will make up about two-thirds of a Latin text. It is important that you master these words. ")],-1)),o[4]||(o[4]=s("p",{class:"mt-4 text-sm font-medium text-gray-600"},[s("span",{class:"font-bold"},"NOT CORE"),a(" words are found less frequently than CORE words, but are still important to know in the context of this section. ")],-1)),s("p",$,[c(y,{href:"/words"},{default:L(()=>o[2]||(o[2]=[a("Learn more about vocabulary.")])),_:1})])]),o[9]||(o[9]=s("h5",{class:"mt-8 text-sm font-bold uppercase text-gray-500"},"Sections",-1)),s("div",E,[(r(!0),n(d,null,f(i.sections,(t,m)=>(r(),n("div",{key:m,class:"h-20 w-full px-4 py-2"},[t.sectionList?(r(),n("div",N,[s("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.sectionList.image_name,class:"h-12 w-12"},null,8,O),s("div",V,[s("h4",F,[a(e(t.sectionList.work)+" "+e(t.sectionList.l1)+" "+e(t.sectionList.book)+".",1),t.sectionList.start===t.sectionList.end?(r(),n("span",W,e(t.sectionList.start),1)):(r(),n("span",j,e(t.sectionList.start)+"-"+e(t.sectionList.end),1))]),s("p",A,e(t.sectionList.word_count)+" words | "+e(t.sectionList.core_count)+" core, "+e(t.sectionList.word_count-t.sectionList.core_count)+" not core ",1)])])):u("",!0),t.vocabList?(r(),n("div",B,[o[5]||(o[5]=s("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),t.vocabList.id=="star"?(r(),n("div",R,[c(b(k),{class:"h-8 w-8 stroke-2"})])):t.vocabList.id=="learn"?(r(),n("div",T,[c(b(C),{class:"h-8 w-8 stroke-2"})])):(r(),n("div",{key:2,class:h(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${t.vocabList.icon_color}-300 text-${t.vocabList.icon_color}-700`])},e(t.vocabList.abbreviation),3)),s("div",q,[s("h4",z,e(t.vocabList.name),1),s("p",D,e(t.vocabList.word_count?t.vocabList.word_count:0)+" words | "+e(t.vocabList.core_count?t.vocabList.core_count:0)+" core, "+e(t.vocabList.word_count-t.vocabList.core_count)+" not core ",1)])])):u("",!0)]))),128)),s("div",I,[s("div",{class:"group mt-2 cursor-pointer rounded-bl-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:o[0]||(o[0]=t=>g())},o[6]||(o[6]=[s("div",{class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"}," Edit Sections ",-1)])),s("div",{class:"group mt-2 cursor-pointer rounded-br-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:o[1]||(o[1]=t=>p())},o[7]||(o[7]=[s("div",{class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"}," View/Filter Words ",-1)]))])]),i.filters?(r(),n("div",M,[o[8]||(o[8]=s("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Filters",-1)),(r(!0),n(d,null,f(i.filters,t=>(r(),n("div",G,e(t),1))),256))])):u("",!0),_(v.$slots,"default")],64)}}};export{Q as default};
