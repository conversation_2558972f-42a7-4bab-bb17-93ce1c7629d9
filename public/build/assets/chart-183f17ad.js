var xn=Object.defineProperty;var yn=(i,t,e)=>t in i?xn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var y=(i,t,e)=>(yn(i,typeof t!="symbol"?t+"":t,e),e);/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 <PERSON><PERSON>
 * Released under the MIT License
 */function ee(i){return i+.5|0}const at=(i,t,e)=>Math.max(Math.min(i,e),t);function Wt(i){return at(ee(i*2.55),0,255)}function lt(i){return at(ee(i*255),0,255)}function st(i){return at(ee(i/2.55)/100,0,1)}function yi(i){return at(ee(i*100),0,100)}const G={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ti=[..."0123456789ABCDEF"],Mn=i=>ti[i&15],kn=i=>ti[(i&240)>>4]+ti[i&15],ne=i=>(i&240)>>4===(i&15),vn=i=>ne(i.r)&&ne(i.g)&&ne(i.b)&&ne(i.a);function Sn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&G[i[1]]*17,g:255&G[i[2]]*17,b:255&G[i[3]]*17,a:t===5?G[i[4]]*17:255}:(t===7||t===9)&&(e={r:G[i[1]]<<4|G[i[2]],g:G[i[3]]<<4|G[i[4]],b:G[i[5]]<<4|G[i[6]],a:t===9?G[i[7]]<<4|G[i[8]]:255})),e}const wn=(i,t)=>i<255?t(i):"";function Pn(i){var t=vn(i)?Mn:kn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+wn(i.a,t):void 0}const On=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Is(i,t,e){const s=t*Math.min(e,1-e),n=(o,a=(o+i/30)%12)=>e-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function Dn(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Cn(i,t,e){const s=Is(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function An(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function ai(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),a=Math.min(e,s,n),r=(o+a)/2;let c,l,h;return o!==a&&(h=o-a,l=r>.5?h/(2-o-a):h/(o+a),c=An(e,s,n,h,o),c=c*60+.5),[c|0,l||0,r]}function ri(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(lt)}function ci(i,t,e){return ri(Is,i,t,e)}function Ln(i,t,e){return ri(Cn,i,t,e)}function Tn(i,t,e){return ri(Dn,i,t,e)}function Es(i){return(i%360+360)%360}function Rn(i){const t=On.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Wt(+t[5]):lt(+t[5]));const n=Es(+t[2]),o=+t[3]/100,a=+t[4]/100;return t[1]==="hwb"?s=Ln(n,o,a):t[1]==="hsv"?s=Tn(n,o,a):s=ci(n,o,a),{r:s[0],g:s[1],b:s[2],a:e}}function In(i,t){var e=ai(i);e[0]=Es(e[0]+t),e=ci(e),i.r=e[0],i.g=e[1],i.b=e[2]}function En(i){if(!i)return;const t=ai(i),e=t[0],s=yi(t[1]),n=yi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${st(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const Mi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ki={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Fn(){const i={},t=Object.keys(ki),e=Object.keys(Mi);let s,n,o,a,r;for(s=0;s<t.length;s++){for(a=r=t[s],n=0;n<e.length;n++)o=e[n],r=r.replace(o,Mi[o]);o=parseInt(ki[a],16),i[r]=[o>>16&255,o>>8&255,o&255]}return i}let oe;function zn(i){oe||(oe=Fn(),oe.transparent=[0,0,0,0]);const t=oe[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Bn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Vn(i){const t=Bn.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const a=+t[7];e=t[8]?Wt(a):at(a*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Wt(s):at(s,0,255)),n=255&(t[4]?Wt(n):at(n,0,255)),o=255&(t[6]?Wt(o):at(o,0,255)),{r:s,g:n,b:o,a:e}}}function Nn(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${st(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Be=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,At=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function jn(i,t,e){const s=At(st(i.r)),n=At(st(i.g)),o=At(st(i.b));return{r:lt(Be(s+e*(At(st(t.r))-s))),g:lt(Be(n+e*(At(st(t.g))-n))),b:lt(Be(o+e*(At(st(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ae(i,t,e){if(i){let s=ai(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=ci(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Fs(i,t){return i&&Object.assign(t||{},i)}function vi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=lt(i[3]))):(t=Fs(i,{r:0,g:0,b:0,a:1}),t.a=lt(t.a)),t}function Wn(i){return i.charAt(0)==="r"?Vn(i):Rn(i)}class Kt{constructor(t){if(t instanceof Kt)return t;const e=typeof t;let s;e==="object"?s=vi(t):e==="string"&&(s=Sn(t)||zn(t)||Wn(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Fs(this._rgb);return t&&(t.a=st(t.a)),t}set rgb(t){this._rgb=vi(t)}rgbString(){return this._valid?Nn(this._rgb):void 0}hexString(){return this._valid?Pn(this._rgb):void 0}hslString(){return this._valid?En(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const a=e===o?.5:e,r=2*a-1,c=s.a-n.a,l=((r*c===-1?r:(r+c)/(1+r*c))+1)/2;o=1-l,s.r=255&l*s.r+o*n.r+.5,s.g=255&l*s.g+o*n.g+.5,s.b=255&l*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=jn(this._rgb,t._rgb,e)),this}clone(){return new Kt(this.rgb)}alpha(t){return this._rgb.a=lt(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ee(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ae(this._rgb,2,t),this}darken(t){return ae(this._rgb,2,-t),this}saturate(t){return ae(this._rgb,1,t),this}desaturate(t){return ae(this._rgb,1,-t),this}rotate(t){return In(this._rgb,t),this}}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */const Hn=(()=>{let i=0;return()=>i++})();function O(i){return i==null}function z(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function D(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function W(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function K(i,t){return W(i)?i:t}function C(i,t){return typeof i>"u"?t:i}const $n=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,zs=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function F(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function $(i,t,e,s){let n,o,a;if(z(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(D(i))for(a=Object.keys(i),o=a.length,n=0;n<o;n++)t.call(e,i[a[n]],a[n])}function Si(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Se(i){if(z(i))return i.map(Se);if(D(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=Se(i[e[n]]);return t}return i}function Bs(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function Yn(i,t,e,s){if(!Bs(i))return;const n=t[i],o=e[i];D(n)&&D(o)?Gt(n,o,s):t[i]=Se(o)}function Gt(i,t,e){const s=z(t)?t:[t],n=s.length;if(!D(i))return i;e=e||{};const o=e.merger||Yn;let a;for(let r=0;r<n;++r){if(a=s[r],!D(a))continue;const c=Object.keys(a);for(let l=0,h=c.length;l<h;++l)o(c[l],i,a,e)}return i}function $t(i,t){return Gt(i,t,{merger:Un})}function Un(i,t,e){if(!Bs(i))return;const s=t[i],n=e[i];D(s)&&D(n)?$t(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=Se(n))}const wi={"":i=>i,x:i=>i.x,y:i=>i.y};function Xn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Kn(i){const t=Xn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function St(i,t){return(wi[t]||(wi[t]=Kn(t)))(i)}function li(i){return i.charAt(0).toUpperCase()+i.slice(1)}const qt=i=>typeof i<"u",ht=i=>typeof i=="function",Pi=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Gn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const B=Math.PI,I=2*B,qn=I+B,we=Number.POSITIVE_INFINITY,Zn=B/180,N=B/2,gt=B/4,Oi=B*2/3,rt=Math.log10,et=Math.sign;function Yt(i,t,e){return Math.abs(i-t)<e}function Di(i){const t=Math.round(i);i=Yt(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(rt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Qn(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Jn(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function Zt(i){return!Jn(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function to(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Vs(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function tt(i){return i*(B/180)}function hi(i){return i*(180/B)}function Ci(i){if(!W(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Ns(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*B&&(o+=I),{angle:o,distance:n}}function Ai(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function eo(i,t){return(i-t+qn)%I-B}function Q(i){return(i%I+I)%I}function Qt(i,t,e,s){const n=Q(i),o=Q(t),a=Q(e),r=Q(o-n),c=Q(a-n),l=Q(n-o),h=Q(n-a);return n===o||n===a||s&&o===a||r>c&&l<h}function U(i,t,e){return Math.max(t,Math.min(e,i))}function io(i){return U(i,-32768,32767)}function Pe(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function di(i,t,e){e=e||(a=>i[a]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Mt=(i,t,e,s)=>di(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),so=(i,t,e)=>di(i,e,s=>i[s][t]>=e);function no(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const js=["push","pop","shift","splice","unshift"];function oo(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),js.forEach(e=>{const s="_onData"+li(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const a=n.apply(this,o);return i._chartjs.listeners.forEach(r=>{typeof r[s]=="function"&&r[s](...o)}),a}})})}function Li(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(js.forEach(o=>{delete i[o]}),delete i._chartjs)}function Ws(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Hs=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function $s(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Hs.call(window,()=>{s=!1,i.apply(t,e)}))}}function ao(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const ro=i=>i==="start"?"left":i==="end"?"right":"center",Ti=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2;function co(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:a,vScale:r,_parsed:c}=i,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null,h=a.axis,{min:d,max:u,minDefined:f,maxDefined:p}=a.getUserBounds();if(f){if(n=Math.min(Mt(c,h,d).lo,e?s:Mt(t,h,a.getPixelForValue(d)).lo),l){const g=c.slice(0,n+1).reverse().findIndex(m=>!O(m[r.axis]));n-=Math.max(0,g)}n=U(n,0,s-1)}if(p){let g=Math.max(Mt(c,a.axis,u,!0).hi+1,e?0:Mt(t,h,a.getPixelForValue(u),!0).hi+1);if(l){const m=c.slice(g-1).findIndex(b=>!O(b[r.axis]));g+=Math.max(0,m)}o=U(g,n,s)-n}else o=s-n}return{start:n,count:o}}function lo(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const re=i=>i===0||i===1,Ri=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*I/e)),Ii=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*I/e)+1,Ut={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*N)+1,easeOutSine:i=>Math.sin(i*N),easeInOutSine:i=>-.5*(Math.cos(B*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>re(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>re(i)?i:Ri(i,.075,.3),easeOutElastic:i=>re(i)?i:Ii(i,.075,.3),easeInOutElastic(i){return re(i)?i:i<.5?.5*Ri(i*2,.1125,.45):.5+.5*Ii(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Ut.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Ut.easeInBounce(i*2)*.5:Ut.easeOutBounce(i*2-1)*.5+.5};function ui(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Ei(i){return ui(i)?i:new Kt(i)}function Ve(i){return ui(i)?i:new Kt(i).saturate(.5).darken(.1).hexString()}const ho=["x","y","borderWidth","radius","tension"],uo=["color","borderColor","backgroundColor"];function fo(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:uo},numbers:{type:"number",properties:ho}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function go(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Fi=new Map;function po(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Fi.get(e);return s||(s=new Intl.NumberFormat(i,t),Fi.set(e,s)),s}function Ie(i,t,e){return po(t,e).format(i)}const Ys={values(i){return z(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const l=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(l<1e-4||l>1e15)&&(n="scientific"),o=mo(i,e)}const a=rt(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),c={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(c,this.options.ticks.format),Ie(i,s,c)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(rt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Ys.numeric.call(this,i,t,e):""}};function mo(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Ee={formatters:Ys};function bo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ee.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const wt=Object.create(null),ei=Object.create(null);function Xt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Ne(i,t,e){return typeof t=="string"?Gt(Xt(i,t),e):Gt(Xt(i,""),t)}class _o{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Ve(n.backgroundColor),this.hoverBorderColor=(s,n)=>Ve(n.borderColor),this.hoverColor=(s,n)=>Ve(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Ne(this,t,e)}get(t){return Xt(this,t)}describe(t,e){return Ne(ei,t,e)}override(t,e){return Ne(wt,t,e)}route(t,e,s,n){const o=Xt(this,t),a=Xt(this,s),r="_"+e;Object.defineProperties(o,{[r]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const c=this[r],l=a[n];return D(c)?Object.assign({},l,c):C(c,l)},set(c){this[r]=c}}})}apply(t){t.forEach(e=>e(this))}}var j=new _o({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[fo,go,bo]);function xo(i){return!i||O(i.size)||O(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Oe(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function yo(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let a=0;const r=e.length;let c,l,h,d,u;for(c=0;c<r;c++)if(d=e[c],d!=null&&!z(d))a=Oe(i,n,o,a,d);else if(z(d))for(l=0,h=d.length;l<h;l++)u=d[l],u!=null&&!z(u)&&(a=Oe(i,n,o,a,u));i.restore();const f=o.length/2;if(f>e.length){for(c=0;c<f;c++)delete n[o[c]];o.splice(0,f)}return a}function pt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function zi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Mo(i,t,e,s){ko(i,t,e,s,null)}function ko(i,t,e,s,n){let o,a,r,c,l,h,d,u;const f=t.pointStyle,p=t.rotation,g=t.radius;let m=(p||0)*Zn;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(g)||g<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,g,0,0,I):i.arc(e,s,g,0,I),i.closePath();break;case"triangle":h=n?n/2:g,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*g),m+=Oi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*g),m+=Oi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*g),i.closePath();break;case"rectRounded":l=g*.516,c=g-l,a=Math.cos(m+gt)*c,d=Math.cos(m+gt)*(n?n/2-l:c),r=Math.sin(m+gt)*c,u=Math.sin(m+gt)*(n?n/2-l:c),i.arc(e-d,s-r,l,m-B,m-N),i.arc(e+u,s-a,l,m-N,m),i.arc(e+d,s+r,l,m,m+N),i.arc(e-u,s+a,l,m+N,m+B),i.closePath();break;case"rect":if(!p){c=Math.SQRT1_2*g,h=n?n/2:c,i.rect(e-h,s-c,2*h,2*c);break}m+=gt;case"rectRot":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-r),i.lineTo(e+u,s-a),i.lineTo(e+d,s+r),i.lineTo(e-u,s+a),i.closePath();break;case"crossRot":m+=gt;case"cross":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"star":d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a),m+=gt,d=Math.cos(m)*(n?n/2:g),a=Math.cos(m)*g,r=Math.sin(m)*g,u=Math.sin(m)*(n?n/2:g),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"line":a=n?n/2:Math.cos(m)*g,r=Math.sin(m)*g,i.moveTo(e-a,s-r),i.lineTo(e+a,s+r);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:g),s+Math.sin(m)*g);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function nt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Us(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Xs(i){i.restore()}function vo(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function So(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function wo(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),O(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Po(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),a=t-o.actualBoundingBoxLeft,r=t+o.actualBoundingBoxRight,c=e-o.actualBoundingBoxAscent,l=e+o.actualBoundingBoxDescent,h=n.strikethrough?(c+l)/2:l;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(a,h),i.lineTo(r,h),i.stroke()}}function Oo(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function De(i,t,e,s,n,o={}){const a=z(t)?t:[t],r=o.strokeWidth>0&&o.strokeColor!=="";let c,l;for(i.save(),i.font=n.string,wo(i,o),c=0;c<a.length;++c)l=a[c],o.backdrop&&Oo(i,o.backdrop),r&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),O(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(l,e,s,o.maxWidth)),i.fillText(l,e,s,o.maxWidth),Po(i,e,s,l,o),s+=Number(n.lineHeight);i.restore()}function Ks(i,t){const{x:e,y:s,w:n,h:o,radius:a}=t;i.arc(e+a.topLeft,s+a.topLeft,a.topLeft,1.5*B,B,!0),i.lineTo(e,s+o-a.bottomLeft),i.arc(e+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,B,N,!0),i.lineTo(e+n-a.bottomRight,s+o),i.arc(e+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,N,0,!0),i.lineTo(e+n,s+a.topRight),i.arc(e+n-a.topRight,s+a.topRight,a.topRight,0,-N,!0),i.lineTo(e+a.topLeft,s)}const Do=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Co=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Ao(i,t){const e=(""+i).match(Do);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Lo=i=>+i||0;function fi(i,t){const e={},s=D(t),n=s?Object.keys(t):t,o=D(i)?s?a=>C(i[a],i[t[a]]):a=>i[a]:()=>i;for(const a of n)e[a]=Lo(o(a));return e}function Gs(i){return fi(i,{top:"y",right:"x",bottom:"y",left:"x"})}function qs(i){return fi(i,["topLeft","topRight","bottomLeft","bottomRight"])}function ot(i){const t=Gs(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Tt(i,t){i=i||{},t=t||j.font;let e=C(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=C(i.style,t.style);s&&!(""+s).match(Co)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:C(i.family,t.family),lineHeight:Ao(C(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:C(i.weight,t.weight),string:""};return n.string=xo(n),n}function ce(i,t,e,s){let n=!0,o,a,r;for(o=0,a=i.length;o<a;++o)if(r=i[o],r!==void 0&&(t!==void 0&&typeof r=="function"&&(r=r(t),n=!1),e!==void 0&&z(r)&&(r=r[e%r.length],n=!1),r!==void 0))return s&&!n&&(s.cacheable=!1),r}function To(i,t,e){const{min:s,max:n}=i,o=zs(t,(n-s)/2),a=(r,c)=>e&&r===0?0:r+c;return{min:a(s,-Math.abs(o)),max:a(n,o)}}function Pt(i,t){return Object.assign(Object.create(i),t)}function gi(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=tn("_fallback",i));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>gi([r,...i],t,o,s)};return new Proxy(a,{deleteProperty(r,c){return delete r[c],delete r._keys,delete i[0][c],!0},get(r,c){return Qs(r,c,()=>No(c,t,i,r))},getOwnPropertyDescriptor(r,c){return Reflect.getOwnPropertyDescriptor(r._scopes[0],c)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(r,c){return Vi(r).includes(c)},ownKeys(r){return Vi(r)},set(r,c,l){const h=r._storage||(r._storage=n());return r[c]=h[c]=l,delete r._keys,!0}})}function Rt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Zs(i,s),setContext:o=>Rt(i,o,e,s),override:o=>Rt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,a){return delete o[a],delete i[a],!0},get(o,a,r){return Qs(o,a,()=>Io(o,a,r))},getOwnPropertyDescriptor(o,a){return o._descriptors.allKeys?Reflect.has(i,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,a)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,a){return Reflect.has(i,a)},ownKeys(){return Reflect.ownKeys(i)},set(o,a,r){return i[a]=r,delete o[a],!0}})}function Zs(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ht(e)?e:()=>e,isIndexable:ht(s)?s:()=>s}}const Ro=(i,t)=>i?i+li(t):t,pi=(i,t)=>D(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Qs(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function Io(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=i;let r=s[t];return ht(r)&&a.isScriptable(t)&&(r=Eo(t,r,i,e)),z(r)&&r.length&&(r=Fo(t,r,i,a.isIndexable)),pi(t,r)&&(r=Rt(r,n,o&&o[t],a)),r}function Eo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_stack:r}=e;if(r.has(i))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+i);r.add(i);let c=t(o,a||s);return r.delete(i),pi(i,c)&&(c=mi(n._scopes,n,i,c)),c}function Fo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(D(t[0])){const c=t,l=n._scopes.filter(h=>h!==c);t=[];for(const h of c){const d=mi(l,n,i,h);t.push(Rt(d,o,a&&a[i],r))}}return t}function Js(i,t,e){return ht(i)?i(t,e):i}const zo=(i,t)=>i===!0?t:typeof i=="string"?St(t,i):void 0;function Bo(i,t,e,s,n){for(const o of t){const a=zo(e,o);if(a){i.add(a);const r=Js(a._fallback,e,n);if(typeof r<"u"&&r!==e&&r!==s)return r}else if(a===!1&&typeof s<"u"&&e!==s)return null}return!1}function mi(i,t,e,s){const n=t._rootScopes,o=Js(t._fallback,e,s),a=[...i,...n],r=new Set;r.add(s);let c=Bi(r,a,e,o||e,s);return c===null||typeof o<"u"&&o!==e&&(c=Bi(r,a,o,c,s),c===null)?!1:gi(Array.from(r),[""],n,o,()=>Vo(t,e,s))}function Bi(i,t,e,s,n){for(;e;)e=Bo(i,t,e,s,n);return e}function Vo(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return z(n)&&D(e)?e:n||{}}function No(i,t,e,s){let n;for(const o of t)if(n=tn(Ro(o,i),e),typeof n<"u")return pi(i,n)?mi(e,s,i,n):n}function tn(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Vi(i){let t=i._keys;return t||(t=i._keys=jo(i._scopes)),t}function jo(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const Wo=Number.EPSILON||1e-14,It=(i,t)=>t<i.length&&!i[t].skip&&i[t],en=i=>i==="x"?"y":"x";function Ho(i,t,e,s){const n=i.skip?t:i,o=t,a=e.skip?t:e,r=Ai(o,n),c=Ai(a,o);let l=r/(r+c),h=c/(r+c);l=isNaN(l)?0:l,h=isNaN(h)?0:h;const d=s*l,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function $o(i,t,e){const s=i.length;let n,o,a,r,c,l=It(i,0);for(let h=0;h<s-1;++h)if(c=l,l=It(i,h+1),!(!c||!l)){if(Yt(t[h],0,Wo)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),e[h]=n*a*t[h],e[h+1]=o*a*t[h])}}function Yo(i,t,e="x"){const s=en(e),n=i.length;let o,a,r,c=It(i,0);for(let l=0;l<n;++l){if(a=r,r=c,c=It(i,l+1),!r)continue;const h=r[e],d=r[s];a&&(o=(h-a[e])/3,r[`cp1${e}`]=h-o,r[`cp1${s}`]=d-o*t[l]),c&&(o=(c[e]-h)/3,r[`cp2${e}`]=h+o,r[`cp2${s}`]=d+o*t[l])}}function Uo(i,t="x"){const e=en(t),s=i.length,n=Array(s).fill(0),o=Array(s);let a,r,c,l=It(i,0);for(a=0;a<s;++a)if(r=c,c=l,l=It(i,a+1),!!c){if(l){const h=l[t]-c[t];n[a]=h!==0?(l[e]-c[e])/h:0}o[a]=r?l?et(n[a-1])!==et(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}$o(i,n,o),Yo(i,o,t)}function le(i,t,e){return Math.max(Math.min(i,e),t)}function Xo(i,t){let e,s,n,o,a,r=nt(i[0],t);for(e=0,s=i.length;e<s;++e)a=o,o=r,r=e<s-1&&nt(i[e+1],t),o&&(n=i[e],a&&(n.cp1x=le(n.cp1x,t.left,t.right),n.cp1y=le(n.cp1y,t.top,t.bottom)),r&&(n.cp2x=le(n.cp2x,t.left,t.right),n.cp2y=le(n.cp2y,t.top,t.bottom)))}function Ko(i,t,e,s,n){let o,a,r,c;if(t.spanGaps&&(i=i.filter(l=>!l.skip)),t.cubicInterpolationMode==="monotone")Uo(i,n);else{let l=s?i[i.length-1]:i[0];for(o=0,a=i.length;o<a;++o)r=i[o],c=Ho(l,r,i[Math.min(o+1,a-(s?0:1))%a],t.tension),r.cp1x=c.previous.x,r.cp1y=c.previous.y,r.cp2x=c.next.x,r.cp2y=c.next.y,l=r}t.capBezierPoints&&Xo(i,e)}function bi(){return typeof window<"u"&&typeof document<"u"}function _i(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ce(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Fe=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Go(i,t){return Fe(i).getPropertyValue(t)}const qo=["top","right","bottom","left"];function kt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=qo[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Zo=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Qo(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let a=!1,r,c;if(Zo(n,o,i.target))r=n,c=o;else{const l=t.getBoundingClientRect();r=s.clientX-l.left,c=s.clientY-l.top,a=!0}return{x:r,y:c,box:a}}function _t(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Fe(e),o=n.boxSizing==="border-box",a=kt(n,"padding"),r=kt(n,"border","width"),{x:c,y:l,box:h}=Qo(i,e),d=a.left+(h&&r.left),u=a.top+(h&&r.top);let{width:f,height:p}=t;return o&&(f-=a.width+r.width,p-=a.height+r.height),{x:Math.round((c-d)/f*e.width/s),y:Math.round((l-u)/p*e.height/s)}}function Jo(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&_i(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const a=o.getBoundingClientRect(),r=Fe(o),c=kt(r,"border","width"),l=kt(r,"padding");t=a.width-l.width-c.width,e=a.height-l.height-c.height,s=Ce(r.maxWidth,o,"clientWidth"),n=Ce(r.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||we,maxHeight:n||we}}const he=i=>Math.round(i*10)/10;function ta(i,t,e,s){const n=Fe(i),o=kt(n,"margin"),a=Ce(n.maxWidth,i,"clientWidth")||we,r=Ce(n.maxHeight,i,"clientHeight")||we,c=Jo(i,t,e);let{width:l,height:h}=c;if(n.boxSizing==="content-box"){const u=kt(n,"border","width"),f=kt(n,"padding");l-=f.width+u.width,h-=f.height+u.height}return l=Math.max(0,l-o.width),h=Math.max(0,s?l/s:h-o.height),l=he(Math.min(l,a,c.maxWidth)),h=he(Math.min(h,r,c.maxHeight)),l&&!h&&(h=he(l/2)),(t!==void 0||e!==void 0)&&s&&c.height&&h>c.height&&(h=c.height,l=he(Math.floor(h*s))),{width:l,height:h}}function Ni(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const a=i.canvas;return a.style&&(e||!a.style.height&&!a.style.width)&&(a.style.height=`${i.height}px`,a.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||a.height!==n||a.width!==o?(i.currentDevicePixelRatio=s,a.height=n,a.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const ea=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};bi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function ji(i,t){const e=Go(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function xt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function ia(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function sa(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},a=xt(i,n,e),r=xt(n,o,e),c=xt(o,t,e),l=xt(a,r,e),h=xt(r,c,e);return xt(l,h,e)}function sn(i){return i==="angle"?{between:Qt,compare:eo,normalize:Q}:{between:Pe,compare:(t,e)=>t-e,normalize:t=>t}}function Wi({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function na(i,t,e){const{property:s,start:n,end:o}=e,{between:a,normalize:r}=sn(s),c=t.length;let{start:l,end:h,loop:d}=i,u,f;if(d){for(l+=c,h+=c,u=0,f=c;u<f&&a(r(t[l%c][s]),n,o);++u)l--,h--;l%=c,h%=c}return h<l&&(h+=c),{start:l,end:h,loop:d,style:i.style}}function oa(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,a=t.length,{compare:r,between:c,normalize:l}=sn(s),{start:h,end:d,loop:u,style:f}=na(i,t,e),p=[];let g=!1,m=null,b,_,M;const v=()=>c(n,M,b)&&r(n,M)!==0,x=()=>r(o,b)===0||c(o,M,b),w=()=>g||v(),k=()=>!g||x();for(let S=h,P=h;S<=d;++S)_=t[S%a],!_.skip&&(b=l(_[s]),b!==M&&(g=c(b,n,o),m===null&&w()&&(m=r(b,n)===0?S:P),m!==null&&k()&&(p.push(Wi({start:m,end:S,loop:u,count:a,style:f})),m=null),P=S,M=b));return m!==null&&p.push(Wi({start:m,end:d,loop:u,count:a,style:f})),p}function aa(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=oa(s[n],i.points,t);o.length&&e.push(...o)}return e}function ra(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function ca(i,t,e,s){const n=i.length,o=[];let a=t,r=i[t],c;for(c=t+1;c<=e;++c){const l=i[c%n];l.skip||l.stop?r.skip||(s=!1,o.push({start:t%n,end:(c-1)%n,loop:s}),t=a=l.stop?c:null):(a=c,r.skip&&(t=c)),r=l}return a!==null&&o.push({start:t%n,end:a%n,loop:s}),o}function la(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:a,end:r}=ra(e,n,o,s);if(s===!0)return Hi(i,[{start:a,end:r,loop:o}],e,t);const c=r<a?r+n:r,l=!!i._fullLoop&&a===0&&r===n-1;return Hi(i,ca(e,a,c,l),e,t)}function Hi(i,t,e,s){return!s||!s.setContext||!e?t:ha(i,t,e,s)}function ha(i,t,e,s){const n=i._chart.getContext(),o=$i(i.options),{_datasetIndex:a,options:{spanGaps:r}}=i,c=e.length,l=[];let h=o,d=t[0].start,u=d;function f(p,g,m,b){const _=r?-1:1;if(p!==g){for(p+=c;e[p%c].skip;)p-=_;for(;e[g%c].skip;)g+=_;p%c!==g%c&&(l.push({start:p%c,end:g%c,loop:m,style:b}),h=b,d=g%c)}}for(const p of t){d=r?d:p.start;let g=e[d%c],m;for(u=d+1;u<=p.end;u++){const b=e[u%c];m=$i(s.setContext(Pt(n,{type:"segment",p0:g,p1:b,p0DataIndex:(u-1)%c,p1DataIndex:u%c,datasetIndex:a}))),da(m,h)&&f(d,u-1,p.loop,h),g=b,h=m}d<u-1&&f(d,u-1,p.loop,h)}return l}function $i(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function da(i,t){if(!t)return!1;const e=[],s=function(n,o){return ui(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}/*!
 * Chart.js v4.4.8
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class ua{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],a=e.duration;o.forEach(r=>r({chart:t,initial:e.initial,numSteps:a,currentStep:Math.min(s-e.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Hs.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let a=o.length-1,r=!1,c;for(;a>=0;--a)c=o[a],c._active?(c._total>s.duration&&(s.duration=c._total),c.tick(t),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var it=new ua;const Yi="transparent",fa={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Ei(i||Yi),n=s.valid&&Ei(t||Yi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class ga{constructor(t,e,s,n){const o=e[s];n=ce([t.to,n,o,t.from]);const a=ce([t.from,o,n]);this._active=!0,this._fn=t.fn||fa[t.type||typeof a],this._easing=Ut[t.easing]||Ut.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ce([t.to,e,n,t.from]),this._from=ce([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to;let c;if(this._active=o!==r&&(a||e<s),!this._active){this._target[n]=r,this._notify(!0);return}if(e<0){this._target[n]=o;return}c=e/s%2,c=a&&c>1?2-c:c,c=this._easing(Math.min(1,Math.max(0,c))),this._target[n]=this._fn(o,r,c)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class pa{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!D(t))return;const e=Object.keys(j.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!D(o))return;const a={};for(const r of e)a[r]=o[r];(z(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(t,e){const s=e.options,n=ba(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&ma(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),a=Object.keys(e),r=Date.now();let c;for(c=a.length-1;c>=0;--c){const l=a[c];if(l.charAt(0)==="$")continue;if(l==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[l];let d=o[l];const u=s.get(l);if(d)if(u&&d.active()){d.update(u,h,r);continue}else d.cancel();if(!u||!u.duration){t[l]=h;continue}o[l]=d=new ga(u,t,l,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return it.add(this._chart,s),!0}}function ma(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function ba(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Ui(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function _a(i,t,e){if(e===!1)return!1;const s=Ui(i,e),n=Ui(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function xa(i){let t,e,s,n;return D(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function nn(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Xi(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let a,r,c,l;if(t===null)return;let h=!1;for(a=0,r=n.length;a<r;++a){if(c=+n[a],c===e){if(h=!0,s.all)continue;break}l=i.values[c],W(l)&&(o||t===0||et(t)===et(l))&&(t+=l)}return!h&&!s.all?0:t}function ya(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",a=Object.keys(i),r=new Array(a.length);let c,l,h;for(c=0,l=a.length;c<l;++c)h=a[c],r[c]={[n]:h,[o]:i[h]};return r}function je(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Ma(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function ka(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function va(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Ki(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Gi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:a,index:r}=s,c=o.axis,l=a.axis,h=Ma(o,a,s),d=t.length;let u;for(let f=0;f<d;++f){const p=t[f],{[c]:g,[l]:m}=p,b=p._stacks||(p._stacks={});u=b[l]=va(n,h,g),u[r]=m,u._top=Ki(u,a,!0,s.type),u._bottom=Ki(u,a,!1,s.type);const _=u._visualValues||(u._visualValues={});_[r]=m}}function We(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Sa(i,t){return Pt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function wa(i,t,e){return Pt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function zt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const He=i=>i==="reset"||i==="none",qi=(i,t)=>t?i:Object.assign({},i),Pa=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:nn(e,!0),values:null};class vt{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=je(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&zt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,p)=>d==="x"?u:d==="r"?p:f,o=e.xAxisID=C(s.xAxisID,We(t,"x")),a=e.yAxisID=C(s.yAxisID,We(t,"y")),r=e.rAxisID=C(s.rAxisID,We(t,"r")),c=e.indexAxis,l=e.iAxisID=n(c,o,a,r),h=e.vAxisID=n(c,a,o,r);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(a),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Li(this._data,this),t._stacked&&zt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(D(e)){const n=this._cachedMeta;this._data=ya(e,n)}else if(s!==e){if(s){Li(s,this);const n=this._cachedMeta;zt(n),n._parsed=[]}e&&Object.isExtensible(e)&&oo(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=je(e.vScale,e),e.stack!==s.stack&&(n=!0,zt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(Gi(this,e._parsed),e._stacked=je(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:a}=s,r=o.axis;let c=t===0&&e===n.length?!0:s._sorted,l=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{z(n[t])?u=this.parseArrayData(s,n,t,e):D(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[r]===null||l&&d[r]<l[r];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],c&&(f()&&(c=!1),l=d);s._sorted=c}a&&Gi(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:a}=t,r=o.axis,c=a.axis,l=o.getLabels(),h=o===a,d=new Array(n);let u,f,p;for(u=0,f=n;u<f;++u)p=u+s,d[u]={[r]:h||o.parse(l[p],p),[c]:a.parse(e[p],p)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:a}=t,r=new Array(n);let c,l,h,d;for(c=0,l=n;c<l;++c)h=c+s,d=e[h],r[c]={x:o.parse(d[0],h),y:a.parse(d[1],h)};return r}parseObjectData(t,e,s,n){const{xScale:o,yScale:a}=t,{xAxisKey:r="x",yAxisKey:c="y"}=this._parsing,l=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],l[h]={x:o.parse(St(f,r),u),y:a.parse(St(f,c),u)};return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,a=e[t.axis],r={keys:nn(n,!0),values:e._stacks[t.axis]._visualValues};return Xi(r,a,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let a=o===null?NaN:o;const r=n&&s._stacks[e.axis];n&&r&&(n.values=r,a=Xi(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,a=n.length,r=this._getOtherScale(t),c=Pa(e,s,this.chart),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=ka(r);let u,f;function p(){f=n[u];const g=f[r.axis];return!W(f[t.axis])||h>g||d<g}for(u=0;u<a&&!(!p()&&(this.updateRangeFromParsed(l,t,f,c),o));++u);if(o){for(u=a-1;u>=0;--u)if(!p()){this.updateRangeFromParsed(l,t,f,c);break}}return l}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,a;for(n=0,o=e.length;n<o;++n)a=e[n][t.axis],W(a)&&s.push(a);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=xa(C(this.options.clip,_a(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,a=[],r=this._drawStart||0,c=this._drawCount||n.length-r,l=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,r,c),h=r;h<r+c;++h){const d=n[h];d.hidden||(d.active&&l?a.push(d):d.draw(t,o))}for(h=0;h<a.length;++h)a[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const a=this._cachedMeta.data[t];o=a.$context||(a.$context=wa(this.getContext(),t,a)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Sa(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,a=t+"-"+e,r=o[a],c=this.enableOptionSharing&&qt(s);if(r)return qi(r,c);const l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=l.getOptionScopes(this.getDataset(),h),f=Object.keys(j.elements[t]),p=()=>this.getContext(s,n,e),g=l.resolveNamedOptions(u,f,p,d);return g.$shared&&(g.$shared=c,o[a]=Object.freeze(qi(g,c))),g}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,a=`animation-${e}`,r=o[a];if(r)return r;let c;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);c=h.createResolver(u,this.getContext(t,s,e))}const l=new pa(n,c&&c.animations);return c&&c._cacheable&&(o[a]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||He(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),a=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:a}}updateElement(t,e,s,n){He(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!He(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[r,c,l]of this._syncList)this[r](c,l);this._syncList=[];const n=s.length,o=e.length,a=Math.min(o,n);a&&this.parse(0,a),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,a=t+e;let r;const c=l=>{for(l.length+=e,r=l.length-1;r>=a;r--)l[r]=l[r-e]};for(c(o),r=t;r<a;++r)o[r]=new this.dataElementType;this._parsing&&c(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&zt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}y(vt,"defaults",{}),y(vt,"datasetElementType",null),y(vt,"dataElementType",null);function Oa(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Ws(s.sort((n,o)=>n-o))}return i._cache.$bar}function Da(i){const t=i.iScale,e=Oa(t,i.type);let s=t._length,n,o,a,r;const c=()=>{a===32767||a===-32768||(qt(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=e.length;n<o;++n)a=t.getPixelForValue(e[n]),c();for(r=void 0,n=0,o=t.ticks.length;n<o;++n)a=t.getPixelForTick(n),c();return s}function Ca(i,t,e,s){const n=e.barThickness;let o,a;return O(n)?(o=t.min*e.categoryPercentage,a=e.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:t.pixels[i]-o/2}}function Aa(i,t,e,s){const n=t.pixels,o=n[i];let a=i>0?n[i-1]:null,r=i<n.length-1?n[i+1]:null;const c=e.categoryPercentage;a===null&&(a=o-(r===null?t.end-t.start:r-o)),r===null&&(r=o+o-a);const l=o-(o-Math.min(a,r))/2*c;return{chunk:Math.abs(r-a)/2*c/s,ratio:e.barPercentage,start:l}}function La(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),a=Math.min(n,o),r=Math.max(n,o);let c=a,l=r;Math.abs(a)>Math.abs(r)&&(c=r,l=a),t[e.axis]=l,t._custom={barStart:c,barEnd:l,start:n,end:o,min:a,max:r}}function on(i,t,e,s){return z(i)?La(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Zi(i,t,e,s){const n=i.iScale,o=i.vScale,a=n.getLabels(),r=n===o,c=[];let l,h,d,u;for(l=e,h=e+s;l<h;++l)u=t[l],d={},d[n.axis]=r||n.parse(a[l],l),c.push(on(u,d,o,l));return c}function $e(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function Ta(i,t,e){return i!==0?et(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Ra(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function Ia(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:a,end:r,reverse:c,top:l,bottom:h}=Ra(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=l:(e._bottom||0)===s?n=h:(o[Qi(h,a,r,c)]=!0,n=l)),o[Qi(n,a,r,c)]=!0,i.borderSkipped=o}function Qi(i,t,e,s){return s?(i=Ea(i,t,e),i=Ji(i,e,t)):i=Ji(i,t,e),i}function Ea(i,t,e){return i===t?e:i===e?t:i}function Ji(i,t,e){return i==="start"?t:i==="end"?e:i}function Fa(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class Ye extends vt{parsePrimitiveData(t,e,s,n){return Zi(t,e,s,n)}parseArrayData(t,e,s,n){return Zi(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:a}=t,{xAxisKey:r="x",yAxisKey:c="y"}=this._parsing,l=o.axis==="x"?r:c,h=a.axis==="x"?r:c,d=[];let u,f,p,g;for(u=s,f=s+n;u<f;++u)g=e[u],p={},p[o.axis]=o.parse(St(g,l),u),d.push(on(St(g,h),p,a,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),a=o._custom,r=$e(a)?"["+a.start+", "+a.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:a,_cachedMeta:{vScale:r}}=this,c=r.getBasePixel(),l=r.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const p=this.getParsed(f),g=o||O(p[r.axis])?{base:c,head:c}:this._calculateBarValuePixels(f),m=this._calculateBarIndexPixels(f,h),b=(p._stacks||{})[r.axis],_={horizontal:l,base:g.base,enableBorderRadius:!b||$e(p._custom)||a===b._top||a===b._bottom,x:l?g.head:m.center,y:l?m.center:g.head,height:l?m.size:Math.abs(g.size),width:l?Math.abs(g.size):m.size};u&&(_.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const M=_.options||t[f].options;Ia(_,M,b,a),Fa(_,M,h.ratio),this.updateElement(t[f],f,_,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,a=[],r=this._cachedMeta.controller.getParsed(e),c=r&&r[s.axis],l=h=>{const d=h._parsed.find(f=>f[s.axis]===c),u=d&&d[h.vScale.axis];if(O(u)||isNaN(u))return!0};for(const h of n)if(!(e!==void 0&&l(h))&&((o===!1||a.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&a.push(h.stack),h.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,a;for(o=0,a=e.data.length;o<a;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const r=t.barThickness;return{min:r||Da(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:r?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:a}}=this,r=o||0,c=this.getParsed(t),l=c._custom,h=$e(l);let d=c[e.axis],u=0,f=s?this.applyStack(e,c,s):d,p,g;f!==d&&(u=f-d,f=d),h&&(d=l.barStart,f=l.barEnd-l.barStart,d!==0&&et(d)!==et(l.barEnd)&&(u=0),u+=d);const m=!O(o)&&!h?o:u;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?p=e.getPixelForValue(u+f):p=b,g=p-b,Math.abs(g)<a){g=Ta(g,e,r)*a,d===r&&(b-=g/2);const _=e.getPixelForDecimal(0),M=e.getPixelForDecimal(1),v=Math.min(_,M),x=Math.max(_,M);b=Math.max(Math.min(b,x),v),p=b+g,s&&!h&&(c._stacks[e.axis]._visualValues[n]=e.getValueForPixel(p)-e.getValueForPixel(b))}if(b===e.getPixelForValue(r)){const _=et(g)*e.getLineWidthForValue(r)/2;b+=_,g-=_}return{size:g,base:b,head:p,center:p+g/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,a=C(n.maxBarThickness,1/0);let r,c;if(e.grouped){const l=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?Aa(t,e,n,l):Ca(t,e,n,l),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);r=h.start+h.chunk*d+h.chunk/2,c=Math.min(a,h.chunk*h.ratio)}else r=s.getPixelForValue(this.getParsed(t)[s.axis],t),c=Math.min(a,e.min*e.ratio);return{base:r-c/2,head:r+c/2,center:r,size:c}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}y(Ye,"id","bar"),y(Ye,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),y(Ye,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});function za(i,t,e){let s=1,n=1,o=0,a=0;if(t<I){const r=i,c=r+t,l=Math.cos(r),h=Math.sin(r),d=Math.cos(c),u=Math.sin(c),f=(M,v,x)=>Qt(M,r,c,!0)?1:Math.max(v,v*e,x,x*e),p=(M,v,x)=>Qt(M,r,c,!0)?-1:Math.min(v,v*e,x,x*e),g=f(0,l,d),m=f(N,h,u),b=p(B,l,d),_=p(B+N,h,u);s=(g-b)/2,n=(m-_)/2,o=-(g+b)/2,a=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}class de extends vt{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=c=>+s[c];if(D(s[t])){const{key:c="value"}=this._parsing;o=l=>+St(s[l],c)}let a,r;for(a=t,r=t+e;a<r;++a)n._parsed[a]=o(a)}}_getRotation(){return tt(this.options.rotation-90)}_getCircumference(){return tt(this.options.circumference)}_getRotationExtents(){let t=I,e=-I;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),a=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+a)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,a=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,r=Math.max((Math.min(s.width,s.height)-a)/2,0),c=Math.min($n(this.options.cutout,r),1),l=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:p,offsetY:g}=za(d,h,c),m=(s.width-a)/u,b=(s.height-a)/f,_=Math.max(Math.min(m,b)/2,0),M=zs(this.options.radius,_),v=Math.max(M*c,0),x=(M-v)/this._getVisibleDatasetWeightTotal();this.offsetX=p*M,this.offsetY=g*M,n.total=this.calculateTotal(),this.outerRadius=M-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*l,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/I)}updateElements(t,e,s,n){const o=n==="reset",a=this.chart,r=a.chartArea,l=a.options.animation,h=(r.left+r.right)/2,d=(r.top+r.bottom)/2,u=o&&l.animateScale,f=u?0:this.innerRadius,p=u?0:this.outerRadius,{sharedOptions:g,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),_;for(_=0;_<e;++_)b+=this._circumference(_,o);for(_=e;_<e+s;++_){const M=this._circumference(_,o),v=t[_],x={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+M,circumference:M,outerRadius:p,innerRadius:f};m&&(x.options=g||this.resolveDataElementOptions(_,v.active?"active":n)),b+=M,this.updateElement(v,_,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?I*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Ie(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,a,r,c;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){a=s.getDatasetMeta(n),t=a.data,r=a.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)c=r.resolveDataElementOptions(n),c.borderAlign!=="inner"&&(e=Math.max(e,c.borderWidth||0,c.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(C(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}y(de,"id","doughnut"),y(de,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),y(de,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),y(de,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{const c=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:n,lineWidth:c.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});class Ue extends vt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,a=this.chart._animationsDisabled;let{start:r,count:c}=co(e,n,a);this._drawStart=r,this._drawCount=c,lo(e)&&(r=0,c=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(s,void 0,{animated:!a,options:l},t),this.updateElements(n,r,c,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r,_stacked:c,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=a.axis,f=r.axis,{spanGaps:p,segment:g}=this.options,m=Zt(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",_=e+s,M=t.length;let v=e>0&&this.getParsed(e-1);for(let x=0;x<M;++x){const w=t[x],k=b?w:{};if(x<e||x>=_){k.skip=!0;continue}const S=this.getParsed(x),P=O(S[f]),A=k[u]=a.getPixelForValue(S[u],x),T=k[f]=o||P?r.getBasePixel():r.getPixelForValue(c?this.applyStack(r,S,c):S[f],x);k.skip=isNaN(A)||isNaN(T)||P,k.stop=x>0&&Math.abs(S[u]-v[u])>m,g&&(k.parsed=S,k.raw=l.data[x]),d&&(k.options=h||this.resolveDataElementOptions(x,w.active?"active":n)),b||this.updateElement(w,x,k,n),v=S}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),a=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,a)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}y(Ue,"id","line"),y(Ue,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),y(Ue,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});function mt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class xi{constructor(t){y(this,"options");this.options=t||{}}static override(t){Object.assign(xi.prototype,t)}init(){}formats(){return mt()}parse(){return mt()}format(){return mt()}add(){return mt()}diff(){return mt()}startOf(){return mt()}endOf(){return mt()}}var Ba={_date:xi};function Va(i,t,e,s){const{controller:n,data:o,_sorted:a}=i,r=n._cachedMeta.iScale,c=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(r&&t===r.axis&&t!=="r"&&a&&o.length){const l=r._reversePixels?so:Mt;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const u=l(o,t,e-d),f=l(o,t,e+d);return{lo:u.lo,hi:f.hi}}}}else{const h=l(o,t,e);if(c){const{vScale:d}=n._cachedMeta,{_parsed:u}=i,f=u.slice(0,h.lo+1).reverse().findIndex(g=>!O(g[d.axis]));h.lo-=Math.max(0,f);const p=u.slice(h.hi).findIndex(g=>!O(g[d.axis]));h.hi+=Math.max(0,p)}return h}}return{lo:0,hi:o.length-1}}function ie(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),a=e[t];for(let r=0,c=o.length;r<c;++r){const{index:l,data:h}=o[r],{lo:d,hi:u}=Va(o[r],t,a,n);for(let f=d;f<=u;++f){const p=h[f];p.skip||s(p,l,f)}}}function Na(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,a=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}function Xe(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||ie(i,e,t,function(r,c,l){!n&&!nt(r,i.chartArea,0)||r.inRange(t.x,t.y,s)&&o.push({element:r,datasetIndex:c,index:l})},!0),o}function ja(i,t,e,s){let n=[];function o(a,r,c){const{startAngle:l,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=Ns(a,{x:t.x,y:t.y});Qt(d,l,h)&&n.push({element:a,datasetIndex:r,index:c})}return ie(i,e,t,o),n}function Wa(i,t,e,s,n,o){let a=[];const r=Na(e);let c=Number.POSITIVE_INFINITY;function l(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const p=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(p))&&!f)return;const m=r(t,p);m<c?(a=[{element:h,datasetIndex:d,index:u}],c=m):m===c&&a.push({element:h,datasetIndex:d,index:u})}return ie(i,e,t,l),a}function Ke(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?ja(i,t,e,n):Wa(i,t,e,s,n,o)}function ts(i,t,e,s,n){const o=[],a=e==="x"?"inXRange":"inYRange";let r=!1;return ie(i,e,t,(c,l,h)=>{c[a]&&c[a](t[e],n)&&(o.push({element:c,datasetIndex:l,index:h}),r=r||c.inRange(t.x,t.y,n))}),s&&!r?[]:o}var Ha={evaluateInteractionItems:ie,modes:{index(i,t,e,s){const n=_t(t,i),o=e.axis||"x",a=e.includeInvisible||!1,r=e.intersect?Xe(i,n,o,s,a):Ke(i,n,o,!1,s,a),c=[];return r.length?(i.getSortedVisibleDatasetMetas().forEach(l=>{const h=r[0].index,d=l.data[h];d&&!d.skip&&c.push({element:d,datasetIndex:l.index,index:h})}),c):[]},dataset(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;let r=e.intersect?Xe(i,n,o,s,a):Ke(i,n,o,!1,s,a);if(r.length>0){const c=r[0].datasetIndex,l=i.getDatasetMeta(c).data;r=[];for(let h=0;h<l.length;++h)r.push({element:l[h],datasetIndex:c,index:h})}return r},point(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return Xe(i,n,o,s,a)},nearest(i,t,e,s){const n=_t(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return Ke(i,n,o,e.intersect,s,a)},x(i,t,e,s){const n=_t(t,i);return ts(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=_t(t,i);return ts(i,n,"y",e.intersect,s)}}};const an=["left","top","right","bottom"];function Bt(i,t){return i.filter(e=>e.pos===t)}function es(i,t){return i.filter(e=>an.indexOf(e.pos)===-1&&e.box.axis===t)}function Vt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function $a(i){const t=[];let e,s,n,o,a,r;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:a,stackWeight:r=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return t}function Ya(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!an.includes(n))continue;const a=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return t}function Ua(i,t){const e=Ya(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,a,r;for(o=0,a=i.length;o<a;++o){r=i[o];const{fullSize:c}=r.box,l=e[r.stack],h=l&&r.stackWeight/l.weight;r.horizontal?(r.width=h?h*s:c&&t.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:c&&t.availableHeight)}return e}function Xa(i){const t=$a(i),e=Vt(t.filter(l=>l.box.fullSize),!0),s=Vt(Bt(t,"left"),!0),n=Vt(Bt(t,"right")),o=Vt(Bt(t,"top"),!0),a=Vt(Bt(t,"bottom")),r=es(t,"x"),c=es(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(c).concat(a).concat(r),chartArea:Bt(t,"chartArea"),vertical:s.concat(n).concat(c),horizontal:o.concat(a).concat(r)}}function is(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function rn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Ka(i,t,e,s){const{pos:n,box:o}=e,a=i.maxPadding;if(!D(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&rn(a,o.getPadding());const r=Math.max(0,t.outerWidth-is(a,i,"left","right")),c=Math.max(0,t.outerHeight-is(a,i,"top","bottom")),l=r!==i.w,h=c!==i.h;return i.w=r,i.h=c,e.horizontal?{same:l,other:h}:{same:h,other:l}}function Ga(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function qa(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(t[a],e[a])}),o}return s(i?["left","right"]:["top","bottom"])}function Ht(i,t,e,s){const n=[];let o,a,r,c,l,h;for(o=0,a=i.length,l=0;o<a;++o){r=i[o],c=r.box,c.update(r.width||t.w,r.height||t.h,qa(r.horizontal,t));const{same:d,other:u}=Ka(t,e,r,s);l|=d&&n.length,h=h||u,c.fullSize||n.push(r)}return l&&Ht(n,t,e,s)||h}function ue(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function ss(i,t,e,s){const n=e.padding;let{x:o,y:a}=t;for(const r of i){const c=r.box,l=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/l.weight||1;if(r.horizontal){const d=t.w*h,u=l.size||c.height;qt(l.start)&&(a=l.start),c.fullSize?ue(c,n.left,a,e.outerWidth-n.right-n.left,u):ue(c,t.left+l.placed,a,d,u),l.start=a,l.placed+=d,a=c.bottom}else{const d=t.h*h,u=l.size||c.width;qt(l.start)&&(o=l.start),c.fullSize?ue(c,o,n.top,u,e.outerHeight-n.bottom-n.top):ue(c,o,t.top+l.placed,u,d),l.start=o,l.placed+=d,o=c.right}}t.x=o,t.y=a}var fe={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=ot(i.options.layout.padding),o=Math.max(t-n.width,0),a=Math.max(e-n.height,0),r=Xa(i.boxes),c=r.vertical,l=r.horizontal;$(i.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=c.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);rn(u,ot(s));const f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),p=Ua(c.concat(l),d);Ht(r.fullSize,f,d,p),Ht(c,f,d,p),Ht(l,f,d,p)&&Ht(c,f,d,p),Ga(f),ss(r.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,ss(r.rightAndBottom,f,d,p),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},$(r.chartArea,g=>{const m=g.box;Object.assign(m,i.chartArea),m.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class cn{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Za extends cn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ke="$chartjs",Qa={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ns=i=>i===null||i==="";function Ja(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[ke]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",ns(n)){const o=ji(i,"width");o!==void 0&&(i.width=o)}if(ns(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=ji(i,"height");o!==void 0&&(i.height=o)}return i}const ln=ea?{passive:!0}:!1;function tr(i,t,e){i&&i.addEventListener(t,e,ln)}function er(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,ln)}function ir(i,t){const e=Qa[i.type]||i.type,{x:s,y:n}=_t(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Ae(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function sr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ae(r.addedNodes,s),a=a&&!Ae(r.removedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function nr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||Ae(r.removedNodes,s),a=a&&!Ae(r.addedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Jt=new Map;let os=0;function hn(){const i=window.devicePixelRatio;i!==os&&(os=i,Jt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function or(i,t){Jt.size||window.addEventListener("resize",hn),Jt.set(i,t)}function ar(i){Jt.delete(i),Jt.size||window.removeEventListener("resize",hn)}function rr(i,t,e){const s=i.canvas,n=s&&_i(s);if(!n)return;const o=$s((r,c)=>{const l=n.clientWidth;e(r,c),l<n.clientWidth&&e()},window),a=new ResizeObserver(r=>{const c=r[0],l=c.contentRect.width,h=c.contentRect.height;l===0&&h===0||o(l,h)});return a.observe(n),or(i,o),a}function Ge(i,t,e){e&&e.disconnect(),t==="resize"&&ar(i)}function cr(i,t,e){const s=i.canvas,n=$s(o=>{i.ctx!==null&&e(ir(o,i))},i);return tr(s,t,n),n}class lr extends cn{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Ja(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[ke])return!1;const s=e[ke].initial;["height","width"].forEach(o=>{const a=s[o];O(a)?e.removeAttribute(o):e.setAttribute(o,a)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[ke],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),a={attach:sr,detach:nr,resize:rr}[e]||cr;n[e]=a(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ge,detach:Ge,resize:Ge}[e]||er)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return ta(t,e,s,n)}isAttached(t){const e=t&&_i(t);return!!(e&&e.isConnected)}}function hr(i){return!bi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Za:lr}class dt{constructor(){y(this,"x");y(this,"y");y(this,"active",!1);y(this,"options");y(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Zt(this.x)&&Zt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}y(dt,"defaults",{}),y(dt,"defaultRoutes");function dr(i,t){const e=i.options.ticks,s=ur(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?gr(t):[],a=o.length,r=o[0],c=o[a-1],l=[];if(a>n)return pr(t,l,o,a/n),l;const h=fr(o,t,n);if(a>0){let d,u;const f=a>1?Math.round((c-r)/(a-1)):null;for(ge(t,l,h,O(f)?0:r-f,r),d=0,u=a-1;d<u;d++)ge(t,l,h,o[d],o[d+1]);return ge(t,l,h,c,O(f)?t.length:c+f),l}return ge(t,l,h),l}function ur(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function fr(i,t,e){const s=mr(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Qn(s);for(let a=0,r=o.length-1;a<r;a++){const c=o[a];if(c>n)return c}return Math.max(n,1)}function gr(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function pr(i,t,e,s){let n=0,o=e[0],a;for(s=Math.ceil(s),a=0;a<i.length;a++)a===o&&(t.push(i[a]),n++,o=e[n*s])}function ge(i,t,e,s,n){const o=C(s,0),a=Math.min(C(n,i.length),i.length);let r=0,c,l,h;for(e=Math.ceil(e),n&&(c=n-s,e=c/Math.floor(c/e)),h=o;h<0;)r++,h=Math.round(o+r*e);for(l=Math.max(o,0);l<a;l++)l===h&&(t.push(i[l]),r++,h=Math.round(o+r*e))}function mr(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const br=i=>i==="left"?"right":i==="right"?"left":i,as=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,rs=(i,t)=>Math.min(t||i,i);function cs(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function _r(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,a=i._endPixel,r=1e-6;let c=i.getPixelForTick(n),l;if(!(e&&(s===1?l=Math.max(c-o,a-c):t===0?l=(i.getPixelForTick(1)-c)/2:l=(c-i.getPixelForTick(n-1))/2,c+=n<t?l:-l,c<o-r||c>a+r)))return c}function xr(i,t){$(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Nt(i){return i.drawTicks?i.tickLength:0}function ls(i,t){if(!i.display)return 0;const e=Tt(i.font,t),s=ot(i.padding);return(z(i.text)?i.text.length:1)*e.lineHeight+s.height}function yr(i,t){return Pt(i,{scale:t,type:"scale"})}function Mr(i,t,e){return Pt(i,{tick:e,index:t,type:"tick"})}function kr(i,t,e){let s=ro(i);return(e&&t!=="right"||!e&&t==="right")&&(s=br(s)),s}function vr(i,t,e,s){const{top:n,left:o,bottom:a,right:r,chart:c}=i,{chartArea:l,scales:h}=c;let d=0,u,f,p;const g=a-n,m=r-o;if(i.isHorizontal()){if(f=Ti(s,o,r),D(e)){const b=Object.keys(e)[0],_=e[b];p=h[b].getPixelForValue(_)+g-t}else e==="center"?p=(l.bottom+l.top)/2+g-t:p=as(i,e,t);u=r-o}else{if(D(e)){const b=Object.keys(e)[0],_=e[b];f=h[b].getPixelForValue(_)-m+t}else e==="center"?f=(l.left+l.right)/2-m+t:f=as(i,e,t);p=Ti(s,a,n),d=e==="left"?-N:N}return{titleX:f,titleY:p,maxWidth:u,rotation:d}}class Ot extends dt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=K(t,Number.POSITIVE_INFINITY),e=K(e,Number.NEGATIVE_INFINITY),s=K(s,Number.POSITIVE_INFINITY),n=K(n,Number.NEGATIVE_INFINITY),{min:K(t,s),max:K(e,n),minDefined:W(t),maxDefined:W(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),a;if(n&&o)return{min:e,max:s};const r=this.getMatchingVisibleMetas();for(let c=0,l=r.length;c<l;++c)a=r[c].controller.getMinMax(this,t),n||(e=Math.min(e,a.min)),o||(s=Math.max(s,a.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:K(e,K(s,e)),max:K(s,K(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){F(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=To(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const c=r<this.ticks.length;this._convertTicksToLabels(c?cs(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=dr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),c&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){F(this.options.afterUpdate,[this])}beforeSetDimensions(){F(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){F(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),F(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){F(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=F(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){F(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){F(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=rs(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let a=n,r,c,l;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=U(this.chart.width-d,0,this.maxWidth);r=t.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(t.offset?.5:1)),c=this.maxHeight-Nt(t.grid)-e.padding-ls(t.title,this.chart.options.font),l=Math.sqrt(d*d+u*u),a=hi(Math.min(Math.asin(U((h.highest.height+6)/r,-1,1)),Math.asin(U(c/l,-1,1))-Math.asin(U(u/l,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){F(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){F(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){const c=ls(n,e.options.font);if(r?(t.width=this.maxWidth,t.height=Nt(o)+c):(t.height=this.maxHeight,t.width=Nt(o)+c),s.display&&this.ticks.length){const{first:l,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,p=tt(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(r){const b=s.mirror?0:m*d.width+g*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:g*d.width+m*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(l,h,m,g)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:a},position:r}=this.options,c=this.labelRotation!==0,l=r!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;c?l?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){F(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)O(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=cs(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,a=[],r=[],c=Math.floor(e/rs(e,s));let l=0,h=0,d,u,f,p,g,m,b,_,M,v,x;for(d=0;d<e;d+=c){if(p=t[d].label,g=this._resolveTickFontOptions(d),n.font=m=g.string,b=o[m]=o[m]||{data:{},gc:[]},_=g.lineHeight,M=v=0,!O(p)&&!z(p))M=Oe(n,b.data,b.gc,M,p),v=_;else if(z(p))for(u=0,f=p.length;u<f;++u)x=p[u],!O(x)&&!z(x)&&(M=Oe(n,b.data,b.gc,M,x),v+=_);a.push(M),r.push(v),l=Math.max(M,l),h=Math.max(v,h)}xr(o,e);const w=a.indexOf(l),k=r.indexOf(h),S=P=>({width:a[P]||0,height:r[P]||0});return{first:S(0),last:S(e-1),widest:S(w),highest:S(k),widths:a,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return io(this._alignToPixels?pt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Mr(this.getContext(),t,s))}return this.$context||(this.$context=yr(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=tt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),a=t.autoSkipPadding||0,r=o?o.widest.width+a:0,c=o?o.highest.height+a:0;return this.isHorizontal()?c*s>r*n?r/s:c/n:c*n<r*s?c/s:r/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,c=o.offset,l=this.isHorizontal(),d=this.ticks.length+(c?1:0),u=Nt(o),f=[],p=r.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,b=function(R){return pt(s,R,g)};let _,M,v,x,w,k,S,P,A,T,E,X;if(a==="top")_=b(this.bottom),k=this.bottom-u,P=_-m,T=b(t.top)+m,X=t.bottom;else if(a==="bottom")_=b(this.top),T=t.top,X=b(t.bottom)-m,k=_+m,P=this.top+u;else if(a==="left")_=b(this.right),w=this.right-u,S=_-m,A=b(t.left)+m,E=t.right;else if(a==="right")_=b(this.left),A=t.left,E=b(t.right)-m,w=_+m,S=this.left+u;else if(e==="x"){if(a==="center")_=b((t.top+t.bottom)/2+.5);else if(D(a)){const R=Object.keys(a)[0],V=a[R];_=b(this.chart.scales[R].getPixelForValue(V))}T=t.top,X=t.bottom,k=_+m,P=k+u}else if(e==="y"){if(a==="center")_=b((t.left+t.right)/2);else if(D(a)){const R=Object.keys(a)[0],V=a[R];_=b(this.chart.scales[R].getPixelForValue(V))}w=_-m,S=w-u,A=t.left,E=t.right}const q=C(n.ticks.maxTicksLimit,d),L=Math.max(1,Math.ceil(d/q));for(M=0;M<d;M+=L){const R=this.getContext(M),V=o.setContext(R),Z=r.setContext(R),H=V.lineWidth,Dt=V.color,se=Z.dash||[],Ct=Z.dashOffset,Et=V.tickWidth,ut=V.tickColor,Ft=V.tickBorderDash||[],ft=V.tickBorderDashOffset;v=_r(this,M,c),v!==void 0&&(x=pt(s,v,H),l?w=S=A=E=x:k=P=T=X=x,f.push({tx1:w,ty1:k,tx2:S,ty2:P,x1:A,y1:T,x2:E,y2:X,width:H,color:Dt,borderDash:se,borderDashOffset:Ct,tickWidth:Et,tickColor:ut,tickBorderDash:Ft,tickBorderDashOffset:ft}))}return this._ticksLength=d,this._borderValue=_,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:c,crossAlign:l,padding:h,mirror:d}=o,u=Nt(s.grid),f=u+h,p=d?-h:f,g=-tt(this.labelRotation),m=[];let b,_,M,v,x,w,k,S,P,A,T,E,X="middle";if(n==="top")w=this.bottom-p,k=this._getXAxisLabelAlignment();else if(n==="bottom")w=this.top+p,k=this._getXAxisLabelAlignment();else if(n==="left"){const L=this._getYAxisLabelAlignment(u);k=L.textAlign,x=L.x}else if(n==="right"){const L=this._getYAxisLabelAlignment(u);k=L.textAlign,x=L.x}else if(e==="x"){if(n==="center")w=(t.top+t.bottom)/2+f;else if(D(n)){const L=Object.keys(n)[0],R=n[L];w=this.chart.scales[L].getPixelForValue(R)+f}k=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-f;else if(D(n)){const L=Object.keys(n)[0],R=n[L];x=this.chart.scales[L].getPixelForValue(R)}k=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(c==="start"?X="top":c==="end"&&(X="bottom"));const q=this._getLabelSizes();for(b=0,_=r.length;b<_;++b){M=r[b],v=M.label;const L=o.setContext(this.getContext(b));S=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),A=P.lineHeight,T=z(v)?v.length:1;const R=T/2,V=L.color,Z=L.textStrokeColor,H=L.textStrokeWidth;let Dt=k;a?(x=S,k==="inner"&&(b===_-1?Dt=this.options.reverse?"left":"right":b===0?Dt=this.options.reverse?"right":"left":Dt="center"),n==="top"?l==="near"||g!==0?E=-T*A+A/2:l==="center"?E=-q.highest.height/2-R*A+A:E=-q.highest.height+A/2:l==="near"||g!==0?E=A/2:l==="center"?E=q.highest.height/2-R*A:E=q.highest.height-T*A,d&&(E*=-1),g!==0&&!L.showLabelBackdrop&&(x+=A/2*Math.sin(g))):(w=S,E=(1-T)*A/2);let se;if(L.showLabelBackdrop){const Ct=ot(L.backdropPadding),Et=q.heights[b],ut=q.widths[b];let Ft=E-Ct.top,ft=0-Ct.left;switch(X){case"middle":Ft-=Et/2;break;case"bottom":Ft-=Et;break}switch(k){case"center":ft-=ut/2;break;case"right":ft-=ut;break;case"inner":b===_-1?ft-=ut:b>0&&(ft-=ut/2);break}se={left:ft,top:Ft,width:ut+Ct.width,height:Et+Ct.height,color:L.backdropColor}}m.push({label:v,font:P,textOffset:E,options:{rotation:g,color:V,strokeColor:Z,strokeWidth:H,textAlign:Dt,textBaseline:X,translation:[x,w],backdrop:se}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-tt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,a=this._getLabelSizes(),r=t+o,c=a.widest.width;let l,h;return e==="left"?n?(h=this.right+o,s==="near"?l="left":s==="center"?(l="center",h+=c/2):(l="right",h+=c)):(h=this.right-r,s==="near"?l="right":s==="center"?(l="center",h-=c/2):(l="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?l="right":s==="center"?(l="center",h-=c/2):(l="left",h-=c)):(h=this.left+r,s==="near"?l="left":s==="center"?(l="center",h+=c/2):(l="right",h=this.right)):l="right",{textAlign:l,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:a}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,a),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,a;const r=(c,l,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(c.x,c.y),s.lineTo(l.x,l.y),s.stroke(),s.restore())};if(e.display)for(o=0,a=n.length;o<a;++o){const c=n[o];e.drawOnChartArea&&r({x:c.x1,y:c.y1},{x:c.x2,y:c.y2},c),e.drawTicks&&r({x:c.tx1,y:c.ty1},{x:c.tx2,y:c.ty2},{color:c.tickColor,width:c.tickWidth,borderDash:c.tickBorderDash,borderDashOffset:c.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;const r=n.setContext(this.getContext(0)).lineWidth,c=this._borderValue;let l,h,d,u;this.isHorizontal()?(l=pt(t,this.left,a)-a/2,h=pt(t,this.right,r)+r/2,d=u=c):(d=pt(t,this.top,a)-a/2,u=pt(t,this.bottom,r)+r/2,l=h=c),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(l,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Us(s,n);const o=this.getLabelItems(t);for(const a of o){const r=a.options,c=a.font,l=a.label,h=a.textOffset;De(s,l,0,h,c,r)}n&&Xs(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=Tt(s.font),a=ot(s.padding),r=s.align;let c=o.lineHeight/2;e==="bottom"||e==="center"||D(e)?(c+=a.bottom,z(s.text)&&(c+=o.lineHeight*(s.text.length-1))):c+=a.top;const{titleX:l,titleY:h,maxWidth:d,rotation:u}=vr(this,c,e,r);De(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:kr(r,e,n),textBaseline:"middle",translation:[l,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=C(t.grid&&t.grid.z,-1),n=C(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ot.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,a;for(o=0,a=e.length;o<a;++o){const r=e[o];r[s]===this.id&&(!t||r.type===t)&&n.push(r)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return Tt(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class pe{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Pr(e)&&(s=this.register(e));const n=this.items,o=t.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Sr(t,a,s),this.override&&j.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in j[n]&&(delete j[n][s],this.override&&delete wt[s])}}function Sr(i,t,e){const s=Gt(Object.create(null),[e?j.get(e):{},j.get(t),i.defaults]);j.set(t,s),i.defaultRoutes&&wr(t,i.defaultRoutes),i.descriptors&&j.describe(t,i.descriptors)}function wr(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),a=t[e].split("."),r=a.pop(),c=a.join(".");j.route(o,n,c,r)})}function Pr(i){return"id"in i&&"defaults"in i}class Or{constructor(){this.controllers=new pe(vt,"datasets",!0),this.elements=new pe(dt,"elements"),this.plugins=new pe(Object,"plugins"),this.scales=new pe(Ot,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):$(n,a=>{const r=s||this._getRegistryForType(a);this._exec(t,r,a)})})}_exec(t,e,s){const n=li(t);F(s["before"+n],[],s),e[t](s),F(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var J=new Or;class Dr{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),a=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),a}_notify(t,e,s,n){n=n||{};for(const o of t){const a=o.plugin,r=a[s],c=[e,n,o.options];if(F(r,c,a)===!1&&n.cancelable)return!1}return!0}invalidate(){O(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=C(s.options&&s.options.plugins,{}),o=Cr(s);return n===!1&&!e?[]:Lr(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(c=>r.plugin.id===c.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function Cr(i){const t={},e=[],s=Object.keys(J.plugins.items);for(let o=0;o<s.length;o++)e.push(J.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const a=n[o];e.indexOf(a)===-1&&(e.push(a),t[a.id]=!0)}return{plugins:e,localIds:t}}function Ar(i,t){return!t&&i===!1?null:i===!0?{}:i}function Lr(i,{plugins:t,localIds:e},s,n){const o=[],a=i.getContext();for(const r of t){const c=r.id,l=Ar(s[c],n);l!==null&&o.push({plugin:r,options:Tr(i.config,{plugin:r,local:e[c]},l,a)})}return o}function Tr(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),a=i.getOptionScopes(s,o);return e&&t.defaults&&a.push(t.defaults),i.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ii(i,t){const e=j.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Rr(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Ir(i,t){return i===t?"_index_":"_value_"}function hs(i){if(i==="x"||i==="y"||i==="r")return i}function Er(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function si(i,...t){if(hs(i))return i;for(const e of t){const s=e.axis||Er(e.position)||i.length>1&&hs(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function ds(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function Fr(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return ds(i,"x",e[0])||ds(i,"y",e[0])}return{}}function zr(i,t){const e=wt[i.type]||{scales:{}},s=t.scales||{},n=ii(i.type,t),o=Object.create(null);return Object.keys(s).forEach(a=>{const r=s[a];if(!D(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const c=si(a,r,Fr(a,i),j.scales[r.type]),l=Ir(c,n),h=e.scales||{};o[a]=$t(Object.create(null),[{axis:c},r,h[c],h[l]])}),i.data.datasets.forEach(a=>{const r=a.type||i.type,c=a.indexAxis||ii(r,t),h=(wt[r]||{}).scales||{};Object.keys(h).forEach(d=>{const u=Rr(d,c),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),$t(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{const r=o[a];$t(r,[j.scales[r.type],j.scale])}),o}function dn(i){const t=i.options||(i.options={});t.plugins=C(t.plugins,{}),t.scales=zr(i,t)}function un(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Br(i){return i=i||{},i.data=un(i.data),dn(i),i}const us=new Map,fn=new Set;function me(i,t){let e=us.get(i);return e||(e=t(),us.set(i,e),fn.add(e)),e}const jt=(i,t,e)=>{const s=St(t,e);s!==void 0&&i.add(s)};class Vr{constructor(t){this._config=Br(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=un(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),dn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return me(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return me(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return me(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return me(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,a=this._cachedScopes(t,s),r=a.get(e);if(r)return r;const c=new Set;e.forEach(h=>{t&&(c.add(t),h.forEach(d=>jt(c,t,d))),h.forEach(d=>jt(c,n,d)),h.forEach(d=>jt(c,wt[o]||{},d)),h.forEach(d=>jt(c,j,d)),h.forEach(d=>jt(c,ei,d))});const l=Array.from(c);return l.length===0&&l.push(Object.create(null)),fn.has(e)&&a.set(e,l),l}chartOptionScopes(){const{options:t,type:e}=this;return[t,wt[e]||{},j.datasets[e]||{},{type:e},j,ei]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:a,subPrefixes:r}=fs(this._resolverCache,t,n);let c=a;if(jr(a,e)){o.$shared=!1,s=ht(s)?s():s;const l=this.createResolver(t,s,r);c=Rt(a,s,l)}for(const l of e)o[l]=c[l];return o}createResolver(t,e,s=[""],n){const{resolver:o}=fs(this._resolverCache,t,s);return D(e)?Rt(o,e,void 0,n):o}}function fs(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:gi(t,e),subPrefixes:e.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}const Nr=i=>D(i)&&Object.getOwnPropertyNames(i).some(t=>ht(i[t]));function jr(i,t){const{isScriptable:e,isIndexable:s}=Zs(i);for(const n of t){const o=e(n),a=s(n),r=(a||o)&&i[n];if(o&&(ht(r)||Nr(r))||a&&z(r))return!0}return!1}var Wr="4.4.8";const Hr=["top","bottom","left","right","chartArea"];function gs(i,t){return i==="top"||i==="bottom"||Hr.indexOf(i)===-1&&t==="x"}function ps(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function ms(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),F(e&&e.onComplete,[i],t)}function $r(i){const t=i.chart,e=t.options.animation;F(e&&e.onProgress,[i],t)}function gn(i){return bi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ve={},bs=i=>{const t=gn(i);return Object.values(ve).filter(e=>e.canvas===t).pop()};function Yr(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const a=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=a)}}}function Ur(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function be(i,t,e){return i.options.clip?i[e]:t[e]}function Xr(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:be(e,t,"left"),right:be(e,t,"right"),top:be(s,t,"top"),bottom:be(s,t,"bottom")}:t}class yt{static register(...t){J.add(...t),_s()}static unregister(...t){J.remove(...t),_s()}constructor(t,e){const s=this.config=new Vr(e),n=gn(t),o=bs(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const a=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||hr(n)),this.platform.updateConfig(s);const r=this.platform.acquireContext(n,a.aspectRatio),c=r&&r.canvas,l=c&&c.height,h=c&&c.width;if(this.id=Hn(),this.ctx=r,this.canvas=c,this.width=h,this.height=l,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Dr,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=ao(d=>this.update(d),a.resizeDelay||0),this._dataChanges=[],ve[this.id]=this,!r||!c){console.error("Failed to create chart: can't acquire context from the given item");return}it.listen(this,"complete",ms),it.listen(this,"progress",$r),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return O(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return J}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Ni(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return zi(this.canvas,this.ctx),this}stop(){return it.stop(this),this}resize(t,e){it.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(n,t,e,o),r=s.devicePixelRatio||this.platform.getDevicePixelRatio(),c=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,Ni(this,r,!0)&&(this.notifyPlugins("resize",{size:a}),F(s.onResize,[this,a],this),this.attached&&this._doResize(c)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};$(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((a,r)=>(a[r]=!1,a),{});let o=[];e&&(o=o.concat(Object.keys(e).map(a=>{const r=e[a],c=si(a,r),l=c==="r",h=c==="x";return{options:r,dposition:l?"chartArea":h?"bottom":"left",dtype:l?"radialLinear":h?"category":"linear"}}))),$(o,a=>{const r=a.options,c=r.id,l=si(c,r),h=C(r.type,a.dtype);(r.position===void 0||gs(r.position,l)!==gs(a.dposition))&&(r.position=a.dposition),n[c]=!0;let d=null;if(c in s&&s[c].type===h)d=s[c];else{const u=J.getScale(h);d=new u({id:c,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(r,t)}),$(n,(a,r)=>{a||delete s[r]}),$(s,a=>{fe.configure(this,a,a.options),fe.addBox(this,a)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(ps("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let a=this.getDatasetMeta(s);const r=o.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(s),a=this.getDatasetMeta(s)),a.type=r,a.indexAxis=o.indexAxis||ii(r,this.options),a.order=o.order||0,a.index=s,a.label=""+o.label,a.visible=this.isDatasetVisible(s),a.controller)a.controller.updateIndex(s),a.controller.linkScales();else{const c=J.getController(r),{datasetElementType:l,dataElementType:h}=j.datasets[r];Object.assign(c,{dataElementType:J.getElement(h),datasetElementType:l&&J.getElement(l)}),a.controller=new c(this,s),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){$(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let l=0,h=this.data.datasets.length;l<h;l++){const{controller:d}=this.getDatasetMeta(l),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),a=Math.max(+d.getMaxOverflow(),a)}a=this._minPadding=s.layout.autoPadding?a:0,this._updateLayout(a),n||$(o,l=>{l.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(ps("z","_idx"));const{_active:r,_lastEvent:c}=this;c?this._eventHandler(c,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){$(this.scales,t=>{fe.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Pi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const a=s==="_removeElements"?-o:o;Yr(t,n,a)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(a=>a[0]===o).map((a,r)=>r+","+a.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Pi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;fe.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],$(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ht(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(it.has(this)?this.attached&&!it.running(this)&&it.start(this):(this.draw(),ms({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const a=e[n];(!t||a.visible)&&s.push(a)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=Xr(t,this.chartArea),a={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",a)!==!1&&(n&&Us(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&Xs(e),a.cancelable=!1,this.notifyPlugins("afterDatasetDraw",a))}isPointInArea(t){return nt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=Ha.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Pt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),a=o.controller._resolveAnimations(void 0,n);qt(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),a.update(o,{visible:s}),this.update(r=>r.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),it.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),zi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ve[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,a)=>{e.addEventListener(this,o,a),t[o]=a},n=(o,a,r)=>{o.offsetX=a,o.offsetY=r,this._eventHandler(o)};$(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(c,l)=>{e.addEventListener(this,c,l),t[c]=l},n=(c,l)=>{t[c]&&(e.removeEventListener(this,c,l),delete t[c])},o=(c,l)=>{this.canvas&&this.resize(c,l)};let a;const r=()=>{n("attach",r),this.attached=!0,this.resize(),s("resize",o),s("detach",a)};a=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",r)},e.isAttached(this.canvas)?r():a()}unbindEvents(){$(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},$(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,a,r,c;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),r=0,c=t.length;r<c;++r){a=t[r];const l=a&&this.getDatasetMeta(a.datasetIndex).controller;l&&l[n+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:a})=>{const r=this.getDatasetMeta(o);if(!r)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:r.data[a],index:a}});!Si(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(c,l)=>c.filter(h=>!l.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),a=o(e,t),r=s?t:o(t,e);a.length&&this.updateHoverStyle(a,n.mode,!1),r.length&&n.mode&&this.updateHoverStyle(r,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=a=>(a.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,a=e,r=this._getActiveElements(t,n,s,a),c=Gn(t),l=Ur(t,this._lastEvent,s,c);s&&(this._lastEvent=null,F(o.onHover,[t,r,this],this),c&&F(o.onClick,[t,r,this],this));const h=!Si(r,n);return(h||e)&&(this._active=r,this._updateHoverStyles(r,n,e)),this._lastEvent=l,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}y(yt,"defaults",j),y(yt,"instances",ve),y(yt,"overrides",wt),y(yt,"registry",J),y(yt,"version",Wr),y(yt,"getChart",bs);function _s(){return $(yt.instances,i=>i._plugins.invalidate())}function Kr(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:c}=t;let l=n/r;i.beginPath(),i.arc(o,a,r,s-l,e+l),c>n?(l=n/c,i.arc(o,a,c,e+l,s-l,!0)):i.arc(o,a,n,e+N,s-N),i.closePath(),i.clip()}function Gr(i){return fi(i,["outerStart","outerEnd","innerStart","innerEnd"])}function qr(i,t,e,s){const n=Gr(i.options.borderRadius),o=(e-t)/2,a=Math.min(o,s*t/2),r=c=>{const l=(e-Math.min(o,c))*s/2;return U(c,0,Math.min(o,l))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:U(n.innerStart,0,a),innerEnd:U(n.innerEnd,0,a)}}function Lt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function Le(i,t,e,s,n,o){const{x:a,y:r,startAngle:c,pixelMargin:l,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-l,0),u=h>0?h+s+e+l:0;let f=0;const p=n-c;if(s){const L=h>0?h-s:0,R=d>0?d-s:0,V=(L+R)/2,Z=V!==0?p*V/(V+s):p;f=(p-Z)/2}const g=Math.max(.001,p*d-e/B)/d,m=(p-g)/2,b=c+m+f,_=n-m-f,{outerStart:M,outerEnd:v,innerStart:x,innerEnd:w}=qr(t,u,d,_-b),k=d-M,S=d-v,P=b+M/k,A=_-v/S,T=u+x,E=u+w,X=b+x/T,q=_-w/E;if(i.beginPath(),o){const L=(P+A)/2;if(i.arc(a,r,d,P,L),i.arc(a,r,d,L,A),v>0){const H=Lt(S,A,a,r);i.arc(H.x,H.y,v,A,_+N)}const R=Lt(E,_,a,r);if(i.lineTo(R.x,R.y),w>0){const H=Lt(E,q,a,r);i.arc(H.x,H.y,w,_+N,q+Math.PI)}const V=(_-w/u+(b+x/u))/2;if(i.arc(a,r,u,_-w/u,V,!0),i.arc(a,r,u,V,b+x/u,!0),x>0){const H=Lt(T,X,a,r);i.arc(H.x,H.y,x,X+Math.PI,b-N)}const Z=Lt(k,b,a,r);if(i.lineTo(Z.x,Z.y),M>0){const H=Lt(k,P,a,r);i.arc(H.x,H.y,M,b-N,P)}}else{i.moveTo(a,r);const L=Math.cos(P)*d+a,R=Math.sin(P)*d+r;i.lineTo(L,R);const V=Math.cos(A)*d+a,Z=Math.sin(A)*d+r;i.lineTo(V,Z)}i.closePath()}function Zr(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r}=t;let c=t.endAngle;if(o){Le(i,t,e,s,c,n);for(let l=0;l<o;++l)i.fill();isNaN(r)||(c=a+(r%I||I))}return Le(i,t,e,s,c,n),i.fill(),c}function Qr(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r,options:c}=t,{borderWidth:l,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=c,f=c.borderAlign==="inner";if(!l)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=l*2,i.lineJoin=h||"round"):(i.lineWidth=l,i.lineJoin=h||"bevel");let p=t.endAngle;if(o){Le(i,t,e,s,p,n);for(let g=0;g<o;++g)i.stroke();isNaN(r)||(p=a+(r%I||I))}f&&Kr(i,t,p),o||(Le(i,t,e,s,p,n),i.stroke())}class _e extends dt{constructor(e){super();y(this,"circumference");y(this,"endAngle");y(this,"fullCircles");y(this,"innerRadius");y(this,"outerRadius");y(this,"pixelMargin");y(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.getProps(["x","y"],n),{angle:a,distance:r}=Ns(o,{x:e,y:s}),{startAngle:c,endAngle:l,innerRadius:h,outerRadius:d,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),f=(this.options.spacing+this.options.borderWidth)/2,p=C(u,l-c),g=Qt(a,c,l)&&c!==l,m=p>=I||g,b=Pe(r,h+f,d+f);return m&&b}getCenterPoint(e){const{x:s,y:n,startAngle:o,endAngle:a,innerRadius:r,outerRadius:c}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:l,spacing:h}=this.options,d=(o+a)/2,u=(r+c+h+l)/2;return{x:s+Math.cos(d)*u,y:n+Math.sin(d)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:s,circumference:n}=this,o=(s.offset||0)/4,a=(s.spacing||0)/2,r=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>I?Math.floor(n/I):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const c=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(c)*o,Math.sin(c)*o);const l=1-Math.sin(Math.min(B,n||0)),h=o*l;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,Zr(e,this,h,a,r),Qr(e,this,h,a,r),e.restore()}}y(_e,"id","arc"),y(_e,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),y(_e,"defaultRoutes",{backgroundColor:"backgroundColor"}),y(_e,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function pn(i,t,e=t){i.lineCap=C(e.borderCapStyle,t.borderCapStyle),i.setLineDash(C(e.borderDash,t.borderDash)),i.lineDashOffset=C(e.borderDashOffset,t.borderDashOffset),i.lineJoin=C(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=C(e.borderWidth,t.borderWidth),i.strokeStyle=C(e.borderColor,t.borderColor)}function Jr(i,t,e){i.lineTo(e.x,e.y)}function tc(i){return i.stepped?vo:i.tension||i.cubicInterpolationMode==="monotone"?So:Jr}function mn(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:a,end:r}=t,c=Math.max(n,a),l=Math.min(o,r),h=n<a&&o<a||n>r&&o>r;return{count:s,start:c,loop:t.loop,ilen:l<c&&!h?s+l-c:l-c}}function ec(i,t,e,s){const{points:n,options:o}=t,{count:a,start:r,loop:c,ilen:l}=mn(n,e,s),h=tc(o);let{move:d=!0,reverse:u}=s||{},f,p,g;for(f=0;f<=l;++f)p=n[(r+(u?l-f:f))%a],!p.skip&&(d?(i.moveTo(p.x,p.y),d=!1):h(i,g,p,u,o.stepped),g=p);return c&&(p=n[(r+(u?l:0))%a],h(i,g,p,u,o.stepped)),!!c}function ic(i,t,e,s){const n=t.points,{count:o,start:a,ilen:r}=mn(n,e,s),{move:c=!0,reverse:l}=s||{};let h=0,d=0,u,f,p,g,m,b;const _=v=>(a+(l?r-v:v))%o,M=()=>{g!==m&&(i.lineTo(h,m),i.lineTo(h,g),i.lineTo(h,b))};for(c&&(f=n[_(0)],i.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[_(u)],f.skip)continue;const v=f.x,x=f.y,w=v|0;w===p?(x<g?g=x:x>m&&(m=x),h=(d*h+v)/++d):(M(),i.lineTo(v,x),p=w,d=0,g=m=x),b=x}M()}function ni(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?ic:ec}function sc(i){return i.stepped?ia:i.tension||i.cubicInterpolationMode==="monotone"?sa:xt}function nc(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),pn(i,t.options),i.stroke(n)}function oc(i,t,e,s){const{segments:n,options:o}=t,a=ni(t);for(const r of n)pn(i,o,r.style),i.beginPath(),a(i,t,r,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const ac=typeof Path2D=="function";function rc(i,t,e,s){ac&&!t.options.segment?nc(i,t,e,s):oc(i,t,e,s)}class xe extends dt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;Ko(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=la(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,a=aa(this,{property:e,start:n,end:n});if(!a.length)return;const r=[],c=sc(s);let l,h;for(l=0,h=a.length;l<h;++l){const{start:d,end:u}=a[l],f=o[d],p=o[u];if(f===p){r.push(f);continue}const g=Math.abs((n-f[e])/(p[e]-f[e])),m=c(f,p,g,s.stepped);m[e]=t[e],r.push(m)}return r.length===1?r[0]:r}pathSegment(t,e,s){return ni(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=ni(this);let a=this._loop;e=e||0,s=s||this.points.length-e;for(const r of n)a&=o(t,this,r,{start:e,end:e+s-1});return!!a}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),rc(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}y(xe,"id","line"),y(xe,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),y(xe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),y(xe,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function xs(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class qe extends dt{constructor(e){super();y(this,"parsed");y(this,"skip");y(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(e-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return xs(this,e,"x",s)}inYRange(e,s){return xs(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!nt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,Mo(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}y(qe,"id","point"),y(qe,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),y(qe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function bn(i,t){const{x:e,y:s,base:n,width:o,height:a}=i.getProps(["x","y","base","width","height"],t);let r,c,l,h,d;return i.horizontal?(d=a/2,r=Math.min(e,n),c=Math.max(e,n),l=s-d,h=s+d):(d=o/2,r=e-d,c=e+d,l=Math.min(s,n),h=Math.max(s,n)),{left:r,top:l,right:c,bottom:h}}function ct(i,t,e,s){return i?0:U(t,e,s)}function cc(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Gs(s);return{t:ct(n.top,o.top,0,e),r:ct(n.right,o.right,0,t),b:ct(n.bottom,o.bottom,0,e),l:ct(n.left,o.left,0,t)}}function lc(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=qs(n),a=Math.min(t,e),r=i.borderSkipped,c=s||D(n);return{topLeft:ct(!c||r.top||r.left,o.topLeft,0,a),topRight:ct(!c||r.top||r.right,o.topRight,0,a),bottomLeft:ct(!c||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:ct(!c||r.bottom||r.right,o.bottomRight,0,a)}}function hc(i){const t=bn(i),e=t.right-t.left,s=t.bottom-t.top,n=cc(i,e/2,s/2),o=lc(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Ze(i,t,e,s){const n=t===null,o=e===null,r=i&&!(n&&o)&&bn(i,s);return r&&(n||Pe(t,r.left,r.right))&&(o||Pe(e,r.top,r.bottom))}function dc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function uc(i,t){i.rect(t.x,t.y,t.w,t.h)}function Qe(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,a=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+a,radius:i.radius}}class Je extends dt{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=hc(this),r=dc(a.radius)?Ks:uc;t.save(),(a.w!==o.w||a.h!==o.h)&&(t.beginPath(),r(t,Qe(a,e,o)),t.clip(),r(t,Qe(o,-e,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),r(t,Qe(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Ze(this,t,e,s)}inXRange(t,e){return Ze(this,t,null,e)}inYRange(t,e){return Ze(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}y(Je,"id","bar"),y(Je,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),y(Je,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const fc=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function gc(i,t,e,s){const n=i.indexOf(t);if(n===-1)return fc(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const pc=(i,t)=>i===null?null:U(Math.round(i),0,t);function ys(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ms extends Ot{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(O(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:gc(s,t,C(e,t),this._addedLabels),pc(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let a=t;a<=e;a++)n.push({value:a});return n}getLabelForValue(t){return ys.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}y(Ms,"id","category"),y(Ms,"defaults",{ticks:{callback:ys}});function mc(i,t){const e=[],{bounds:n,step:o,min:a,max:r,precision:c,count:l,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,p=h-1,{min:g,max:m}=t,b=!O(a),_=!O(r),M=!O(l),v=(m-g)/(d+1);let x=Di((m-g)/p/f)*f,w,k,S,P;if(x<1e-14&&!b&&!_)return[{value:g},{value:m}];P=Math.ceil(m/x)-Math.floor(g/x),P>p&&(x=Di(P*x/p/f)*f),O(c)||(w=Math.pow(10,c),x=Math.ceil(x*w)/w),n==="ticks"?(k=Math.floor(g/x)*x,S=Math.ceil(m/x)*x):(k=g,S=m),b&&_&&o&&to((r-a)/o,x/1e3)?(P=Math.round(Math.min((r-a)/x,h)),x=(r-a)/P,k=a,S=r):M?(k=b?a:k,S=_?r:S,P=l-1,x=(S-k)/P):(P=(S-k)/x,Yt(P,Math.round(P),x/1e3)?P=Math.round(P):P=Math.ceil(P));const A=Math.max(Ci(x),Ci(k));w=Math.pow(10,O(c)?A:c),k=Math.round(k*w)/w,S=Math.round(S*w)/w;let T=0;for(b&&(u&&k!==a?(e.push({value:a}),k<a&&T++,Yt(Math.round((k+T*x)*w)/w,a,ks(a,v,i))&&T++):k<a&&T++);T<P;++T){const E=Math.round((k+T*x)*w)/w;if(_&&E>r)break;e.push({value:E})}return _&&u&&S!==r?e.length&&Yt(e[e.length-1].value,r,ks(r,v,i))?e[e.length-1].value=r:e.push({value:r}):(!_||S===r)&&e.push({value:S}),e}function ks(i,t,{horizontal:e,minRotation:s}){const n=tt(s),o=(e?Math.sin(n):Math.cos(n))||.001,a=.75*t*(""+i).length;return Math.min(t/o,a)}class Te extends Ot{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return O(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const a=c=>n=e?n:c,r=c=>o=s?o:c;if(t){const c=et(n),l=et(o);c<0&&l<0?r(0):c>0&&l>0&&a(0)}if(n===o){let c=o===0?1:Math.abs(o*.05);r(o+c),t||a(n-c)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,a=mc(n,o);return t.bounds==="ticks"&&Vs(a,this,"value"),t.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Ie(t,this.chart.options.locale,this.options.ticks.format)}}class vs extends Te{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?t:0,this.max=W(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=tt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}y(vs,"id","linear"),y(vs,"defaults",{ticks:{callback:Ee.formatters.numeric}});const te=i=>Math.floor(rt(i)),bt=(i,t)=>Math.pow(10,te(i)+t);function Ss(i){return i/Math.pow(10,te(i))===1}function ws(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function bc(i,t){const e=t-i;let s=te(e);for(;ws(i,t,s)>10;)s++;for(;ws(i,t,s)<10;)s--;return Math.min(s,te(i))}function _c(i,{min:t,max:e}){t=K(i.min,t);const s=[],n=te(t);let o=bc(t,e),a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o),c=n>o?Math.pow(10,n):0,l=Math.round((t-c)*a)/a,h=Math.floor((t-c)/r/10)*r*10;let d=Math.floor((l-h)/Math.pow(10,o)),u=K(i.min,Math.round((c+h+d*Math.pow(10,o))*a)/a);for(;u<e;)s.push({value:u,major:Ss(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((c+h+d*Math.pow(10,o))*a)/a;const f=K(i.max,u);return s.push({value:f,major:Ss(f),significand:d}),s}class Ps extends Ot{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=Te.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return W(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=W(t)?Math.max(0,t):null,this.max=W(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!W(this._userMin)&&(this.min=t===bt(this.min,0)?bt(this.min,-1):bt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=r=>s=t?s:r,a=r=>n=e?n:r;s===n&&(s<=0?(o(1),a(10)):(o(bt(s,-1)),a(bt(n,1)))),s<=0&&o(bt(n,-1)),n<=0&&a(bt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=_c(e,this);return t.bounds==="ticks"&&Vs(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":Ie(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=rt(t),this._valueRange=rt(this.max)-rt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(rt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}y(Ps,"id","logarithmic"),y(Ps,"defaults",{ticks:{callback:Ee.formatters.logarithmic,major:{enabled:!0}}});function oi(i){const t=i.ticks;if(t.display&&i.display){const e=ot(t.backdropPadding);return C(t.font&&t.font.size,j.font.size)+e.height}return 0}function xc(i,t,e){return e=z(e)?e:[e],{w:yo(i,t.string,e),h:e.length*t.lineHeight}}function Os(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function yc(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,a=i.options.pointLabels,r=a.centerPointLabels?B/o:0;for(let c=0;c<o;c++){const l=a.setContext(i.getPointLabelContext(c));n[c]=l.padding;const h=i.getPointPosition(c,i.drawingArea+n[c],r),d=Tt(l.font),u=xc(i.ctx,d,i._pointLabels[c]);s[c]=u;const f=Q(i.getIndexAngle(c)+r),p=Math.round(hi(f)),g=Os(p,h.x,u.w,0,180),m=Os(p,h.y,u.h,90,270);Mc(e,t,f,g,m)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=Sc(i,s,n)}function Mc(i,t,e,s,n){const o=Math.abs(Math.sin(e)),a=Math.abs(Math.cos(e));let r=0,c=0;s.start<t.l?(r=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-r)):s.end>t.r&&(r=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+r)),n.start<t.t?(c=(t.t-n.start)/a,i.t=Math.min(i.t,t.t-c)):n.end>t.b&&(c=(n.end-t.b)/a,i.b=Math.max(i.b,t.b+c))}function kc(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=e,c=i.getPointPosition(t,s+n+a,o),l=Math.round(hi(Q(c.angle+N))),h=Oc(c.y,r.h,l),d=wc(l),u=Pc(c.x,r.w,d);return{visible:!0,x:c.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function vc(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(nt({x:e,y:s},t)||nt({x:e,y:o},t)||nt({x:n,y:s},t)||nt({x:n,y:o},t))}function Sc(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:a,display:r}=o.pointLabels,c={extra:oi(o)/2,additionalAngle:a?B/n:0};let l;for(let h=0;h<n;h++){c.padding=e[h],c.size=t[h];const d=kc(i,h,c);s.push(d),r==="auto"&&(d.visible=vc(d,l),d.visible&&(l=d))}return s}function wc(i){return i===0||i===180?"center":i<180?"left":"right"}function Pc(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function Oc(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function Dc(i,t,e){const{left:s,top:n,right:o,bottom:a}=e,{backdropColor:r}=t;if(!O(r)){const c=qs(t.borderRadius),l=ot(t.backdropPadding);i.fillStyle=r;const h=s-l.left,d=n-l.top,u=o-s+l.width,f=a-n+l.height;Object.values(c).some(p=>p!==0)?(i.beginPath(),Ks(i,{x:h,y:d,w:u,h:f,radius:c}),i.fill()):i.fillRect(h,d,u,f)}}function Cc(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const a=s.setContext(i.getPointLabelContext(n));Dc(e,a,o);const r=Tt(a.font),{x:c,y:l,textAlign:h}=o;De(e,i._pointLabels[n],c,l+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}function _n(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,I);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=i.getPointPosition(a,t),n.lineTo(o.x,o.y)}}function Ac(i,t,e,s,n){const o=i.ctx,a=t.circular,{color:r,lineWidth:c}=t;!a&&!s||!r||!c||e<0||(o.save(),o.strokeStyle=r,o.lineWidth=c,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),_n(i,e,a,s),o.closePath(),o.stroke(),o.restore())}function Lc(i,t,e){return Pt(i,{label:e,index:t,type:"pointLabel"})}class ye extends Te{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=ot(oi(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=W(t)&&!isNaN(t)?t:0,this.max=W(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/oi(this.options))}generateTickLabels(t){Te.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=F(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?yc(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=I/(this._pointLabels.length||1),s=this.options.startAngle||0;return Q(t*e+tt(s))}getDistanceFromCenterForValue(t){if(O(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(O(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Lc(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-N+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),_n(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,a=this._pointLabels.length;let r,c,l;if(e.pointLabels.display&&Cc(this,a),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){c=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),p=o.setContext(u);Ac(this,f,c,a,p)}}),s.display){for(t.save(),r=a-1;r>=0;r--){const h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,c=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),l=this.getPointPosition(r,c),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(l.x,l.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,a;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((r,c)=>{if(c===0&&this.min>=0&&!e.reverse)return;const l=s.setContext(this.getContext(c)),h=Tt(l.font);if(o=this.getDistanceFromCenterForValue(this.ticks[c].value),l.showLabelBackdrop){t.font=h.string,a=t.measureText(r.label).width,t.fillStyle=l.backdropColor;const d=ot(l.backdropPadding);t.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}De(t,r.label,0,-o,h,{color:l.color,strokeColor:l.textStrokeColor,strokeWidth:l.textStrokeWidth})}),t.restore()}drawTitle(){}}y(ye,"id","radialLinear"),y(ye,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ee.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),y(ye,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),y(ye,"descriptors",{angleLines:{_fallback:"grid"}});const ze={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Y=Object.keys(ze);function Ds(i,t){return i-t}function Cs(i,t){if(O(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let a=t;return typeof s=="function"&&(a=s(a)),W(a)||(a=typeof s=="string"?e.parse(a,s):e.parse(a)),a===null?null:(n&&(a=n==="week"&&(Zt(o)||o===!0)?e.startOf(a,"isoWeek",o):e.startOf(a,n)),+a)}function As(i,t,e,s){const n=Y.length;for(let o=Y.indexOf(i);o<n-1;++o){const a=ze[Y[o]],r=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((e-t)/(r*a.size))<=s)return Y[o]}return Y[n-1]}function Tc(i,t,e,s,n){for(let o=Y.length-1;o>=Y.indexOf(e);o--){const a=Y[o];if(ze[a].common&&i._adapter.diff(n,s,a)>=t-1)return a}return Y[e?Y.indexOf(e):0]}function Rc(i){for(let t=Y.indexOf(i)+1,e=Y.length;t<e;++t)if(ze[Y[t]].common)return Y[t]}function Ls(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=di(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Ic(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),a=t[t.length-1].value;let r,c;for(r=o;r<=a;r=+n.add(r,1,s))c=e[r],c>=0&&(t[c].major=!0);return t}function Ts(i,t,e){const s=[],n={},o=t.length;let a,r;for(a=0;a<o;++a)r=t[a],n[r]=a,s.push({value:r,major:!1});return o===0||!e?s:Ic(i,s,n,e)}class Re extends Ot{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Ba._date(t.adapters.date);n.init(e),$t(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Cs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:a,maxDefined:r}=this.getUserBounds();function c(l){!a&&!isNaN(l.min)&&(n=Math.min(n,l.min)),!r&&!isNaN(l.max)&&(o=Math.max(o,l.max))}(!a||!r)&&(c(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&c(this.getMinMax(!1))),n=W(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=W(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,a=this.max,r=no(n,o,a);return this._unit=e.unit||(s.autoSkip?As(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Tc(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Rc(this._unit),this.initOffsets(n),t.reverse&&r.reverse(),Ts(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const a=t.length<3?.5:.25;e=U(e,0,a),s=U(s,0,a),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,a=o.unit||As(o.minUnit,e,s,this._getLabelCapacity(e)),r=C(n.ticks.stepSize,1),c=a==="week"?o.isoWeekday:!1,l=Zt(c)||c===!0,h={};let d=e,u,f;if(l&&(d=+t.startOf(d,"isoWeek",c)),d=+t.startOf(d,l?"day":a),t.diff(s,e,a)>1e5*r)throw new Error(e+" and "+s+" are too far apart with stepSize of "+r+" "+a);const p=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,r,a),f++)Ls(h,u,p);return(u===s||n.bounds==="ticks"||f===1)&&Ls(h,u,p),Object.keys(h).sort(Ds).map(g=>+g)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,a=e||n[o];return this._adapter.format(t,a)}_tickFormatFunction(t,e,s,n){const o=this.options,a=o.ticks.callback;if(a)return F(a,[t,e,s],this);const r=o.time.displayFormats,c=this._unit,l=this._majorUnit,h=c&&r[c],d=l&&r[l],u=s[e],f=l&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=tt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),a=Math.sin(n),r=this._resolveTickFontOptions(0).size;return{w:s*o+r*a,h:s*a+r*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Ts(this,[t],this._majorUnit),n),a=this._getLabelSize(o),r=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return r>0?r:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Cs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Ws(t.sort(Ds))}}y(Re,"id","time"),y(Re,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Me(i,t,e){let s=0,n=i.length-1,o,a,r,c;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Mt(i,"pos",t)),{pos:o,time:r}=i[s],{pos:a,time:c}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Mt(i,"time",t)),{time:o,pos:r}=i[s],{time:a,pos:c}=i[n]);const l=a-o;return l?r+(c-r)*(t-o)/l:r}class Rs extends Re{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Me(e,this.min),this._tableRange=Me(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let a,r,c,l,h;for(a=0,r=t.length;a<r;++a)l=t[a],l>=e&&l<=s&&n.push(l);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],c=n[a-1],l=n[a],Math.round((h+c)/2)!==l&&o.push({time:l,pos:a/(r-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(Me(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return Me(this._table,s*this._tableRange+this._minPos,!0)}}y(Rs,"id","timeseries"),y(Rs,"defaults",Re.defaults);export{_e as A,Ye as B,yt as C,de as D,Ue as L,qe as P,xe as a,Ms as b,vs as c,Je as d};
