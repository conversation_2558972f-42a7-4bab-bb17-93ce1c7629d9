import{e as o}from"./app-f0078ddb.js";function s(r){return[r.screenX,r.screenY]}function i(){let r=o([-1,-1]);return{wasMoved(l){let t=s(l);return r.value[0]===t[0]&&r.value[1]===t[1]?!1:(r.value=t,!0)},update(l){r.value=s(l)}}}function a(r){throw new Error("Unexpected object: "+r)}var c=(r=>(r[r.First=0]="First",r[r.Previous=1]="Previous",r[r.Next=2]="Next",r[r.Last=3]="Last",r[r.Specific=4]="Specific",r[r.Nothing=5]="Nothing",r))(c||{});function v(r,l){let t=l.resolveItems();if(t.length<=0)return null;let u=l.resolveActiveIndex(),n=u??-1;switch(r.focus){case 0:{for(let e=0;e<t.length;++e)if(!l.resolveDisabled(t[e],e,t))return e;return u}case 1:{n===-1&&(n=t.length);for(let e=n-1;e>=0;--e)if(!l.resolveDisabled(t[e],e,t))return e;return u}case 2:{for(let e=n+1;e<t.length;++e)if(!l.resolveDisabled(t[e],e,t))return e;return u}case 3:{for(let e=t.length-1;e>=0;--e)if(!l.resolveDisabled(t[e],e,t))return e;return u}case 4:{for(let e=0;e<t.length;++e)if(l.resolveId(t[e],e,t)===r.id)return e;return u}case 5:return null;default:a(r)}}export{c,v as f,i as u};
