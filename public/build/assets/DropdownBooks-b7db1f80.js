import{e as _,p as x,A as k,o,c as d,w as s,u as e,g as V,t as m,f as v,a as i,b as l,d as y,h as B,n as u,F as N,T as A}from"./app-f0078ddb.js";import{r as F}from"./ChevronUpDownIcon-aff937c8.js";import{r as C}from"./CheckIcon-4bbdc2ab.js";import{E,j as S,A as T,F as j,I as z}from"./listbox-f702e976.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./keyboard-982fc047.js";import"./open-closed-7f51e238.js";const D={class:"relative mt-2"},I={class:"block truncate"},L={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Z={__name:"DropdownBooks",props:{items:Array,title:String,current:Number},emits:["update:selected"],setup(n,{emit:w}){const t=n,a=_(t.items[0]),b=w,c=()=>{a.value=t.items.find(p=>p.id===t.current)||t.items[0]};return x(()=>t.items,()=>{c()},{immediate:!0}),x(()=>a.value,()=>{b("update:selected",a.value)}),k(()=>{c()}),(p,f)=>(o(),d(e(z),{as:"div",modelValue:a.value,"onUpdate:modelValue":f[0]||(f[0]=r=>a.value=r)},{default:s(()=>[t.title?(o(),d(e(E),{key:0,class:"block text-sm leading-6 font-medium text-gray-900"},{default:s(()=>[V(m(n.title),1)]),_:1})):v("",!0),i("div",D,[l(e(S),{class:"relative w-full cursor-default rounded-md bg-white py-1.5 pr-10 pl-3 text-left text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset focus:ring-2 focus:ring-indigo-600 focus:outline-hidden sm:text-sm sm:leading-6"},{default:s(()=>[i("span",I,m(a.value.name),1),i("span",L,[l(e(F),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(A,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:s(()=>[l(e(T),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg focus:outline-hidden sm:text-sm"},{default:s(()=>[(o(!0),y(N,null,B(n.items,r=>(o(),d(e(j),{as:"template",key:r.id,value:r},{default:s(({active:g,selected:h})=>[i("li",{class:u([g?"bg-indigo-600 text-white":"text-gray-900","relative cursor-default py-2 pr-9 pl-3 select-none"])},[i("span",{class:u([h?"font-semibold":"font-normal","block truncate"])},m(r.name),3),h?(o(),y("span",{key:0,class:u([g?"text-white":"text-indigo-600","absolute inset-y-0 right-0 flex items-center pr-4"])},[l(e(C),{class:"h-5 w-5","aria-hidden":"true"})],2)):v("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]))}};export{Z as default};
