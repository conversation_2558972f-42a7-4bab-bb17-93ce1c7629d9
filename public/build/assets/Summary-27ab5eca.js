import v from"./SummaryWord-5b872645.js";import{_ as S}from"./ButtonItem-718c0517.js";import{p as h}from"./pluralize-d25a928b.js";import{e as x,o,d as u,r as B,a as t,t as y,u as i,b as k,w as C,F as w,h as b,f as _,n as f,g as W,c as A}from"./app-f0078ddb.js";import{r as N}from"./CheckCircleIcon-d86d1232.js";import"./StarIcon-155a2a28.js";/* empty css            */const U={class:"px-4"},V={key:0,class:"w-full rounded-xl border-2 border-amber-400 bg-amber-50 pt-6"},$={class:"flex w-full flex-col items-center px-4 sm:flex-row"},z={class:"grow"},j={id:"desktop-teams-headline",class:"text-sm font-bold uppercase tracking-wider text-gray-500"},E={class:"mt-4 sm:mt-0"},F={key:0},M={class:"grid grid-cols-1 divide-y"},O={ref:"landmark"},D={id:"desktop-teams-headline",class:"px-4 text-sm font-bold uppercase tracking-wider text-gray-500"},P={class:"flex w-full flex-col"},T={class:"grid grid-cols-1 divide-y"},R={__name:"Summary",props:{stats:Object,summary:Object,learned:Array,starred:Array,learnedWords:Array},setup(a){const c=a;let d=x(c.learned),n=x(c.starred);const g=s=>{d.value.some(e=>e.id==s.id)?d.value=d.value.filter(e=>e.id!=s.id):d.value.push(s)},p=s=>{n.value.some(e=>e.id==s.id)?n.value=n.value.filter(e=>e.id!=s.id):n.value.push(s)},L=()=>{c.learnedWords.forEach(s=>{d.value.some(e=>e.id===s.word_id)||d.value.push({id:s.word_id})}),axios.post("/api/mark-all-as-learned",{words:c.learnedWords.map(s=>s.word_id)})};return(s,e)=>(o(),u("div",U,[B(s.$slots,"default"),a.learnedWords.length>0?(o(),u("div",V,[t("div",$,[t("div",z,[t("h3",j," Mastered "+y(i(h)("Word",a.learnedWords.length)),1)]),t("div",E,[k(S,{id:"desktop-teams-headline",size:"xs",color:"white",class:"w-full sm:w-auto",onClick:e[0]||(e[0]=l=>L())},{default:C(()=>[k(i(N),{class:"-ml-1 -mt-0.5 mr-0.5 inline h-5 w-5 self-center stroke-2 text-blue-600","aria-hidden":"true"}),e[5]||(e[5]=W(" Mark ")),a.learnedWords.length>1?(o(),u("span",F,"All")):_("",!0),e[6]||(e[6]=W(" as Learned "))]),_:1})])]),t("div",null,[t("div",M,[(o(!0),u(w,null,b(a.learnedWords,(l,m)=>(o(),A(v,{class:f(["px-4 last:rounded-b-xl hover:bg-amber-100",{"mt-3":m===0}]),key:m,word:l,"is-learned":i(d).some(r=>r.id==l.word_id),"is-starred":i(n).some(r=>r.id==l.word_id),"onUpdate:toggleLearned":e[1]||(e[1]=r=>g(r)),"onUpdate:toggleStarred":e[2]||(e[2]=r=>p(r))},null,8,["word","class","is-learned","is-starred"]))),128))]),t("div",O,null,512)])])):_("",!0),t("div",{class:f(["w-full py-6",{"mt-6":a.learnedWords.length>0}])},[t("h3",D,y(i(h)("Word",a.summary.length))+" in Progress ",1),t("div",P,[t("div",T,[(o(!0),u(w,null,b(a.summary,(l,m)=>(o(),A(v,{key:m,word:l,class:f([{"mt-3":m===0},"px-4 hover:bg-slate-50"]),"is-learned":i(d).some(r=>r.id==l.word_id),"is-starred":i(n).some(r=>r.id==l.word_id),"onUpdate:toggleLearned":e[3]||(e[3]=r=>g(r)),"onUpdate:toggleStarred":e[4]||(e[4]=r=>p(r))},null,8,["word","class","is-learned","is-starred"]))),128))])])],2)]))}};export{R as default};
