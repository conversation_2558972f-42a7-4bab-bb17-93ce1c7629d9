import{V as oe,e as m,k as b,l as N,i as Ve,o as r,c as h,w as c,b as u,a as t,j as ge,u as e,d as a,t as l,F as B,h as H,n as X,g as k,f,T as F,W as G}from"./app-f0078ddb.js";import{_ as Se}from"./AppLayout-33f062bc.js";import{_ as re}from"./ProgressBar-b7203293.js";import{_ as Le}from"./Breadcrumbs-c96e9207.js";import Me from"./Options-d6b29c2b.js";import xe from"./OptionsSidebar-7953908b.js";import{_ as J}from"./ButtonItem-718c0517.js";import{T as ae}from"./easytimer-3d932146.js";import Ae from"./MultipleChoice-080711a4.js";import Ee from"./Matching-1875e265.js";import ze from"./Type-dfa282d2.js";import Pe from"./Drag-75baa519.js";import{_ as je}from"./TruncateVerse-a9219b3d.js";import{r as ye}from"./replaceMacra-3b9666ed.js";import{p as K}from"./pluralize-d25a928b.js";import{_ as qe}from"./Footer-0988dcd8.js";import{_ as Qe}from"./MobileSidebar-5e21b4cd.js";import{r as Ne}from"./InformationCircleIcon-716f3ffb.js";import{r as he}from"./PlusIcon-4ea782f1.js";import{r as _e,a as we}from"./PlayIcon-8c672cba.js";import{a as I}from"./animate.es-f1432f5f.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./OptionsMode-26ccd316.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";import"./OptionsDifficulty-c63856d1.js";import"./NextButtonSmall-9e6ffefc.js";import"./XCircleIcon-63af2b2a.js";import"./vuedraggable.umd-aab17b5c.js";import"./lodash-631955d9.js";import"./removePunctuation-702d8a66.js";const Be={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},He={class:"px-4 py-8 sm:px-8"},Fe={class:"flex flex-row items-center justify-between"},Ie={class:"mt-8"},Oe={key:0},Ue={key:1},De={class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},Re={class:"mt-2 mb-8"},We={class:"text-center text-xs font-bold text-gray-500 uppercase"},Xe={class:"mb-8"},Ge={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},Je={key:0,class:"mb-8 text-left"},Ke={class:"text-sm font-bold text-gray-500 uppercase"},Ye={key:0},Ze={key:1},et={key:0,class:"mt-2 flex flex-row items-center"},tt={key:1,class:"w-full"},st={key:0,class:"mx-auto"},ot={key:1,class:"mx-auto"},rt=["innerHTML"],at={class:"text-xl"},nt={key:0},it={key:1},lt={key:0,class:"mx-auto mt-8 text-center lg:hidden"},ct={key:0,class:"mx-auto"},ut={key:1,class:"mx-auto"},dt={class:"mt-2"},mt={class:"text-center text-xs font-bold text-gray-500 uppercase"},pt={key:1},ft={class:"mx-auto mt-8 text-center"},vt={key:0,class:"mt-4"},gt={key:1,class:"mt-4"},xt={class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"},yt={key:2,class:"mt-8 grid grid-cols-1 gap-8"},ht={class:"mt-2 rounded-lg bg-white shadow-sm"},_t={class:"mt-2 flex flex-row items-center"},wt=["src"],bt={class:"ml-2 flex grow flex-col"},kt={class:"font-intro text-lg font-bold text-gray-900"},Ct={key:0},$t={key:1},Tt={class:"text-xs text-gray-600"},Vt={"aria-labelledby":"userStats"},St={class:"grid grid-cols-1 gap-px overflow-hidden bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},Lt={class:"text-sm leading-6 font-medium text-gray-500"},Mt={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},At={key:0},Et={key:0,class:"ml-1 text-sm font-medium text-gray-500"},zt={key:1},Pt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},jt={class:"mt-2"},qt={class:"text-center text-xs font-bold text-gray-500 uppercase"},Qt={key:0},Nt={class:"mx-auto mt-8 text-center"},Bt={key:0,class:"mt-4"},Ht={key:1,class:"mt-4"},Ft={class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"},It={key:0,class:"mx-auto"},Ot={key:1,class:"mx-auto"},Ut={key:1,class:"mt-8 grid grid-cols-1 gap-8"},Dt={class:"mt-2 rounded-lg bg-white shadow-sm"},Rt={class:"mt-2 flex flex-row items-center"},Wt=["src"],Xt={class:"ml-2 flex grow flex-col"},Gt={class:"font-intro text-lg font-bold text-gray-900"},Jt={key:0},Kt={key:1},Yt={class:"text-xs text-gray-600"},Zt={key:0,class:"text-left"},es={class:"text-sm font-bold text-gray-500 uppercase"},ts={key:0},ss={key:1},os={key:0,class:"mt-2 flex flex-row items-center"},rs={"aria-labelledby":"userStats"},as={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},ns={class:"text-sm leading-6 font-medium text-gray-500"},is={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},ls={key:0},cs={key:0,class:"ml-1 text-sm font-medium text-gray-500"},us={key:1},ds={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},ms={key:1,class:"mt-8 w-full"},ps={key:0,class:"mx-auto"},fs={key:1,class:"mx-auto"},_o={__name:"Custom",props:{sections:{type:Array,required:!0},session:{type:Object,required:!1},question:{type:Object,required:!1},initialQuestion:{type:Object,required:!1}},setup(y){const _=y;let x=oe({time:0,difficulty:0}),ne=m(b().props.user.xp),O=m(b().props.user.xp),S=m(b().props.user.level),P=m(!1),Y=m(!1),L=m(!1);const T=N(()=>{let i=0;return _.sections.forEach(s=>{i+=s.word_count}),i});let n=m(),Z=m(),j=m(!0),w=m(!1),g=m(!1),ee=m(0),v=m(),M=oe([]),A=m(0),p=new ae,V=new ae,ie=m([]),te=m(0),E=m(!0),C=m(!1),d=oe({correct:0,incorrect:0});const be=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:"Custom",href:"#",current:!0}],se=N(()=>{let i=S.value.max-S.value.min+1;return(O.value-S.value.min)/i*100}),ke=(i,s)=>Math.floor(Math.random()*(s-i+1))+i,q=i=>i.days>1?"more than "+i.days+" days":i.days>0?"more than "+i.days+" day":i.hours>1?"more than "+i.hours+" hours":i.hours>0?"more than "+i.hours+" hour":i.seconds<=9?i.minutes+":0"+i.seconds:i.minutes+":"+i.seconds;let z=m(!1);const le=()=>{x.time>0?p.isRunning()?(p.pause(),z.value=!0):(p.start(),z.value=!1):V.isRunning()?(V.pause(),z.value=!0):(V.start(),z.value=!1)},ce=()=>{let i=p.getTimeValues();i.minutes+=1,p.removeAllEventListeners("targetAchieved"),p=new ae,p.start({countdown:!0,startValues:{minutes:i.minutes,seconds:i.seconds}}),z.value&&p.pause(),v.value=q(p.getTimeValues()),p.addEventListener("secondsUpdated",function(){v.value=q(p.getTimeValues())}),p.addEventListener("targetAchieved",function(){W()})},ue=N(()=>[{name:"Correct",value:d.correct,unit:"out of "+(d.correct+d.incorrect)},{name:"Current Streak",value:A.value,unit:"in a row"},{name:"XP Earned",value:te.value},{name:"Accuracy",value:ee.value,unit:"%"}]),de=N(()=>d.correct+d.incorrect==0?-1:d.correct+d.incorrect<10?d.correct:Math.round(10*d.correct/(d.correct+d.incorrect))),me=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],pe=N(()=>{var i=[];return _.sections.forEach(s=>{i.push(s.book_id+":"+parseInt(s.start)+":"+parseInt(s.end))}),i}),Ce=N(()=>T.value<5),fe=()=>{Y.value=!0,G.reload({only:["session"],preserveState:!0,preserveScroll:!0,onSuccess:()=>{axios.post("/api/practice/grammar/update-session-options",{session:_.session.id,settings:x}).then(()=>{setTimeout(()=>{V.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),x.time>0?(p.start({countdown:!0,startValues:{seconds:x.time}}),v.value=q(p.getTimeValues()),p.addEventListener("secondsUpdated",function(){v.value=q(p.getTimeValues())}),p.addEventListener("targetAchieved",function(){W()})):(v.value=q(V.getTimeValues()),V.addEventListener("secondsUpdated",function(){v.value=q(V.getTimeValues())})),G.reload({method:"get",data:{session:_.session.id},only:["initialQuestion"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{n.value=_.initialQuestion.first,Z.value=_.initialQuestion.second,L.value=!0}})},ke(200,500))})}})};function D(){n.value=Z.value,j.value=!1,P.value=!1,G.reload({data:{session:_.session.id},only:["question"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{Z.value=_.question,j.value=!0}})}const R=i=>{if(E.value=!0,w.value)return;if(i.isArray?(ie.value=i.answer,g.value=JSON.stringify(i.answer)===JSON.stringify(i.key)):g.value=ye(i.answer).toUpperCase()==ye(i.key).toUpperCase(),g.value){if(A.value<1)I($=>{A.value=Math.round($)},{duration:.3,easing:"ease-out"});else{let $=A.value;I(Te=>{A.value=Math.round(Te+$)},{duration:.3,easing:"ease-out"})}let o=d.correct;I($=>{d.correct=Math.round($+o)},{duration:.3,easing:"ease-out"});let U=ne.value;I($=>{ne.value=Math.round(U+$)},{duration:.3,easing:"ease-out"});let $e=te.value;I($=>{te.value=Math.round($e+$*ve())},{duration:.3,easing:"ease-out"})}else g.value=!1,A.value=0,d.incorrect++;let s=ee.value;if(I(o=>{ee.value=s+Math.round((Math.round(d.correct/(d.correct+d.incorrect)*100)-s)*o)},{duration:.3,easing:"ease-out"}),M.find(o=>o.id===n.value.id)){var Q=M.findIndex(o=>o.id===n.value.id);M[Q].correct=M[Q].correct+g.value,M[Q].total=M[Q].total+1}else M.push({id:n.value.id,word:n.value.word,pos:n.value.pos,definition:n.value.short_definition,core:n.value.core,correct:g.value?1:0,gender:n.value.gender,total:1,lexicon_id:n.value.lexicon_id});P.value=!0,g.value?E.value=!1:setTimeout(()=>{E.value=!1},2e3),axios.post("/api/practice/grammar/add-attempt",{session:_.session.id,item_id:n.value.id,type:n.value.type,correct:g.value,attempt:i.answer,options:n.value.options?n.value.options.join("|"):null,xp:ve(),streak:A.value,time:V.getTotalTimeValues().secondTenths}).then(o=>{O.value=o.data.xp,S.value=o.data.level})},ve=()=>{if(g.value)switch(n.value.type){case"drag":switch(x.difficulty){case 1:return 3;case 2:return 4;default:return 2}case"type":switch(x.difficulty){case 1:return 4;case 2:return 5;default:return 3}case"match":switch(x.difficulty){case 1:return 3;case 2:return 4;default:return 2}default:return 1}return 0},W=()=>{w.value=!0,G.post("/api/practice/grammar/finish",{session:_.session.id})};return(i,s)=>{const Q=Ve("Head");return r(),h(Se,null,{default:c(()=>[u(Q,null,{default:c(()=>s[19]||(s[19]=[t("title",null,"Practice",-1)])),_:1}),t("main",Be,[t("div",He,[t("div",Fe,[u(Le,{class:"lg:col-span-9 xl:grid-cols-10",pages:be}),u(e(Ne),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:s[0]||(s[0]=o=>ge(C)?C.value=!e(C):C=!e(C))})]),t("div",Ie,[u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:c(()=>[e(L)?(r(),a("div",Ue,[t("div",De,[u(re,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(S).level,"post-text":e(O)+" XP",progress:se.value},null,8,["pre-text","post-text","progress"]),t("div",Re,[t("p",We,l(e(b)().props.user.level.max-e(b)().props.user.xp+1)+" xp to the next level ",1)]),t("section",Xe,[s[20]||(s[20]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),t("div",Ge,[(r(),a(B,null,H(me,o=>t("div",{class:X(["h-4 w-full rounded-sm shadow-sm",de.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),e(v)?(r(),a("section",Je,[t("h5",Ke,[s[21]||(s[21]=k(" Time ")),e(x).time>0?(r(),a("span",Ye,"Remaining")):(r(),a("span",Ze,"Elapsed"))]),e(v)?(r(),a("div",et,[t("div",{class:X(["grow text-left font-medium text-gray-900",e(v).length>10?"text-2xl":"text-4xl"])},l(e(v)),3),e(x).time>0?(r(),a("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:s[2]||(s[2]=o=>ce())},[u(e(he),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):f("",!0),t("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:s[3]||(s[3]=o=>le())},[e(z)?(r(),h(e(_e),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(r(),h(e(we),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):f("",!0)])):f("",!0),e(L)?(r(),a("div",tt,[u(J,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(w),onClick:s[4]||(s[4]=o=>W())},{default:c(()=>[u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(w)?(r(),a("span",st,s[22]||(s[22]=[t("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),t("span",{class:"ml-2"},"Finishing ...",-1)]))):(r(),a("span",ot,"Finish"))]),_:1})]),_:1},8,["disabled"])])):f("",!0)]),u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:c(()=>[e(n).type==="mc"?(r(),h(Ae,{key:`${e(n).id}-mc`,options:e(n).options,"question-key":e(n).key,"is-correct":e(g),"is-answer":e(P),disabled:e(E)||e(w)||!e(j),attempts:e(d).correct+e(d).incorrect,onSubmit:s[5]||(s[5]=o=>R(o)),onNextQuestion:s[6]||(s[6]=o=>D())},{stem:c(()=>[t("div",{innerHTML:e(n).instructions.replace("_____","<span class=text-blue-600>"+e(n).token.split(":")[0]+"</span>")},null,8,rt)]),instructions:c(()=>[t("div",at,[u(je,{token:e(n).token,verse:e(n).verse},null,8,["token","verse"])])]),_:1},8,["options","question-key","is-correct","is-answer","disabled","attempts"])):e(n).type==="match"?(r(),h(Ee,{key:`${e(n).id}-match`,options:e(n).options,"is-answer":e(P),"is-correct":e(g),"user-attempt":e(ie),disabled:e(E)||e(w)||!e(j),onSubmit:s[7]||(s[7]=o=>R(o)),onNextQuestion:s[8]||(s[8]=o=>D())},{instructions:c(()=>[k(l(e(n).instructions),1)]),_:1},8,["options","is-answer","is-correct","user-attempt","disabled"])):e(n).type==="type"?(r(),h(ze,{key:`${e(n).id}-type`,options:e(n).key,"is-answer":e(P),"is-correct":e(g),difficulty:e(n).difficulty,disabled:e(E)||e(w)||!e(j),onSubmit:s[9]||(s[9]=o=>R(o)),onNextQuestion:s[10]||(s[10]=o=>D())},{stem:c(()=>[k(l(e(n).stem),1)]),instructions:c(()=>[k(l(e(n).instructions),1)]),_:1},8,["options","is-answer","is-correct","difficulty","disabled"])):e(n).type==="drag"?(r(),h(Pe,{key:`${e(n).id}-drag`,options:e(n).options,"question-key":e(n).key,stem:Array.isArray(e(n).stem)?e(n).stem[1]:"","is-answer":e(P),"is-correct":e(g),difficulty:e(n).difficulty,disabled:e(E)||e(w)||!e(j),onSubmit:s[11]||(s[11]=o=>R(o)),onNextQuestion:s[12]||(s[12]=o=>D())},{stem:c(()=>[Array.isArray(e(n).stem)?(r(),a("span",nt,l(e(n).stem[0]),1)):(r(),a("span",it,l(e(n).stem),1))]),instructions:c(()=>[k(l(e(n).instructions),1)]),_:1},8,["options","question-key","stem","is-answer","is-correct","difficulty","disabled"])):f("",!0)]),_:1})])):(r(),a("div",Oe,[u(Me,{"onUpdate:options":s[1]||(s[1]=o=>Object.assign(e(x),o))})]))]),_:1})]),e(L)?f("",!0):(r(),a("div",lt,[u(J,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:s[13]||(s[13]=o=>fe())},{default:c(()=>[u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(Y)?(r(),a("span",ct,s[23]||(s[23]=[t("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),t("span",{class:"ml-2"},"Loading ...",-1)]))):(r(),a("span",ut,"Continue"))]),_:1})]),_:1})]))]),u(qe)]),u(Qe,{class:"lg:hidden",show:e(C),onClose:s[14]||(s[14]=o=>ge(C)?C.value=!1:C=!1)},{default:c(()=>[t("div",null,[e(b)().props.authenticated?(r(),h(re,{key:0,class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(S).level,"post-text":e(O)+" XP",progress:se.value},null,8,["pre-text","post-text","progress"])):f("",!0),t("div",dt,[t("p",mt,l(e(b)().props.user.level.max-e(b)().props.user.xp+1)+" xp to the next level ",1)]),s[26]||(s[26]=t("h2",{class:"mt-8 font-intro text-2xl font-bold"}," Custom Grammar Practice ",-1)),e(L)?(r(),a("div",yt,[t("section",null,[s[25]||(s[25]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Sections",-1)),t("div",ht,[(r(!0),a(B,null,H(y.sections,(o,U)=>(r(),a("div",{key:U,class:"h-20 w-full px-4 py-2"},[t("div",_t,[t("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+o.image_name,class:"h-12 w-12"},null,8,wt),t("div",bt,[t("h4",kt,[k(l(o.work)+" "+l(o.l1)+" "+l(o.book)+".",1),o.start===o.end?(r(),a("span",Ct,l(o.start),1)):(r(),a("span",$t,l(o.start)+"-"+l(o.end),1))]),t("p",Tt,l(o.word_count)+" nouns and verbs ",1)])])]))),128))])]),t("section",Vt,[t("div",null,[t("dl",St,[(r(!0),a(B,null,H(ue.value,o=>(r(),a("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[t("dt",Lt,l(o.name),1),t("dd",Mt,[o.value?(r(),a("span",At,[k(l(o.value)+" ",1),o.unit?(r(),a("span",Et,l(o.unit),1)):f("",!0)])):(r(),a("span",zt,"–"))])]))),128))])])])])):(r(),a("div",pt,[u(xe,{sections:y.sections,"current-list":pe.value},{default:c(()=>[t("div",ft,[T.value==0?(r(),a("div",vt,s[24]||(s[24]=[t("div",{class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"}," We can only generate custom questions on sections with verified grammar. Please select a different section. ",-1)]))):T.value<6&&T.value>0?(r(),a("div",gt,[t("div",xt,l(e(K)("This",y.sections.length))+" "+l(e(K)("section",y.sections.length))+" "+l(y.sections.length==1?"does":"do")+" not contain enough words with verified grammar to generate custom questions. This set needs more than 5 words to continue. ",1)])):f("",!0)])]),_:1},8,["sections","current-list"])]))])]),_:1},8,["show"]),t("aside",Pt,[t("div",null,[u(re,{class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(S).level,"post-text":e(O)+" XP",progress:se.value},null,8,["pre-text","post-text","progress"]),t("div",jt,[t("p",qt,l(e(b)().props.user.level.max-e(b)().props.user.xp+1)+" xp to the next level ",1)]),s[33]||(s[33]=t("h2",{class:"mt-8 font-intro text-2xl font-bold"}," Custom Grammar Practice ",-1)),e(L)?(r(),a("div",Ut,[t("section",null,[s[29]||(s[29]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Sections",-1)),t("div",Dt,[(r(!0),a(B,null,H(y.sections,(o,U)=>(r(),a("div",{key:U,class:"h-20 w-full px-4 py-2"},[t("div",Rt,[t("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+o.image_name,class:"h-12 w-12"},null,8,Wt),t("div",Xt,[t("h4",Gt,[k(l(o.work)+" "+l(o.l1)+" "+l(o.book)+".",1),o.start===o.end?(r(),a("span",Jt,l(o.start),1)):(r(),a("span",Kt,l(o.start)+"-"+l(o.end),1))]),t("p",Yt,l(o.word_count)+" nouns and verbs ",1)])])]))),128))])]),e(v)?(r(),a("section",Zt,[t("h5",es,[s[30]||(s[30]=k(" Time ")),e(x).time>0?(r(),a("span",ts,"Remaining")):(r(),a("span",ss,"Elapsed"))]),e(v)?(r(),a("div",os,[t("div",{class:X(["grow text-left font-medium text-gray-900",e(v).length>10?"text-2xl":"text-4xl"])},l(e(v)),3),e(x).time>0?(r(),a("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:s[16]||(s[16]=o=>ce())},[u(e(he),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):f("",!0),t("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:s[17]||(s[17]=o=>le())},[e(z)?(r(),h(e(_e),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(r(),h(e(we),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):f("",!0)])):f("",!0),t("section",rs,[t("div",null,[t("dl",as,[(r(!0),a(B,null,H(ue.value,o=>(r(),a("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[t("dt",ns,l(o.name),1),t("dd",is,[o.value?(r(),a("span",ls,[k(l(o.value)+" ",1),o.unit?(r(),a("span",cs,l(o.unit),1)):f("",!0)])):(r(),a("span",us,"–"))])]))),128))])])]),t("section",null,[s[31]||(s[31]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),t("div",ds,[(r(),a(B,null,H(me,o=>t("div",{class:X(["h-4 w-full rounded-sm shadow-sm",de.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),e(L)?(r(),a("div",ms,[u(J,{class:"inline-flex w-full disabled:bg-blue-400",size:"lg",color:"indigo",disabled:e(w),onClick:s[18]||(s[18]=o=>W())},{default:c(()=>[u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(w)?(r(),a("span",ps,s[32]||(s[32]=[t("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),t("span",{class:"ml-2"},"Finishing ...",-1)]))):(r(),a("span",fs,"Finish"))]),_:1})]),_:1},8,["disabled"])])):f("",!0)])):(r(),a("div",Qt,[u(xe,{sections:y.sections,"current-list":pe.value},{default:c(()=>[t("div",Nt,[T.value==0?(r(),a("div",Bt,s[27]||(s[27]=[t("div",{class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"}," We can only generate custom questions on sections with verified grammar. Please select a different section. ",-1)]))):T.value<6&&T.value>0?(r(),a("div",Ht,[t("div",Ft,l(e(K)("This",y.sections.length))+" "+l(e(K)("section",y.sections.length))+" "+l(y.sections.length==1?"does":"do")+" not contain enough words with verified grammar to generate custom questions. This set needs more than 5 words to continue. ",1)])):f("",!0),T.value>5?(r(),h(J,{key:2,color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",disabled:Ce.value,onClick:s[15]||(s[15]=o=>fe())},{default:c(()=>[u(F,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(Y)?(r(),a("span",It,s[28]||(s[28]=[t("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),t("span",{class:"ml-2"},"Loading ...",-1)]))):(r(),a("span",Ot,"Continue"))]),_:1})]),_:1},8,["disabled"])):f("",!0)])]),_:1},8,["sections","current-list"])]))])])]),_:1})}}};export{_o as default};
