import{e as y,V as q,o as t,d as r,a as u,r as E,b as n,w as o,g as l,t as S,a9 as K,n as x,z as H,C as N,l as Q,p as W,c as _,k as Y,f as m,u as z,F as D,h as I,W as P}from"./app-f0078ddb.js";import{b as U,a as G}from"./DialogModal-b2ffd0f0.js";import{_ as R}from"./InputError-7edb5cf8.js";import{_ as w}from"./ButtonItem-718c0517.js";import{_ as B}from"./TextInput-940981ae.js";import{_ as O}from"./InputLabel-3b7f7747.js";/* empty css            */import"./SectionTitle-05f6d081.js";import"./_plugin-vue_export-helper-c27b6911.js";const j={class:"mt-4"},b={__name:"ConfirmsPassword",props:{title:{type:String,default:"Confirm Password"},content:{type:String,default:"For your security, please confirm your password to continue."},button:{type:String,default:"Confirm"}},emits:["confirmed"],setup(k,{emit:T}){const c=T,a=y(!1),s=q({password:"",error:"",processing:!1}),p=y(null),g=()=>{axios.get(route("password.confirmation")).then(i=>{i.data.confirmed?c("confirmed"):(a.value=!0,setTimeout(()=>p.value.focus(),250))})},v=()=>{s.processing=!0,axios.post(route("password.confirm"),{password:s.password}).then(()=>{s.processing=!1,d(),H().then(()=>c("confirmed"))}).catch(i=>{s.processing=!1,s.error=i.response.data.errors.password[0],p.value.focus()})},d=()=>{a.value=!1,s.password="",s.error=""};return(i,h)=>(t(),r("span",null,[u("span",{onClick:g},[E(i.$slots,"default")]),n(U,{show:a.value,onClose:d},{title:o(()=>[l(S(k.title),1)]),content:o(()=>[l(S(k.content)+" ",1),u("div",j,[n(B,{ref_key:"passwordInput",ref:p,modelValue:s.password,"onUpdate:modelValue":h[0]||(h[0]=F=>s.password=F),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",autocomplete:"current-password",onKeyup:K(v,["enter"])},null,8,["modelValue"]),n(R,{message:s.error,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[n(w,{size:"sm",color:"white",onClick:d},{default:o(()=>h[1]||(h[1]=[l(" Cancel ")])),_:1}),n(w,{size:"sm",color:"black",class:x(["ml-3",{"opacity-25":s.processing}]),disabled:s.processing,onClick:v},{default:o(()=>[l(S(k.button),1)]),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}},J={key:0,class:"text-lg font-medium text-gray-900"},X={key:1,class:"text-lg font-medium text-gray-900"},Z={key:2,class:"text-lg font-medium text-gray-900"},ee={key:3},te={key:0},oe={class:"mt-4 max-w-xl text-sm text-gray-600"},se={key:0,class:"font-semibold"},ae={key:1},re=["innerHTML"],ne={key:0,class:"mt-4 max-w-xl text-sm text-gray-600"},le={class:"font-semibold"},ie=["innerHTML"],ue={key:1,class:"mt-4"},ce={key:1},de={class:"grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg"},me={class:"mt-5"},fe={key:0},pe={key:1},Ce={__name:"TwoFactorAuthenticationForm",props:{requiresConfirmation:Boolean},setup(k){const T=k,c=y(!1),a=y(!1),s=y(!1),p=y(null),g=y(null),v=y([]),d=N({code:""}),i=Q(()=>{var f;return!c.value&&((f=Y().props.auth.user)==null?void 0:f.two_factor_enabled)});W(i,()=>{i.value||(d.reset(),d.clearErrors())});const h=()=>{c.value=!0,P.post(route("two-factor.enable"),{},{preserveScroll:!0,onSuccess:()=>Promise.all([F(),L(),V()]),onFinish:()=>{c.value=!1,a.value=T.requiresConfirmation}})},F=()=>axios.get(route("two-factor.qr-code")).then(f=>{p.value=f.data.svg}),L=()=>axios.get(route("two-factor.secret-key")).then(f=>{g.value=f.data.secretKey}),V=()=>axios.get(route("two-factor.recovery-codes")).then(f=>{v.value=f.data}),$=()=>{d.post(route("two-factor.confirm"),{errorBag:"confirmTwoFactorAuthentication",preserveScroll:!0,preserveState:!0,onSuccess:()=>{a.value=!1,p.value=null,g.value=null}})},M=()=>{axios.post(route("two-factor.recovery-codes")).then(()=>V())},A=()=>{s.value=!0,P.delete(route("two-factor.disable"),{preserveScroll:!0,onSuccess:()=>{s.value=!1,a.value=!1}})};return(f,e)=>(t(),_(G,null,{title:o(()=>e[1]||(e[1]=[l(" Two Factor Authentication ")])),description:o(()=>e[2]||(e[2]=[l(" Add additional security to your account using two factor authentication. ")])),content:o(()=>[i.value&&!a.value?(t(),r("h3",J," You have enabled two factor authentication. ")):i.value&&a.value?(t(),r("h3",X," Finish enabling two factor authentication. ")):(t(),r("h3",Z," You have not enabled two factor authentication. ")),e[11]||(e[11]=u("div",{class:"mt-3 max-w-xl text-sm text-gray-600"},[u("p",null," When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application. ")],-1)),i.value?(t(),r("div",ee,[p.value?(t(),r("div",te,[u("div",oe,[a.value?(t(),r("p",se," To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code. ")):(t(),r("p",ae," Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application or enter the setup key. "))]),u("div",{class:"mt-4 p-2 inline-block bg-white",innerHTML:p.value},null,8,re),g.value?(t(),r("div",ne,[u("p",le,[e[3]||(e[3]=l(" Setup Key: ")),u("span",{innerHTML:g.value},null,8,ie)])])):m("",!0),a.value?(t(),r("div",ue,[n(O,{for:"code",value:"Code"}),n(B,{id:"code",modelValue:z(d).code,"onUpdate:modelValue":e[0]||(e[0]=C=>z(d).code=C),type:"text",name:"code",class:"block mt-1 w-1/2",inputmode:"numeric",autofocus:"",autocomplete:"one-time-code",onKeyup:K($,["enter"])},null,8,["modelValue"]),n(R,{message:z(d).errors.code,class:"mt-2"},null,8,["message"])])):m("",!0)])):m("",!0),v.value.length>0&&!a.value?(t(),r("div",ce,[e[4]||(e[4]=u("div",{class:"mt-4 max-w-xl text-sm text-gray-600"},[u("p",{class:"font-semibold"}," Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost. ")],-1)),u("div",de,[(t(!0),r(D,null,I(v.value,C=>(t(),r("div",{key:C},S(C),1))),128))])])):m("",!0)])):m("",!0),u("div",me,[i.value?(t(),r("div",pe,[n(b,{onConfirmed:$},{default:o(()=>[a.value?(t(),_(w,{key:0,size:"sm",color:"black",type:"button",class:x(["mr-3",{"opacity-25":c.value}]),disabled:c.value},{default:o(()=>e[6]||(e[6]=[l(" Confirm ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),n(b,{onConfirmed:M},{default:o(()=>[v.value.length>0&&!a.value?(t(),_(w,{key:0,size:"sm",color:"white",class:"mr-3"},{default:o(()=>e[7]||(e[7]=[l(" Regenerate Recovery Codes ")])),_:1})):m("",!0)]),_:1}),n(b,{onConfirmed:V},{default:o(()=>[v.value.length===0&&!a.value?(t(),_(w,{key:0,size:"sm",color:"white",class:"mr-3"},{default:o(()=>e[8]||(e[8]=[l(" Show Recovery Codes ")])),_:1})):m("",!0)]),_:1}),n(b,{onConfirmed:A},{default:o(()=>[a.value?(t(),_(w,{key:0,size:"sm",color:"white",class:x({"opacity-25":s.value}),disabled:s.value},{default:o(()=>e[9]||(e[9]=[l(" Cancel ")])),_:1},8,["class","disabled"])):m("",!0)]),_:1}),n(b,{onConfirmed:A},{default:o(()=>[a.value?m("",!0):(t(),_(w,{key:0,size:"sm",color:"red",class:x({"opacity-25":s.value}),disabled:s.value},{default:o(()=>e[10]||(e[10]=[l(" Disable ")])),_:1},8,["class","disabled"]))]),_:1})])):(t(),r("div",fe,[n(b,{onConfirmed:h},{default:o(()=>[n(w,{size:"sm",color:"black",type:"button",class:x({"opacity-25":c.value}),disabled:c.value},{default:o(()=>e[5]||(e[5]=[l(" Enable ")])),_:1},8,["class","disabled"])]),_:1})]))])]),_:1}))}};export{Ce as default};
