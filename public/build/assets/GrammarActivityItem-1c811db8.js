import{D as b}from"./datetime-8ddd27a0.js";import{l as c,i as p,o as l,c as h,w,n,a as t,t as r,d as o,f as d,h as u,F as m}from"./app-f0078ddb.js";/* empty css            */const _={class:"grow flex flex-col justify-center pr-4"},k={class:"flex items-center"},j={class:"text-xl font-bold text-gray-900"},B={key:0,class:"flex items-center gap-2 ml-4"},L={class:"mt-1 text-sm font-medium text-gray-600 line-clamp-3"},N={class:"flex gap-2 mt-2"},A={key:0,class:"flex-none flex flex-col justify-center w-full md:w-auto"},C={class:"grid grid-cols-2 gap-8 w-full"},D={class:"w-full justify-center font-sans"},F={class:"text-gray-900 font-semibold text-4xl mt-4 text-center"},S={class:"w-full justify-center font-sans"},V={class:"flex gap-0.5 justify-center mt-5 opacity-75"},I={__name:"GrammarActivityItem",props:{activity:Object,authenticated:Boolean},setup(e){const i=e,f=c(()=>i.activity.tags.includes("basic")?"border-sky-500":i.activity.tags.includes("intermediate")?"border-amber-500":i.activity.tags.includes("advanced")?"border-red-500":"border-gray-300"),y=c(()=>b.fromSQL(i.activity.published_at).diffNow("days").days>-7),v=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}];return(g,s)=>{const x=p("Link");return l(),h(x,{class:n(["w-full p-4 flex flex-col md:flex-row items-start gap-4 min-h-32 border-l-4 font-intro hover:bg-slate-50 duration-150 transition",f.value]),href:`/practice/grammar/a/${e.activity.id}`},{default:w(()=>[t("div",_,[t("div",k,[t("h3",j,r(e.activity.name),1),y.value?(l(),o("div",B,s[0]||(s[0]=[t("span",{class:"text-xs font-semibold text-sky-600 bg-sky-100 rounded-full px-2 py-1"}," New ",-1)]))):d("",!0)]),t("p",L,r(e.activity.description),1),t("div",N,[(l(!0),o(m,null,u(e.activity.tags,a=>(l(),o("div",{key:a,class:"px-3 py-0.5 rounded-full bg-gray-200 font-semibold text-xs text-gray-600 font-sans"},r(a),1))),128))])]),e.authenticated?(l(),o("div",A,[t("div",C,[t("div",D,[s[1]||(s[1]=t("h5",{class:"text-gray-600 uppercase text-sm font-semibold text-center"}," Attempts ",-1)),t("h2",F,r(e.activity.attempts),1)]),t("div",S,[s[2]||(s[2]=t("h4",{class:"text-gray-600 uppercase text-sm font-semibold text-center"}," Accuracy ",-1)),t("div",V,[(l(),o(m,null,u(v,a=>t("div",{class:n(["rounded-sm w-full h-6 shadow-sm mt-1",e.activity.accuracy>=a.value?a.color:"bg-gray-300"]),key:a.value},null,2)),64))])])])])):d("",!0)]),_:1},8,["class","href"])}}};export{I as default};
