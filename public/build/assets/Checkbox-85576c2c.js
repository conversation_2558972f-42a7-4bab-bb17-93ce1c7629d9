import{l as d,q as r,v as u,o as i,d as p}from"./app-f0078ddb.js";const m=["value"],k={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON>an],default:!1},value:{type:String,default:null}},emits:["update:checked"],setup(e,{emit:c}){const s=c,l=e,t=d({get(){return l.checked},set(o){s("update:checked",o)}});return(o,a)=>r((i(),p("input",{"onUpdate:modelValue":a[0]||(a[0]=n=>t.value=n),type:"checkbox",value:e.value,class:"rounded-sm border-gray-300 text-indigo-600 shadow-xs focus:ring-indigo-500"},null,8,m)),[[u,t.value]])}};export{k as _};
