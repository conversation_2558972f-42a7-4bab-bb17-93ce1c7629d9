import{e as d,l as et,p as q,i as st,o,c as k,w as L,b as i,a as e,q as j,bP as ot,u as r,j as C,d as n,h as g,t as l,F as v,n as _,g as M,x as D,f as T,R as rt,k as nt,W as A}from"./app-f0078ddb.js";import{_ as at}from"./AppLayout-33f062bc.js";import{_ as lt}from"./Breadcrumbs-c96e9207.js";import{_ as it}from"./DropdownWorks-53c01eb4.js";import{_ as R}from"./ButtonItem-718c0517.js";import dt from"./DropdownBooks-b7db1f80.js";import{P as ct}from"./Promotion-3eee0057.js";import{u as ut}from"./useIntersect-6e15125e.js";import{_ as mt}from"./Footer-0988dcd8.js";import{r as pt}from"./PlusCircleIcon-e71ff64a.js";import{r as z}from"./StarIcon-155a2a28.js";import{r as I}from"./CheckCircleIcon-d86d1232.js";import{r as H}from"./PlusIcon-fbc40698.js";import{r as E}from"./XCircleIcon-63af2b2a.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./listbox-f702e976.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";const ft={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},xt={class:"p-8"},bt={class:"overflow-visible"},gt={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},vt={class:"order-2 sm:order-1"},_t={class:"sm:hidden"},ht=["value"],yt={class:"hidden sm:block"},wt={class:"border-b border-gray-200"},kt={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Lt=["onClick","aria-current"],Ct={key:0},Tt={class:"sm:pr-4 mt-8"},$t={class:"mt-8"},Mt={class:"block text-sm font-medium text-gray-700"},At={class:"mt-8 grid grid-cols-2 gap-8"},Ht={for:"start",class:"block text-sm font-medium text-gray-700"},Vt=["innerHTML"],St=["innerHTML"],jt={class:"mt-1"},zt={for:"last",class:"block text-sm font-medium text-gray-700"},It=["innerHTML"],Bt=["innerHTML"],Nt={class:"mt-1"},Ut={class:"mt-2 text-xs text-gray-600"},Ft=["innerHTML"],Gt={class:"mx-auto flex items-center"},Pt={key:1},Wt={class:"mt-8"},qt=["onClick"],Dt={class:"flex grow flex-col ml-4"},Rt={class:"text-lg font-bold leading-5 text-gray-900 font-intro capitalize"},Et={class:"mt-0.5 line-clamp-2 flex items-center text-sm font-sans font-medium leading-5 text-gray-500"},Kt={class:"group-hover:bg-slate-100 transition duration-150 ease-in-out rounded-full p-1"},Ot=["onClick"],Jt={class:"flex grow flex-col ml-4"},Qt={class:"text-lg font-bold leading-5 text-gray-900 capitalize font-intro"},Xt={class:"mt-0.5 line-clamp-2 flex items-center text-sm font-sans font-medium leading-5 text-gray-500"},Yt={class:"group-hover:bg-slate-100 transition duration-150 ease-in-out rounded-full p-1"},Zt=["onClick"],te={class:"flex grow flex-col ml-4"},ee={class:"text-lg font-bold leading-5 text-gray-900 capitalize font-intro"},se={class:"mt-0.5 line-clamp-2 flex items-center text-sm font-sans font-medium leading-5 text-gray-500"},oe={class:"group-hover:bg-slate-100 transition duration-150 ease-in-out rounded-full p-1"},re={class:"sm:pl-4 mt-4 order-1 sm:order-2 mb-4 sm:mb-0"},ne={key:0,class:"mt-4"},ae={key:0,class:"my-2 flex flex-row items-center"},le=["src"],ie={class:"ml-2 flex grow flex-col"},de={class:"text-lg font-bold text-gray-900 font-intro leading-5"},ce={key:0},ue={key:1},me={class:"text-xs text-gray-600 mt-1"},pe={class:"ml-2 w-5 grow-0"},fe={key:1,class:"my-2 flex flex-row items-center"},xe={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full text-sm font-bold p-2 font-intro bg-green-100 text-green-700"},be={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full text-sm font-bold p-2 font-intro bg-blue-100 text-blue-700"},ge={class:"ml-2 flex grow flex-col"},ve={class:"text-lg font-bold font-intro text-gray-900"},_e={class:"text-xs text-gray-600"},he={class:"ml-2 w-5 grow-0"},ye={key:0,class:"mx-auto mb-1 mt-1"},we={key:1,class:"mx-auto"},ke={key:1},Le={class:"bg-slate-50 hidden lg:inline-block lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},Ce={key:0},Te=["onClick"],$e={class:"flex grow flex-col ml-4"},Me={class:"text-lg font-bold leading-5 text-gray-900 capitalize font-intro"},Ae={class:"mt-0.5 line-clamp-2 flex items-center text-sm font-sans font-medium leading-5 text-gray-500"},He={class:"group-hover:bg-slate-100 transition duration-150 ease-in-out rounded-full p-1"},Ve={key:1},ws={__name:"Custom",props:{works:Array,books:Array,sections:Array,items:Array,activities:Array,tab:String,frequencyList:Array,groups:Object,personalVocabLists:Array},setup(m){const K=[{name:"Words",href:"/words",current:!1},{name:"Custom List",href:"#",current:!0}],c=m;let h=d(c.tab),p=d([]),B=d(c.books),u=d(c.works[0]),y=d(c.books[0]),x=d(""),b=d(""),N=d(),O=d(),U=d(!1);const V=d(null);let $=d(c.groups),S=d($.value.data);const J=et(()=>$.value.next_page_url!==null),Q=()=>{J.value&&axios.get($.value.next_page_url).then(a=>{S.value=[...S.value,...a.data.data],$.value=a.data})};V!==null&&c.tab=="lists"&&ut(V,Q,{rootMargin:"0px 0px 250px 0px"}),c.items.forEach(a=>{a.vocabList?p.value.push(a.vocabList.id):p.value.push(a.sectionList.book_id+":"+parseInt(a.sectionList.start)+":"+parseInt(a.sectionList.end))});const F=a=>{A.get("/words/custom",{sections:p.value,tab:a},{replace:!0,preserveScroll:!0})},G=[{name:"Lists",current:c.tab=="lists"||c.tab==null},{name:"Works",current:c.tab=="works"}],P=a=>{p.value.splice(a,1),A.get("/words/custom",{sections:p.value,tab:c.tab},{preserveState:!0,replace:!0,preserveScroll:!0})},X=a=>{switch(!0){case a=="basic":return"Basic";case a=="intermediate":return"Inter";case a=="advanced":return"Adv";default:return"Basic"}},w=(a=null)=>{a?p.value.push(a):p.value.push(y.value.id+":"+parseInt(x.value)+":"+parseInt(b.value)),x.value="",b.value="",A.get("/words/custom",{sections:p.value,tab:c.tab},{preserveState:!0,replace:!0,preserveScroll:!0})},W=a=>{let s=a.keyCode?a.keyCode:a.which;(s<48||s>57)&&s!==46&&a.preventDefault()},Y=()=>{U.value=!0,A.get("/words/c/list",{sections:p.value})};return q(()=>y,()=>{x.value="",b.value=""},{deep:!0}),q(u,a=>{axios.post("/api/practice/vocabulary/get-books",{work:a.id}).then(s=>{B.value=s.data,x.value="",b.value=""})},{deep:!0}),(a,s)=>{const Z=st("Head");return o(),k(at,null,{default:L(()=>[i(Z,null,{default:L(()=>s[8]||(s[8]=[e("title",null,"Custom Vocabulary List",-1)])),_:1}),e("main",ft,[e("div",xt,[i(lt,{class:"lg:col-span-9 xl:grid-cols-10",pages:K}),e("div",bt,[s[18]||(s[18]=e("div",{class:"mt-8 pb-8"},[e("h1",{class:"text-4xl text-gray-900 font-bold"}," Create a Custom Vocabulary List "),e("p",{class:"text-base font-normal text-gray-600 mt-4"}," Create your own list of words from our texts or lists. ")],-1)),e("div",gt,[e("div",vt,[e("div",_t,[s[9]||(s[9]=e("label",{for:"tabs",class:"sr-only"},"Select a tab",-1)),j(e("select",{id:"tabs",name:"tabs","onUpdate:modelValue":s[0]||(s[0]=t=>C(h)?h.value=t:h=t),onChange:s[1]||(s[1]=t=>F(r(h).toLowerCase())),class:"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"},[(o(),n(v,null,g(G,t=>e("option",{key:t.name,value:t.name.toLowerCase()},l(t.name),9,ht)),64))],544),[[ot,r(h)]])]),e("div",yt,[e("div",wt,[e("nav",kt,[(o(),n(v,null,g(G,t=>e("div",{key:t.name,onClick:f=>F(t.name.toLowerCase()),class:_([t.current?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700","whitespace-nowrap border-b-2 py-4 px-1 text-sm font-bold cursor-pointer"]),"aria-current":t.current?"page":void 0},l(t.name),11,Lt)),64))])])]),m.tab=="works"?(o(),n("div",Ct,[e("div",Tt,[i(it,{list:m.works,"onUpdate:work":s[2]||(s[2]=t=>C(u)?u.value=t:u=t)},null,8,["list"]),e("div",$t,[e("p",Mt," Select the "+l(r(u).l1),1),i(dt,{items:r(B),"onUpdate:selected":s[3]||(s[3]=t=>C(y)?y.value=t:y=t)},null,8,["items"])]),e("div",At,[e("div",null,[e("label",Ht,[s[10]||(s[10]=M("First ")),r(u).l2?(o(),n("span",{key:0,innerHTML:r(u).l2},null,8,Vt)):(o(),n("span",{key:1,innerHTML:r(u).l4},null,8,St))]),e("div",jt,[j(e("input",{id:"start","onUpdate:modelValue":s[4]||(s[4]=t=>C(x)?x.value=t:x=t),type:"text",name:"start",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 101",onKeypress:W},null,544),[[D,r(x)]])])]),e("div",null,[e("label",zt,[s[11]||(s[11]=M("Last ")),r(u).l2?(o(),n("span",{key:0,innerHTML:r(u).l2},null,8,It)):(o(),n("span",{key:1,innerHTML:r(u).l4},null,8,Bt))]),e("div",Nt,[j(e("input",{id:"last","onUpdate:modelValue":s[5]||(s[5]=t=>C(b)?b.value=t:b=t),type:"text",name:"last",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 157",onKeypress:W},null,544),[[D,r(b)]])])])]),e("p",Ut," Leaving the first or last blank will default to the start or end of this "+l(r(u).l1.toLowerCase())+". ",1),r(N)?(o(),n("p",{key:0,class:"mt-2 text-xs text-red-600",innerHTML:r(N)},null,8,Ft)):T("",!0),i(R,{class:"mb-8 mt-8 flex text-center float-right px-4 w-full",color:"gray",size:"sm",disabled:r(O),onClick:s[6]||(s[6]=t=>w())},{default:L(()=>[e("span",Gt,[s[12]||(s[12]=M("Add to List ")),i(r(pt),{class:"ml-2 w-5 h-5 inline-block self-center"})])]),_:1},8,["disabled"])])])):T("",!0),m.tab=="lists"?(o(),n("div",Pt,[e("div",Wt,[(o(!0),n(v,null,g(m.personalVocabLists,t=>(o(),n("div",{class:"duration-250 flex flex-row items-center rounded-xl bg-white py-4 px-2 mb-4 transition ease-in-out group cursor-pointer hover:bg-gray-50",key:t.id,onClick:f=>w(t.id)},[e("div",{class:_(["rounded-full w-12 h-12 p-2 flex items-center justify-center",t.id=="star"?"bg-green-100 text-green-600 ":"bg-blue-100 text-blue-600 "])},[t.id=="star"?(o(),k(r(z),{key:0,class:"h-10 w-10 text-green-700 stroke-2"})):(o(),k(r(I),{key:1,class:"h-10 w-10 text-blue-700 stroke-2"}))],2),e("div",Dt,[e("h3",Rt,l(t.name),1),e("p",Et,l(t.count)+" words ",1)]),e("div",Kt,[i(r(H),{class:"h-6 w-6 text-slate-400 stroke-2 group-hover:text-slate-500 transition duration-150 ease-in-out"})])],8,qt))),128)),(o(!0),n(v,null,g(m.frequencyList,t=>(o(),n("div",{class:"duration-250 flex flex-row items-center rounded-xl bg-white py-4 px-2 mb-4 transition ease-in-out group cursor-pointer hover:bg-gray-50",key:t.list,onClick:f=>w(t.abbreviation)},[e("div",{class:_(["rounded-full w-12 h-12 p-2 flex items-center justify-center font-intro font-bold text-large",t.abbreviation=="basic"?"bg-emerald-300 text-emerald-700":t.abbreviation=="intermediate"?"bg-blue-300 text-blue-700":"bg-orange-300 text-orange-700"])},l(X(t.abbreviation)),3),e("div",Jt,[e("h3",Qt,l(t.list),1),e("p",Xt,l(t.count)+" words ",1)]),e("div",Yt,[i(r(H),{class:"h-6 w-6 text-slate-400 stroke-2 group-hover:text-slate-500 transition duration-150 ease-in-out"})])],8,Ot))),128)),(o(!0),n(v,null,g(r(S),t=>(o(),n("div",{class:"duration-250 flex flex-row items-center rounded-xl bg-white py-4 px-2 mb-4 transition ease-in-out group cursor-pointer hover:bg-gray-50",key:t.id,onClick:f=>w("group-"+t.id)},[s[13]||(s[13]=e("div",{class:"rounded-full w-12 h-12 p-2 flex items-center justify-center font-intro font-bold bg-slate-300 text-slate-700 text-sm"}," Group ",-1)),e("div",te,[e("h3",ee,l(t.name),1),e("p",se,l(t.count)+" words ",1)]),e("div",oe,[i(r(H),{class:"h-6 w-6 text-slate-400 stroke-2 group-hover:text-slate-500 transition duration-150 ease-in-out"})])],8,Zt))),128))]),e("div",{ref_key:"landmark",ref:V},null,512)])):T("",!0)]),e("div",re,[s[17]||(s[17]=e("h3",{class:"text-sm font-bold text-gray-700"},"Current List",-1)),m.items.length>0?(o(),n("div",ne,[i(rt,{"enter-active-class":"transition ease-out duration-250","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-250","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:L(()=>[(o(!0),n(v,null,g(m.items,(t,f)=>(o(),n("div",{key:f,class:_(["min-h-20 w-full rounded-xl shadow-md border border-gray-300 px-4 py-2",f>0?"mt-4":"mt-2"])},[t.sectionList?(o(),n("div",ae,[e("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.sectionList.image_name,class:"h-12 w-12"},null,8,le),e("div",ie,[e("h4",de,[M(l(t.sectionList.work)+" "+l(t.sectionList.l1)+" "+l(t.sectionList.book)+".",1),t.sectionList.start===t.sectionList.end?(o(),n("span",ce,l(t.sectionList.start),1)):(o(),n("span",ue,l(t.sectionList.start)+"-"+l(t.sectionList.end),1))]),e("p",me,l(t.word_count)+" words | "+l(t.core_count)+" core, "+l(t.word_count-t.core_count)+" not core ",1)]),e("div",pe,[i(r(E),{class:"duration-250 h-6 w-6 cursor-pointer text-gray-400 transition hover:text-red-600 stroke-2",onClick:tt=>P(f)},null,8,["onClick"])])])):T("",!0),t.vocabList?(o(),n("div",fe,[s[14]||(s[14]=e("div",{class:"hidden from-blue-500 to-blue-600 from-orange-500 to-orange-600 from-rose-500 to-rose-600 opacity-0"},null,-1)),t.vocabList.id=="star"?(o(),n("div",xe,[i(r(z),{class:"h-8 w-8 stroke-2"})])):t.vocabList.id=="learn"?(o(),n("div",be,[i(r(I),{class:"h-8 w-8 stroke-2"})])):(o(),n("div",{key:2,class:_(["flex h-12 w-12 items-center justify-center rounded-full text-sm font-bold p-2 font-intro",`bg-${t.vocabList.icon_color}-300 text-${t.vocabList.icon_color}-700`])},l(t.vocabList.abbreviation),3)),e("div",ge,[e("h4",ve,l(t.vocabList.name),1),e("p",_e,l(t.word_count)+" words | "+l(t.core_count)+" core, "+l(t.word_count-t.core_count)+" not core ",1)]),e("div",he,[i(r(E),{class:"duration-250 h-6 w-6 cursor-pointer text-gray-400 transition hover:text-red-600 stroke-2",onClick:tt=>P(f)},null,8,["onClick"])])])):T("",!0)],2))),128))]),_:1}),i(R,{class:"w-full mt-8",color:"blue",size:"md",onClick:s[7]||(s[7]=t=>Y())},{default:L(()=>[r(U)?(o(),n("span",ye,s[15]||(s[15]=[e("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",{class:"ml-2"},"Preparing ...",-1)]))):(o(),n("span",we,"Continue"))]),_:1})])):(o(),n("div",ke,s[16]||(s[16]=[e("div",{class:"relative mt-4 block h-20 w-full rounded-xl border-2 border-dashed border-gray-300 py-5 text-center"},[e("span",{class:"mt-2 block text-sm font-medium text-gray-600"}," No Sections Added ")],-1)])))])])])]),i(mt)]),e("aside",Le,[r(nt)().props.authenticated?(o(),n("div",Ce,[e("section",null,[s[19]||(s[19]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"}," Recommendations ",-1)),(o(!0),n(v,null,g(m.personalVocabLists,t=>(o(),n("div",{class:"mt-4 duration-250 flex flex-row items-center rounded-xl hover:bg-white py-4 px-2 mb-4 transition ease-in-out group cursor-pointer bg-gray-50",key:t.id,onClick:f=>w(t.id)},[e("div",{class:_(["rounded-full w-12 h-12 p-2 flex items-center justify-center",t.id=="star"?"bg-green-100 text-green-600 ":"bg-blue-100 text-blue-600 "])},[t.id=="star"?(o(),k(r(z),{key:0,class:"h-10 w-10 text-green-700 stroke-2"})):(o(),k(r(I),{key:1,class:"h-10 w-10 text-blue-700 stroke-2"}))],2),e("div",$e,[e("h3",Me,l(t.name),1),e("p",Ae,l(t.count)+" words ",1)]),e("div",He,[i(r(H),{class:"h-6 w-6 text-slate-400 stroke-2 group-hover:text-slate-500 transition duration-150 ease-in-out"})])],8,Te))),128))])])):(o(),n("div",Ve,[i(ct)]))])]),_:1})}}};export{ws as default};
