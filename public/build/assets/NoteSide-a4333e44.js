import{e as z,C as V,k as D,l as L,i as I,o as l,d as i,a as r,u as a,q as U,x as M,t as w,f as d,n as x,b as c,w as y,j as g,c as v,s as T,g as u,T as j,F as E,h as P}from"./app-f0078ddb.js";import{_ as b}from"./ButtonItem-718c0517.js";import{_ as S}from"./ToggleItem-94c3ab1e.js";import{r as A}from"./PlusCircleIcon-e71ff64a.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";const F={key:0},H={key:0,class:"mt-2"},R={class:"mb-2 text-xs font-medium text-red-600"},h={key:0,class:"w-full"},G=["innerHTML"],O={class:"mt-2 grid w-full grid-cols-1 gap-4"},Y={key:1,class:"flex w-full items-center justify-center text-sm font-semibold text-gray-500 transition duration-150 ease-in-out group-hover:text-gray-600"},J={class:"grid grid-cols-1 gap-2"},K={key:3,class:"mt-4"},Q={key:0,class:"mt-4"},W={class:"grid grid-cols-2 gap-2"},X={class:"mt-4 -ml-4 text-center"},de={__name:"NoteSide",props:{note:{type:String,required:!1},sectionId:{type:Number,required:!0},selectingText:{type:Boolean,required:!0},splitSections:{type:Boolean,required:!0},displaySyntax:{type:Boolean,required:!0},caseColor:{type:Boolean,required:!0},hasProse:{type:Boolean,required:!0},section:{type:Object,required:!0},verifiedSyntax:{type:Boolean,required:!0}},emits:["update:caseColor","update:displaySyntax","update:selectingText","update:splitSections"],setup(o,{emit:N}){const k=o;let s=z(!1);const n=V({id:k.sectionId,note:k.note}),p=N,q=D().props.user.case_colors,B=L(()=>[{id:1,name:"Nominative",color:"blue-600"},{id:5,name:"Ablative",color:"purple-600"},{id:2,name:"Genitive",color:"emerald-600"},{id:6,name:"Vocative",color:"fuchsia-600"},{id:3,name:"Dative",color:"orange-600"},{id:7,name:"Locative",color:"yellow-600"},{id:4,name:"Accusative",color:"pink-600"},{id:8,name:"Verbs",color:"gray-900"}].map(m=>{const e=q.find(C=>C.case_id===m.id);return{...m,color:e?e.color:m.color}})),f=(m,e)=>{switch(m){case"color":p("update:caseColor",e),localStorage.setItem("case-color",e);break;case"syntax":p("update:displaySyntax",e),localStorage.setItem("display-syntax",e);break;case"select":p("update:selectingText",e),localStorage.setItem("selecting-text",e);break;case"split":p("update:splitSections",e),localStorage.setItem("split-sections",e);break}};return(m,e)=>{const C=I("Link");return l(),i("div",null,[e[17]||(e[17]=r("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"},"Section Note",-1)),a(s)?(l(),i("div",F,[r("form",{onSubmit:e[3]||(e[3]=T(t=>{a(n).post("/api/submit-sectionNote",{preserveScroll:!0,onSuccess:()=>{g(s)?s.value=!1:s=!1}})},["prevent"]))},[U(r("textarea",{id:"note",name:"note",rows:"3","onUpdate:modelValue":e[0]||(e[0]=t=>a(n).note=t),class:"mt-4 block w-full rounded-lg border-0 py-1.5 font-intro font-medium text-gray-900 shadow-xs ring-1 ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 focus:ring-inset sm:text-sm sm:leading-6"},null,512),[[M,a(n).note]]),a(n).errors.note?(l(),i("div",H,[r("p",R,w(a(n).errors.note),1)])):d("",!0),r("div",{class:x(["mt-4 grid gap-4",o.note?"grid-cols-3":"grid-cols-2"])},[c(b,{size:"sm",color:"gray",onClick:e[1]||(e[1]=t=>{g(s)?s.value=!1:s=!1,a(n).note=k.note})},{default:y(()=>e[10]||(e[10]=[u(" Cancel ")])),_:1}),o.note?(l(),v(b,{key:0,size:"sm",color:"red",onClick:e[2]||(e[2]=T(t=>{a(n).delete(`/api/delete-sectionNote/${o.sectionId}`,{preserveScroll:!0,onSuccess:()=>{a(n).note="",g(s)?s.value=!1:s=!1}})},["prevent"]))},{default:y(()=>e[11]||(e[11]=[u(" Delete ")])),_:1})):d("",!0),c(b,{size:"sm",color:"blue",disabled:a(n).processing,type:"submit"},{default:y(()=>e[12]||(e[12]=[u(" Save Note ")])),_:1},8,["disabled"])],2)],32)])):(l(),i("div",{key:1,class:x(["group mt-4 flex min-h-16 cursor-pointer rounded-lg font-intro font-medium transition duration-150 ease-in-out",{"border-2 border-dashed border-gray-300 hover:border-gray-400":!o.note}]),onClick:e[5]||(e[5]=t=>g(s)?s.value=!0:s=!0)},[o.note?(l(),i("div",h,[r("p",{class:"items-start px-3 py-1.5 text-left text-gray-600 sm:text-sm sm:leading-6",innerHTML:o.note},null,8,G),r("div",O,[c(b,{class:"mt-2 w-full",size:"sm",color:"gray",onClick:e[4]||(e[4]=t=>g(s)?s.value=!0:s=!0)},{default:y(()=>e[13]||(e[13]=[u(" Edit this Note ")])),_:1})])])):(l(),i("div",Y,[c(a(A),{class:"mx-2 inline h-6 w-6 stroke-2 text-gray-400"}),e[14]||(e[14]=u(" Click to add a personal note on this section. "))]))],2)),e[18]||(e[18]=r("h3",{id:"reading-toolkit",class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Reading Toolkit ",-1)),r("div",J,[c(S,{class:"mt-4",label:"Selecting Text",enabled:o.selectingText,description:"Toggle vocabulary and text selection.","onUpdate:toggle":e[6]||(e[6]=t=>f("select",t))},null,8,["enabled"]),o.hasProse?(l(),v(S,{key:0,class:"mt-4",label:"Split Sections",enabled:o.splitSections,description:"Split sections by line.","onUpdate:toggle":e[7]||(e[7]=t=>f("split",t))},null,8,["enabled"])):d("",!0),o.verifiedSyntax?(l(),v(S,{key:1,class:"mt-4",label:"Display Syntax",enabled:o.displaySyntax,description:"Display syntax in vocablary card.","onUpdate:toggle":e[8]||(e[8]=t=>f("syntax",t))},null,8,["enabled"])):d("",!0),o.verifiedSyntax?(l(),v(S,{key:2,class:"mt-4",label:"Case Colorize",enabled:o.caseColor,"text-color":"iris",description:"Show noun case by color.","onUpdate:toggle":e[9]||(e[9]=t=>f("color",t))},null,8,["enabled"])):d("",!0),o.verifiedSyntax?d("",!0):(l(),i("div",K,e[15]||(e[15]=[r("p",{class:"text-sm text-gray-600"}," Syntax has not yet been verified for this section. Please check back later. ",-1)]))),c(j,{"enter-active-class":"transition ease-in-out duration-300","enter-from-class":"opacity-0 transform scale-y-0 -translate-y-1/2","enter-to-class":"opacity-100 transform scale-y-100 translate-y-0","leave-active-class":"transition ease-in-out duration-300","leave-from-class":"opacity-100 transform scale-y-100 translate-y-0","leave-to-class":"opacity-0 transform scale-y-0 -translate-y-1/2"},{default:y(()=>[o.caseColor&&o.verifiedSyntax?(l(),i("div",Q,[r("div",W,[(l(!0),i(E,null,P(B.value,(t,$)=>(l(),i("div",{key:$,class:x(["ml-4 flex items-center text-sm font-semibold",`text-${t.color}`])},[r("span",{class:x(["mr-2 h-5 w-5 rounded-full",`bg-${t.color}`])},null,2),u(" "+w(t.name),1)],2))),128))]),r("div",X,[c(C,{class:"text-sm font-medium text-blue-600 hover:text-blue-800",href:"/user/profile#case-colors"},{default:y(()=>e[16]||(e[16]=[u(" Customize Your Case Colors ")])),_:1})])])):d("",!0)]),_:1})])])}}};export{de as default};
