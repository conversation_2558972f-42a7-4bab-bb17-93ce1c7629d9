import{e as g,l as p,p as B,A as V,o as s,d as n,n as i,s as d,c as M,w as T,u as v,a as f,g as A,t as b}from"./app-f0078ddb.js";import{D as L}from"./index-b0adb136.js";/* empty css            */const N={key:0},P={key:1},j={class:"flex flex-wrap"},q=["onClick"],z=["onClick"],U={__name:"SelectDate",props:{team:Object,publish:{type:Boolean,default:!1},color:{type:String,default:"primary"},defaultDate:{type:String,required:!1}},emits:["update:selected"],setup(h,{emit:x}){const o=h,e=g(null);let t=g("");const D=x,u=p(()=>({indigo:"text-indigo-600 hover:text-indigo-700",orange:"text-orange-600 hover:text-orange-700",emerald:"text-emerald-600 hover:text-emerald-700"})[o.color]),k=p(()=>({indigo:"bg-indigo-100 hover:bg-indigo-200 text-indigo-600 focus:border-indigo-600",orange:"bg-orange-100 hover:bg-orange-200 text-orange-600 focus:border-orange-600",emerald:"bg-emerald-100 hover:bg-emerald-200 text-emerald-600 focus:border-emerald-600"})[o.color]||"bg-indigo-100 hover:bg-indigo-200 text-indigo-600 focus:border-indigo-600");B(e,a=>{D("update:selected",a)});const c=()=>{t.value=new Date,o.team&&!o.publish&&(e.value=new Date(o.team.due_at));let a=0;if(!o.publish){switch(t.value.getDay()){case 5:a=3;break;case 6:a=2;break;default:a=1}t.value.setDate(t.value.getDate()+a),o.publish||(t.value.setHours(23),t.value.setMinutes(59),t.value.setSeconds(59))}};V(()=>{o.defaultDate?(t.value=new Date(o.defaultDate),e.value=t.value):c()});const w=()=>{e.value=t.value},_=a=>{e.value=null,a(),c()},y=(a,l,r)=>{e.value=l,r({ref:a.target})};return(a,l)=>e.value?(s(),M(v(L),{key:1,modelValue:e.value,"onUpdate:modelValue":l[0]||(l[0]=r=>e.value=r),mode:"dateTime","min-date":new Date,class:"inline"},{default:T(({inputValue:r,togglePopover:C,hidePopover:S})=>[f("div",j,[(s(),n("button",{key:v(t).getTime(),ref:"button",class:i(["flex items-center text-sm font-semibold h-8 px-2 m-1 rounded-lg border-2 border-transparent focus:outline-hidden",k.value]),onClick:d(m=>y(m,e.value,C),["stop"])},[A(b(e.value.toLocaleDateString())+" at "+b(e.value.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))+" ",1),(s(),n("svg",{class:i(["w-4 h-4 ml-1 -mr-1",u.value]),viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2",onClick:d(m=>_(S),["stop"])},l[1]||(l[1]=[f("path",{d:"M6 18L18 6M6 6l12 12"},null,-1)]),10,z))],10,q))])]),_:1},8,["modelValue","min-date"])):(s(),n("button",{key:0,class:i(["text-sm font-semibold px-2 h-8 focus:outline-hidden transition duration-150",u.value]),onClick:d(w,["stop"])},[o.publish?(s(),n("span",N,"Publish Now")):(s(),n("span",P,"Add Due Date"))],2))}};export{U as default};
