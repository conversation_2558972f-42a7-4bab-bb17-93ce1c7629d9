import{o as l,d as u,a as i,e as L,k as S,l as M,p as ve,C as pe,i as te,c as U,w as x,b as c,Q as xe,u as s,f as g,j as m,t as v,g as k,s as R,q as se,x as oe,n as A,aB as ge}from"./app-f0078ddb.js";import{d as be,_ as ke}from"./AppLayout-33f062bc.js";import{_ as z}from"./ButtonItem-718c0517.js";import{_ as ye}from"./Breadcrumbs-c96e9207.js";import he from"./VocabularyDisplay-209b83d3.js";import we from"./TextMain-d07bc842.js";import ie from"./TextSide-2c0cf7dd.js";import Se from"./VocabMain-7d78e629.js";import ne from"./VocabSide-00546f24.js";import Ce from"./NoteMain-611ac2a0.js";import re from"./NoteSide-a4333e44.js";import Ne from"./TranslationMain-3d4e46eb.js";import Ue from"./TabComponent-c1a19bfd.js";import{_ as $e}from"./MobileSidebar-5e21b4cd.js";import{r as Z}from"./removePunctuation-702d8a66.js";import{p as G}from"./pluralize-d25a928b.js";import Be from"./VocabularyDisplayMobile-88f99eb0.js";import{_ as je}from"./Footer-0988dcd8.js";import{_ as Le}from"./CopyToClipboard-21badf5d.js";import{r as ze}from"./InformationCircleIcon-716f3ffb.js";import{r as le}from"./ChatBubbleBottomCenterTextIcon-4924c50e.js";import{r as ae}from"./PencilSquareIcon-048eb348.js";import{r as Te}from"./PlayCircleIcon-8bd12a30.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./StarIcon-155a2a28.js";import"./ArrowRightOnRectangleIcon-b404433d.js";import"./CheckIcon-6a201aa1.js";import"./RenderToken-2b0a5644.js";import"./ToggleItem-94c3ab1e.js";import"./label-6c8c1cbc.js";import"./Promotion-3eee0057.js";import"./WordItem-d0f526f8.js";import"./clipboard-a66b13b3.js";import"./NoteItem-3ad59666.js";import"./lodash-631955d9.js";import"./PlusCircleIcon-e71ff64a.js";function de(t,r){return l(),u("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})])}function De(t,r){return l(),u("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"})])}function ce(t,r){return l(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[i("path",{"fill-rule":"evenodd",d:"M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z","clip-rule":"evenodd"})])}function ue(t,r){return l(),u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[i("path",{"fill-rule":"evenodd",d:"M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}function Oe(t,r,f,p){function b(a,j){var C=Number(a[2])+1;if(j.length==C)return!1;for(;C<j.length;)return Z(j[C])+"["+a[1]+"]["+C+"]";return!1}function y(a,j){var C=Number(a[2])-1;if(C==-1)return!1;for(;C>-1;)return Z(j[C])+"["+a[1]+"]["+C+"]";return!1}function T(a,j){for(var C=0;C<a.length;)return Z(a[C])+"["+(Number(j)+1)+"]["+C+"]";return!1}function D(a,j){for(var C=a.length-1;C>-1;)return Z(a[C])+"["+(Number(j)-1)+"]["+C+"]";return!1}function B(a){return a.split(" ")}var w;if(r){var $=r.replace(/]/g,"").split("[");w=f.find(a=>a.id==$[1]).text.split(" ");var h;switch(t.keyCode){case 39:if(b($,w))return h=b($,w),[h,p.find(a=>a.token==h.replace(/]/g,"").split("[")[0]&&a.verse_id==h.replace(/]/g,"").split("[")[1]&&a.line_order==h.replace(/]/g,"").split("[")[2])];if(f.find(a=>a.id==Number($[1])+1))return w=B(f.find(a=>a.id==Number($[1])+1).text),h=T(w,$[1]),[h,p.find(a=>a.token==h.replace(/]/g,"").split("[")[0]&&a.verse_id==h.replace(/]/g,"").split("[")[1]&&a.line_order==h.replace(/]/g,"").split("[")[2])];break;case 37:if(y($,w))return h=y($,w),[h,p.find(a=>a.token==h.replace(/]/g,"").split("[")[0]&&a.verse_id==h.replace(/]/g,"").split("[")[1]&&a.line_order==h.replace(/]/g,"").split("[")[2])];if(f.find(a=>a.id==Number($[1])-1))return w=B(f.find(a=>a.id==Number($[1])-1).text),h=D(w,$[1]),[h,p.find(a=>a.token==h.replace(/]/g,"").split("[")[0]&&a.verse_id==h.replace(/]/g,"").split("[")[1]&&a.line_order==h.replace(/]/g,"").split("[")[2])]}}else if(t.keyCode==39)return w=B(f.find(a=>a.id==p[0].verse_id).text),[T(w,f[0].id-1),p[0]]}const Pe={class:"z-0 pb-16 lg:z-20 lg:pr-96 2xl:pr-[32rem]"},Ie={class:"px-4 py-8 sm:px-8"},Ve={class:"flex flex-row items-center justify-between"},Ee={class:"mt-8 flex flex-col font-intro lg:hidden"},Me={class:"flex flex-col sm:flex-row"},Ge=["src"],Ae={class:"ml-4 grow items-center"},He={class:"mt-2 text-center text-4xl font-bold text-gray-900 sm:text-left"},Je={class:"text-center text-xl font-semibold text-gray-600 sm:text-left"},Re={key:0},Ze={key:1},qe={key:0},Ke={key:1},Fe={class:"mt-4"},Qe={key:0,class:"font-semibold text-gray-600"},Xe={class:"text-center sm:text-left"},Ye={key:1,class:"block w-full resize-none px-3 py-1.5 font-normal font-semibold text-gray-600 sm:text-sm sm:leading-6"},We={class:"mt-2 py-1.5 text-xs text-gray-500"},_e={key:2,class:"font-intro font-semibold text-gray-600"},et={key:1},tt={class:"mt-2"},st={key:0},ot={class:"mb-2 text-xs font-medium text-red-600"},it={class:"mt-4 grid grid-cols-2 font-sans"},nt={key:0,class:"flex"},rt={key:0,class:"mt-8"},lt=["src"],at=["src"],dt={class:"mt-8"},ct={class:"mt-8 mb-8"},ut={class:"mt-4 grid grid-cols-1 gap-4"},ft={class:"flex items-center justify-center transition duration-150"},mt={class:"h-10 w-10 rounded-full bg-orange-100 p-2 transition duration-150"},vt={class:"ml-4 flex grow flex-col justify-start"},pt={class:"font-intro text-sm font-medium text-orange-700"},xt={class:"flex items-center justify-center transition duration-150"},gt={class:"h-10 w-10 rounded-full bg-purple-100 p-2 transition duration-150"},bt={class:"ml-4 flex grow flex-col justify-start"},kt={class:"font-intro text-sm font-medium text-purple-700"},yt={class:"hidden bg-slate-50 p-8 focus:outline-hidden lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},ht={class:"grid grid-cols-2 gap-4 text-sm font-medium text-gray-500"},wt={key:0,class:"flex"},St={class:"mt-8 flex flex-col font-intro"},Ct={class:"flex flex-row items-center"},Nt=["src"],Ut={class:"ml-4 flex grow flex-col"},$t={class:"text-left text-3xl font-bold text-gray-900"},Bt={class:"-mt-1 text-left text-lg font-semibold text-gray-600"},jt={key:0},Lt={key:1},zt={key:0},Tt={key:1},Dt={class:"mt-4"},Ot={key:0,class:"font-semibold text-gray-600"},Pt={class:""},It={key:1,class:"block w-full resize-none px-3 py-1.5 font-normal font-semibold text-gray-600 sm:text-sm sm:leading-6"},Vt={class:"mt-2 py-1.5 text-xs text-gray-500"},Et={key:2,class:"font-intro font-semibold text-gray-600"},Mt={key:1},Gt={class:"mt-2"},At={key:0},Ht={class:"mb-2 text-xs font-medium text-red-600"},Jt={class:"mt-8"},Rt={class:"mt-4 grid grid-cols-1 gap-4"},Zt={class:"flex items-center justify-center transition duration-150"},qt={class:"h-10 w-10 rounded-full bg-orange-100 p-2 transition duration-150"},Kt={class:"ml-4 flex grow flex-col justify-start"},Ft={class:"font-intro text-sm font-medium text-orange-700"},Qt={class:"flex items-center justify-center transition duration-150"},Xt={class:"h-10 w-10 rounded-full bg-purple-100 p-2 transition duration-150"},Yt={class:"ml-4 flex grow flex-col justify-start"},Wt={class:"font-intro text-sm font-medium text-purple-700"},_t={class:"mt-8"},ao={__name:"Show",props:{section:Object,previousSection:Object,nextSection:Object,tokens:Array,wordsLearned:Array,wordsStarred:Array,vocab:Array,tab:[Array,Object],filters:Array,sectionDescription:String,sectionNote:String,actionButtons:Object,verses:Array,assignment:Object},setup(t){const r=t;let f=L(!1),p=L(!1),b=L(!1),y=L(!1),T=L(""),D=L(!1),B=L({}),w=L(!1),$=L(!1);localStorage.getItem("case-color")&&(r.section.syntax_verified===1&&S().props.authenticated?f.value=JSON.parse(localStorage.getItem("case-color")):f.value=!1),localStorage.getItem("display-syntax")&&(r.section.syntax_verified===1&&S().props.authenticated?p.value=JSON.parse(localStorage.getItem("display-syntax")):p.value=!1),S().props.authenticated?localStorage.getItem("selecting-text")&&(b.value=JSON.parse(localStorage.getItem("selecting-text"))):b.value=!0,localStorage.getItem("split-sections")&&(y.value=JSON.parse(localStorage.getItem("split-sections"))),document.addEventListener("keydown",n=>{const e=document.activeElement;if(!(e.tagName==="TEXTAREA"||e.isContentEditable)&&!$.value){var V=Oe(n,T.value,r.section.verses,r.tokens);typeof V<"u"&&(n.preventDefault(),[T.value,B.value]=V,D.value=!0)}});const h=M(()=>r.section.chapter?r.section.book.work.name+" "+r.section.book.work.l1+" "+r.section.book.book+" "+r.section.book.work.l2+" "+r.section.chapter:r.section.book.work.name+" "+r.section.book.work.l1+" "+r.section.book.book+" Lines "+r.section.line_start+"-"+r.section.line_end),a=M(()=>r.section.chapter?r.section.book_id+":"+r.section.chapter+":"+r.section.chapter:r.section.book_id+":"+r.section.line_start+":"+r.section.line_end),j=M(()=>!(r.nextSection&&r.nextSection.book==r.section.book.url||r.previousSection&&r.previousSection.book==r.section.book.url)),C=M(()=>{let n=[{name:"Read",href:"/read",current:!1},{name:r.section.book.work.name,href:"/read/"+r.section.book.work.author.url+"/"+r.section.book.work.url,current:!1},{name:r.section.book.url=="preface"?"Preface":r.section.book.work.l1+" "+r.section.book.book,href:"/read/"+r.section.book.work.author.url+"/"+r.section.book.work.url+"/"+r.section.book.work.l1.toLowerCase()+"-"+r.section.book.book,current:!1}];return j.value?n:r.section.chapter?[...n,{name:r.section.book.work.l2+" "+r.section.chapter,href:"#",current:!0}]:[...n,{name:r.section.book.work.l4+"s "+r.section.line_start+"-"+r.section.line_end,href:"#",current:!0}]}),Q=M(()=>{var n="";return r.vocab.forEach(function(e){var V="";e.gender&&(V=", "+e.gender),n=n+e.latin+V+"	"+e.definition+`\r
`}),n}),E=M(()=>r.section.syntax_verified===1),H=n=>n.chapter?route("read.section-chapter",{author:n.book.work.author.url,work:n.book.work.url,book:n.book.url,chapter:n.book.work.l2.toLowerCase()+"-"+n.chapter,line_start:n.line_start,line_end:n.line_end}):route("read.section",{author:n.book.work.author.url,work:n.book.work.url,book:n.book.url,line_start:n.line_start,line_end:n.line_end}),J=n=>n.chapter?route("read.section-chapter",{author:n.author,work:n.work,book:n.book,chapter:n.chapter,line_start:n.line_start,line_end:n.line_end,tab:r.tab.tab}):route("read.section",{author:n.author,work:n.work,book:n.book,line_start:n.line_start,line_end:n.line_end,tab:r.tab.tab}),O=()=>r.section.verses[0].prose==1,q=n=>{n.token.length>0?(T.value=n.token+"["+n.verse+"]["+n.index+"]",B.value=r.tokens.find(e=>e.token===n.token&&e.verse_id===n.verse&&e.line_order===n.index),D.value=!0):K()};let P=r.wordsLearned?L(r.wordsLearned):null,I=r.wordsStarred?L(r.wordsStarred):null;const X=n=>!n||!n.word_id?!1:P&&P.value?P.value.includes(n.word_id):!1,Y=n=>!n||!n.word_id?!1:I&&I.value?I.value.includes(n.word_id):!1,K=()=>{D.value=!1,T.value=""};ve(B,be(n=>{n&&axios.post("/api/add-word-user",{word_id:n.word_id,token_id:n.id})},1e3));let d=L(!1);const N=pe({id:r.section.id,description:r.sectionDescription}),W=n=>{axios.post("/words/"+n+"/learn").then(e=>{P.value.indexOf(n)>-1?P.value.splice(P.value.indexOf(n),1):P.value.push(n)})},_=n=>{axios.post("/words/"+n+"/star").then(e=>{I.value.indexOf(n)>-1?I.value.splice(I.value.indexOf(n),1):I.value.push(n)})},fe=["KB9rUZg2","ABIGts7p","cMAdiNGB","IRq9GwEY"];let ee=L(!1);const me=()=>{ee.value=!0,setTimeout(()=>{ee.value=!1},1e3)};return(n,e)=>{const V=te("Head"),F=te("Link");return l(),U(ke,null,{default:x(()=>[c(V,{title:h.value},null,8,["title"]),(l(),U(xe,{to:"body"},[s(S)().props.authenticated?(l(),U(he,{key:0,class:"z-50 hidden lg:block",word:s(B),toggle:s(D),"display-syntax":s(p),"verified-syntax":E.value,"is-learned":X(s(B)),"is-starred":Y(s(B)),"onUpdate:learnedWords":e[0]||(e[0]=o=>W(o)),"onUpdate:starredWords":e[1]||(e[1]=o=>_(o)),onCloseModal:e[2]||(e[2]=o=>K())},null,8,["word","toggle","display-syntax","verified-syntax","is-learned","is-starred"])):g("",!0),s(S)().props.authenticated?(l(),U(Be,{key:1,class:"z-50 lg:hidden",word:s(B),toggle:s(D),"display-syntax":s(p),"verified-syntax":E.value,"is-learned":X(s(B)),"is-starred":Y(s(B)),"onUpdate:learnedWords":e[3]||(e[3]=o=>W(o)),"onUpdate:starredWords":e[4]||(e[4]=o=>_(o)),onCloseModal:e[5]||(e[5]=o=>K())},null,8,["word","toggle","display-syntax","verified-syntax","is-learned","is-starred"])):g("",!0)])),i("main",Pe,[i("div",Ie,[i("div",Ve,[c(ye,{class:"lg:col-span-9 xl:grid-cols-10",pages:C.value},null,8,["pages"]),c(s(ze),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:e[6]||(e[6]=o=>m(w)?w.value=!s(w):w=!s(w))})]),i("div",Ee,[i("div",Me,[i("img",{class:"mx-auto h-36 w-36 rounded-full sm:h-20 sm:w-20",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.section.book.work.image_name,alt:""},null,8,Ge),i("div",Ae,[i("h1",He,v(t.section.book.work.name),1),i("h3",Je,[t.section.book.url=="preface"?(l(),u("span",Re,v(t.section.title),1)):(l(),u("span",Ze,[k(v(t.section.book.work.l1)+" "+v(t.section.book.book)+" ",1),t.section.chapter?(l(),u("span",qe,v(t.section.book.work.l2)+" "+v(t.section.chapter),1)):j.value?g("",!0):(l(),u("span",Ke,v(t.section.book.work.l4)+"s "+v(t.section.line_start)+"-"+v(t.section.line_end),1))]))])])]),i("div",Fe,[t.section.description?(l(),u("div",Qe,[i("p",Xe,v(t.section.description),1)])):t.sectionDescription&&!s(d)?(l(),u("div",Ye,[i("p",null,v(t.sectionDescription),1),i("p",We,[e[41]||(e[41]=k(" This description has been suggested by you. ")),i("span",{class:"cursor-pointer text-gray-500 transition duration-150 ease-in-out hover:text-blue-600 hover:underline",onClick:e[7]||(e[7]=o=>m(d)?d.value=!0:d=!0)},"Click to edit.")])])):(l(),u("div",_e,[s(d)?(l(),u("div",et,[i("form",{onSubmit:e[12]||(e[12]=R(o=>{s(N).post("/api/submit-description",{preserveScroll:!0,onSuccess:()=>{m(d)?d.value=!1:d=!1}})},["prevent"]))},[se(i("textarea",{id:"description",name:"description",rows:"3","onUpdate:modelValue":e[9]||(e[9]=o=>s(N).description=o),placeholder:"Suggest a description for this section.",class:"block w-full resize-none rounded-md border-0 py-1.5 font-normal font-semibold text-gray-500 ring-1 shadow-xs ring-gray-300 ring-inset placeholder:text-gray-400 hover:text-gray-600 focus:ring-2 focus:ring-gray-400 focus:ring-inset sm:text-sm sm:leading-6"},null,512),[[oe,s(N).description]]),i("div",tt,[s(N).errors.description?(l(),u("div",st,[i("p",ot,v(s(N).errors.description),1)])):g("",!0),e[43]||(e[43]=i("p",{class:"text-xs font-medium text-gray-500"}," Try to keep your description in the present tense and avoid any major plot spoilers, while keeping it short and sweet. ",-1))]),i("div",{class:A(["mt-2 grid gap-4",t.sectionDescription?"grid-cols-3":"grid-cols-2"])},[c(z,{size:"sm",color:"gray",onClick:e[10]||(e[10]=o=>{m(d)?d.value=!1:d=!1,s(N).description=""})},{default:x(()=>e[44]||(e[44]=[k(" Cancel ")])),_:1}),t.sectionDescription?(l(),U(z,{key:0,size:"sm",color:"red",onClick:e[11]||(e[11]=R(o=>{s(N).delete(`/api/delete-description/${t.section.id}`,{preserveScroll:!0,onSuccess:()=>{s(N).description="",m(d)?d.value=!1:d=!1}})},["prevent"]))},{default:x(()=>e[45]||(e[45]=[k(" Delete ")])),_:1})):g("",!0),c(z,{size:"sm",color:"blue",type:"submit",disabled:s(N).processing},{default:x(()=>e[46]||(e[46]=[k(" Suggest ")])),_:1},8,["disabled"])],2)],32)])):(l(),u("div",{key:0,class:"flex h-20 cursor-pointer flex-row items-center rounded-lg border-2 border-dashed border-gray-300 px-3 py-1.5 transition duration-150 ease-in-out hover:border-gray-400",onClick:e[8]||(e[8]=o=>m(d)?d.value=!0:d=!0)},[c(s(de),{class:"mx-auto mr-4 h-8 w-8 text-gray-400","aria-hidden":"true"}),e[42]||(e[42]=i("div",{class:"flex grow flex-col justify-start"},[i("h3",{class:"text-sm text-gray-900"},"No description"),i("p",{class:"mt-1 text-sm text-gray-500"}," Click to suggest a description for this section. ")],-1))]))]))]),i("div",it,[t.previousSection?(l(),u("div",nt,[c(z,{color:"white",size:"xs",class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",link:J(t.previousSection)},{default:x(()=>[c(s(ce),{class:"mr-1 inline h-5 w-5 shrink-0","aria-hidden":"true"}),e[47]||(e[47]=k(" Previous Section "))]),_:1},8,["link"])])):g("",!0),t.nextSection?(l(),u("div",{key:1,class:A(["flex justify-end",{"col-span-2":!t.previousSection}])},[c(z,{color:"white",size:"xs",class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",link:J(t.nextSection)},{default:x(()=>[e[48]||(e[48]=k(" Next Section ")),c(s(ue),{class:"ml-1 inline h-5 w-5 shrink-0","aria-hidden":"true"})]),_:1},8,["link"])],2)):g("",!0)])]),t.section.video?(l(),u("div",rt,[s(S)().props.authenticated&&t.section.video.vimeo_id&&!s(S)().props.user.classes.filter(o=>fe.includes(o.slug)).length>0?(l(),u("iframe",{key:0,src:"https://player.vimeo.com/video/"+t.section.video.vimeo_id,frameborder:"0",allow:"autoplay; fullscreen; picture-in-picture",allowfullscreen:"",class:"aspect-video w-full rounded-2xl"},null,8,lt)):t.section.video.youtube_id?(l(),u("iframe",{key:1,class:"aspect-video w-full rounded-2xl",src:"https://www.youtube.com/embed/"+t.section.video.youtube_id},null,8,at)):g("",!0)])):g("",!0),i("div",dt,[c(Ue,{"current-url":H(t.section),"current-tab":t.tab.tab,authenticated:s(S)().props.authenticated},ge({tab1:x(()=>[e[49]||(e[49]=k(" Text "))]),content1:x(()=>[c(we,{verses:t.verses,tokens:t.tokens,highlight:s(T),"case-color":s(f),"selecting-text":s(b),"show-line-numbers":!0,"has-prose":O(),"split-sections":s(y),"onUpdate:highlighted":e[13]||(e[13]=o=>q(o))},null,8,["verses","tokens","highlight","case-color","selecting-text","has-prose","split-sections"])]),tab2:x(()=>[e[50]||(e[50]=k(" Vocabulary "))]),content2:x(()=>[c(Se,{vocab:t.vocab,learned:t.wordsLearned,starred:t.wordsStarred,authenticated:s(S)().props.authenticated},null,8,["vocab","learned","starred","authenticated"])]),_:2},[s(S)().props.authenticated?{name:"tab3",fn:x(()=>[e[51]||(e[51]=k(" Notes "))]),key:"0"}:void 0,s(S)().props.authenticated?{name:"content3",fn:x(()=>[c(Ce,{verses:t.verses,tokens:t.tokens,highlight:s(T),"case-color":s(f),"selecting-text":s(b),"has-prose":O(),"section-id":t.section.id,"verse-name":t.section.book.work.l4,"onUpdate:highlighted":e[14]||(e[14]=o=>q(o)),"onUpdate:isEditing":e[15]||(e[15]=o=>m($)?$.value=o:$=o)},null,8,["verses","tokens","highlight","case-color","selecting-text","has-prose","section-id","verse-name"])]),key:"1"}:void 0,s(S)().props.authenticated?{name:"tab4",fn:x(()=>[e[52]||(e[52]=k(" Translation "))]),key:"2"}:void 0,s(S)().props.authenticated?{name:"content4",fn:x(()=>[c(Ne,{verses:t.verses,tokens:t.tokens,highlight:s(T),"case-color":s(f),"selecting-text":s(b),"has-prose":O(),"split-sections":s(y),"section-id":t.section.id,"onUpdate:highlighted":e[16]||(e[16]=o=>q(o))},null,8,["verses","tokens","highlight","case-color","selecting-text","has-prose","split-sections","section-id"])]),key:"3"}:void 0]),1032,["current-url","current-tab","authenticated"])])]),c(je)]),c($e,{class:"lg:hidden",show:s(w),onClose:e[25]||(e[25]=o=>m(w)?w.value=!1:w=!1)},{default:x(()=>[i("div",ct,[e[57]||(e[57]=i("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"},"Practice",-1)),i("div",ut,[i("div",ft,[i("div",mt,[c(s(le),{class:"h-6 w-6 text-orange-700"})]),i("div",vt,[e[53]||(e[53]=i("h2",{class:"font-intro text-xl font-semibold text-orange-700"}," Vocabulary ",-1)),i("h5",pt,v(t.actionButtons.vocab_count+" "+s(G)("word",t.actionButtons.vocab_count)),1)]),t.actionButtons.vocab_count>0?(l(),U(z,{key:0,size:"md",color:"lightOrange",class:"ml-4 w-32",link:`/practice/vocabulary/attempt?sections[]=${a.value}`},{default:x(()=>e[54]||(e[54]=[k("Practice")])),_:1},8,["link"])):g("",!0)]),i("div",xt,[i("div",gt,[c(s(ae),{class:"h-6 w-6 text-purple-700"})]),i("div",bt,[e[55]||(e[55]=i("h2",{class:"font-intro text-xl font-semibold text-purple-700"}," Grammar ",-1)),i("h5",kt,v(t.actionButtons.noun_verb_count+" "+s(G)("noun",t.actionButtons.noun_verb_count)+" and "+s(G)("verb",t.actionButtons.noun_verb_count)),1)]),t.actionButtons.noun_verb_count>0?(l(),U(z,{key:0,size:"md",color:"lightPurple",class:"ml-4 w-32",link:`/practice/grammar/c/attempt?sections[]=${a.value}`},{default:x(()=>e[56]||(e[56]=[k("Practice")])),_:1},8,["link"])):g("",!0)])])]),t.tab.tab==null?(l(),U(ie,{key:0,"selecting-text":s(b),"split-sections":s(y),"display-syntax":s(p),"case-color":s(f),"has-prose":O(),section:t.section,"verified-syntax":E.value,subscribed:s(S)().props.subscribed,"onUpdate:caseColor":e[17]||(e[17]=o=>m(f)?f.value=o:f=o),"onUpdate:displaySyntax":e[18]||(e[18]=o=>m(p)?p.value=o:p=o),"onUpdate:selectingText":e[19]||(e[19]=o=>m(b)?b.value=o:b=o),"onUpdate:splitSections":e[20]||(e[20]=o=>m(y)?y.value=o:y=o)},null,8,["selecting-text","split-sections","display-syntax","case-color","has-prose","section","verified-syntax","subscribed"])):g("",!0),t.tab.tab=="vocab"?(l(),U(ne,{key:1,"current-url":H(t.section),filters:t.filters,"clipboard-list":Q.value,subscribed:s(S)().props.subscribed},null,8,["current-url","filters","clipboard-list","subscribed"])):g("",!0),t.tab.tab=="notes"&&s(S)().props.subscribed?(l(),U(re,{key:2,note:t.sectionNote,"section-id":t.section.id,"selecting-text":s(b),"split-sections":s(y),"display-syntax":s(p),"case-color":s(f),"has-prose":O(),"verified-syntax":E.value,section:t.section,"onUpdate:caseColor":e[21]||(e[21]=o=>m(f)?f.value=o:f=o),"onUpdate:displaySyntax":e[22]||(e[22]=o=>m(p)?p.value=o:p=o),"onUpdate:selectingText":e[23]||(e[23]=o=>m(b)?b.value=o:b=o),"onUpdate:splitSections":e[24]||(e[24]=o=>m(y)?y.value=o:y=o)},null,8,["note","section-id","selecting-text","split-sections","display-syntax","case-color","has-prose","verified-syntax","section"])):g("",!0)]),_:1},8,["show"]),i("aside",yt,[i("div",ht,[t.previousSection?(l(),u("div",wt,[c(F,{class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",href:J(t.previousSection)},{default:x(()=>[c(s(ce),{class:"mr-1 inline h-5 w-5 shrink-0","aria-hidden":"true"}),e[58]||(e[58]=k(" Previous "))]),_:1},8,["href"])])):g("",!0),t.nextSection?(l(),u("div",{key:1,class:A(["flex justify-end",{"col-span-2":!t.previousSection}])},[c(F,{class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",href:J(t.nextSection)},{default:x(()=>[e[59]||(e[59]=k(" Next ")),c(s(ue),{class:"ml-1 inline h-5 w-5 shrink-0","aria-hidden":"true"})]),_:1},8,["href"])],2)):g("",!0)]),i("div",St,[i("div",Ct,[i("img",{class:"mx-auto h-20 w-20 rounded-full",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.section.book.work.image_name,alt:""},null,8,Nt),i("div",Ut,[i("h1",$t,v(t.section.book.work.name),1),i("h3",Bt,[t.section.book.url=="preface"?(l(),u("span",jt,v(t.section.title),1)):(l(),u("span",Lt,[k(v(t.section.book.work.l1)+" "+v(t.section.book.book)+" ",1),t.section.chapter?(l(),u("span",zt,v(t.section.book.work.l2)+" "+v(t.section.chapter),1)):j.value?g("",!0):(l(),u("span",Tt,v(t.section.book.work.l4)+"s "+v(t.section.line_start)+"-"+v(t.section.line_end),1))]))])])]),i("div",Dt,[t.section.description?(l(),u("div",Ot,[i("p",Pt,v(t.section.description),1)])):t.sectionDescription&&!s(d)?(l(),u("div",It,[i("p",null,v(t.sectionDescription),1),i("p",Vt,[e[60]||(e[60]=k(" This description has been suggested by you. ")),i("span",{class:"cursor-pointer text-gray-500 transition duration-150 ease-in-out hover:text-blue-600 hover:underline",onClick:e[26]||(e[26]=o=>m(d)?d.value=!0:d=!0)},"Click to edit.")])])):(l(),u("div",Et,[s(d)?(l(),u("div",Mt,[i("form",{onSubmit:e[31]||(e[31]=R(o=>{s(N).post("/api/submit-description",{preserveScroll:!0,onSuccess:()=>{m(d)?d.value=!1:d=!1}})},["prevent"]))},[se(i("textarea",{id:"description",name:"description",rows:"3","onUpdate:modelValue":e[28]||(e[28]=o=>s(N).description=o),placeholder:"Suggest a description for this section.",class:"block w-full resize-none rounded-md border-0 py-1.5 font-normal font-semibold text-gray-500 ring-1 shadow-xs ring-gray-300 ring-inset placeholder:text-gray-400 hover:text-gray-600 focus:ring-2 focus:ring-gray-400 focus:ring-inset sm:text-sm sm:leading-6"},null,512),[[oe,s(N).description]]),i("div",Gt,[s(N).errors.description?(l(),u("div",At,[i("p",Ht,v(s(N).errors.description),1)])):g("",!0),e[62]||(e[62]=i("p",{class:"text-xs font-medium text-gray-500"}," Try to keep your description in the present tense and avoid any major plot spoilers, while keeping it short and sweet. ",-1))]),i("div",{class:A(["mt-2 grid gap-4",t.sectionDescription?"grid-cols-3":"grid-cols-2"])},[c(z,{size:"sm",color:"gray",onClick:e[29]||(e[29]=o=>{m(d)?d.value=!1:d=!1,s(N).description=""})},{default:x(()=>e[63]||(e[63]=[k(" Cancel ")])),_:1}),t.sectionDescription?(l(),U(z,{key:0,size:"sm",color:"red",onClick:e[30]||(e[30]=R(o=>{s(N).delete(`/api/delete-description/${t.section.id}`,{preserveScroll:!0,onSuccess:()=>{s(N).description="",m(d)?d.value=!1:d=!1}})},["prevent"]))},{default:x(()=>e[64]||(e[64]=[k(" Delete ")])),_:1})):g("",!0),c(z,{size:"sm",color:"blue",type:"submit",disabled:s(N).processing},{default:x(()=>e[65]||(e[65]=[k(" Suggest ")])),_:1},8,["disabled"])],2)],32)])):(l(),u("div",{key:0,class:"flex h-20 cursor-pointer flex-row items-center rounded-lg border-2 border-dashed border-gray-300 px-3 py-1.5 transition duration-150 ease-in-out hover:border-gray-400",onClick:e[27]||(e[27]=o=>m(d)?d.value=!0:d=!0)},[c(s(de),{class:"mx-auto mr-4 h-8 w-8 text-gray-400","aria-hidden":"true"}),e[61]||(e[61]=i("div",{class:"flex grow flex-col justify-start"},[i("h3",{class:"text-sm text-gray-900"},"No description"),i("p",{class:"mt-1 text-sm text-gray-500"}," Click to suggest a description for this section. ")],-1))]))]))])]),i("div",{class:A(["mt-8 grid w-full items-center gap-4",t.section.video?"grid-cols-2":"grid-cols-1"])},[c(Le,{"full-width":!1,class:"group flex items-center justify-center rounded-lg border border-gray-300 bg-white p-1.5 text-sm font-medium text-gray-800 transition duration-250 hover:text-sky-600",data:H(t.section)+(t.tab.tab?`?tab=${t.tab.tab}`:""),message:"Link Copied",onClick:e[32]||(e[32]=o=>me())},{default:x(()=>[c(s(De),{class:"mr-2 h-5 w-5 cursor-pointer stroke-2 text-gray-500 transition duration-250 group-hover:text-sky-600"}),e[66]||(e[66]=k("Copy Link"))]),_:1},8,["data"]),t.section.video?(l(),U(F,{key:0,href:`/watch/${t.section.video.url_slug}`,class:"group flex items-center justify-center rounded-lg border border-gray-300 bg-white p-1.5 text-sm font-medium text-gray-800 transition duration-250 hover:text-pink-600"},{default:x(()=>[c(s(Te),{class:"mr-2 h-5 w-5 cursor-pointer stroke-2 text-gray-500 transition duration-250 group-hover:text-pink-600"}),e[67]||(e[67]=k(" Go to Video"))]),_:1},8,["href"])):g("",!0)],2),i("div",Jt,[e[72]||(e[72]=i("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"},"Practice",-1)),i("div",Rt,[i("div",Zt,[i("div",qt,[c(s(le),{class:"h-6 w-6 text-orange-700"})]),i("div",Kt,[e[68]||(e[68]=i("h2",{class:"font-intro text-xl font-semibold text-orange-700"}," Vocabulary ",-1)),i("h5",Ft,v(t.actionButtons.vocab_count+" "+s(G)("word",t.actionButtons.vocab_count)),1)]),t.actionButtons.vocab_count>0?(l(),U(z,{key:0,size:"md",color:"lightOrange",class:"ml-4 w-32",link:`/practice/vocabulary/attempt?sections[]=${a.value}`},{default:x(()=>e[69]||(e[69]=[k("Practice")])),_:1},8,["link"])):g("",!0)]),i("div",Qt,[i("div",Xt,[c(s(ae),{class:"h-6 w-6 text-purple-700"})]),i("div",Yt,[e[70]||(e[70]=i("h2",{class:"font-intro text-xl font-semibold text-purple-700"}," Grammar ",-1)),i("h5",Wt,v(t.actionButtons.noun_verb_count+" "+s(G)("noun",t.actionButtons.noun_verb_count)+" and "+s(G)("verb",t.actionButtons.noun_verb_count)),1)]),t.actionButtons.noun_verb_count>0?(l(),U(z,{key:0,size:"md",color:"lightPurple",class:"ml-4 w-32",link:`/practice/grammar/c/attempt?sections[]=${a.value}`},{default:x(()=>e[71]||(e[71]=[k("Practice")])),_:1},8,["link"])):g("",!0)])])]),i("div",_t,[t.tab.tab==null?(l(),U(ie,{key:0,"selecting-text":s(b),"split-sections":s(y),"display-syntax":s(p),"case-color":s(f),"has-prose":O(),section:t.section,"verified-syntax":E.value,subscribed:s(S)().props.subscribed,"onUpdate:caseColor":e[33]||(e[33]=o=>m(f)?f.value=o:f=o),"onUpdate:displaySyntax":e[34]||(e[34]=o=>m(p)?p.value=o:p=o),"onUpdate:selectingText":e[35]||(e[35]=o=>m(b)?b.value=o:b=o),"onUpdate:splitSections":e[36]||(e[36]=o=>m(y)?y.value=o:y=o)},null,8,["selecting-text","split-sections","display-syntax","case-color","has-prose","section","verified-syntax","subscribed"])):g("",!0),t.tab.tab=="vocab"?(l(),U(ne,{key:1,"current-url":H(t.section),filters:t.filters,"clipboard-list":Q.value,subscribed:s(S)().props.subscribed},null,8,["current-url","filters","clipboard-list","subscribed"])):g("",!0),t.tab.tab=="notes"&&s(S)().props.subscribed?(l(),U(re,{key:2,note:t.sectionNote,"section-id":t.section.id,"selecting-text":s(b),"split-sections":s(y),"display-syntax":s(p),"case-color":s(f),"has-prose":O(),"verified-syntax":E.value,subscribed:s(S)().props.subscribed,section:t.section,"onUpdate:caseColor":e[37]||(e[37]=o=>m(f)?f.value=o:f=o),"onUpdate:displaySyntax":e[38]||(e[38]=o=>m(p)?p.value=o:p=o),"onUpdate:selectingText":e[39]||(e[39]=o=>m(b)?b.value=o:b=o),"onUpdate:splitSections":e[40]||(e[40]=o=>m(y)?y.value=o:y=o)},null,8,["note","section-id","selecting-text","split-sections","display-syntax","case-color","has-prose","verified-syntax","subscribed","section"])):g("",!0)])])]),_:1})}}};export{ao as default};
