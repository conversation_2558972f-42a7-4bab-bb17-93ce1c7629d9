import{o as S,d as O,a as p,e as J,A as $e,k as I,i as Pe,a1 as ze,c as ie,w as ue,W as Ee,b as Y,t as z,u as A,g as Te,f as V,q as le,M as We,n as ve,T as Be,h as ge,F as ye,z as Ue}from"./app-f0078ddb.js";import{_ as Ge}from"./AppLayout-33f062bc.js";import{_ as Ye}from"./Breadcrumbs-c96e9207.js";import{p as He}from"./pluralize-d25a928b.js";import Qe from"./VideoItemSmall-c82820c8.js";import{u as Je}from"./useInfiniteScroll-1e8e8e17.js";import Ke from"./GrammarActivityItem-1c811db8.js";import{_ as Xe}from"./Footer-0988dcd8.js";import{P as Ze}from"./Promotion-3eee0057.js";import{_ as et}from"./AssignmentModule-fc2620bb.js";import{r as tt}from"./PlayIcon-01d0f73f.js";import{r as rt}from"./CheckCircleIcon-d86d1232.js";import{r as nt}from"./CheckBadgeIcon-e7792a17.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./useIntersect-6e15125e.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-6a201aa1.js";import"./ArrowPathIcon-f74cb8d6.js";function it(r,e){return S(),O("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"})])}function ot(r,e){return S(),O("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[p("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15 3 9m0 0 6-6M3 9h12a6 6 0 0 1 0 12h-3"})])}/*! @vimeo/player v2.26.0 | (c) 2025 Vimeo | MIT License | https://github.com/vimeo/player.js */function xe(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable})),t.push.apply(t,n)}return t}function Ce(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?xe(Object(t),!0).forEach(function(n){ce(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):xe(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function $(){$=function(){return r};var r={},e=Object.prototype,t=e.hasOwnProperty,n=Object.defineProperty||function(u,l,d){u[l]=d.value},i=typeof Symbol=="function"?Symbol:{},o=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function f(u,l,d){return Object.defineProperty(u,l,{value:d,enumerable:!0,configurable:!0,writable:!0}),u[l]}try{f({},"")}catch{f=function(l,d,x){return l[d]=x}}function s(u,l,d,x){var b=l&&l.prototype instanceof C?l:C,j=Object.create(b.prototype),N=new F(x||[]);return n(j,"_invoke",{value:_(u,d,N)}),j}function E(u,l,d){try{return{type:"normal",arg:u.call(l,d)}}catch(x){return{type:"throw",arg:x}}}r.wrap=s;var P={};function C(){}function T(){}function k(){}var D={};f(D,o,function(){return this});var R=Object.getPrototypeOf,q=R&&R(R(L([])));q&&q!==e&&t.call(q,o)&&(D=q);var g=k.prototype=C.prototype=Object.create(D);function h(u){["next","throw","return"].forEach(function(l){f(u,l,function(d){return this._invoke(l,d)})})}function v(u,l){function d(b,j,N,B){var U=E(u[b],u,j);if(U.type!=="throw"){var ee=U.arg,ne=ee.value;return ne&&typeof ne=="object"&&t.call(ne,"__await")?l.resolve(ne.__await).then(function(Q){d("next",Q,N,B)},function(Q){d("throw",Q,N,B)}):l.resolve(ne).then(function(Q){ee.value=Q,N(ee)},function(Q){return d("throw",Q,N,B)})}B(U.arg)}var x;n(this,"_invoke",{value:function(b,j){function N(){return new l(function(B,U){d(b,j,B,U)})}return x=x?x.then(N,N):N()}})}function _(u,l,d){var x="suspendedStart";return function(b,j){if(x==="executing")throw new Error("Generator is already running");if(x==="completed"){if(b==="throw")throw j;return M()}for(d.method=b,d.arg=j;;){var N=d.delegate;if(N){var B=m(N,d);if(B){if(B===P)continue;return B}}if(d.method==="next")d.sent=d._sent=d.arg;else if(d.method==="throw"){if(x==="suspendedStart")throw x="completed",d.arg;d.dispatchException(d.arg)}else d.method==="return"&&d.abrupt("return",d.arg);x="executing";var U=E(u,l,d);if(U.type==="normal"){if(x=d.done?"completed":"suspendedYield",U.arg===P)continue;return{value:U.arg,done:d.done}}U.type==="throw"&&(x="completed",d.method="throw",d.arg=U.arg)}}}function m(u,l){var d=l.method,x=u.iterator[d];if(x===void 0)return l.delegate=null,d==="throw"&&u.iterator.return&&(l.method="return",l.arg=void 0,m(u,l),l.method==="throw")||d!=="return"&&(l.method="throw",l.arg=new TypeError("The iterator does not provide a '"+d+"' method")),P;var b=E(x,u.iterator,l.arg);if(b.type==="throw")return l.method="throw",l.arg=b.arg,l.delegate=null,P;var j=b.arg;return j?j.done?(l[u.resultName]=j.value,l.next=u.nextLoc,l.method!=="return"&&(l.method="next",l.arg=void 0),l.delegate=null,P):j:(l.method="throw",l.arg=new TypeError("iterator result is not an object"),l.delegate=null,P)}function w(u){var l={tryLoc:u[0]};1 in u&&(l.catchLoc=u[1]),2 in u&&(l.finallyLoc=u[2],l.afterLoc=u[3]),this.tryEntries.push(l)}function y(u){var l=u.completion||{};l.type="normal",delete l.arg,u.completion=l}function F(u){this.tryEntries=[{tryLoc:"root"}],u.forEach(w,this),this.reset(!0)}function L(u){if(u){var l=u[o];if(l)return l.call(u);if(typeof u.next=="function")return u;if(!isNaN(u.length)){var d=-1,x=function b(){for(;++d<u.length;)if(t.call(u,d))return b.value=u[d],b.done=!1,b;return b.value=void 0,b.done=!0,b};return x.next=x}}return{next:M}}function M(){return{value:void 0,done:!0}}return T.prototype=k,n(g,"constructor",{value:k,configurable:!0}),n(k,"constructor",{value:T,configurable:!0}),T.displayName=f(k,c,"GeneratorFunction"),r.isGeneratorFunction=function(u){var l=typeof u=="function"&&u.constructor;return!!l&&(l===T||(l.displayName||l.name)==="GeneratorFunction")},r.mark=function(u){return Object.setPrototypeOf?Object.setPrototypeOf(u,k):(u.__proto__=k,f(u,c,"GeneratorFunction")),u.prototype=Object.create(g),u},r.awrap=function(u){return{__await:u}},h(v.prototype),f(v.prototype,a,function(){return this}),r.AsyncIterator=v,r.async=function(u,l,d,x,b){b===void 0&&(b=Promise);var j=new v(s(u,l,d,x),b);return r.isGeneratorFunction(l)?j:j.next().then(function(N){return N.done?N.value:j.next()})},h(g),f(g,c,"Generator"),f(g,o,function(){return this}),f(g,"toString",function(){return"[object Generator]"}),r.keys=function(u){var l=Object(u),d=[];for(var x in l)d.push(x);return d.reverse(),function b(){for(;d.length;){var j=d.pop();if(j in l)return b.value=j,b.done=!1,b}return b.done=!0,b}},r.values=L,F.prototype={constructor:F,reset:function(u){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(y),!u)for(var l in this)l.charAt(0)==="t"&&t.call(this,l)&&!isNaN(+l.slice(1))&&(this[l]=void 0)},stop:function(){this.done=!0;var u=this.tryEntries[0].completion;if(u.type==="throw")throw u.arg;return this.rval},dispatchException:function(u){if(this.done)throw u;var l=this;function d(U,ee){return j.type="throw",j.arg=u,l.next=U,ee&&(l.method="next",l.arg=void 0),!!ee}for(var x=this.tryEntries.length-1;x>=0;--x){var b=this.tryEntries[x],j=b.completion;if(b.tryLoc==="root")return d("end");if(b.tryLoc<=this.prev){var N=t.call(b,"catchLoc"),B=t.call(b,"finallyLoc");if(N&&B){if(this.prev<b.catchLoc)return d(b.catchLoc,!0);if(this.prev<b.finallyLoc)return d(b.finallyLoc)}else if(N){if(this.prev<b.catchLoc)return d(b.catchLoc,!0)}else{if(!B)throw new Error("try statement without catch or finally");if(this.prev<b.finallyLoc)return d(b.finallyLoc)}}}},abrupt:function(u,l){for(var d=this.tryEntries.length-1;d>=0;--d){var x=this.tryEntries[d];if(x.tryLoc<=this.prev&&t.call(x,"finallyLoc")&&this.prev<x.finallyLoc){var b=x;break}}b&&(u==="break"||u==="continue")&&b.tryLoc<=l&&l<=b.finallyLoc&&(b=null);var j=b?b.completion:{};return j.type=u,j.arg=l,b?(this.method="next",this.next=b.finallyLoc,P):this.complete(j)},complete:function(u,l){if(u.type==="throw")throw u.arg;return u.type==="break"||u.type==="continue"?this.next=u.arg:u.type==="return"?(this.rval=this.arg=u.arg,this.method="return",this.next="end"):u.type==="normal"&&l&&(this.next=l),P},finish:function(u){for(var l=this.tryEntries.length-1;l>=0;--l){var d=this.tryEntries[l];if(d.finallyLoc===u)return this.complete(d.completion,d.afterLoc),y(d),P}},catch:function(u){for(var l=this.tryEntries.length-1;l>=0;--l){var d=this.tryEntries[l];if(d.tryLoc===u){var x=d.completion;if(x.type==="throw"){var b=x.arg;y(d)}return b}}throw new Error("illegal catch attempt")},delegateYield:function(u,l,d){return this.delegate={iterator:L(u),resultName:l,nextLoc:d},this.method==="next"&&(this.arg=void 0),P}},r}function Se(r,e,t,n,i,o,a){try{var c=r[o](a),f=c.value}catch(s){t(s);return}c.done?e(f):Promise.resolve(f).then(n,i)}function X(r){return function(){var e=this,t=arguments;return new Promise(function(n,i){var o=r.apply(e,t);function a(f){Se(o,n,i,a,c,"next",f)}function c(f){Se(o,n,i,a,c,"throw",f)}a(void 0)})}}function Le(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Me(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Fe(n.key),n)}}function Re(r,e,t){return e&&Me(r.prototype,e),t&&Me(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}function ce(r,e,t){return e=Fe(e),e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function at(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&se(r,e)}function ae(r){return ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ae(r)}function se(r,e){return se=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},se(r,e)}function Ae(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function de(r,e,t){return Ae()?de=Reflect.construct.bind():de=function(i,o,a){var c=[null];c.push.apply(c,o);var f=Function.bind.apply(i,c),s=new f;return a&&se(s,a.prototype),s},de.apply(null,arguments)}function st(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function be(r){var e=typeof Map=="function"?new Map:void 0;return be=function(n){if(n===null||!st(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return de(n,arguments,ae(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),se(i,n)},be(r)}function fe(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ut(r,e){if(e&&(typeof e=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fe(r)}function lt(r){var e=Ae();return function(){var n=ae(r),i;if(e){var o=ae(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return ut(this,i)}}function ct(r,e){if(typeof r!="object"||r===null)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function Fe(r){var e=ct(r,"string");return typeof e=="symbol"?e:String(e)}var Ne=typeof global<"u"&&{}.toString.call(global)==="[object global]";function Oe(r,e){return r.indexOf(e.toLowerCase())===0?r:"".concat(e.toLowerCase()).concat(r.substr(0,1).toUpperCase()).concat(r.substr(1))}function dt(r){return!!(r&&r.nodeType===1&&"nodeName"in r&&r.ownerDocument&&r.ownerDocument.defaultView)}function ft(r){return!isNaN(parseFloat(r))&&isFinite(r)&&Math.floor(r)==r}function Z(r){return/^(https?:)?\/\/((((player|www)\.)?vimeo\.com)|((player\.)?[a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))(?=$|\/)/.test(r)}function Ie(r){var e=/^https:\/\/player\.((vimeo\.com)|([a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))\/video\/\d+/;return e.test(r)}function ht(r){for(var e=(r||"").match(/^(?:https?:)?(?:\/\/)?([^/?]+)/),t=(e&&e[1]||"").replace("player.",""),n=[".videoji.hk",".vimeo.work",".videoji.cn"],i=0,o=n;i<o.length;i++){var a=o[i];if(t.endsWith(a))return t}return"vimeo.com"}function De(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=r.id,t=r.url,n=e||t;if(!n)throw new Error("An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.");if(ft(n))return"https://vimeo.com/".concat(n);if(Z(n))return n.replace("http:","https:");throw e?new TypeError("“".concat(e,"” is not a valid video id.")):new TypeError("“".concat(n,"” is not a vimeo.com url."))}var je=function(e,t,n){var i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:"addEventListener",o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"removeEventListener",a=typeof t=="string"?[t]:t;return a.forEach(function(c){e[i](c,n)}),{cancel:function(){return a.forEach(function(f){return e[o](f,n)})}}},mt=typeof Array.prototype.indexOf<"u",pt=typeof window<"u"&&typeof window.postMessage<"u";if(!Ne&&(!mt||!pt))throw new Error("Sorry, the Vimeo Player API is not available in this browser.");var re=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function vt(r,e){return e={exports:{}},r(e,e.exports),e.exports}/*!
 * weakmap-polyfill v2.0.4 - ECMAScript6 WeakMap polyfill
 * https://github.com/polygonplanet/weakmap-polyfill
 * Copyright (c) 2015-2021 polygonplanet <<EMAIL>>
 * @license MIT
 */(function(r){if(r.WeakMap)return;var e=Object.prototype.hasOwnProperty,t=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{value:1}).x===1}catch{}}(),n=function(o,a,c){t?Object.defineProperty(o,a,{configurable:!0,writable:!0,value:c}):o[a]=c};r.WeakMap=function(){function o(){if(this===void 0)throw new TypeError("Constructor WeakMap requires 'new'");if(n(this,"_id",c("_WeakMap")),arguments.length>0)throw new TypeError("WeakMap iterable is not supported")}n(o.prototype,"delete",function(s){if(a(this,"delete"),!i(s))return!1;var E=s[this._id];return E&&E[0]===s?(delete s[this._id],!0):!1}),n(o.prototype,"get",function(s){if(a(this,"get"),!!i(s)){var E=s[this._id];if(E&&E[0]===s)return E[1]}}),n(o.prototype,"has",function(s){if(a(this,"has"),!i(s))return!1;var E=s[this._id];return!!(E&&E[0]===s)}),n(o.prototype,"set",function(s,E){if(a(this,"set"),!i(s))throw new TypeError("Invalid value used as weak map key");var P=s[this._id];return P&&P[0]===s?(P[1]=E,this):(n(s,this._id,[s,E]),this)});function a(s,E){if(!i(s)||!e.call(s,"_id"))throw new TypeError(E+" method called on incompatible receiver "+typeof s)}function c(s){return s+"_"+f()+"."+f()}function f(){return Math.random().toString().substring(2)}return n(o,"_polyfill",!0),o}();function i(o){return Object(o)===o}})(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:re);var G=vt(function(r){/*! Native Promise Only
    v0.8.1 (c) Kyle Simpson
    MIT License: http://getify.mit-license.org
*/(function(t,n,i){n[t]=n[t]||i(),r.exports&&(r.exports=n[t])})("Promise",re,function(){var t,n,i,o=Object.prototype.toString,a=typeof setImmediate<"u"?function(h){return setImmediate(h)}:setTimeout;try{Object.defineProperty({},"x",{}),t=function(h,v,_,m){return Object.defineProperty(h,v,{value:_,writable:!0,configurable:m!==!1})}}catch{t=function(v,_,m){return v[_]=m,v}}i=function(){var h,v,_;function m(w,y){this.fn=w,this.self=y,this.next=void 0}return{add:function(y,F){_=new m(y,F),v?v.next=_:h=_,v=_,_=void 0},drain:function(){var y=h;for(h=v=n=void 0;y;)y.fn.call(y.self),y=y.next}}}();function c(g,h){i.add(g,h),n||(n=a(i.drain))}function f(g){var h,v=typeof g;return g!=null&&(v=="object"||v=="function")&&(h=g.then),typeof h=="function"?h:!1}function s(){for(var g=0;g<this.chain.length;g++)E(this,this.state===1?this.chain[g].success:this.chain[g].failure,this.chain[g]);this.chain.length=0}function E(g,h,v){var _,m;try{h===!1?v.reject(g.msg):(h===!0?_=g.msg:_=h.call(void 0,g.msg),_===v.promise?v.reject(TypeError("Promise-chain cycle")):(m=f(_))?m.call(_,v.resolve,v.reject):v.resolve(_))}catch(w){v.reject(w)}}function P(g){var h,v=this;if(!v.triggered){v.triggered=!0,v.def&&(v=v.def);try{(h=f(g))?c(function(){var _=new k(v);try{h.call(g,function(){P.apply(_,arguments)},function(){C.apply(_,arguments)})}catch(m){C.call(_,m)}}):(v.msg=g,v.state=1,v.chain.length>0&&c(s,v))}catch(_){C.call(new k(v),_)}}}function C(g){var h=this;h.triggered||(h.triggered=!0,h.def&&(h=h.def),h.msg=g,h.state=2,h.chain.length>0&&c(s,h))}function T(g,h,v,_){for(var m=0;m<h.length;m++)(function(y){g.resolve(h[y]).then(function(L){v(y,L)},_)})(m)}function k(g){this.def=g,this.triggered=!1}function D(g){this.promise=g,this.state=0,this.triggered=!1,this.chain=[],this.msg=void 0}function R(g){if(typeof g!="function")throw TypeError("Not a function");if(this.__NPO__!==0)throw TypeError("Not a promise");this.__NPO__=1;var h=new D(this);this.then=function(_,m){var w={success:typeof _=="function"?_:!0,failure:typeof m=="function"?m:!1};return w.promise=new this.constructor(function(F,L){if(typeof F!="function"||typeof L!="function")throw TypeError("Not a function");w.resolve=F,w.reject=L}),h.chain.push(w),h.state!==0&&c(s,h),w.promise},this.catch=function(_){return this.then(void 0,_)};try{g.call(void 0,function(_){P.call(h,_)},function(_){C.call(h,_)})}catch(v){C.call(h,v)}}var q=t({},"constructor",R,!1);return R.prototype=q,t(q,"__NPO__",0,!1),t(R,"resolve",function(h){var v=this;return h&&typeof h=="object"&&h.__NPO__===1?h:new v(function(m,w){if(typeof m!="function"||typeof w!="function")throw TypeError("Not a function");m(h)})}),t(R,"reject",function(h){return new this(function(_,m){if(typeof _!="function"||typeof m!="function")throw TypeError("Not a function");m(h)})}),t(R,"all",function(h){var v=this;return o.call(h)!="[object Array]"?v.reject(TypeError("Not an array")):h.length===0?v.resolve([]):new v(function(m,w){if(typeof m!="function"||typeof w!="function")throw TypeError("Not a function");var y=h.length,F=Array(y),L=0;T(v,h,function(u,l){F[u]=l,++L===y&&m(F)},w)})}),t(R,"race",function(h){var v=this;return o.call(h)!="[object Array]"?v.reject(TypeError("Not an array")):new v(function(m,w){if(typeof m!="function"||typeof w!="function")throw TypeError("Not a function");T(v,h,function(F,L){m(L)},w)})}),R})}),H=new WeakMap;function oe(r,e,t){var n=H.get(r.element)||{};e in n||(n[e]=[]),n[e].push(t),H.set(r.element,n)}function he(r,e){var t=H.get(r.element)||{};return t[e]||[]}function me(r,e,t){var n=H.get(r.element)||{};if(!n[e])return!0;if(!t)return n[e]=[],H.set(r.element,n),!0;var i=n[e].indexOf(t);return i!==-1&&n[e].splice(i,1),H.set(r.element,n),n[e]&&n[e].length===0}function gt(r,e){var t=he(r,e);if(t.length<1)return!1;var n=t.shift();return me(r,e,n),n}function yt(r,e){var t=H.get(r);H.set(e,t),H.delete(r)}function pe(r){if(typeof r=="string")try{r=JSON.parse(r)}catch(e){return console.warn(e),{}}return r}function K(r,e,t){if(!(!r.element.contentWindow||!r.element.contentWindow.postMessage)){var n={method:e};t!==void 0&&(n.value=t);var i=parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\d+).*$/,"$1"));i>=8&&i<10&&(n=JSON.stringify(n)),r.element.contentWindow.postMessage(n,r.origin)}}function wt(r,e){e=pe(e);var t=[],n;if(e.event){if(e.event==="error"){var i=he(r,e.data.method);i.forEach(function(a){var c=new Error(e.data.message);c.name=e.data.name,a.reject(c),me(r,e.data.method,a)})}t=he(r,"event:".concat(e.event)),n=e.data}else if(e.method){var o=gt(r,e.method);o&&(t.push(o),n=e.value)}t.forEach(function(a){try{if(typeof a=="function"){a.call(r,n);return}a.resolve(n)}catch{}})}var bt=["airplay","audio_tracks","audiotrack","autopause","autoplay","background","byline","cc","chapter_id","chapters","chromecast","color","colors","controls","dnt","end_time","fullscreen","height","id","interactive_params","keyboard","loop","maxheight","maxwidth","muted","play_button_position","playsinline","portrait","progress_bar","quality_selector","responsive","skipping_forward","speed","start_time","texttrack","title","transcript","transparent","unmute_button","url","vimeo_logo","volume","watch_full_video","width"];function Ve(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return bt.reduce(function(t,n){var i=r.getAttribute("data-vimeo-".concat(n));return(i||i==="")&&(t[n]=i===""?1:i),t},e)}function ke(r,e){var t=r.html;if(!e)throw new TypeError("An element must be provided");if(e.getAttribute("data-vimeo-initialized")!==null)return e.querySelector("iframe");var n=document.createElement("div");return n.innerHTML=t,e.appendChild(n.firstChild),e.setAttribute("data-vimeo-initialized","true"),e.querySelector("iframe")}function qe(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=arguments.length>2?arguments[2]:void 0;return new Promise(function(n,i){if(!Z(r))throw new TypeError("“".concat(r,"” is not a vimeo.com url."));var o=ht(r),a="https://".concat(o,"/api/oembed.json?url=").concat(encodeURIComponent(r));for(var c in e)e.hasOwnProperty(c)&&(a+="&".concat(c,"=").concat(encodeURIComponent(e[c])));var f="XDomainRequest"in window?new XDomainRequest:new XMLHttpRequest;f.open("GET",a,!0),f.onload=function(){if(f.status===404){i(new Error("“".concat(r,"” was not found.")));return}if(f.status===403){i(new Error("“".concat(r,"” is not embeddable.")));return}try{var s=JSON.parse(f.responseText);if(s.domain_status_code===403){ke(s,t),i(new Error("“".concat(r,"” is not embeddable.")));return}n(s)}catch(E){i(E)}},f.onerror=function(){var s=f.status?" (".concat(f.status,")"):"";i(new Error("There was an error fetching the embed code from Vimeo".concat(s,".")))},f.send()})}function kt(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document,e=[].slice.call(r.querySelectorAll("[data-vimeo-id], [data-vimeo-url]")),t=function(i){"console"in window&&console.error&&console.error("There was an error creating an embed: ".concat(i))};e.forEach(function(n){try{if(n.getAttribute("data-vimeo-defer")!==null)return;var i=Ve(n),o=De(i);qe(o,i,n).then(function(a){return ke(a,n)}).catch(t)}catch(a){t(a)}})}function _t(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;if(!window.VimeoPlayerResizeEmbeds_){window.VimeoPlayerResizeEmbeds_=!0;var e=function(n){if(Z(n.origin)&&!(!n.data||n.data.event!=="spacechange")){for(var i=r.querySelectorAll("iframe"),o=0;o<i.length;o++)if(i[o].contentWindow===n.source){var a=i[o].parentElement;a.style.paddingBottom="".concat(n.data.data[0].bottom,"px");break}}};window.addEventListener("message",e)}}function Pt(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;if(!window.VimeoSeoMetadataAppended){window.VimeoSeoMetadataAppended=!0;var e=function(n){if(Z(n.origin)){var i=pe(n.data);if(!(!i||i.event!=="ready"))for(var o=r.querySelectorAll("iframe"),a=0;a<o.length;a++){var c=o[a],f=c.contentWindow===n.source;if(Ie(c.src)&&f){var s=new _e(c);s.callMethod("appendVideoMetadata",window.location.href)}}}};window.addEventListener("message",e)}}function Et(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document;if(!window.VimeoCheckedUrlTimeParam){window.VimeoCheckedUrlTimeParam=!0;var e=function(i){"console"in window&&console.error&&console.error("There was an error getting video Id: ".concat(i))},t=function(i){if(Z(i.origin)){var o=pe(i.data);if(!(!o||o.event!=="ready"))for(var a=r.querySelectorAll("iframe"),c=function(){var E=a[f],P=E.contentWindow===i.source;if(Ie(E.src)&&P){var C=new _e(E);C.getVideoId().then(function(T){var k=new RegExp("[?&]vimeo_t_".concat(T,"=([^&#]*)")).exec(window.location.href);if(k&&k[1]){var D=decodeURI(k[1]);C.setCurrentTime(D)}}).catch(e)}},f=0;f<a.length;f++)c()}};window.addEventListener("message",t)}}function Tt(){var r=function(){for(var n,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],o=0,a=i.length,c={};o<a;o++)if(n=i[o],n&&n[1]in document){for(o=0;o<n.length;o++)c[i[0][o]]=n[o];return c}return!1}(),e={fullscreenchange:r.fullscreenchange,fullscreenerror:r.fullscreenerror},t={request:function(i){return new Promise(function(o,a){var c=function s(){t.off("fullscreenchange",s),o()};t.on("fullscreenchange",c),i=i||document.documentElement;var f=i[r.requestFullscreen]();f instanceof Promise&&f.then(c).catch(a)})},exit:function(){return new Promise(function(i,o){if(!t.isFullscreen){i();return}var a=function f(){t.off("fullscreenchange",f),i()};t.on("fullscreenchange",a);var c=document[r.exitFullscreen]();c instanceof Promise&&c.then(a).catch(o)})},on:function(i,o){var a=e[i];a&&document.addEventListener(a,o)},off:function(i,o){var a=e[i];a&&document.removeEventListener(a,o)}};return Object.defineProperties(t,{isFullscreen:{get:function(){return!!document[r.fullscreenElement]}},element:{enumerable:!0,get:function(){return document[r.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return!!document[r.fullscreenEnabled]}}}),t}var xt={role:"viewer",autoPlayMuted:!0,allowedDrift:.3,maxAllowedDrift:1,minCheckInterval:.1,maxRateAdjustment:.2,maxTimeToCatchUp:1},Ct=function(r){at(t,r);var e=lt(t);function t(n,i){var o,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},c=arguments.length>3?arguments[3]:void 0;return Le(this,t),o=e.call(this),ce(fe(o),"logger",void 0),ce(fe(o),"speedAdjustment",0),ce(fe(o),"adjustSpeed",function(){var f=X($().mark(function s(E,P){var C;return $().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:if(o.speedAdjustment!==P){k.next=2;break}return k.abrupt("return");case 2:return k.next=4,E.getPlaybackRate();case 4:return k.t0=k.sent,k.t1=o.speedAdjustment,k.t2=k.t0-k.t1,k.t3=P,C=k.t2+k.t3,o.log("New playbackRate:  ".concat(C)),k.next=12,E.setPlaybackRate(C);case 12:o.speedAdjustment=P;case 13:case"end":return k.stop()}},s)}));return function(s,E){return f.apply(this,arguments)}}()),o.logger=c,o.init(i,n,Ce(Ce({},xt),a)),o}return Re(t,[{key:"disconnect",value:function(){this.dispatchEvent(new Event("disconnect"))}},{key:"init",value:function(){var n=X($().mark(function o(a,c,f){var s=this,E,P,C;return $().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:return k.next=2,this.waitForTOReadyState(a,"open");case 2:if(f.role!=="viewer"){k.next=10;break}return k.next=5,this.updatePlayer(a,c,f);case 5:E=je(a,"change",function(){return s.updatePlayer(a,c,f)}),P=this.maintainPlaybackPosition(a,c,f),this.addEventListener("disconnect",function(){P.cancel(),E.cancel()}),k.next=14;break;case 10:return k.next=12,this.updateTimingObject(a,c);case 12:C=je(c,["seeked","play","pause","ratechange"],function(){return s.updateTimingObject(a,c)},"on","off"),this.addEventListener("disconnect",function(){return C.cancel()});case 14:case"end":return k.stop()}},o,this)}));function i(o,a,c){return n.apply(this,arguments)}return i}()},{key:"updateTimingObject",value:function(){var n=X($().mark(function o(a,c){return $().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.t0=a,s.next=3,c.getCurrentTime();case 3:return s.t1=s.sent,s.next=6,c.getPaused();case 6:if(!s.sent){s.next=10;break}s.t2=0,s.next=13;break;case 10:return s.next=12,c.getPlaybackRate();case 12:s.t2=s.sent;case 13:s.t3=s.t2,s.t4={position:s.t1,velocity:s.t3},s.t0.update.call(s.t0,s.t4);case 16:case"end":return s.stop()}},o)}));function i(o,a){return n.apply(this,arguments)}return i}()},{key:"updatePlayer",value:function(){var n=X($().mark(function o(a,c,f){var s,E,P;return $().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:if(s=a.query(),E=s.position,P=s.velocity,typeof E=="number"&&c.setCurrentTime(E),typeof P!="number"){T.next=25;break}if(P!==0){T.next=11;break}return T.next=6,c.getPaused();case 6:if(T.t0=T.sent,T.t0!==!1){T.next=9;break}c.pause();case 9:T.next=25;break;case 11:if(!(P>0)){T.next=25;break}return T.next=14,c.getPaused();case 14:if(T.t1=T.sent,T.t1!==!0){T.next=19;break}return T.next=18,c.play().catch(function(){var k=X($().mark(function D(R){return $().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:if(!(R.name==="NotAllowedError"&&f.autoPlayMuted)){g.next=5;break}return g.next=3,c.setMuted(!0);case 3:return g.next=5,c.play().catch(function(h){return console.error("Couldn't play the video from TimingSrcConnector. Error:",h)});case 5:case"end":return g.stop()}},D)}));return function(D){return k.apply(this,arguments)}}());case 18:this.updatePlayer(a,c,f);case 19:return T.next=21,c.getPlaybackRate();case 21:if(T.t2=T.sent,T.t3=P,T.t2===T.t3){T.next=25;break}c.setPlaybackRate(P);case 25:case"end":return T.stop()}},o,this)}));function i(o,a,c){return n.apply(this,arguments)}return i}()},{key:"maintainPlaybackPosition",value:function(i,o,a){var c=this,f=a.allowedDrift,s=a.maxAllowedDrift,E=a.minCheckInterval,P=a.maxRateAdjustment,C=a.maxTimeToCatchUp,T=Math.min(C,Math.max(E,s))*1e3,k=function(){var R=X($().mark(function q(){var g,h,v,_,m;return $().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:if(y.t0=i.query().velocity===0,y.t0){y.next=6;break}return y.next=4,o.getPaused();case 4:y.t1=y.sent,y.t0=y.t1===!0;case 6:if(!y.t0){y.next=8;break}return y.abrupt("return");case 8:return y.t2=i.query().position,y.next=11,o.getCurrentTime();case 11:if(y.t3=y.sent,g=y.t2-y.t3,h=Math.abs(g),c.log("Drift: ".concat(g)),!(h>s)){y.next=22;break}return y.next=18,c.adjustSpeed(o,0);case 18:o.setCurrentTime(i.query().position),c.log("Resync by currentTime"),y.next=29;break;case 22:if(!(h>f)){y.next=29;break}return v=h/C,_=P,m=v<_?(_-v)/2:_,y.next=28,c.adjustSpeed(o,m*Math.sign(g));case 28:c.log("Resync by playbackRate");case 29:case"end":return y.stop()}},q)}));return function(){return R.apply(this,arguments)}}(),D=setInterval(function(){return k()},T);return{cancel:function(){return clearInterval(D)}}}},{key:"log",value:function(i){var o;(o=this.logger)===null||o===void 0||o.call(this,"TimingSrcConnector: ".concat(i))}},{key:"waitForTOReadyState",value:function(i,o){return new Promise(function(a){var c=function f(){i.readyState===o?a():i.addEventListener("readystatechange",f,{once:!0})};c()})}}]),t}(be(EventTarget)),te=new WeakMap,we=new WeakMap,W={},_e=function(){function r(e){var t=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(Le(this,r),window.jQuery&&e instanceof jQuery&&(e.length>1&&window.console&&console.warn&&console.warn("A jQuery object with multiple elements was passed, using the first element."),e=e[0]),typeof document<"u"&&typeof e=="string"&&(e=document.getElementById(e)),!dt(e))throw new TypeError("You must pass either a valid element or a valid id.");if(e.nodeName!=="IFRAME"){var i=e.querySelector("iframe");i&&(e=i)}if(e.nodeName==="IFRAME"&&!Z(e.getAttribute("src")||""))throw new Error("The player element passed isn’t a Vimeo embed.");if(te.has(e))return te.get(e);this._window=e.ownerDocument.defaultView,this.element=e,this.origin="*";var o=new G(function(c,f){if(t._onMessage=function(P){if(!(!Z(P.origin)||t.element.contentWindow!==P.source)){t.origin==="*"&&(t.origin=P.origin);var C=pe(P.data),T=C&&C.event==="error",k=T&&C.data&&C.data.method==="ready";if(k){var D=new Error(C.data.message);D.name=C.data.name,f(D);return}var R=C&&C.event==="ready",q=C&&C.method==="ping";if(R||q){t.element.setAttribute("data-ready","true"),c();return}wt(t,C)}},t._window.addEventListener("message",t._onMessage),t.element.nodeName!=="IFRAME"){var s=Ve(e,n),E=De(s);qe(E,s,e).then(function(P){var C=ke(P,e);return t.element=C,t._originalElement=e,yt(e,C),te.set(t.element,t),P}).catch(f)}});if(we.set(this,o),te.set(this.element,this),this.element.nodeName==="IFRAME"&&K(this,"ping"),W.isEnabled){var a=function(){return W.exit()};this.fullscreenchangeHandler=function(){W.isFullscreen?oe(t,"event:exitFullscreen",a):me(t,"event:exitFullscreen",a),t.ready().then(function(){K(t,"fullscreenchange",W.isFullscreen)})},W.on("fullscreenchange",this.fullscreenchangeHandler)}return this}return Re(r,[{key:"callMethod",value:function(t){var n=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new G(function(o,a){return n.ready().then(function(){oe(n,t,{resolve:o,reject:a}),K(n,t,i)}).catch(a)})}},{key:"get",value:function(t){var n=this;return new G(function(i,o){return t=Oe(t,"get"),n.ready().then(function(){oe(n,t,{resolve:i,reject:o}),K(n,t)}).catch(o)})}},{key:"set",value:function(t,n){var i=this;return new G(function(o,a){if(t=Oe(t,"set"),n==null)throw new TypeError("There must be a value to set.");return i.ready().then(function(){oe(i,t,{resolve:o,reject:a}),K(i,t,n)}).catch(a)})}},{key:"on",value:function(t,n){if(!t)throw new TypeError("You must pass an event name.");if(!n)throw new TypeError("You must pass a callback function.");if(typeof n!="function")throw new TypeError("The callback must be a function.");var i=he(this,"event:".concat(t));i.length===0&&this.callMethod("addEventListener",t).catch(function(){}),oe(this,"event:".concat(t),n)}},{key:"off",value:function(t,n){if(!t)throw new TypeError("You must pass an event name.");if(n&&typeof n!="function")throw new TypeError("The callback must be a function.");var i=me(this,"event:".concat(t),n);i&&this.callMethod("removeEventListener",t).catch(function(o){})}},{key:"loadVideo",value:function(t){return this.callMethod("loadVideo",t)}},{key:"ready",value:function(){var t=we.get(this)||new G(function(n,i){i(new Error("Unknown player. Probably unloaded."))});return G.resolve(t)}},{key:"addCuePoint",value:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.callMethod("addCuePoint",{time:t,data:n})}},{key:"removeCuePoint",value:function(t){return this.callMethod("removeCuePoint",t)}},{key:"enableTextTrack",value:function(t,n){if(!t)throw new TypeError("You must pass a language.");return this.callMethod("enableTextTrack",{language:t,kind:n})}},{key:"disableTextTrack",value:function(){return this.callMethod("disableTextTrack")}},{key:"pause",value:function(){return this.callMethod("pause")}},{key:"play",value:function(){return this.callMethod("play")}},{key:"requestFullscreen",value:function(){return W.isEnabled?W.request(this.element):this.callMethod("requestFullscreen")}},{key:"exitFullscreen",value:function(){return W.isEnabled?W.exit():this.callMethod("exitFullscreen")}},{key:"getFullscreen",value:function(){return W.isEnabled?G.resolve(W.isFullscreen):this.get("fullscreen")}},{key:"requestPictureInPicture",value:function(){return this.callMethod("requestPictureInPicture")}},{key:"exitPictureInPicture",value:function(){return this.callMethod("exitPictureInPicture")}},{key:"getPictureInPicture",value:function(){return this.get("pictureInPicture")}},{key:"remotePlaybackPrompt",value:function(){return this.callMethod("remotePlaybackPrompt")}},{key:"unload",value:function(){return this.callMethod("unload")}},{key:"destroy",value:function(){var t=this;return new G(function(n){if(we.delete(t),te.delete(t.element),t._originalElement&&(te.delete(t._originalElement),t._originalElement.removeAttribute("data-vimeo-initialized")),t.element&&t.element.nodeName==="IFRAME"&&t.element.parentNode&&(t.element.parentNode.parentNode&&t._originalElement&&t._originalElement!==t.element.parentNode?t.element.parentNode.parentNode.removeChild(t.element.parentNode):t.element.parentNode.removeChild(t.element)),t.element&&t.element.nodeName==="DIV"&&t.element.parentNode){t.element.removeAttribute("data-vimeo-initialized");var i=t.element.querySelector("iframe");i&&i.parentNode&&(i.parentNode.parentNode&&t._originalElement&&t._originalElement!==i.parentNode?i.parentNode.parentNode.removeChild(i.parentNode):i.parentNode.removeChild(i))}t._window.removeEventListener("message",t._onMessage),W.isEnabled&&W.off("fullscreenchange",t.fullscreenchangeHandler),n()})}},{key:"getAutopause",value:function(){return this.get("autopause")}},{key:"setAutopause",value:function(t){return this.set("autopause",t)}},{key:"getBuffered",value:function(){return this.get("buffered")}},{key:"getCameraProps",value:function(){return this.get("cameraProps")}},{key:"setCameraProps",value:function(t){return this.set("cameraProps",t)}},{key:"getChapters",value:function(){return this.get("chapters")}},{key:"getCurrentChapter",value:function(){return this.get("currentChapter")}},{key:"getColor",value:function(){return this.get("color")}},{key:"getColors",value:function(){return G.all([this.get("colorOne"),this.get("colorTwo"),this.get("colorThree"),this.get("colorFour")])}},{key:"setColor",value:function(t){return this.set("color",t)}},{key:"setColors",value:function(t){if(!Array.isArray(t))return new G(function(o,a){return a(new TypeError("Argument must be an array."))});var n=new G(function(o){return o(null)}),i=[t[0]?this.set("colorOne",t[0]):n,t[1]?this.set("colorTwo",t[1]):n,t[2]?this.set("colorThree",t[2]):n,t[3]?this.set("colorFour",t[3]):n];return G.all(i)}},{key:"getCuePoints",value:function(){return this.get("cuePoints")}},{key:"getCurrentTime",value:function(){return this.get("currentTime")}},{key:"setCurrentTime",value:function(t){return this.set("currentTime",t)}},{key:"getDuration",value:function(){return this.get("duration")}},{key:"getEnded",value:function(){return this.get("ended")}},{key:"getLoop",value:function(){return this.get("loop")}},{key:"setLoop",value:function(t){return this.set("loop",t)}},{key:"setMuted",value:function(t){return this.set("muted",t)}},{key:"getMuted",value:function(){return this.get("muted")}},{key:"getPaused",value:function(){return this.get("paused")}},{key:"getPlaybackRate",value:function(){return this.get("playbackRate")}},{key:"setPlaybackRate",value:function(t){return this.set("playbackRate",t)}},{key:"getPlayed",value:function(){return this.get("played")}},{key:"getQualities",value:function(){return this.get("qualities")}},{key:"getQuality",value:function(){return this.get("quality")}},{key:"setQuality",value:function(t){return this.set("quality",t)}},{key:"getRemotePlaybackAvailability",value:function(){return this.get("remotePlaybackAvailability")}},{key:"getRemotePlaybackState",value:function(){return this.get("remotePlaybackState")}},{key:"getSeekable",value:function(){return this.get("seekable")}},{key:"getSeeking",value:function(){return this.get("seeking")}},{key:"getTextTracks",value:function(){return this.get("textTracks")}},{key:"getVideoEmbedCode",value:function(){return this.get("videoEmbedCode")}},{key:"getVideoId",value:function(){return this.get("videoId")}},{key:"getVideoTitle",value:function(){return this.get("videoTitle")}},{key:"getVideoWidth",value:function(){return this.get("videoWidth")}},{key:"getVideoHeight",value:function(){return this.get("videoHeight")}},{key:"getVideoUrl",value:function(){return this.get("videoUrl")}},{key:"getVolume",value:function(){return this.get("volume")}},{key:"setVolume",value:function(t){return this.set("volume",t)}},{key:"setTimingSrc",value:function(){var e=X($().mark(function n(i,o){var a=this,c;return $().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(i){s.next=2;break}throw new TypeError("A Timing Object must be provided.");case 2:return s.next=4,this.ready();case 4:return c=new Ct(this,i,o),K(this,"notifyTimingObjectConnect"),c.addEventListener("disconnect",function(){return K(a,"notifyTimingObjectDisconnect")}),s.abrupt("return",c);case 8:case"end":return s.stop()}},n,this)}));function t(n,i){return e.apply(this,arguments)}return t}()}]),r}();Ne||(W=Tt(),kt(),_t(),Pt(),Et());const St={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Mt={class:"p-8"},Ot={class:"sr-only"},jt={class:"mx-8 space-y-4 py-4"},Lt={"aria-labelledby":"video"},Rt={class:"w-full overflow-hidden rounded-xl shadow-md"},At={class:"group relative"},Ft={key:0,class:"relative aspect-video w-full"},Nt={key:0,class:"absolute z-0 flex h-full w-full items-center justify-center bg-gray-800 opacity-50"},It={class:"flex w-full flex-col items-center justify-center"},Dt={class:"mx-auto grid w-full grid-cols-1 justify-center gap-4 px-4 md:grid-cols-2 2xl:w-2/3"},Vt={key:0,class:"absolute -z-10 h-full w-full bg-gray-500 opacity-25"},qt=["src"],$t={class:"relative"},zt=["src"],Wt={key:1},Bt=["src"],Ut={"aria-labelledby":"video-description"},Gt={class:"overflow-visible"},Yt={class:"mt-8 font-intro text-3xl leading-tight font-bold text-gray-900"},Ht={class:"mt-4 inline-flex flex-col items-start sm:flex-row sm:items-center"},Qt={class:"text-sm font-semibold text-indigo-500 uppercase"},Jt={key:0,class:"mt-3 ml-0 flex items-center sm:mt-0 sm:ml-4"},Kt={key:0,class:"ml-0 sm:ml-4"},Xt={key:1,class:"ml-0 sm:ml-4"},Zt={class:"mt-3 flex flex-col md:flex-row"},er={class:"flex flex-row"},tr={class:"inline-flex h-8 items-center text-sm leading-4 font-medium"},rr={class:"group mr-4 ml-4 inline-flex rounded-md"},nr={class:"rounded-full p-1 transition duration-250 ease-in-out group-hover:bg-red-50"},ir={class:"ml-1 inline text-gray-700 transition duration-250 ease-in-out group-hover:text-red-600"},or={key:1,class:"mr-4 inline-flex items-center text-sm leading-4 font-medium text-gray-700"},ar={class:"ml-2 inline text-red-600"},sr={key:0,class:"mr-4 inline-flex items-center"},ur=["href"],lr={key:0},cr={class:"mr-4 ml-4 inline-flex items-center"},dr={class:"mt-4 font-intro text-base leading-6 font-medium text-gray-600"},fr={class:"mt-6 inline-flex"},hr={class:"text-sm"},mr=["href"],pr={class:"inline"},vr={key:0,class:"border-b border-gray-200 pt-8"},gr={key:1},yr={class:"mt-4"},wr=["src"],br={class:"flex grow flex-col font-intro"},kr={class:"flex items-center text-lg leading-5 font-bold text-gray-900"},_r={key:0},Pr={key:0,class:"ml-4"},Er={class:"mt-1 text-sm font-medium text-gray-400"},Tr={class:"mt-2 flex items-center text-base leading-5 font-semibold text-gray-600"},xr={key:0,class:""},Cr={key:1,class:"text-gray-400"},Sr={key:2,class:"border-b border-gray-200 pt-8"},Mr={key:3},Or={class:"mt-4 grid grid-cols-1 gap-4"},jr={class:"bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Lr={key:0,class:"mb-8"},Rr={key:1,"aria-labelledby":"assignment-panel",class:"mb-8"},Ar={"aria-labelledby":"related-videos-panel"},Fr={class:"xs:grid-cols-1 mt-2 grid gap-8 sm:grid-cols-2 lg:flow-root"},Sn={__name:"Show",props:{video:Object,favorite:Boolean,relatedVideos:Object,likes:Number,activities:Array,completed:Object,assignment:{type:Object,required:!1}},setup(r){const e=r;let t=J(e.favorite);const n=[{name:"Watch",href:"/watch",current:!1},{name:e.video.title,href:"#",current:!0}];let i=null,o=J(0);const a=J(e.completed.last_interaction&&e.completed.last_interaction.current_time),c=(m,w=!1)=>{const y=document.querySelector("iframe");i=new _e(y);let F=!1;i.setCurrentTime(m).catch(M=>{M.name==="RangeError"?(console.log("Timestamp exceeds video duration. Starting from the beginning."),i.setCurrentTime(0)):console.error("Error setting video time:",M)}),w&&i.play().catch(M=>{console.error("Error attempting to autoplay:",M)}),i.on("pause",M=>{axios.post("/track-video",{video_id:e.video.id,user_id:I().props.user.id,event:"pause",current_time:Math.floor(M.seconds)})});let L=null;i.on("seeked",M=>{clearTimeout(L),M.seconds!==0&&(L=setTimeout(()=>{axios.post("/track-video",{video_id:e.video.id,user_id:I().props.user.id,event:"seek",current_time:Math.floor(M.seconds)})},2e3))}),i.on("timeupdate",M=>{const u=Math.floor(M.seconds),d=Math.floor(M.duration)-u;u-o.value>=10&&!F&&(o.value=u,axios.post("/track-video",{video_id:e.video.id,user_id:I().props.user.id,event:"timeupdate",current_time:u})),!F&&d<=25&&(F=!0,axios.post("/track-video",{video_id:e.video.id,user_id:I().props.user.id,event:"fully_watched",current_time:u,assignment_id:e.assignment?e.assignment.assignment_completion_id:null}).then(x=>{x.data.assignment_completed&&Ee.reload({only:["assignment"],preserveState:!0,preserveScroll:!0,replace:!0})}))}),i.on("ended",()=>{axios.post("/track-video",{video_id:e.video.id,user_id:I().props.user.id,event:"ended"})})};$e(()=>{I().props.authenticated&&!a.value&&c(0)});const f=J(e.likes),s=()=>{t.value=!t.value,t.value?f.value++:f.value--,Ee.post("/api/toggle-video-favorite",{video_id:e.video.id},{preserveState:!0,preserveScroll:!0,onError:()=>{t.value=!t.value}})},E=J(null);let P=J(e.relatedVideos);const{items:C}=Je(P.value,E),T=m=>"Published on "+new Intl.DateTimeFormat("en-US",{month:"long",day:"numeric",year:"numeric"}).format(new Date(m)),k=m=>m.chapter?route("read.section-chapter",{author:m.author,work:m.work,book:m.l1.toLowerCase()+"-"+m.book,chapter:m.l2.toLowerCase()+"-"+m.chapter,line_start:m.line_start,line_end:m.line_end}):route("read.section",{author:m.author,work:m.work,book:m.l1.toLowerCase()+"-"+m.book,line_start:m.line_start,line_end:m.line_end}),D=["KB9rUZg2","ABIGts7p","cMAdiNGB","IRq9GwEY"];let R=J(!1);const q=()=>{R.value=!0,axios.post(route("watch.download"),{id:e.video.id},{responseType:"blob"}).then(m=>{const w=m.headers["content-disposition"];let y="video.mp4";if(w){const M=w.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);M&&M[1]&&(y=M[1].replace(/['"]/g,""))}const F=window.URL.createObjectURL(new Blob([m.data])),L=document.createElement("a");L.href=F,L.setAttribute("download",y),document.body.appendChild(L),L.click(),document.body.removeChild(L),window.URL.revokeObjectURL(F),R.value=!1}).catch(m=>{R.value=!1,console.error("Download error:",m)})},g=()=>e.video.youtube_id&&new Date(e.video.youtube_published_at)<new Date,h=m=>{const w=Math.floor(m/60),y=m%60;return`${w}:${y<10?"0":""}${y}`},v=()=>{a.value=!1,c(e.completed.last_interaction.current_time,!0)},_=()=>{a.value=!1,Ue(()=>{c(0,!0)})};return(m,w)=>{const y=Pe("Head"),F=Pe("Link"),L=ze("tippy");return S(),ie(Ge,null,{default:ue(()=>[Y(y,null,{default:ue(()=>[p("title",null,z(r.video.title),1)]),_:1}),p("main",St,[p("div",Mt,[Y(Ye,{class:"lg:col-span-9 xl:grid-cols-10",pages:n})]),p("h1",Ot,z(r.video.title),1),p("div",jt,[p("section",Lt,[p("div",Rt,[p("div",At,[A(I)().props.authenticated&&!A(I)().props.user.classes.filter(M=>D.includes(M.slug)).length>0&&(A(I)().props.user.membership.subscribed||A(I)().props.user.membership.onTrial)?(S(),O("div",Ft,[a.value?(S(),O("div",Nt,[p("div",It,[p("div",Dt,[p("button",{onClick:_,class:"flex flex-col items-center justify-center rounded-xl bg-slate-300 px-4 py-2 text-xl text-gray-900 opacity-75 transition duration-250 hover:bg-slate-200 hover:opacity-75"},[Y(A(ot),{class:"h-10 w-10"}),w[1]||(w[1]=Te(" Start Over "))]),p("button",{onClick:v,class:"flex flex-col items-center justify-center rounded-xl bg-slate-500 px-4 py-2 text-xl text-white opacity-75 transition duration-250 hover:bg-slate-600 hover:opacity-75"},[Y(A(tt),{class:"h-10 w-10"}),Te(" Resume at "+z(h(r.completed.last_interaction.current_time)),1)])])]),r.completed.last_interaction?(S(),O("div",Vt,[p("img",{src:r.video.thumbnail,alt:""},null,8,qt)])):V("",!0)])):V("",!0),le(p("div",$t,[p("iframe",{class:"z-0 aspect-video w-full",src:"https://player.vimeo.com/video/"+r.video.vimeo_id,frameborder:"0",allow:"autoplay; fullscreen",allowfullscreen:""},null,8,zt)],512),[[We,!a.value]])])):(S(),O("div",Wt,[p("iframe",{class:"embed-responsive-item aspect-video w-full",src:"https://www.youtube.com/embed/"+r.video.youtube_id},null,8,Bt)]))])])]),p("section",Ut,[p("div",Gt,[p("div",null,[p("h3",Yt,z(r.video.title),1),p("div",Ht,[p("div",null,[p("h5",Qt,z(T(r.video.published_at)),1)]),g()?V("",!0):(S(),O("div",Jt,[w[4]||(w[4]=p("span",{class:"hidden font-semibold text-gray-400 sm:inline-block"},"|",-1)),r.video.youtube_id?V("",!0):(S(),O("span",Kt,w[2]||(w[2]=[p("div",{class:"text-sm font-semibold text-green-600 uppercase"}," LatinTutorial Exclusive ",-1)]))),r.video.youtube_id&&new Date(r.video.youtube_published_at)>new Date?(S(),O("span",Xt,w[3]||(w[3]=[p("div",{class:"text-sm font-semibold text-indigo-500 uppercase"}," Early Access ",-1)]))):V("",!0)]))]),p("div",Zt,[p("div",er,[p("div",tr,z(r.video.views.toLocaleString())+" Views ",1),p("div",rr,[A(I)().props.authenticated&&(A(I)().props.user.membership.subscribed||A(I)().props.user.membership.onTrial)?le((S(),O("button",{key:0,class:"inline-flex h-8 items-center rounded-full pr-2 text-sm leading-4 font-medium focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:outline-hidden",onClick:s},[p("div",nr,[(S(),O("svg",{class:ve([A(t)?"fill-current text-red-600":"fill-none","mx-auto h-5 w-5 transform stroke-current text-gray-700 transition-all duration-250 group-hover:text-red-600"]),fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",stroke:"currentColor",viewBox:"0 0 24 24"},w[5]||(w[5]=[p("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"},null,-1)]),2))]),p("div",ir,z(f.value.toLocaleString()+" "+A(He)("like",e.likeCount)),1)])),[[L,{content:A(t)?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]):(S(),O("div",or,[w[6]||(w[6]=p("svg",{class:"mx-auto h-5 w-5 transform stroke-current text-gray-700 text-red-600 transition-all duration-1000",fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",stroke:"currentColor",viewBox:"0 0 24 24"},[p("path",{d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})],-1)),p("div",ar,z(f.value),1)]))]),g()?(S(),O("div",sr,[p("a",{href:`http://www.youtube.com/watch?v=${r.video.youtube_id}`,target:"”_blank”",class:"cursor-pointer items-center"},w[7]||(w[7]=[p("svg",{viewBox:"0 0 48 48",class:"inline h-8"},[p("path",{fill:"#FF0000",d:"M43.2,33.9c-0.4,2.1-2.1,3.7-4.2,4c-3.3,0.5-8.8,1.1-15,1.1c-6.1,0-11.6-0.6-15-1.1c-2.1-0.3-3.8-1.9-4.2-4C4.4,31.6,4,28.2,4,24c0-4.2,0.4-7.6,0.8-9.9c0.4-2.1,2.1-3.7,4.2-4C12.3,9.6,17.8,9,24,9c6.2,0,11.6,0.6,15,1.1c2.1,0.3,3.8,1.9,4.2,4c0.4,2.3,0.9,5.7,0.9,9.9C44,28.2,43.6,31.6,43.2,33.9z"}),p("path",{fill:"#FFF",d:"M20 31L20 17 32 24z"})],-1)]),8,ur)])):V("",!0),A(I)().props.authenticated&&A(I)().props.user.membership.subscribed?le((S(),O("button",{key:1,onClick:q,class:ve(["inline-flex cursor-pointer items-center rounded-full px-2 py-1 font-medium text-gray-700 transition ease-in-out hover:bg-gray-200 hover:text-gray-600 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:outline-hidden",A(R)?"bg-gray-200":""])},[Y(Be,{mode:"out-in","enter-active-class":"transition ease-in-out duration-300 transform","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in-out duration-300 transform","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:ue(()=>[A(R)?(S(),O("div",lr,w[8]||(w[8]=[p("svg",{class:"h-5 w-5 animate-spin",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48"},[p("path",{fill:"#4b5563",d:"M24,44C12.972,44,4,35.028,4,24S12.972,4,24,4s20,8.972,20,20S35.028,44,24,44z M24,8 C15.178,8,8,15.178,8,24s7.178,16,16,16s16-7.178,16-16S32.822,8,24,8z"}),p("path",{fill:"#9ca3af",d:"M24,44C12.972,44,4,35.028,4,24h4c0,8.822,7.178,16,16,16s16-7.178,16-16S32.822,8,24,8V4 c11.028,0,20,8.972,20,20S35.028,44,24,44z"})],-1)]))):(S(),ie(A(it),{key:1,class:"h-5 w-5 transform stroke-current stroke-2 text-gray-700 transition-all duration-250"}))]),_:1})],2)),[[L,{content:"Download this video",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]):V("",!0),p("div",cr,[r.completed.last_interaction?le((S(),ie(A(rt),{key:0,class:ve(["h-6 w-6 stroke-2 text-green-500",r.completed.is_completed?"text-green-500":"text-amber-400"])},null,8,["class"])),[[L,{content:r.completed.is_completed?"You watched this video on "+new Date(r.completed.completed_at).toLocaleDateString():"You've started watching this video",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]):V("",!0)])])]),p("p",dr,z(r.video.description),1)]),p("div",fr,[(S(!0),O(ye,null,ge(r.video.tags,M=>(S(),O("button",{key:M,class:"mr-2 flex h-8 items-center rounded-full border-2 border-sky-600 bg-sky-50 px-4 py-1 font-medium text-sky-700 transition ease-in-out hover:text-sky-600 focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:outline-hidden"},[p("div",hr,[p("a",{href:"/watch?topics[]="+M.id},[p("div",pr,z(M.name),1)],8,mr)])]))),128))])])]),r.video.section?(S(),O("div",vr)):V("",!0),r.video.section?(S(),O("section",gr,[w[9]||(w[9]=p("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Read This ",-1)),p("div",yr,[Y(F,{href:k(r.video.section),class:"group flex cursor-pointer flex-row items-center rounded-xl px-2 py-6 transition duration-250 ease-in-out hover:bg-slate-50 sm:px-4"},{default:ue(()=>[p("img",{class:"mx-auto mr-4 size-20 rounded-full",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+r.video.section.image,alt:""},null,8,wr),p("div",br,[p("h3",kr,[p("span",null,z(r.video.section.title),1),A(I)().props.authenticated?(S(),O("span",_r,[A(I)().props.user.id===1&&r.video.section.syntax_verified?(S(),O("span",Pr,[Y(A(nt),{class:"-mt-2 inline h-6 w-6 stroke-2 text-green-500"})])):V("",!0)])):V("",!0)]),p("span",Er,z(r.video.section.token_count)+" words",1),p("p",Tr,[r.video.section.description?(S(),O("span",xr,z(r.video.section.description),1)):(S(),O("span",Cr,z(r.video.section.verse),1))])])]),_:1},8,["href"])])])):V("",!0),r.activities.length>0?(S(),O("div",Sr)):V("",!0),r.activities.length>0?(S(),O("section",Mr,[w[10]||(w[10]=p("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Practice This ",-1)),p("div",Or,[(S(!0),O(ye,null,ge(r.activities,M=>(S(),ie(Ke,{key:M.id,activity:M},null,8,["activity"]))),128))])])):V("",!0)]),Y(Xe)]),p("aside",jr,[A(I)().props.authenticated?V("",!0):(S(),O("section",Lr,[Y(Ze)])),r.assignment?(S(),O("section",Rr,[Y(et,{assignment:r.assignment,onCompleted:w[0]||(w[0]=()=>{m.emit("completed")})},null,8,["assignment"])])):V("",!0),p("section",Ar,[w[11]||(w[11]=p("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Related Videos ",-1)),p("div",Fr,[(S(!0),O(ye,null,ge(A(C),M=>(S(),ie(Qe,{item:M,key:M.id,class:"group lg:pt-4"},null,8,["item"]))),128)),p("div",{ref_key:"landmark",ref:E},null,512)])])])]),_:1})}}};export{Sn as default};
