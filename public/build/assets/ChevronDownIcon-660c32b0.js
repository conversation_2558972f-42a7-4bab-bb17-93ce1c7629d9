import{o as c,u as F,A as P,i as D,N as A}from"./render-c34c346a.js";import{w as N}from"./use-outside-click-484df218.js";import{s as L}from"./use-resolve-button-type-24d8b5c5.js";import{p as $}from"./use-text-value-2c18b2b1.js";import{u as K,f as _,c as I}from"./calculate-active-index-51ff911b.js";import{i as j}from"./use-tree-walker-100527b8.js";import{t as U,i as w,l as V}from"./open-closed-7f51e238.js";import{o as v}from"./keyboard-982fc047.js";import{w as q,h as H,v as J,N as O,_ as E,O as Q}from"./focus-management-8406d052.js";import{H as k,e as M,l as R,I as Z,A as z,B as W,J as G,z as x,K as X,o as Y,d as ee,a as te}from"./app-f0078ddb.js";var ae=(a=>(a[a.Open=0]="Open",a[a.Closed=1]="Closed",a))(ae||{}),ne=(a=>(a[a.Pointer=0]="Pointer",a[a.Other=1]="Other",a))(ne||{});function le(a){requestAnimationFrame(()=>requestAnimationFrame(a))}let C=Symbol("MenuContext");function T(a){let m=X(C,null);if(m===null){let g=new Error(`<${a} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(g,T),g}return m}let fe=k({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(a,{slots:m,attrs:g}){let f=M(1),b=M(null),d=M(null),e=M([]),s=M(""),o=M(null),S=M(1);function h(t=l=>l){let l=o.value!==null?e.value[o.value]:null,n=Q(t(e.value.slice()),p=>c(p.dataRef.domRef)),u=l?n.indexOf(l):null;return u===-1&&(u=null),{items:n,activeItemIndex:u}}let r={menuState:f,buttonRef:b,itemsRef:d,items:e,searchQuery:s,activeItemIndex:o,activationTrigger:S,closeMenu:()=>{f.value=1,o.value=null},openMenu:()=>f.value=0,goToItem(t,l,n){let u=h(),p=_(t===I.Specific?{focus:I.Specific,id:l}:{focus:t},{resolveItems:()=>u.items,resolveActiveIndex:()=>u.activeItemIndex,resolveId:i=>i.id,resolveDisabled:i=>i.dataRef.disabled});s.value="",o.value=p,S.value=n??1,e.value=u.items},search(t){let l=s.value!==""?0:1;s.value+=t.toLowerCase();let n=(o.value!==null?e.value.slice(o.value+l).concat(e.value.slice(0,o.value+l)):e.value).find(p=>p.dataRef.textValue.startsWith(s.value)&&!p.dataRef.disabled),u=n?e.value.indexOf(n):-1;u===-1||u===o.value||(o.value=u,S.value=1)},clearSearch(){s.value=""},registerItem(t,l){let n=h(u=>[...u,{id:t,dataRef:l}]);e.value=n.items,o.value=n.activeItemIndex,S.value=1},unregisterItem(t){let l=h(n=>{let u=n.findIndex(p=>p.id===t);return u!==-1&&n.splice(u,1),n});e.value=l.items,o.value=l.activeItemIndex,S.value=1}};return N([b,d],(t,l)=>{var n;r.closeMenu(),q(l,H.Loose)||(t.preventDefault(),(n=c(b))==null||n.focus())},R(()=>f.value===0)),Z(C,r),U(R(()=>F(f.value,{0:w.Open,1:w.Closed}))),()=>{let t={open:f.value===0,close:r.closeMenu};return P({ourProps:{},theirProps:a,slot:t,slots:m,attrs:g,name:"Menu"})}}}),be=k({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(a,{attrs:m,slots:g,expose:f}){var b;let d=(b=a.id)!=null?b:`headlessui-menu-button-${D()}`,e=T("MenuButton");f({el:e.buttonRef,$el:e.buttonRef});function s(r){switch(r.key){case v.Space:case v.Enter:case v.ArrowDown:r.preventDefault(),r.stopPropagation(),e.openMenu(),x(()=>{var t;(t=c(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(I.First)});break;case v.ArrowUp:r.preventDefault(),r.stopPropagation(),e.openMenu(),x(()=>{var t;(t=c(e.itemsRef))==null||t.focus({preventScroll:!0}),e.goToItem(I.Last)});break}}function o(r){switch(r.key){case v.Space:r.preventDefault();break}}function S(r){a.disabled||(e.menuState.value===0?(e.closeMenu(),x(()=>{var t;return(t=c(e.buttonRef))==null?void 0:t.focus({preventScroll:!0})})):(r.preventDefault(),e.openMenu(),le(()=>{var t;return(t=c(e.itemsRef))==null?void 0:t.focus({preventScroll:!0})})))}let h=L(R(()=>({as:a.as,type:m.type})),e.buttonRef);return()=>{var r;let t={open:e.menuState.value===0},{...l}=a,n={ref:e.buttonRef,id:d,type:h.value,"aria-haspopup":"menu","aria-controls":(r=c(e.itemsRef))==null?void 0:r.id,"aria-expanded":e.menuState.value===0,onKeydown:s,onKeyup:o,onClick:S};return P({ourProps:n,theirProps:l,slot:t,attrs:m,slots:g,name:"MenuButton"})}}}),Ie=k({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(a,{attrs:m,slots:g,expose:f}){var b;let d=(b=a.id)!=null?b:`headlessui-menu-items-${D()}`,e=T("MenuItems"),s=M(null);f({el:e.itemsRef,$el:e.itemsRef}),j({container:R(()=>c(e.itemsRef)),enabled:R(()=>e.menuState.value===0),accept(t){return t.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:t.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute("role","none")}});function o(t){var l;switch(s.value&&clearTimeout(s.value),t.key){case v.Space:if(e.searchQuery.value!=="")return t.preventDefault(),t.stopPropagation(),e.search(t.key);case v.Enter:if(t.preventDefault(),t.stopPropagation(),e.activeItemIndex.value!==null){let n=e.items.value[e.activeItemIndex.value];(l=c(n.dataRef.domRef))==null||l.click()}e.closeMenu(),E(c(e.buttonRef));break;case v.ArrowDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(I.Next);case v.ArrowUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(I.Previous);case v.Home:case v.PageUp:return t.preventDefault(),t.stopPropagation(),e.goToItem(I.First);case v.End:case v.PageDown:return t.preventDefault(),t.stopPropagation(),e.goToItem(I.Last);case v.Escape:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>{var n;return(n=c(e.buttonRef))==null?void 0:n.focus({preventScroll:!0})});break;case v.Tab:t.preventDefault(),t.stopPropagation(),e.closeMenu(),x(()=>J(c(e.buttonRef),t.shiftKey?O.Previous:O.Next));break;default:t.key.length===1&&(e.search(t.key),s.value=setTimeout(()=>e.clearSearch(),350));break}}function S(t){switch(t.key){case v.Space:t.preventDefault();break}}let h=V(),r=R(()=>h!==null?(h.value&w.Open)===w.Open:e.menuState.value===0);return()=>{var t,l;let n={open:e.menuState.value===0},{...u}=a,p={"aria-activedescendant":e.activeItemIndex.value===null||(t=e.items.value[e.activeItemIndex.value])==null?void 0:t.id,"aria-labelledby":(l=c(e.buttonRef))==null?void 0:l.id,id:d,onKeydown:o,onKeyup:S,role:"menu",tabIndex:0,ref:e.itemsRef};return P({ourProps:p,theirProps:u,slot:n,attrs:m,slots:g,features:A.RenderStrategy|A.Static,visible:r.value,name:"MenuItems"})}}}),ge=k({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(a,{slots:m,attrs:g,expose:f}){var b;let d=(b=a.id)!=null?b:`headlessui-menu-item-${D()}`,e=T("MenuItem"),s=M(null);f({el:s,$el:s});let o=R(()=>e.activeItemIndex.value!==null?e.items.value[e.activeItemIndex.value].id===d:!1),S=$(s),h=R(()=>({disabled:a.disabled,get textValue(){return S()},domRef:s}));z(()=>e.registerItem(d,h)),W(()=>e.unregisterItem(d)),G(()=>{e.menuState.value===0&&o.value&&e.activationTrigger.value!==0&&x(()=>{var i,y;return(y=(i=c(s))==null?void 0:i.scrollIntoView)==null?void 0:y.call(i,{block:"nearest"})})});function r(i){if(a.disabled)return i.preventDefault();e.closeMenu(),E(c(e.buttonRef))}function t(){if(a.disabled)return e.goToItem(I.Nothing);e.goToItem(I.Specific,d)}let l=K();function n(i){l.update(i)}function u(i){l.wasMoved(i)&&(a.disabled||o.value||e.goToItem(I.Specific,d,0))}function p(i){l.wasMoved(i)&&(a.disabled||o.value&&e.goToItem(I.Nothing))}return()=>{let{disabled:i,...y}=a,B={active:o.value,disabled:i,close:e.closeMenu};return P({ourProps:{id:d,ref:s,role:"menuitem",tabIndex:i===!0?void 0:-1,"aria-disabled":i===!0?!0:void 0,onClick:r,onFocus:t,onPointerenter:n,onMouseenter:n,onPointermove:u,onMousemove:u,onPointerleave:p,onMouseleave:p},theirProps:{...g,...y},slot:B,attrs:g,slots:m,name:"MenuItem"})}}});function Se(a,m){return Y(),ee("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[te("path",{"fill-rule":"evenodd",d:"M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}export{Ie as M,be as S,ge as b,fe as g,Se as r};
