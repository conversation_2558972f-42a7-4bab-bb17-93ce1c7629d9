import{e as u,V as B,p as D,a1 as O,o as v,c as V,w as p,a as e,b as d,u as l,j as c,q as f,x as b,d as M,h as U,n as $,F as j,W as G}from"./app-f0078ddb.js";import{_ as I}from"./ClassroomModal-05d10768.js";import y from"./SelectDate-fab0bbcb.js";import{_ as R}from"./Combobox-f427c07d.js";import{r as T}from"./RectangleGroupIcon-04390470.js";/* empty css            */import"./ButtonItem-718c0517.js";import"./XMarkIcon-9bc7c0bd.js";import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./index-b0adb136.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./use-resolve-button-type-24d8b5c5.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";const F={class:"flex flex-row items-center justify-center"},N={class:"inline h-10 w-10 rounded-full bg-orange-100 transition duration-150 group-hover:bg-orange-200"},W={class:"py-4"},z={class:"flex h-12 w-full flex-row items-center"},E={class:"flex h-12 w-full grow flex-row items-center"},L={class:"flex h-12 w-full grow flex-row items-center"},P={class:"my-4 text-left"},q={class:"mt-4 flex w-full flex-row items-center"},H={class:"mx-auto h-8 items-center space-x-1 self-center rounded-lg bg-gray-200 p-0.5"},J={class:"mt-4 flex flex-row items-center"},K={class:"mx-auto h-8 items-center space-x-1 self-center rounded-lg p-0.5"},Q={class:"mt-4 flex flex-row items-center"},X={class:"mx-auto h-8 w-1/2 items-center space-x-1 self-center rounded-lg p-0.5"},Y={class:"grid w-full grid-cols-10 gap-1 opacity-75"},Z=["onClick"],ee=["disabled"],Oe={__name:"AssignGrammar",props:{team:Object,open:Boolean,activities:Array},emits:["close-modal"],setup(m,{emit:w}){let g=m,i=u(""),r=u(""),s=B({time:10,correct:100,accuracy:null}),a=u(null);const h=w,x=()=>{h("close-modal")},_=()=>{setTimeout(()=>{i.value="",r.value="",s.time=10,s.correct=100,s.accuracy=null,a.value=null},250)};D(()=>g.open,n=>{n||_()});const S=()=>{G.post("/api/assign/store",{team_id:g.team.id,due_at:i.value,publish_at:r.value?r.value:new Date,section:`grammar:section-${a.value}${s.time?":time-"+s.time:""}${s.correct?":correct-"+s.correct:""}${s.accuracy?":accuracy-"+s.accuracy:""}`},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{x()}})},k=n=>{if(s.accuracy==n){s.accuracy=null;return}s.accuracy=n},C=[{value:5,color:"bg-red-500"},{value:15,color:"bg-orange-600"},{value:25,color:"bg-amber-500"},{value:35,color:"bg-yellow-600"},{value:45,color:"bg-lime-600"},{value:55,color:"bg-green-600"},{value:65,color:"bg-teal-600"},{value:75,color:"bg-blue-600"},{value:85,color:"bg-indigo-600"},{value:95,color:"bg-purple-600"}];return(n,t)=>{const A=O("tippy");return v(),V(I,{open:m.open,onCloseModal:t[6]||(t[6]=o=>x())},{title:p(()=>[e("div",F,[e("div",N,[d(l(T),{class:"mt-2 ml-2 h-6 w-6 stroke-2 text-orange-600"})]),t[7]||(t[7]=e("span",{class:"ml-2 text-4xl font-semibold tracking-tight text-orange-500"}," Grammar ",-1))])]),main:p(()=>[e("div",W,[e("div",z,[t[8]||(t[8]=e("div",{class:"flex inline grow text-xs font-bold text-gray-500 uppercase"}," Activity ",-1)),d(R,{items:m.activities,"onUpdate:modelValue":t[0]||(t[0]=o=>c(a)?a.value=o.id:a=o.id)},null,8,["items"])]),e("div",E,[t[9]||(t[9]=e("div",{class:"flex inline grow text-xs font-bold text-gray-500 uppercase"}," Due on ",-1)),d(y,{class:"inline pl-4",color:"orange","onUpdate:selected":t[1]||(t[1]=o=>o?c(i)?i.value=o.toISOString():i=o.toISOString():c(i)?i.value="":i="")})]),e("div",L,[t[10]||(t[10]=e("div",{class:"flex inline grow text-xs font-bold text-gray-500 uppercase"}," Publish on ",-1)),d(y,{class:"inline pl-4",publish:!0,color:"orange","onUpdate:selected":t[2]||(t[2]=o=>o?c(r)?r.value=o.toISOString():r=o.toISOString():c(r)?r.value="":r="")})]),e("div",P,[t[14]||(t[14]=e("p",{class:"text-xs font-bold text-gray-500 uppercase"}," Requirements (Select at least one) ",-1)),e("div",q,[t[11]||(t[11]=e("div",{class:"flex grow flex-col text-left"},[e("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Time in Minutes"),e("span",{class:"text-sm text-gray-500"},"Require a certain amount of time.")],-1)),e("div",H,[f(e("input",{class:"h-8 w-20 rounded-md py-1 text-center text-base leading-5 font-semibold ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden","onUpdate:modelValue":t[3]||(t[3]=o=>l(s).time=o)},null,512),[[b,l(s).time]])])]),e("div",J,[t[12]||(t[12]=e("div",{class:"flex grow flex-col text-left"},[e("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Total Correct"),e("span",{class:"text-sm text-gray-500"},"Require a certain number of correct words.")],-1)),e("div",K,[f(e("input",{class:"ml-2 h-8 w-20 rounded-md py-1 text-center text-base leading-5 font-semibold ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden","onUpdate:modelValue":t[4]||(t[4]=o=>l(s).correct=o)},null,512),[[b,l(s).correct]])])]),e("div",Q,[t[13]||(t[13]=e("div",{class:"flex grow flex-col text-left"},[e("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Minimum Accuracy")],-1)),e("div",X,[e("div",Y,[(v(),M(j,null,U(C,o=>f(e("button",{class:$(["h-6 w-full rounded-sm shadow-sm",!l(s).accuracy||l(s).accuracy==o.value?o.color:"bg-gray-300"]),key:o.value,onClick:te=>k(o.value)},null,10,Z),[[A,{content:o.value+"%",placement:"bottom",trigger:"mouseenter",hideOnClick:!1}]])),64))])])])])])]),actionButton:p(()=>[e("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-2 py-1 text-base font-medium text-white shadow-xs transition duration-150 hover:bg-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75 sm:text-sm",onClick:t[5]||(t[5]=o=>S()),disabled:!l(a)||l(a)&&!l(s).time&&!l(s).correct&&!l(s).accuracy}," Assign ",8,ee)]),_:1},8,["open"])}}};export{Oe as default};
