import{P as B}from"./Promotion-3eee0057.js";import{i as S,o as n,c as k,w as u,u as a,a as e,b as i,d as c,g as p,f,n as j,k as L,t as d,h as O,F as $}from"./app-f0078ddb.js";import{p as b}from"./pluralize-d25a928b.js";import{D as g}from"./datetime-8ddd27a0.js";import{r as H}from"./XMarkIcon-9bc7c0bd.js";import{r as N}from"./ChevronLeftIcon-2a41c533.js";import{r as _}from"./ChevronRightIcon-0e7ec64c.js";import{h as w,S as D}from"./transition-a0923044.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css            */import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";const z={class:"fixed inset-0 z-40 flex"},V={class:"relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl"},E={class:"flex items-center justify-end px-4"},M={class:"mt-4 px-4"},P={class:"mb-8 grid grid-cols-2 gap-4 text-sm font-medium text-gray-500"},T={key:0,class:"flex"},F={key:0,class:"mb-8"},R={class:"flex flex-col justify-between border-b border-gray-300 pb-8"},U={class:"mx-2 text-sm font-bold text-gray-500 uppercase"},A={key:0},I={key:1},W={class:"mt-2 py-4 transition duration-150 ease-in-out"},q={class:"flex grow flex-col font-intro"},G=["textContent"],J={class:"mt-1 text-sm font-medium text-gray-400"},K={class:"mt-2 flex items-center text-base leading-5 font-semibold text-gray-600"},Q={key:0,class:"line-clamp-2"},X={key:1,class:"line-clamp-2 text-gray-400"},Y={class:"self-center rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-200"},Z={key:0,class:"mx-2 mt-2 text-sm font-bold text-indigo-500"},ee={"aria-labelledby":"Works"},te={class:"mt-8 text-sm font-bold text-gray-500 uppercase"},oe={class:"mt-4 flex flex-col items-center font-intro"},se={class:"flex grow flex-col"},re=["textContent"],ne=["textContent"],we={__name:"MobileSidebar",props:{open:Boolean,recentSection:Object,book:Object,author:Object,sectionHero:Object,nextBook:Object,previousBook:Object,books:Object},emits:["close"],setup(t,{emit:y}){const s=t,v=o=>o.chapter?route("read.section-chapter",{author:s.author.url,work:s.book.work_url,book:s.book.work_l1.toLowerCase()+"-"+s.book.book,chapter:s.book.work_l2.toLowerCase()+"-"+o.chapter,line_start:o.line_start,line_end:o.line_end}):route("read.section",{author:s.author.url,work:s.book.work_url,book:s.book.work_l1.toLowerCase()+"-"+s.book.book,line_start:o.line_start,line_end:o.line_end}),C=o=>o.chapter?s.book.work_l2+" "+o.chapter:b(s.book.work_l4,o.line_end-o.line_start)+" "+o.line_start+"-"+o.line_end,x=o=>route("read.book.show",{author:o.author,work:o.work,book:o.book}),h=y;return(o,r)=>{const m=S("Link");return n(),k(a(D),{as:"template",show:s.open},{default:u(()=>[e("div",{class:"relative z-40 lg:hidden",onClose:r[1]||(r[1]=l=>h("close"))},[i(a(w),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:u(()=>r[2]||(r[2]=[e("div",{class:"fixed inset-0 bg-black opacity-25"},null,-1)])),_:1}),e("div",z,[i(a(w),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:u(()=>[e("div",V,[e("div",E,[e("button",{type:"button",class:"-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-slate-100 focus:ring-2 focus:ring-indigo-500 focus:outline-hidden",onClick:r[0]||(r[0]=l=>h("close"))},[r[3]||(r[3]=e("span",{class:"sr-only"},"Close menu",-1)),i(a(H),{class:"h-6 w-6","aria-hidden":"true"})])]),e("div",M,[e("div",P,[t.previousBook?(n(),c("div",T,[i(m,{class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",href:x(t.previousBook)},{default:u(()=>[i(a(N),{class:"mr-1 inline h-5 w-5 shrink-0","aria-hidden":"true"}),r[4]||(r[4]=p(" Previous "))]),_:1},8,["href"])])):f("",!0),t.nextBook?(n(),c("div",{key:1,class:j(["flex justify-end",{"col-span-2":!t.previousBook}])},[i(m,{class:"flex items-center transition duration-150 ease-in-out hover:text-gray-700",href:x(t.nextBook)},{default:u(()=>[r[5]||(r[5]=p(" Next ")),i(a(_),{class:"ml-1 inline h-5 w-5 shrink-0","aria-hidden":"true"})]),_:1},8,["href"])],2)):f("",!0)]),a(L)().props.authenticated?f("",!0):(n(),c("section",F,[i(B)])),e("section",null,[e("div",R,[e("h5",U,[t.recentSection?(n(),c("span",A,"Most Recent Section")):(n(),c("span",I,"Start Reading"))]),e("div",W,[i(m,{class:"group flex cursor-pointer flex-row items-center rounded-lg px-2 py-4 transition duration-250 ease-in-out hover:bg-slate-100",href:v(t.sectionHero)},{default:u(()=>[e("div",q,[e("h3",{class:"line-clamp-1 text-lg leading-5 font-bold text-gray-900",textContent:d(C(t.sectionHero))},null,8,G),e("span",J,d(t.sectionHero.token_count)+" words",1),e("p",K,[t.sectionHero.description?(n(),c("span",Q,d(t.sectionHero.description),1)):(n(),c("span",X,d(t.sectionHero.verse),1))])]),e("div",Y,[i(a(_),{class:"h-8 w-8 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])]),_:1},8,["href"])]),t.recentSection?(n(),c("p",Z," Last visited on "+d(a(g).fromISO(t.recentSection.last_accessed).toLocaleString(a(g).DATE_MED)),1)):f("",!0)])]),e("section",ee,[e("h5",te," Other "+d(a(b)(t.book.work_l1,t.books.length))+" in this work ",1),e("div",oe,[(n(!0),c($,null,O(t.books,l=>(n(),k(m,{key:l.id,href:`/read/${l.author.url}/${l.work}/${l.work_l1.toLowerCase()}-${l.url}`,class:"group flex w-full cursor-pointer flex-row items-center rounded-lg px-2 py-4 transition duration-150 ease-in-out hover:bg-slate-100"},{default:u(()=>[e("div",se,[e("h4",{class:"font-intro text-lg leading-5 font-bold text-gray-900",textContent:d(l.name)},null,8,re),e("p",{class:"text-xs font-semibold text-gray-500",textContent:d(l.subtitle)},null,8,ne)])]),_:2},1032,["href"]))),128))])])])])]),_:1})])],32)]),_:1},8,["show"])}}};export{we as default};
