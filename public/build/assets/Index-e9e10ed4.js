import{o as i,d as x,a as t,e as p,l as S,V as W,p as X,A as P,z as E,i as G,c as V,w as r,W as k,b as l,g as y,j as C,f as N,Q,u as c,q as _,x as I,t as J,F as R,h as K,v as U,a3 as Y}from"./app-f0078ddb.js";import{_ as ee}from"./AppLayout-33f062bc.js";import{_ as te}from"./Breadcrumbs-c96e9207.js";import{_ as T}from"./ButtonItem-718c0517.js";import se from"./ClassItem-38187622.js";import{u as oe}from"./useIntersect-6e15125e.js";import{_ as re}from"./Footer-0988dcd8.js";import{_ as B}from"./ClassroomModal-05d10768.js";import le from"./PricingComponentIndex-20992e7b.js";import"./SortableEvent-286d5fef.js";import{S as ae}from"./Sortable-ff8f985a.js";import{r as ne}from"./AcademicCapIcon-c9b1e0d5.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./index-b0adb136.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-4bbdc2ab.js";import"./ChevronLeftIcon-2a41c533.js";import"./ChevronRightIcon-0e7ec64c.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";function ie(m,M){return i(),x("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z"})])}function de(m,M){return i(),x("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"})])}const me={class:"relative isolate bg-white px-6 pb-16 lg:px-8"},ue={class:"p-2 md:p-8"},ce={class:"flex flex-row items-center justify-between"},pe={class:"mt-12 grid grid-cols-1 lg:grid-cols-3"},xe={class:"mt-24 lg:col-span-3"},fe={class:"mx-auto mt-8 flex flex-row justify-center gap-4"},ge={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100"},ve={class:"mt-1"},be={key:0,class:"mt-1 text-left text-sm font-medium text-red-600"},ye={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100"},Ce={class:"mt-1"},we={key:0,class:"mt-1 text-left text-sm font-medium text-red-600"},he={class:"mx-auto mt-8 max-w-7xl px-6 lg:px-8"},ke={class:"mt-32 grid grid-cols-1 gap-8 lg:grid-cols-5"},_e={class:"order-2 -m-2 flex rounded-xl bg-gray-900/5 p-2 ring-1 ring-gray-900/10 ring-inset lg:order-1 lg:col-span-3 lg:-m-4 lg:rounded-2xl lg:p-4"},Me={key:0,class:"w-full rounded-lg bg-white px-6 lg:rounded-xl",sortableContainer:""},je=["data-id","data-order"],Se={key:1,class:"flex w-full flex-col items-center rounded-xl bg-white px-6 py-4 py-14 text-center text-sm sm:px-14"},Ve={class:"order-1 -mt-4 lg:order-2 lg:col-span-2",id:"sticky"},Ne={class:"sticky top-20 rounded-xl bg-white p-2 lg:p-4"},Te={class:"grid grid-cols-1 gap-3 px-4 py-4"},Be={class:"relative flex items-center"},$e={class:"flex h-5 items-center"},Ae={class:"relative flex items-center"},Fe={class:"flex h-5 items-center"},Vt={__name:"Index",props:{classes:Object,unarchivedClasses:Array,filters:Array,errors:Object},setup(m){const M=[{name:"Classes",href:"/classes",current:!0}],a=m,j=p(null);let f=p(a.classes||{data:[],next_page_url:null}),u=p(f.value.data||[]);const z=S(()=>a.classes.data.length>0&&a.classes.data[0].subscription&&a.classes.data[0].subscription.tier==="full-classroom"),O=S(()=>f.value.next_page_url!==null),Z=()=>{O.value&&Y.get(f.value.next_page_url).then(o=>{u.value=[...u.value,...o.data.data],f.value=o.data})};j.value&&oe(j,Z,{rootMargin:"0px 0px 300px 0px"});let n=p(a.filters);const g=W({enrollmentCode:""}),q=()=>{n.value=[]},v=p(""),w=p(!1),h=p(!1);let d=p(!1);const D=()=>{w.value=!1,setTimeout(()=>{v.value="",a.errors.createClass&&a.errors.createClass.classroomName&&delete a.errors.createClass.classroomName},400)},H=()=>{h.value=!1,setTimeout(()=>{g.enrollmentCode="",a.errors.joinClass&&a.errors.joinClass.enrollment_code&&delete a.errors.joinClass.enrollment_code},400)},$=S({get(){const o=g.enrollmentCode;return o.length<4?o:o.slice(0,3)+"-"+o.slice(3)},set(o){g.enrollmentCode=o.replace(/-/g,"")}}),A=()=>{k.post("/api/join-classroom",{enrollment_code:g.enrollmentCode},{preserveState:!0,preserveScroll:!0,errorBag:"joinClass",onSuccess:()=>{h.value=!1,setTimeout(()=>{g.enrollmentCode=""},400)}})},F=()=>{k.post("/classes",{classroomName:v.value},{preserveState:!0,errorBag:"createClass",onSuccess:()=>{w.value=!1,setTimeout(()=>{v.value=""},400)}})};let L=u.value.map(o=>o.order);return X(()=>a.classes,async o=>{f.value=o||{data:[],next_page_url:null},u.value=f.value.data||[],u.value.sort((e,b)=>e.order-b.order),await E(),L=u.value.map(e=>e.order)},{deep:!0}),X(()=>n.value,()=>{k.get("/classes",{filters:n.value},{only:["classes"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),P(()=>{const o=new ae(document.querySelectorAll("[sortableContainer]"),{draggable:".draggable",handle:".draggable-handle",mirror:{constrainDimensions:!0}});o.on("sortable:stop",()=>{E(()=>{const e=Array.from(document.querySelectorAll("[sortableContainer] > .draggable")).map((b,s)=>({slug:b.dataset.id,order:L[s]}));k.post("/api/classes/sort",{items:e},{preserveState:!0,preserveScroll:!0})})}).on("hook:destroyed",()=>{o.destroy()})}),(o,e)=>{const b=G("Head");return i(),V(ee,null,{default:r(()=>[l(b,null,{default:r(()=>e[9]||(e[9]=[t("title",null,"Classes",-1)])),_:1}),t("div",me,[e[27]||(e[27]=t("div",{class:"absolute inset-x-0 -top-3 -z-10 mt-32 transform-gpu overflow-hidden px-36 blur-3xl sm:mt-56","aria-hidden":"true"},[t("div",{class:"mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-indigo-400 to-sky-200 opacity-30",style:{"clip-path":`polygon(
              74.1% 44.1%,
              100% 61.6%,
              97.5% 26.9%,
              85.5% 0.1%,
              80.7% 2%,
              72.5% 32.5%,
              60.2% 62.4%,
              52.4% 68.1%,
              47.5% 58.3%,
              45.2% 34.5%,
              27.5% 76.7%,
              0.1% 64.9%,
              17.9% 100%,
              27.6% 76.8%,
              76.1% 97.7%,
              74.1% 44.1%
            )`}})],-1)),t("div",ue,[t("div",ce,[l(te,{class:"lg:col-span-9 xl:grid-cols-10",pages:M})]),t("div",pe,[t("div",xe,[e[20]||(e[20]=t("div",{class:"mx-auto max-w-2xl text-center lg:max-w-4xl"},[t("p",{class:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"}," Classes ")],-1)),e[21]||(e[21]=t("p",{class:"mx-auto mt-6 max-w-2xl text-center text-lg leading-7 text-gray-600"}," Join a class or create one as a teacher to give them unfettered access to LatinTutorial, and assign specific activities or tools to help learn Latin. ",-1)),t("div",fe,[l(T,{onClick:e[0]||(e[0]=()=>w.value=!0)},{default:r(()=>e[10]||(e[10]=[y(" Create a Class")])),_:1}),l(T,{color:"white",onClick:e[1]||(e[1]=()=>h.value=!0)},{default:r(()=>e[11]||(e[11]=[y("Join a Class")])),_:1}),z.value?N("",!0):(i(),V(T,{key:0,onClick:e[2]||(e[2]=s=>C(d)?d.value=!0:d=!0),color:"black",size:"md"},{default:r(()=>e[12]||(e[12]=[y("Purchase Classroom Access")])),_:1}))]),(i(),V(Q,{to:"body"},[l(B,{open:h.value,onCloseModal:H,onSubmitForm:A},{title:r(()=>e[13]||(e[13]=[y(" Join a Classroom ")])),icon:r(()=>[t("div",ge,[l(c(ie),{class:"h-6 w-6 text-emerald-600","aria-hidden":"true"})])]),main:r(()=>[e[14]||(e[14]=t("label",{for:"enrollment_code",class:"block text-left text-sm font-medium text-gray-700"},"Enter the Enrollment Code",-1)),t("div",ve,[_(t("input",{id:"enrollment_code","onUpdate:modelValue":e[3]||(e[3]=s=>$.value=s),type:"text",name:"enrollment_code",class:"sm:text-md block w-full rounded-md border-gray-300 uppercase shadow-xs focus:border-teal-400 focus:ring-teal-400",placeholder:"XXX-XXX",maxlength:"7"},null,512),[[I,$.value]])]),m.errors.joinClass?(i(),x("div",be,J(m.errors.joinClass.enrollment_code),1)):N("",!0)]),actionButton:r(()=>[t("button",{type:"button",class:"inline-flex w-full justify-center self-center rounded-md border border-transparent bg-emerald-500 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-emerald-600 focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:outline-hidden sm:text-sm",onClick:A}," Create ")]),_:1},8,["open"]),l(B,{open:w.value,onCloseModal:D,onSubmitForm:F},{title:r(()=>e[15]||(e[15]=[y(" Create a Classroom ")])),icon:r(()=>[t("div",ye,[l(c(ne),{class:"h-6 w-6 text-blue-600","aria-hidden":"true"})])]),main:r(()=>[e[16]||(e[16]=t("label",{for:"classroom_name",class:"block text-left text-sm font-medium text-gray-700"},"Enter your Classroom's Name",-1)),t("div",Ce,[_(t("input",{id:"classroom_name","onUpdate:modelValue":e[4]||(e[4]=s=>v.value=s),type:"text",name:"classroom_name",class:"sm:text-md block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-400 focus:ring-teal-400"},null,512),[[I,v.value]])]),m.errors.createClass?(i(),x("div",we,J(m.errors.createClass.classroomName),1)):N("",!0)]),actionButton:r(()=>[t("button",{type:"button",class:"inline-flex w-full justify-center self-center rounded-md border border-transparent bg-blue-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm",onClick:F}," Create ")]),_:1},8,["open"]),l(B,{open:c(d),onCloseModal:e[6]||(e[6]=s=>C(d)?d.value=!1:d=!1),"modal-size":"xl","show-close":!1},{title:r(()=>e[17]||(e[17]=[t("h1",{class:"text-base leading-7 font-semibold text-indigo-600"}," Extend LatinTutorial ",-1)])),main:r(()=>[t("div",he,[e[18]||(e[18]=t("div",{class:"mx-auto max-w-4xl text-center"},[t("p",{class:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"}," Give access to your students ")],-1)),e[19]||(e[19]=t("p",{class:"mx-auto mt-6 max-w-2xl text-center text-lg leading-6 text-gray-600"}," Choose a plan to supercharge how you and your students use LatinTutorial. ",-1)),l(le,{teams:m.unarchivedClasses,errors:m.errors,"onUpdate:close":e[5]||(e[5]=s=>C(d)?d.value=!1:d=!1)},null,8,["teams","errors"])])]),_:1},8,["open"])]))])]),t("div",ke,[t("div",_e,[c(u)&&c(u).length>0?(i(),x("div",Me,[(i(!0),x(R,null,K(c(u),s=>(i(),x("div",{key:s.id,class:"draggable flex flex-row items-center","data-id":s.slug,"data-order":s.order},[l(c(de),{class:"draggable-handle h-6 w-6 cursor-move text-gray-400"}),l(se,{classItem:s,class:"py-4"},null,8,["classItem"])],8,je))),128)),t("div",{ref_key:"landmark",ref:j},null,512)])):(i(),x("div",Se,e[22]||(e[22]=[t("div",null,[t("p",{class:"mt-4 font-semibold text-gray-900"},"No classes found"),t("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ")],-1)])))]),t("div",Ve,[t("div",Ne,[t("div",{class:"flex flex-row items-center"},[e[23]||(e[23]=t("h3",{class:"text-2xl font-bold text-gray-900"},"Classes",-1)),t("div",{class:"flex-1 text-right"},[t("button",{class:"font-bold text-blue-500 uppercase opacity-100 hover:text-blue-600",onClick:q}," Clear ")])]),e[26]||(e[26]=t("p",{class:"mt-2 text-sm text-gray-600"}," Filter or sort your classes to better manage your students. ",-1)),t("div",Te,[t("div",Be,[t("div",$e,[_(t("input",{id:"active","onUpdate:modelValue":e[7]||(e[7]=s=>C(n)?n.value=s:n=s),value:"active",name:"active",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[U,c(n)]])]),e[24]||(e[24]=t("div",{class:"ml-3 text-base"},[t("label",{for:"active",class:"font-semibold text-gray-900"},"active")],-1))]),t("div",Ae,[t("div",Fe,[_(t("input",{id:"archived","onUpdate:modelValue":e[8]||(e[8]=s=>C(n)?n.value=s:n=s),value:"archived",name:"archived",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[U,c(n)]])]),e[25]||(e[25]=t("div",{class:"ml-3 text-base"},[t("label",{for:"archived",class:"font-semibold text-gray-900"},"archived")],-1))])])])])])]),l(re)])]),_:1})}}};export{Vt as default};
