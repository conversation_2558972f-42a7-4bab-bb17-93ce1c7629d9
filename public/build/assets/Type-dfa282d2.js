import{e as b,p as P,A as Q,B as H,o as u,d,a as i,r as C,F,h as K,u as v,b as p,w as y,R as O,T as J,n as A,j as S,t as U,g as $}from"./app-f0078ddb.js";import{_ as B}from"./ButtonItem-718c0517.js";import{r as h}from"./replaceMacra-3b9666ed.js";import{_ as x}from"./lodash-631955d9.js";import W from"./NextButtonSmall-9e6ffefc.js";import{r as X}from"./CheckCircleIcon-d86d1232.js";import{r as Y}from"./XCircleIcon-63af2b2a.js";/* empty css            */const Z={class:"flex-1"},ee={class:"mt-4 text-center text-2xl font-semibold text-gray-900"},te={class:"mt-2 mb-4 text-center text-xl font-semibold text-gray-500"},se={class:"text-center text-lg font-medium text-gray-600"},le={class:"relative mt-12 grid w-full grid-cols-1 justify-items-center px-16 select-none"},ae={class:"h-24 w-full"},oe={class:"mx-auto flex justify-center"},re=["id","onClick","onFocus"],ie={class:"mx-auto self-center"},ne={key:0},ue={key:1},de={class:"h-24 w-full"},ce=["onClick"],fe={key:0},ve={key:0,class:"flex items-center justify-center text-blue-500"},pe={key:1,class:"flex items-center justify-center text-red-600"},me={key:1},he={class:"grid grid-cols-1 gap-4 sm:grid-cols-3 sm:gap-8 xl:px-10"},Ue={__name:"Type",props:{options:String,difficulty:Number,isAnswer:Boolean,isCorrect:Boolean,disabled:Boolean,isVocabulary:{type:Boolean,required:!1,default:!1}},emits:["submit","next-question","i-know-this"],setup(n,{emit:V}){let m=["A","C","D","E","F","G","H","I","L","M","N","O","P","R","S","T","U","V"];const f=n;let r=b([]),a=b([]),s=b(0),w=b(!1);const k=V,I=e=>{const t=[];for(let l=0;l<e.length;l++)t.push({id:l,data:h(e[l]),used:!1});return t};let g=b("");switch(f.difficulty){case 1:m=x.shuffle(m),g.value=m.join("").substring(0,1);break;case 2:m=x.shuffle(m),g.value=m.join("").substring(0,3);break}g.value=g.value+f.options,r.value=I(x.shuffle(g.value.split(""))),a.value=new Array(f.options.length),P(()=>f.options,e=>{r.value=[],r.value=I(x.shuffle(e.split(""))),a.value=new Array(r.value.length),s.value=0}),Q(()=>{document.addEventListener("keydown",T)}),H(()=>{document.removeEventListener("keydown",T)});const E=(e,t)=>{if(f.isAnswer)return!1;s.value=t,e.target.select(),w.value=!0},T=e=>{e.ctrlKey||e.metaKey||e.altKey||(e.preventDefault(),w.value||(e.which<=90&&e.which>=48&&L(e.key),e.which==8&&D(),e.which==13&&(f.isAnswer||N())),e.which==37&&z(s.value),(e.which==39||e.which==32||e.which==9)&&j(s.value))},j=e=>{e<a.value.length-1&&(s.value=e+1)},z=e=>{e>0&&(s.value=e-1),e==0&&(s.value=0)},q=()=>{r.value=x.shuffle(r.value)},L=(e,t)=>{if(r.value.findIndex(o=>o.used==!1&&o.data.toUpperCase()==e.toUpperCase())>=0){if(a.value[s]){let o=r.value.findIndex(c=>c.used==!0&&c.data.toUpperCase()==a[s].toUpperCase());r.value[o].used=!1}if(a.value.splice(s.value,1,e),t)var l=t;else var l=r.value.findIndex(c=>c.used==!1&&c.data.toUpperCase()==e.toUpperCase());r.value[l].used=!0,j(s.value)}},D=()=>{if(a.value[s.value]||s.value!=0&&s.value--,a.value[s.value]){var e=a.value[s.value],t=r.value.slice().reverse().findIndex(l=>l.used==!0&&l.data.toUpperCase()==e.toUpperCase());t=r.value.length-t-1,r.value[t].used=!1,a.value.splice(s.value,1,"")}},M=()=>{a.value=new Array(a.value.length),r.value.forEach(e=>{e.used=!1}),s.value=0},N=()=>{s=null;let e=a.value.map(t=>t).join("").toUpperCase();k("submit",{answer:h(e),key:h(f.options.toUpperCase()),isArray:!1})},R=()=>{k("next-question")},_=e=>a.value[e]==null?!1:h(a.value[e]).toUpperCase()==h(f.options.charAt(e)).toUpperCase(),G=()=>{k("i-know-this")};return(e,t)=>(u(),d("div",Z,[i("h1",ee,[C(e.$slots,"stem")]),i("h2",te,[C(e.$slots,"word")]),i("h3",se,[C(e.$slots,"instructions")]),i("div",le,[i("div",ae,[i("div",oe,[(u(!0),d(F,null,K(v(a),(l,o)=>(u(),d("div",{key:o,class:A(["mx-1 inline-block",{"rounded-lg ring-2 ring-teal-500 ring-offset-4":v(s)==o}])},[i("div",{id:"input"+o,ref_for:!0,ref:"input",type:"text",maxlength:"1",class:A(["items-auto flex h-12 border-t-0 border-r-0 border-l-0 p-1 text-center uppercase transition duration-150 ease-in-out focus:border-teal-500 focus:ring-white focus:outline-hidden",[v(a).length<9?"w-10 text-3xl":"w-8 text-2xl",n.isAnswer&&_(o)?"rounded-lg border-b-0 bg-teal-500 text-white":n.isAnswer&&!_(o)?"rounded-lg border-b-0 bg-red-500 text-white":"border-b border-gray-700 bg-white text-gray-900"]]),onClick:c=>S(s)?s.value=o:s=o,onFocus:c=>E(c,o),onBlur:t[0]||(t[0]=c=>S(w)?w.value=!1:w=!1)},[i("p",ie,[n.isAnswer?(u(),d("span",ne,U(v(h)(n.options.charAt(o).toUpperCase())),1)):(u(),d("span",ue,U(v(a)[o]),1))])],42,re)],2))),128))])]),t[6]||(t[6]=i("div",{class:"my-8 w-full border border-gray-400"},null,-1)),i("div",de,[p(O,{name:"letter-list",tag:"p",class:"grid auto-cols-max grid-flow-col justify-center"},{default:y(()=>[(u(!0),d(F,null,K(v(r),(l,o)=>(u(),d("span",{key:l.id,class:A(["mx-2 inline-block cursor-pointer text-4xl font-bold text-indigo-700 uppercase",[l.used?"opacity-50":"opacity-100"]]),onClick:c=>L(l.data,o)},U(l.data),11,ce))),128))]),_:1})])]),i("div",null,[p(J,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95 ","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:y(()=>[n.isAnswer?(u(),d("div",fe,[n.isCorrect?(u(),d("div",ve,[p(v(X),{class:"mr-2 inline-block h-10 w-10"}),t[7]||(t[7]=i("p",{class:"inline-block text-2xl font-semibold"},"correct",-1))])):(u(),d("div",pe,[p(v(Y),{class:"mr-2 inline-block h-10 w-10"}),t[8]||(t[8]=i("p",{class:"inline-block text-2xl font-semibold"},"incorrect",-1))])),p(W,{"is-answer":n.isAnswer,"is-correct":n.isCorrect,disabled:n.disabled,"is-vocabulary":n.isVocabulary,onIKnowThis:t[1]||(t[1]=l=>G()),onNextQuestion:t[2]||(t[2]=l=>R())},null,8,["is-answer","is-correct","disabled","is-vocabulary"])])):(u(),d("div",me,[i("div",he,[p(B,{size:"lg",class:"w-full",color:"indigo",onClick:t[3]||(t[3]=l=>q())},{default:y(()=>t[9]||(t[9]=[$("Shuffle")])),_:1}),p(B,{size:"lg",class:"w-full",color:"white",onClick:t[4]||(t[4]=l=>M())},{default:y(()=>t[10]||(t[10]=[$("Clear")])),_:1}),p(B,{size:"lg",class:"w-full",color:"pink",onClick:t[5]||(t[5]=l=>N())},{default:y(()=>t[11]||(t[11]=[$("Submit")])),_:1})])]))]),_:1})])]))}};export{Ue as default};
