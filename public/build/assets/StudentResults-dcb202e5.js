import{_ as H}from"./AppLayout-33f062bc.js";import{_ as I}from"./Breadcrumbs-c96e9207.js";import{D as m}from"./datetime-8ddd27a0.js";import{u as B}from"./useInfiniteScroll-1e8e8e17.js";import{I as F}from"./InfinitasIcon-1a3ae135.js";import{o as s,d as r,a as t,e as x,i as b,c as g,w as f,b as c,t as i,u as n,g as p,h as w,f as u,n as k,F as M}from"./app-f0078ddb.js";import{a as Z,r as N}from"./RectangleGroupIcon-c6b3f31f.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./useIntersect-6e15125e.js";/* empty css            */function T(a,d){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"fill-rule":"evenodd",d:"M5.75 2a.75.75 0 0 1 .75.75V4h7V2.75a.75.75 0 0 1 1.5 0V4h.25A2.75 2.75 0 0 1 18 6.75v8.5A2.75 2.75 0 0 1 15.25 18H4.75A2.75 2.75 0 0 1 2 15.25v-8.5A2.75 2.75 0 0 1 4.75 4H5V2.75A.75.75 0 0 1 5.75 2Zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75Z","clip-rule":"evenodd"})])}function E(a,d){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"fill-rule":"evenodd",d:"M16.403 12.652a3 3 0 0 0 0-5.304 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75 3 3 0 0 0 0 5.305 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75Zm-2.546-4.46a.75.75 0 0 0-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z","clip-rule":"evenodd"})])}function z(a,d){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"fill-rule":"evenodd",d:"M1 11.27c0-.246.033-.492.099-.73l1.523-5.521A2.75 2.75 0 0 1 5.273 3h9.454a2.75 2.75 0 0 1 2.651 2.019l1.523 5.52c.066.239.099.485.099.732V15a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-3.73Zm3.068-5.852A1.25 1.25 0 0 1 5.273 4.5h9.454a1.25 1.25 0 0 1 1.205.918l1.523 5.52c.*************.015.062H14a1 1 0 0 0-.86.49l-.606 1.02a1 1 0 0 1-.86.49H8.236a1 1 0 0 1-.894-.553l-.448-.894A1 1 0 0 0 6 11H2.53l.015-.062 1.523-5.52Z","clip-rule":"evenodd"})])}const J={class:"relative isolate bg-white px-6 lg:px-8 pb-16"},P={class:"py-2 md:py-8"},Q={class:"flex flex-row justify-between items-center"},R={class:"py-6"},X={class:"sm:flex sm:items-center sm:justify-between"},G={class:"sm:flex sm:space-x-5 w-full"},K={class:"shrink-0"},U={class:"px-2"},W={class:"rounded-full bg-indigo-100 transition duration-150 h-24 w-24 flex items-center justify-center overflow-hidden"},Y=["src"],q={class:"flex flex-col sm:mt-0 sm:pt-1 sm:text-left w-full"},tt={class:"flex w-full flex items-end"},et={class:"inline-block text-4xl font-bold text-gray-900 sm:text-5xl"},ot={class:"mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-8"},st={class:"mt-2 flex items-center text-sm text-gray-500"},lt={class:"mt-2 flex items-center text-sm text-gray-500"},rt={class:"mt-2 flex items-center text-sm text-gray-500"},it={class:"mt-8"},at={class:"overflow-x-auto"},ct={class:"w-full whitespace-nowrap text-left"},nt={class:"divide-y divide-white/5"},dt={class:"py-4 pl-4 pr-8 sm:pl-6 lg:pl-8 flex items-center"},mt={key:0},ut={key:1},pt={class:"rounded-full bg-orange-100 transition duration-150 h-12 w-12 flex items-center justify-center"},ht={key:2},ft={class:"rounded-full bg-emerald-100 transition duration-150 h-12 w-12 flex items-center justify-center"},_t={key:3},yt={class:"rounded-full bg-indigo-100 transition duration-150 h-12 w-12 flex items-center justify-center"},vt=["src"],xt={class:"flex flex-col ml-4"},bt={class:"flex flex-col sm:flex-row justify-normal sm:justify-between md:justify-normal items-start sm:items-center text-left"},gt={class:"inline-block text-lg font-bold dark:text-white"},wt={class:"sm:ml-4 text-sm text-gray-500 dark:text-gray-300 font-medium"},kt={class:"text-sm font-medium text-gray-500 line-clamp-2 dark:text-gray-300 mt-1 text-wrap truncate break-words"},Mt={key:0},At={key:1},jt={class:"hidden py-4 pl-0 pr-4 sm:table-cell sm:pr-8"},Lt={class:"flex items-center justify-end gap-x-2 sm:justify-start"},$t=["datetime"],Ot={class:"hidden text-gray-600 text-sm font-medium sm:block"},Ct={class:"py-4 pl-0 pr-4 text-sm font-medium leading-6 sm:pr-8 lg:pr-20"},St={key:0},Vt={class:"py-4 pl-0 text-sm font-medium leading-6"},Dt={key:0},Ht={key:0,class:"flex flex-row items-center"},It={class:"text-gray-900"},Bt={class:"grid grid-cols-2 gap-8 w-48 ml-8"},Ft={class:"opacity-75 flex gap-0.5 justify-center"},Zt={class:"ml-8"},we={__name:"StudentResults",props:{team:Object,student:Object,assignments:Object},setup(a){const d=a,A=[{name:"Classes",href:"/classes",current:!1},{name:d.team.name,href:`/classes/${d.team.slug}`,current:!1},{name:d.student.name,href:"#",current:!0}],_=x(null);let j=x(d.assignments||{data:[],next_page_url:null});const{items:L}=B(j.value,_),$=o=>{if(!o.completion)return"Not yet started";if(o.completion.completed==0)return"In progress";if(o.completion.completed==1)return y(o.completion.completed_at,o.due_at)?"Completed":"Completed late"},y=(o,l)=>m.fromISO(o).ts<=m.fromISO(l).ts,O=o=>{switch(!0){case!o:return null;case Math.round(o/10)<15:return"Just a few seconds";case Math.round(o/10)<30:return"Less than 30 seconds";case Math.round(o/10)<60:return"Less than a minute";case Math.round(o/10)<90:return"About a minute";case Math.round(o/10)<150:return"About 2 minutes";case Math.round(o/10)<210:return"About 3 minutes";case Math.round(o/10)<270:return"About 4 minutes";case Math.round(o/10)<330:return"About 5 minutes";default:return"About "+Math.round(o/600)+" minutes"}},C=o=>{const l=m.fromISO(o);return l.hour===23&&l.minute===59&&l.second===59?l.toFormat("LLLL d, yyyy"):l.toFormat("LLLL d, yyyy 'at' h:mm a")},S=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],V=o=>{switch(o.sections){case"infinitas":return`/practice/vocabulary/attempt/${o.id}`;case"grammar":return`/practice/grammar/attempt/${o.activity_id}`;case"vocabulary":return`/practice/vocabulary/attempt/${o.id}`;case"read":return`${o.url}`}};return(o,l)=>{const v=b("Head"),D=b("Link");return s(),g(H,null,{default:f(()=>[c(v,null,{default:f(()=>[t("title",null,i(a.team.name),1)]),_:1}),t("div",J,[t("div",P,[t("div",Q,[c(I,{class:"lg:col-span-9 xl:grid-cols-10",pages:A})])]),t("section",null,[t("div",R,[t("div",X,[t("div",G,[t("div",K,[t("div",U,[t("div",W,[t("img",{src:`${a.student.photo_url}`,class:"h-24 w-24 text-indigo-600 stroke-2"},null,8,Y)])])]),t("div",q,[t("div",tt,[t("h5",et,i(a.student.name),1)]),t("div",ot,[t("div",st,[c(n(z),{class:"mr-1.5 h-5 w-5 shrink-0 text-gray-400","aria-hidden":"true"}),p(" "+i(a.student.email),1)]),t("div",lt,[c(n(E),{class:"mr-1.5 h-5 w-5 shrink-0 text-gray-400","aria-hidden":"true"}),p(" "+i(a.student.xp)+" XP ",1)]),t("div",rt,[c(n(T),{class:"mr-1.5 h-5 w-5 shrink-0 text-gray-400","aria-hidden":"true"}),p(" Last active "+i(n(m).fromSQL(a.student.last_active_at).toFormat("MMMM dd, yyyy 'at' hh:mm a")),1)])])])])])])]),t("section",it,[t("div",at,[t("table",ct,[l[2]||(l[2]=t("colgroup",null,[t("col",{class:"w-full sm:w-6/12"}),t("col",{class:"lg:w-1/12"}),t("col",{class:"lg:w-1/12"}),t("col",{class:"lg:w-4/12"})],-1)),l[3]||(l[3]=t("thead",{class:"border-b border-white/10 text-sm leading-6 text-gray-600"},[t("tr",null,[t("th",{scope:"col",class:"py-2 pl-4 pr-8 font-semibold sm:pl-6 lg:pl-8"}," Assignment "),t("th",{scope:"col",class:"py-2 pl-0 pr-4 text-right font-semibold sm:pr-8 sm:text-left lg:pr-20"}," Status "),t("th",{scope:"col",class:"hidden py-2 pl-0 pr-8 font-semibold md:table-cell lg:pr-20"}," Duration "),t("th",{scope:"col",class:"hidden py-2 pl-0 pr-8 font-semibold sm:table-cell"}," Accuracy ")])],-1)),t("tbody",nt,[(s(!0),r(M,null,w(n(L),e=>(s(),r("tr",{key:e.id,class:"group even:bg-slate-50 bg-transparent transition duration-150 ease-in-out"},[t("td",dt,[e.image=="infinitas"?(s(),r("div",mt,[c(F,{class:"w-12"})])):e.image=="grammar"?(s(),r("div",ut,[t("div",pt,[c(n(Z),{class:"h-8 w-8 text-orange-600 stroke-2"})])])):e.image=="vocabulary"?(s(),r("div",ht,[t("div",ft,[c(n(N),{class:"h-8 w-8 text-emerald-600 stroke-2"})])])):(s(),r("div",_t,[t("div",yt,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${e.image}`,class:"h-12 w-12 text-indigo-600 stroke-2"},null,8,vt)])])),t("div",xt,[t("div",bt,[t("h3",gt,i(e.title),1),t("span",wt," due "+i(C(e.due_at)),1)]),t("p",kt,[e.description?(s(),r("span",Mt,i(e.description),1)):(s(),r("span",At,i(e.section_description),1))])])]),t("td",jt,[t("div",Lt,[e.completion&&e.completion.completed==1?(s(),r("time",{key:0,class:"text-gray-400 sm:hidden",datetime:e.completion.activity.completed_at},i(n(m).fromISO(e.completion.completed_at).toFormat("MMMM dd, yyyy 'at' hh:mm a")),9,$t)):u("",!0),t("div",null,[t("span",{class:k(["right-0 top-0 block h-2 w-2 rounded-full mx-auto",e.completion?e.completion.completed==1?y(e.completion.completed_at,e.due_at)?"bg-green-500":"bg-amber-500":"bg-sky-500":"bg-gray-300"])},null,2)]),t("div",Ot,i($(e)),1)])]),t("td",Ct,[e.completion&&e.completion.activity?(s(),r("div",St,i(O(e.completion.activity.time)),1)):u("",!0)]),t("td",Vt,[e.completion&&e.completion.activity?(s(),r("div",Dt,[e.completion.completed===1?(s(),r("div",Ht,[t("h2",It,i(e.completion.activity.correct)+" out of "+i(e.completion.activity.attempts)+" ("+i(Math.round(1e3*e.completion.activity.correct/e.completion.activity.attempts)/10)+"%) ",1),t("div",Bt,[t("div",Ft,[(s(),r(M,null,w(S,h=>t("div",{class:k(["rounded-sm w-full h-6 shadow-sm",e.completion.activity.correct/e.completion.activity.attempts*10>=h.value?h.color:"bg-gray-300"]),key:h.value},null,2)),64))])]),t("div",Zt,[e.completion.activity?(s(),g(D,{key:0,class:"text-blue-500 hover:text-blue-600 text-sm font-semibold transition duration-150",href:V(e.completion.activity)},{default:f(()=>l[0]||(l[0]=[p(" View ")])),_:2},1032,["href"])):u("",!0)])])):u("",!0)])):u("",!0),l[1]||(l[1]=t("div",null,null,-1))])]))),128))])]),t("div",{ref_key:"landmark",ref:_},null,512)])])])]),_:1})}}};export{we as default};
