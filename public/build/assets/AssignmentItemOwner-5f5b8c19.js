import{_ as I}from"./ButtonItem-718c0517.js";import{D as r}from"./datetime-8ddd27a0.js";import{_ as B}from"./ClassroomModal-05d10768.js";import{_ as x}from"./ProgressBar-b7203293.js";import C from"./UtilityDropdown-2d786282.js";import{I as b}from"./InfinitasIcon-1a3ae135.js";import{r as v}from"./RectangleGroupIcon-04390470.js";import{r as w}from"./QueueListIcon-824b634b.js";import{r as k}from"./PlayCircleIcon-8bd12a30.js";import{e as m,i as O,o as s,d as n,t as a,u as d,f as u,a as t,n as f,b as o,w as h,c as j,j as S,Q as D,F as L,g as F,h as R}from"./app-f0078ddb.js";import"./XMarkIcon-9bc7c0bd.js";import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./CopyToClipboard-21badf5d.js";import"./clipboard-a66b13b3.js";import"./AssignModule-74fae4e9.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";import"./Combobox-f427c07d.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./use-resolve-button-type-24d8b5c5.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./AssignReading-df4942fb.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./use-text-value-2c18b2b1.js";import"./DropdownBooks-b7db1f80.js";import"./ChevronDownIcon-660c32b0.js";import"./RectangleGroupIcon-c6b3f31f.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css            */const q={key:0,class:"self-start rounded-full bg-gray-100 px-2 py-1 text-left text-sm font-medium text-gray-500 dark:bg-gray-800 dark:text-gray-300"},E={key:0,class:"w-16 shrink-0"},N={key:1},V={class:"flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 transition duration-150"},z={key:2,class:"w-16 shrink-0"},M={class:"flex h-16 w-16 items-center justify-center rounded-full bg-emerald-100 transition duration-150"},Q={key:3,class:"relative flex h-16 w-16 shrink-0 items-center justify-center overflow-hidden rounded-full"},T=["src"],P={key:4,class:"w-16 shrink-0"},Y={class:"flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100 transition duration-150"},G=["src"],H={class:"ml-4 flex grow flex-col"},J={class:"inline-block text-lg font-bold text-gray-900 hover:underline dark:text-white"},K={key:0,class:"mb-2 mt-2 rounded-full bg-gray-200 px-3 py-1 text-xs font-bold text-gray-500 dark:bg-gray-700 dark:text-gray-300 sm:mb-0 sm:ml-4 sm:mt-0"},U={key:0,class:"flex flex-row items-center"},W={class:"mr-2 text-sm font-semibold text-gray-500"},X={key:0,class:"mb-2 mt-2 rounded-full bg-gray-200 px-3 py-1 text-xs font-bold text-gray-500 dark:bg-gray-700 dark:text-gray-300 sm:mb-0 sm:mt-0"},Z={class:"mt-1 line-clamp-2 text-sm font-medium text-gray-500 dark:text-gray-300"},_={key:0},ee={key:1},te={class:"mx-auto flex w-full flex-row items-center justify-center"},se={key:0},ie={key:1},ne={class:"mx-2 flex h-24 w-24 items-center justify-center rounded-full bg-orange-100 transition duration-150"},le={key:2},ae={class:"mx-2 flex h-24 w-24 items-center justify-center rounded-full bg-emerald-100 transition duration-150"},oe={key:3,class:"relative flex h-16 w-16 shrink-0 items-center justify-center overflow-hidden rounded-full"},de=["src"],re={key:4,class:""},me={class:"mx-2 flex h-24 w-24 items-center justify-center rounded-full bg-indigo-100 transition duration-150"},ce=["src"],ge={class:"ml-6 grow"},ue={class:"flex flex-col items-start sm:flex-row sm:items-center"},fe={class:"mt-2 rounded-full bg-gray-200 px-3 py-1 text-xs font-bold text-gray-500 dark:bg-gray-700 dark:text-gray-300 sm:ml-4 sm:mt-0"},he={class:"mt-2 line-clamp-2 text-sm font-medium text-gray-600"},ye={class:"hidden text-sm font-medium text-gray-600 sm:mt-0 sm:inline"},xe={key:0},be={key:1},ve={class:"hidden sm:block"},we={class:"block sm:hidden"},ke={class:"mt-1 line-clamp-2 text-sm font-medium text-gray-600"},je={key:0},Le={key:1},pe={class:"mt-8 grid grid-cols-3 gap-2 sm:grid-cols-5"},$e={class:"shrink-0"},Ae={class:"relative inline-block"},Ie=["src"],Be={class:"mt-2 grow"},Ce={class:"line-clamp-1 font-medium"},Oe=["href"],kt={__name:"AssignmentItemOwner",props:{team:Object,assignment:Object,students:Array,dueDate:{type:Boolean,default:!0},scheduled:{type:Boolean,default:!1},dashboard:{type:Boolean,default:!1},works:{type:Array,default:()=>[],required:!1},books:{type:Array,default:()=>[],required:!1},vocabActivities:{type:Array,default:()=>[],required:!1},grammarActivities:{type:Array,default:()=>[],required:!1},videos:{type:Array,default:()=>[],required:!1},isLastItem:{type:Boolean,default:!1},canEdit:{type:Boolean,default:!1}},setup(e){let c=m(!1);const p=()=>{c.value=!1},$=(y,l)=>r.fromISO(y).ts<=r.fromISO(l).ts;return m(!1),m(!1),m(!1),m(!1),m(!1),(y,l)=>{const A=O("Link");return s(),n(L,null,[e.scheduled?(s(),n("span",q,"Scheduled for "+a(d(r).fromISO(e.assignment.published_at).toFormat("LLL d, yyyy 'at' t")),1)):u("",!0),t("div",{class:f(["flex w-full flex-col items-center md:flex-row",[e.scheduled?"pb-8 pt-2":"py-6"]])},[t("div",{class:f(["flex w-full flex-row items-center",e.scheduled?"grayscale":""])},[e.assignment.image=="infinitas"?(s(),n("div",E,[o(b,{class:"inline w-16"})])):e.assignment.image=="grammar"?(s(),n("div",N,[t("div",V,[o(d(v),{class:"h-12 w-12 stroke-2 text-orange-600"})])])):e.assignment.image=="vocabulary"?(s(),n("div",z,[t("div",M,[o(d(w),{class:"h-12 w-12 stroke-2 text-emerald-600"})])])):e.assignment.section=="video"?(s(),n("div",Q,[o(d(k),{class:"absolute mx-auto h-10 w-10 text-slate-400"}),l[1]||(l[1]=t("div",{class:"absolute inset-0 bg-gray-200 opacity-25"},null,-1)),t("img",{src:e.assignment.image,class:"inset-0 h-full w-full object-cover"},null,8,T)])):(s(),n("div",P,[t("div",Y,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${e.assignment.image}`,class:"h-16 w-16 stroke-2 text-indigo-600"},null,8,G)])])),t("div",H,[t("div",{class:f(["flex flex-col items-start justify-normal text-left sm:flex-row sm:items-center sm:justify-between md:justify-normal",e.dashboard?"justify-between":""])},[o(A,{href:`/classes/${e.team.slug}/assignments/${e.assignment.id}`},{default:h(()=>[t("h3",J,a(e.assignment.title),1)]),_:1},8,["href"]),e.dueDate&&!e.dashboard?(s(),n("span",K,"due "+a(d(r).fromISO(e.assignment.due_at).toFormat("LLL d, yyyy")),1)):u("",!0)],2),e.dashboard?(s(),n("div",U,[t("span",W,a(e.team.name),1),e.dueDate?(s(),n("span",X,"due "+a(d(r).fromISO(e.assignment.due_at).toFormat("LLL d, yyyy")),1)):u("",!0)])):u("",!0),t("p",Z,[e.assignment.description?(s(),n("span",_,a(e.assignment.description),1)):(s(),n("span",ee,a(e.assignment.section_description),1))])])],2),t("div",{class:f(["ml-0 mt-4 grid w-full grid-cols-2 gap-4 md:ml-4 md:mt-0 md:flex md:w-auto md:grid-cols-none",[e.dashboard?"mt-4 w-full":"ml-4"]])},[e.scheduled?u("",!0):(s(),j(I,{key:0,onClick:l[0]||(l[0]=i=>S(c)?c.value=!0:c=!0),class:"w-full md:w-32",size:"sm",color:"indigo"},{default:h(()=>l[2]||(l[2]=[F("Quick Results")])),_:1})),o(C,{scheduled:e.scheduled,reading:e.assignment.section=="read",assignment:e.assignment,works:e.works,books:e.books,team:e.team,vocabActivities:e.vocabActivities,grammarActivities:e.grammarActivities,videos:e.videos,isLastItem:e.isLastItem,canEdit:e.canEdit},null,8,["scheduled","reading","assignment","works","books","team","vocabActivities","grammarActivities","videos","isLastItem","canEdit"])],2),(s(),j(D,{to:"body"},[o(B,{open:d(c),onCloseModal:p,"close-button-text":"Close","modal-size":"xl","show-x-close":!1},{icon:h(()=>[t("div",te,[e.assignment.image=="infinitas"?(s(),n("div",se,[o(b,{class:"inline w-28"})])):e.assignment.image=="grammar"?(s(),n("div",ie,[t("div",ne,[o(d(v),{class:"h-16 w-16 stroke-2 text-orange-600"})])])):e.assignment.image=="vocabulary"?(s(),n("div",le,[t("div",ae,[o(d(w),{class:"h-16 w-16 stroke-2 text-emerald-600"})])])):e.assignment.section=="video"?(s(),n("div",oe,[o(d(k),{class:"absolute mx-auto h-10 w-10 text-slate-400"}),l[3]||(l[3]=t("div",{class:"absolute inset-0 bg-gray-200 opacity-25"},null,-1)),t("img",{src:e.assignment.image,class:"inset-0 h-full w-full object-cover"},null,8,de)])):(s(),n("div",re,[t("div",me,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${e.assignment.image}`,class:"h-24 w-24 stroke-2 text-indigo-600"},null,8,ce)])])),t("div",ge,[t("div",ue,[l[4]||(l[4]=t("h1",{class:"grow text-4xl font-bold text-gray-900"}," Quick Results ",-1)),t("span",fe,"due "+a(d(r).fromISO(e.assignment.due_at).toFormat("LLL d, yyyy")),1)]),t("div",he,[t("p",ye,[e.assignment.description?(s(),n("span",xe,a(e.assignment.description),1)):(s(),n("span",be,a(e.assignment.section_description),1))])]),t("div",ve,[o(x,{class:"mt-2",height:"h-3",color:e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100<50?"rose":e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100<100?"amber":"green","post-text":`${e.assignment.completion.filter(i=>i.completed==1).length} / ${e.students.length}`,"display-progress":!1,progress:e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100},null,8,["color","post-text","progress"])])])]),t("div",we,[t("p",ke,[e.assignment.description?(s(),n("span",je,a(e.assignment.description),1)):(s(),n("span",Le,a(e.assignment.section_description),1))]),o(x,{class:"mt-2",height:"h-3",color:e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100<50?"rose":e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100<100?"amber":"green","post-text":`${e.assignment.completion.filter(i=>i.completed==1).length} / ${e.students.length}`,"display-progress":!1,progress:e.assignment.completion.filter(i=>i.completed==1).length/e.students.length*100},null,8,["color","post-text","progress"])])]),main:h(()=>[t("div",pe,[(s(!0),n(L,null,R(e.students,i=>(s(),n("div",{key:i.id,class:"my-2 flex flex-col items-center justify-between"},[t("div",$e,[t("span",Ae,[t("img",{class:"h-16 w-16 rounded-full",src:i.photo_url,alt:""},null,8,Ie),t("span",{class:f(["absolute right-0 top-0 block h-4 w-4 rounded-full ring-2 ring-white",e.assignment.completion.filter(g=>g.user_id===i.id).length>0?e.assignment.completion.filter(g=>g.user_id===i.id)[0].completed==1?$(e.assignment.completion.filter(g=>g.user_id===i.id)[0].completed_at,e.assignment.due_at)?"bg-green-500":"bg-amber-500":"bg-sky-500":"bg-gray-300"])},null,2)])]),t("div",Be,[t("p",Ce,a(i.name),1)])]))),128))]),l[5]||(l[5]=t("div",{class:"my-12 grid grid-cols-2 gap-4 text-sm font-medium text-gray-600 sm:grid-cols-4"},[t("div",{class:"flex w-full flex-row items-center justify-center"},[t("div",{class:"h-4 w-4 rounded-full bg-green-500"}),t("div",{class:"ml-2"},"Completed")]),t("div",{class:"flex w-full flex-row items-center justify-center"},[t("div",{class:"h-4 w-4 rounded-full bg-amber-500"}),t("div",{class:"ml-2"},"Completed Late")]),t("div",{class:"flex w-full flex-row items-center justify-center"},[t("div",{class:"h-4 w-4 rounded-full bg-sky-500"}),t("div",{class:"ml-2"},"In Progress")]),t("div",{class:"flex w-full flex-row items-center justify-center"},[t("div",{class:"h-4 w-4 rounded-full bg-gray-300"}),t("div",{class:"ml-2"},"Not Yet Started")])],-1))]),actionButton:h(()=>[t("a",{type:"button",class:"inline-flex w-full items-center justify-center rounded-md border border-transparent bg-indigo-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm",href:`/classes/${e.team.slug}/assignments/${e.assignment.id}`}," View Full Results ",8,Oe)]),_:1},8,["open"])]))],2)],64)}}};export{kt as default};
