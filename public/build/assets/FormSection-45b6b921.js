import{l,S as n,o as d,d as r,b as m,w as a,r as e,a as t,s as c,n as p,f as u}from"./app-f0078ddb.js";import{S as _}from"./SectionTitle-05f6d081.js";const f={class:"md:grid md:grid-cols-3 md:gap-6"},g={class:"mt-5 md:mt-0 md:col-span-2"},v={class:"grid grid-cols-6 gap-6"},b={key:0,class:"flex items-center justify-end px-4 py-3 bg-slate-100 text-right sm:px-6 rounded-bl-xl rounded-br-xl"},y={__name:"FormSection",emits:["submitted"],setup(x){const o=l(()=>!!n().actions);return(s,i)=>(d(),r("div",f,[m(_,null,{title:a(()=>[e(s.$slots,"title")]),description:a(()=>[e(s.$slots,"description")]),_:3}),t("div",g,[t("form",{onSubmit:i[0]||(i[0]=c(h=>s.$emit("submitted"),["prevent"]))},[t("div",{class:p(["px-4 py-5 bg-slate-100 sm:p-6",o.value?"rounded-tl-xl rounded-tr-xl":"rounded-xl"])},[t("div",v,[e(s.$slots,"form")])],2),o.value?(d(),r("div",b,[e(s.$slots,"actions")])):u("",!0)],32)])]))}};export{y as _};
