class P extends Error{}class Hn extends P{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class Yn extends P{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class Pn extends P{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class K extends P{}class Zt extends P{constructor(e){super(`Invalid unit ${e}`)}}class N extends P{}class Z extends P{constructor(){super("Zone is an abstract class")}}const c="numeric",W="short",M="long",De={year:c,month:c,day:c},At={year:c,month:W,day:c},Gn={year:c,month:W,day:c,weekday:W},Ut={year:c,month:M,day:c},zt={year:c,month:M,day:c,weekday:M},Rt={hour:c,minute:c},qt={hour:c,minute:c,second:c},Ht={hour:c,minute:c,second:c,timeZoneName:W},Yt={hour:c,minute:c,second:c,timeZoneName:M},Pt={hour:c,minute:c,hourCycle:"h23"},Gt={hour:c,minute:c,second:c,hourCycle:"h23"},Jt={hour:c,minute:c,second:c,hourCycle:"h23",timeZoneName:W},_t={hour:c,minute:c,second:c,hourCycle:"h23",timeZoneName:M},Bt={year:c,month:c,day:c,hour:c,minute:c},jt={year:c,month:c,day:c,hour:c,minute:c,second:c},Qt={year:c,month:W,day:c,hour:c,minute:c},Kt={year:c,month:W,day:c,hour:c,minute:c,second:c},Jn={year:c,month:W,day:c,weekday:W,hour:c,minute:c},Xt={year:c,month:M,day:c,hour:c,minute:c,timeZoneName:W},en={year:c,month:M,day:c,hour:c,minute:c,second:c,timeZoneName:W},tn={year:c,month:M,day:c,weekday:M,hour:c,minute:c,timeZoneName:M},nn={year:c,month:M,day:c,weekday:M,hour:c,minute:c,second:c,timeZoneName:M};class me{get type(){throw new Z}get name(){throw new Z}get ianaName(){return this.name}get isUniversal(){throw new Z}offsetName(e,t){throw new Z}formatOffset(e,t){throw new Z}offset(e){throw new Z}equals(e){throw new Z}get isValid(){throw new Z}}let Ze=null;class Ve extends me{static get instance(){return Ze===null&&(Ze=new Ve),Ze}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return dn(e,t,r)}formatOffset(e,t){return de(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}}let Ne={};function _n(n){return Ne[n]||(Ne[n]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Ne[n]}const Bn={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function jn(n,e){const t=n.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(t),[,s,i,a,o,u,l,f]=r;return[a,s,i,o,u,l,f]}function Qn(n,e){const t=n.formatToParts(e),r=[];for(let s=0;s<t.length;s++){const{type:i,value:a}=t[s],o=Bn[i];i==="era"?r[o]=a:d(o)||(r[o]=parseInt(a,10))}return r}let ke={};class $ extends me{static create(e){return ke[e]||(ke[e]=new $(e)),ke[e]}static resetCache(){ke={},Ne={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch{return!1}}constructor(e){super(),this.zoneName=e,this.valid=$.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:r}){return dn(e,t,r,this.name)}formatOffset(e,t){return de(this.offset(e),t)}offset(e){const t=new Date(e);if(isNaN(t))return NaN;const r=_n(this.name);let[s,i,a,o,u,l,f]=r.formatToParts?Qn(r,t):jn(r,t);o==="BC"&&(s=-Math.abs(s)+1);const O=We({year:s,month:i,day:a,hour:u===24?0:u,minute:l,second:f,millisecond:0});let m=+t;const v=m%1e3;return m-=v>=0?v:1e3+v,(O-m)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}}let ot={};function Kn(n,e={}){const t=JSON.stringify([n,e]);let r=ot[t];return r||(r=new Intl.ListFormat(n,e),ot[t]=r),r}let Pe={};function Ge(n,e={}){const t=JSON.stringify([n,e]);let r=Pe[t];return r||(r=new Intl.DateTimeFormat(n,e),Pe[t]=r),r}let Je={};function Xn(n,e={}){const t=JSON.stringify([n,e]);let r=Je[t];return r||(r=new Intl.NumberFormat(n,e),Je[t]=r),r}let _e={};function er(n,e={}){const{base:t,...r}=e,s=JSON.stringify([n,r]);let i=_e[s];return i||(i=new Intl.RelativeTimeFormat(n,e),_e[s]=i),i}let ce=null;function tr(){return ce||(ce=new Intl.DateTimeFormat().resolvedOptions().locale,ce)}let ut={};function nr(n){let e=ut[n];if(!e){const t=new Intl.Locale(n);e="getWeekInfo"in t?t.getWeekInfo():t.weekInfo,ut[n]=e}return e}function rr(n){const e=n.indexOf("-x-");e!==-1&&(n=n.substring(0,e));const t=n.indexOf("-u-");if(t===-1)return[n];{let r,s;try{r=Ge(n).resolvedOptions(),s=n}catch{const u=n.substring(0,t);r=Ge(u).resolvedOptions(),s=u}const{numberingSystem:i,calendar:a}=r;return[s,i,a]}}function sr(n,e,t){return(t||e)&&(n.includes("-u-")||(n+="-u"),t&&(n+=`-ca-${t}`),e&&(n+=`-nu-${e}`)),n}function ir(n){const e=[];for(let t=1;t<=12;t++){const r=h.utc(2009,t,1);e.push(n(r))}return e}function ar(n){const e=[];for(let t=1;t<=7;t++){const r=h.utc(2016,11,13+t);e.push(n(r))}return e}function Te(n,e,t,r){const s=n.listingMode();return s==="error"?null:s==="en"?t(e):r(e)}function or(n){return n.numberingSystem&&n.numberingSystem!=="latn"?!1:n.numberingSystem==="latn"||!n.locale||n.locale.startsWith("en")||new Intl.DateTimeFormat(n.intl).resolvedOptions().numberingSystem==="latn"}class ur{constructor(e,t,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;const{padTo:s,floor:i,...a}=r;if(!t||Object.keys(a).length>0){const o={useGrouping:!1,...r};r.padTo>0&&(o.minimumIntegerDigits=r.padTo),this.inf=Xn(e,o)}}format(e){if(this.inf){const t=this.floor?Math.floor(e):e;return this.inf.format(t)}else{const t=this.floor?Math.floor(e):tt(e,3);return p(t,this.padTo)}}}class lr{constructor(e,t,r){this.opts=r,this.originalZone=void 0;let s;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){const a=-1*(e.offset/60),o=a>=0?`Etc/GMT+${a}`:`Etc/GMT${a}`;e.offset!==0&&$.create(o).valid?(s=o,this.dt=e):(s="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,s=e.zone.name):(s="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);const i={...this.opts};i.timeZone=i.timeZone||s,this.dtf=Ge(t,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(t=>{if(t.type==="timeZoneName"){const r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...t,value:r}}else return t}):e}resolvedOptions(){return this.dtf.resolvedOptions()}}class cr{constructor(e,t,r){this.opts={style:"long",...r},!t&&cn()&&(this.rtf=er(e,r))}format(e,t){return this.rtf?this.rtf.format(e,t):Cr(t,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}}const fr={firstDay:1,minimalDays:4,weekend:[6,7]};class k{static fromOpts(e){return k.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,r,s,i=!1){const a=e||S.defaultLocale,o=a||(i?"en-US":tr()),u=t||S.defaultNumberingSystem,l=r||S.defaultOutputCalendar,f=Be(s)||S.defaultWeekSettings;return new k(o,u,l,f,a)}static resetCache(){ce=null,Pe={},Je={},_e={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:r,weekSettings:s}={}){return k.create(e,t,r,s)}constructor(e,t,r,s,i){const[a,o,u]=rr(e);this.locale=a,this.numberingSystem=t||o||null,this.outputCalendar=r||u||null,this.weekSettings=s,this.intl=sr(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=or(this)),this.fastNumbersCached}listingMode(){const e=this.isEnglish(),t=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&t?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:k.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,Be(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return Te(this,e,yn,()=>{const r=t?{month:e,day:"numeric"}:{month:e},s=t?"format":"standalone";return this.monthsCache[s][e]||(this.monthsCache[s][e]=ir(i=>this.extract(i,r,"month"))),this.monthsCache[s][e]})}weekdays(e,t=!1){return Te(this,e,kn,()=>{const r=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},s=t?"format":"standalone";return this.weekdaysCache[s][e]||(this.weekdaysCache[s][e]=ar(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[s][e]})}meridiems(){return Te(this,void 0,()=>Tn,()=>{if(!this.meridiemCache){const e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[h.utc(2016,11,13,9),h.utc(2016,11,13,19)].map(t=>this.extract(t,e,"dayperiod"))}return this.meridiemCache})}eras(e){return Te(this,e,Sn,()=>{const t={era:e};return this.eraCache[e]||(this.eraCache[e]=[h.utc(-40,1,1),h.utc(2017,1,1)].map(r=>this.extract(r,t,"era"))),this.eraCache[e]})}extract(e,t,r){const s=this.dtFormatter(e,t),i=s.formatToParts(),a=i.find(o=>o.type.toLowerCase()===r);return a?a.value:null}numberFormatter(e={}){return new ur(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new lr(e,this.intl,t)}relFormatter(e={}){return new cr(this.intl,this.isEnglish(),e)}listFormatter(e={}){return Kn(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:fn()?nr(this.locale):fr}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}let Ae=null;class E extends me{static get utcInstance(){return Ae===null&&(Ae=new E(0)),Ae}static instance(e){return e===0?E.utcInstance:new E(e)}static parseSpecifier(e){if(e){const t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new E(Le(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${de(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${de(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return de(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}}class dr extends me{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function z(n,e){if(d(n)||n===null)return e;if(n instanceof me)return n;if(kr(n)){const t=n.toLowerCase();return t==="default"?e:t==="local"||t==="system"?Ve.instance:t==="utc"||t==="gmt"?E.utcInstance:E.parseSpecifier(t)||$.create(n)}else return R(n)?E.instance(n):typeof n=="object"&&"offset"in n&&typeof n.offset=="function"?n:new dr(n)}const Qe={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},lt={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},hr=Qe.hanidec.replace(/[\[|\]]/g,"").split("");function mr(n){let e=parseInt(n,10);if(isNaN(e)){e="";for(let t=0;t<n.length;t++){const r=n.charCodeAt(t);if(n[t].search(Qe.hanidec)!==-1)e+=hr.indexOf(n[t]);else for(const s in lt){const[i,a]=lt[s];r>=i&&r<=a&&(e+=r-i)}}return parseInt(e,10)}else return e}let Q={};function yr(){Q={}}function F({numberingSystem:n},e=""){const t=n||"latn";return Q[t]||(Q[t]={}),Q[t][e]||(Q[t][e]=new RegExp(`${Qe[t]}${e}`)),Q[t][e]}let ct=()=>Date.now(),ft="system",dt=null,ht=null,mt=null,yt=60,gt,wt=null;class S{static get now(){return ct}static set now(e){ct=e}static set defaultZone(e){ft=e}static get defaultZone(){return z(ft,Ve.instance)}static get defaultLocale(){return dt}static set defaultLocale(e){dt=e}static get defaultNumberingSystem(){return ht}static set defaultNumberingSystem(e){ht=e}static get defaultOutputCalendar(){return mt}static set defaultOutputCalendar(e){mt=e}static get defaultWeekSettings(){return wt}static set defaultWeekSettings(e){wt=Be(e)}static get twoDigitCutoffYear(){return yt}static set twoDigitCutoffYear(e){yt=e%100}static get throwOnInvalid(){return gt}static set throwOnInvalid(e){gt=e}static resetCaches(){k.resetCache(),$.resetCache(),h.resetCache(),yr()}}class C{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const rn=[0,31,59,90,120,151,181,212,243,273,304,334],sn=[0,31,60,91,121,152,182,213,244,274,305,335];function x(n,e){return new C("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${n}, which is invalid`)}function Ke(n,e,t){const r=new Date(Date.UTC(n,e-1,t));n<100&&n>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const s=r.getUTCDay();return s===0?7:s}function an(n,e,t){return t+(ye(n)?sn:rn)[e-1]}function on(n,e){const t=ye(n)?sn:rn,r=t.findIndex(i=>i<e),s=e-t[r];return{month:r+1,day:s}}function Xe(n,e){return(n-e+7)%7+1}function xe(n,e=4,t=1){const{year:r,month:s,day:i}=n,a=an(r,s,i),o=Xe(Ke(r,s,i),t);let u=Math.floor((a-o+14-e)/7),l;return u<1?(l=r-1,u=he(l,e,t)):u>he(r,e,t)?(l=r+1,u=1):l=r,{weekYear:l,weekNumber:u,weekday:o,...$e(n)}}function kt(n,e=4,t=1){const{weekYear:r,weekNumber:s,weekday:i}=n,a=Xe(Ke(r,1,e),t),o=X(r);let u=s*7+i-a-7+e,l;u<1?(l=r-1,u+=X(l)):u>o?(l=r+1,u-=X(r)):l=r;const{month:f,day:y}=on(l,u);return{year:l,month:f,day:y,...$e(n)}}function Ue(n){const{year:e,month:t,day:r}=n,s=an(e,t,r);return{year:e,ordinal:s,...$e(n)}}function Tt(n){const{year:e,ordinal:t}=n,{month:r,day:s}=on(e,t);return{year:e,month:r,day:s,...$e(n)}}function St(n,e){if(!d(n.localWeekday)||!d(n.localWeekNumber)||!d(n.localWeekYear)){if(!d(n.weekday)||!d(n.weekNumber)||!d(n.weekYear))throw new K("Cannot mix locale-based week fields with ISO-based week fields");return d(n.localWeekday)||(n.weekday=n.localWeekday),d(n.localWeekNumber)||(n.weekNumber=n.localWeekNumber),d(n.localWeekYear)||(n.weekYear=n.localWeekYear),delete n.localWeekday,delete n.localWeekNumber,delete n.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function gr(n,e=4,t=1){const r=Ce(n.weekYear),s=b(n.weekNumber,1,he(n.weekYear,e,t)),i=b(n.weekday,1,7);return r?s?i?!1:x("weekday",n.weekday):x("week",n.weekNumber):x("weekYear",n.weekYear)}function wr(n){const e=Ce(n.year),t=b(n.ordinal,1,X(n.year));return e?t?!1:x("ordinal",n.ordinal):x("year",n.year)}function un(n){const e=Ce(n.year),t=b(n.month,1,12),r=b(n.day,1,be(n.year,n.month));return e?t?r?!1:x("day",n.day):x("month",n.month):x("year",n.year)}function ln(n){const{hour:e,minute:t,second:r,millisecond:s}=n,i=b(e,0,23)||e===24&&t===0&&r===0&&s===0,a=b(t,0,59),o=b(r,0,59),u=b(s,0,999);return i?a?o?u?!1:x("millisecond",s):x("second",r):x("minute",t):x("hour",e)}function d(n){return typeof n>"u"}function R(n){return typeof n=="number"}function Ce(n){return typeof n=="number"&&n%1===0}function kr(n){return typeof n=="string"}function Tr(n){return Object.prototype.toString.call(n)==="[object Date]"}function cn(){try{return typeof Intl<"u"&&!!Intl.RelativeTimeFormat}catch{return!1}}function fn(){try{return typeof Intl<"u"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch{return!1}}function Sr(n){return Array.isArray(n)?n:[n]}function Ot(n,e,t){if(n.length!==0)return n.reduce((r,s)=>{const i=[e(s),s];return r&&t(r[0],i[0])===r[0]?r:i},null)[1]}function Or(n,e){return e.reduce((t,r)=>(t[r]=n[r],t),{})}function te(n,e){return Object.prototype.hasOwnProperty.call(n,e)}function Be(n){if(n==null)return null;if(typeof n!="object")throw new N("Week settings must be an object");if(!b(n.firstDay,1,7)||!b(n.minimalDays,1,7)||!Array.isArray(n.weekend)||n.weekend.some(e=>!b(e,1,7)))throw new N("Invalid week settings");return{firstDay:n.firstDay,minimalDays:n.minimalDays,weekend:Array.from(n.weekend)}}function b(n,e,t){return Ce(n)&&n>=e&&n<=t}function pr(n,e){return n-e*Math.floor(n/e)}function p(n,e=2){const t=n<0;let r;return t?r="-"+(""+-n).padStart(e,"0"):r=(""+n).padStart(e,"0"),r}function U(n){if(!(d(n)||n===null||n===""))return parseInt(n,10)}function q(n){if(!(d(n)||n===null||n===""))return parseFloat(n)}function et(n){if(!(d(n)||n===null||n==="")){const e=parseFloat("0."+n)*1e3;return Math.floor(e)}}function tt(n,e,t=!1){const r=10**e;return(t?Math.trunc:Math.round)(n*r)/r}function ye(n){return n%4===0&&(n%100!==0||n%400===0)}function X(n){return ye(n)?366:365}function be(n,e){const t=pr(e-1,12)+1,r=n+(e-t)/12;return t===2?ye(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][t-1]}function We(n){let e=Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,n.second,n.millisecond);return n.year<100&&n.year>=0&&(e=new Date(e),e.setUTCFullYear(n.year,n.month-1,n.day)),+e}function pt(n,e,t){return-Xe(Ke(n,1,e),t)+e-1}function he(n,e=4,t=1){const r=pt(n,e,t),s=pt(n+1,e,t);return(X(n)-r+s)/7}function je(n){return n>99?n:n>S.twoDigitCutoffYear?1900+n:2e3+n}function dn(n,e,t,r=null){const s=new Date(n),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);const a={timeZoneName:e,...i},o=new Intl.DateTimeFormat(t,a).formatToParts(s).find(u=>u.type.toLowerCase()==="timezonename");return o?o.value:null}function Le(n,e){let t=parseInt(n,10);Number.isNaN(t)&&(t=0);const r=parseInt(e,10)||0,s=t<0||Object.is(t,-0)?-r:r;return t*60+s}function hn(n){const e=Number(n);if(typeof n=="boolean"||n===""||Number.isNaN(e))throw new N(`Invalid unit value ${n}`);return e}function Fe(n,e){const t={};for(const r in n)if(te(n,r)){const s=n[r];if(s==null)continue;t[e(r)]=hn(s)}return t}function de(n,e){const t=Math.trunc(Math.abs(n/60)),r=Math.trunc(Math.abs(n%60)),s=n>=0?"+":"-";switch(e){case"short":return`${s}${p(t,2)}:${p(r,2)}`;case"narrow":return`${s}${t}${r>0?`:${r}`:""}`;case"techie":return`${s}${p(t,2)}${p(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function $e(n){return Or(n,["hour","minute","second","millisecond"])}const vr=["January","February","March","April","May","June","July","August","September","October","November","December"],mn=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Nr=["J","F","M","A","M","J","J","A","S","O","N","D"];function yn(n){switch(n){case"narrow":return[...Nr];case"short":return[...mn];case"long":return[...vr];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const gn=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],wn=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],Ir=["M","T","W","T","F","S","S"];function kn(n){switch(n){case"narrow":return[...Ir];case"short":return[...wn];case"long":return[...gn];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Tn=["AM","PM"],Er=["Before Christ","Anno Domini"],Mr=["BC","AD"],Dr=["B","A"];function Sn(n){switch(n){case"narrow":return[...Dr];case"short":return[...Mr];case"long":return[...Er];default:return null}}function xr(n){return Tn[n.hour<12?0:1]}function br(n,e){return kn(e)[n.weekday-1]}function Fr(n,e){return yn(e)[n.month-1]}function Vr(n,e){return Sn(e)[n.year<0?0:1]}function Cr(n,e,t="always",r=!1){const s={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(n)===-1;if(t==="auto"&&i){const y=n==="days";switch(e){case 1:return y?"tomorrow":`next ${s[n][0]}`;case-1:return y?"yesterday":`last ${s[n][0]}`;case 0:return y?"today":`this ${s[n][0]}`}}const a=Object.is(e,-0)||e<0,o=Math.abs(e),u=o===1,l=s[n],f=r?u?l[1]:l[2]||l[1]:u?s[n][0]:n;return a?`${o} ${f} ago`:`in ${o} ${f}`}function vt(n,e){let t="";for(const r of n)r.literal?t+=r.val:t+=e(r.val);return t}const Wr={D:De,DD:At,DDD:Ut,DDDD:zt,t:Rt,tt:qt,ttt:Ht,tttt:Yt,T:Pt,TT:Gt,TTT:Jt,TTTT:_t,f:Bt,ff:Qt,fff:Xt,ffff:tn,F:jt,FF:Kt,FFF:en,FFFF:nn};class I{static create(e,t={}){return new I(e,t)}static parseFormat(e){let t=null,r="",s=!1;const i=[];for(let a=0;a<e.length;a++){const o=e.charAt(a);o==="'"?(r.length>0&&i.push({literal:s||/^\s+$/.test(r),val:r}),t=null,r="",s=!s):s||o===t?r+=o:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=o,t=o)}return r.length>0&&i.push({literal:s||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return Wr[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return p(e,t);const r={...this.opts};return t>0&&(r.padTo=t),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,t){const r=this.loc.listingMode()==="en",s=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(m,v)=>this.loc.extract(e,m,v),a=m=>e.isOffsetFixed&&e.offset===0&&m.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,m.format):"",o=()=>r?xr(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),u=(m,v)=>r?Fr(e,m):i(v?{month:m}:{month:m,day:"numeric"},"month"),l=(m,v)=>r?br(e,m):i(v?{weekday:m}:{weekday:m,month:"long",day:"numeric"},"weekday"),f=m=>{const v=I.macroTokenToFormatOpts(m);return v?this.formatWithSystemDefault(e,v):m},y=m=>r?Vr(e,m):i({era:m},"era"),O=m=>{switch(m){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return a({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return a({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return a({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return o();case"d":return s?i({day:"numeric"},"day"):this.num(e.day);case"dd":return s?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return l("short",!0);case"cccc":return l("long",!0);case"ccccc":return l("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return l("short",!1);case"EEEE":return l("long",!1);case"EEEEE":return l("narrow",!1);case"L":return s?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return s?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return u("short",!0);case"LLLL":return u("long",!0);case"LLLLL":return u("narrow",!0);case"M":return s?i({month:"numeric"},"month"):this.num(e.month);case"MM":return s?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return u("short",!1);case"MMMM":return u("long",!1);case"MMMMM":return u("narrow",!1);case"y":return s?i({year:"numeric"},"year"):this.num(e.year);case"yy":return s?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return s?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return s?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return y("short");case"GG":return y("long");case"GGGGG":return y("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return f(m)}};return vt(I.parseFormat(t),O)}formatDurationFromString(e,t){const r=u=>{switch(u[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},s=u=>l=>{const f=r(l);return f?this.num(u.get(f),l.length):l},i=I.parseFormat(t),a=i.reduce((u,{literal:l,val:f})=>l?u:u.concat(f),[]),o=e.shiftTo(...a.map(r).filter(u=>u));return vt(i,s(o))}}const On=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function ne(...n){const e=n.reduce((t,r)=>t+r.source,"");return RegExp(`^${e}$`)}function re(...n){return e=>n.reduce(([t,r,s],i)=>{const[a,o,u]=i(e,s);return[{...t,...a},o||r,u]},[{},null,1]).slice(0,2)}function se(n,...e){if(n==null)return[null,null];for(const[t,r]of e){const s=t.exec(n);if(s)return r(s)}return[null,null]}function pn(...n){return(e,t)=>{const r={};let s;for(s=0;s<n.length;s++)r[n[s]]=U(e[t+s]);return[r,null,t+s]}}const vn=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Lr=`(?:${vn.source}?(?:\\[(${On.source})\\])?)?`,nt=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Nn=RegExp(`${nt.source}${Lr}`),rt=RegExp(`(?:T${Nn.source})?`),$r=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Zr=/(\d{4})-?W(\d\d)(?:-?(\d))?/,Ar=/(\d{4})-?(\d{3})/,Ur=pn("weekYear","weekNumber","weekDay"),zr=pn("year","ordinal"),Rr=/(\d{4})-(\d\d)-(\d\d)/,In=RegExp(`${nt.source} ?(?:${vn.source}|(${On.source}))?`),qr=RegExp(`(?: ${In.source})?`);function ee(n,e,t){const r=n[e];return d(r)?t:U(r)}function Hr(n,e){return[{year:ee(n,e),month:ee(n,e+1,1),day:ee(n,e+2,1)},null,e+3]}function ie(n,e){return[{hours:ee(n,e,0),minutes:ee(n,e+1,0),seconds:ee(n,e+2,0),milliseconds:et(n[e+3])},null,e+4]}function ge(n,e){const t=!n[e]&&!n[e+1],r=Le(n[e+1],n[e+2]),s=t?null:E.instance(r);return[{},s,e+3]}function we(n,e){const t=n[e]?$.create(n[e]):null;return[{},t,e+1]}const Yr=RegExp(`^T?${nt.source}$`),Pr=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function Gr(n){const[e,t,r,s,i,a,o,u,l]=n,f=e[0]==="-",y=u&&u[0]==="-",O=(m,v=!1)=>m!==void 0&&(v||m&&f)?-m:m;return[{years:O(q(t)),months:O(q(r)),weeks:O(q(s)),days:O(q(i)),hours:O(q(a)),minutes:O(q(o)),seconds:O(q(u),u==="-0"),milliseconds:O(et(l),y)}]}const Jr={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function st(n,e,t,r,s,i,a){const o={year:e.length===2?je(U(e)):U(e),month:mn.indexOf(t)+1,day:U(r),hour:U(s),minute:U(i)};return a&&(o.second=U(a)),n&&(o.weekday=n.length>3?gn.indexOf(n)+1:wn.indexOf(n)+1),o}const _r=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function Br(n){const[,e,t,r,s,i,a,o,u,l,f,y]=n,O=st(e,s,r,t,i,a,o);let m;return u?m=Jr[u]:l?m=0:m=Le(f,y),[O,new E(m)]}function jr(n){return n.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}const Qr=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,Kr=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,Xr=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function Nt(n){const[,e,t,r,s,i,a,o]=n;return[st(e,s,r,t,i,a,o),E.utcInstance]}function es(n){const[,e,t,r,s,i,a,o]=n;return[st(e,o,t,r,s,i,a),E.utcInstance]}const ts=ne($r,rt),ns=ne(Zr,rt),rs=ne(Ar,rt),ss=ne(Nn),En=re(Hr,ie,ge,we),is=re(Ur,ie,ge,we),as=re(zr,ie,ge,we),os=re(ie,ge,we);function us(n){return se(n,[ts,En],[ns,is],[rs,as],[ss,os])}function ls(n){return se(jr(n),[_r,Br])}function cs(n){return se(n,[Qr,Nt],[Kr,Nt],[Xr,es])}function fs(n){return se(n,[Pr,Gr])}const ds=re(ie);function hs(n){return se(n,[Yr,ds])}const ms=ne(Rr,qr),ys=ne(In),gs=re(ie,ge,we);function ws(n){return se(n,[ms,En],[ys,gs])}const It="Invalid Duration",Mn={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},ks={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Mn},D=146097/400,_=146097/4800,Ts={years:{quarters:4,months:12,weeks:D/7,days:D,hours:D*24,minutes:D*24*60,seconds:D*24*60*60,milliseconds:D*24*60*60*1e3},quarters:{months:3,weeks:D/28,days:D/4,hours:D*24/4,minutes:D*24*60/4,seconds:D*24*60*60/4,milliseconds:D*24*60*60*1e3/4},months:{weeks:_/7,days:_,hours:_*24,minutes:_*24*60,seconds:_*24*60*60,milliseconds:_*24*60*60*1e3},...Mn},Y=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Ss=Y.slice(0).reverse();function A(n,e,t=!1){const r={values:t?e.values:{...n.values,...e.values||{}},loc:n.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||n.conversionAccuracy,matrix:e.matrix||n.matrix};return new g(r)}function Dn(n,e){let t=e.milliseconds??0;for(const r of Ss.slice(1))e[r]&&(t+=e[r]*n[r].milliseconds);return t}function Et(n,e){const t=Dn(n,e)<0?-1:1;Y.reduceRight((r,s)=>{if(d(e[s]))return r;if(r){const i=e[r]*t,a=n[s][r],o=Math.floor(i/a);e[s]+=o*t,e[r]-=o*a*t}return s},null),Y.reduce((r,s)=>{if(d(e[s]))return r;if(r){const i=e[r]%1;e[r]-=i,e[s]+=i*n[r][s]}return s},null)}function Os(n){const e={};for(const[t,r]of Object.entries(n))r!==0&&(e[t]=r);return e}class g{constructor(e){const t=e.conversionAccuracy==="longterm"||!1;let r=t?Ts:ks;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||k.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,t){return g.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(e==null||typeof e!="object")throw new N(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new g({values:Fe(e,g.normalizeUnit),loc:k.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(R(e))return g.fromMillis(e);if(g.isDuration(e))return e;if(typeof e=="object")return g.fromObject(e);throw new N(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){const[r]=fs(e);return r?g.fromObject(r,t):g.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){const[r]=hs(e);return r?g.fromObject(r,t):g.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new N("need to specify a reason the Duration is invalid");const r=e instanceof C?e:new C(e,t);if(S.throwOnInvalid)throw new Pn(r);return new g({invalid:r})}static normalizeUnit(e){const t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!t)throw new Zt(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){const r={...t,floor:t.round!==!1&&t.floor!==!1};return this.isValid?I.create(this.loc,r).formatDurationFromString(this,e):It}toHuman(e={}){if(!this.isValid)return It;const t=Y.map(r=>{const s=this.values[r];return d(s)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(s)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=tt(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;const t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},h.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Dn(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;const t=g.fromDurationLike(e),r={};for(const s of Y)(te(t.values,s)||te(this.values,s))&&(r[s]=t.get(s)+this.get(s));return A(this,{values:r},!0)}minus(e){if(!this.isValid)return this;const t=g.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;const t={};for(const r of Object.keys(this.values))t[r]=hn(e(this.values[r],r));return A(this,{values:t},!0)}get(e){return this[g.normalizeUnit(e)]}set(e){if(!this.isValid)return this;const t={...this.values,...Fe(e,g.normalizeUnit)};return A(this,{values:t})}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:r,matrix:s}={}){const a={loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:s,conversionAccuracy:r};return A(this,a)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;const e=this.toObject();return Et(this.matrix,e),A(this,{values:e},!0)}rescale(){if(!this.isValid)return this;const e=Os(this.normalize().shiftToAll().toObject());return A(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(a=>g.normalizeUnit(a));const t={},r={},s=this.toObject();let i;for(const a of Y)if(e.indexOf(a)>=0){i=a;let o=0;for(const l in r)o+=this.matrix[l][a]*r[l],r[l]=0;R(s[a])&&(o+=s[a]);const u=Math.trunc(o);t[a]=u,r[a]=(o*1e3-u*1e3)/1e3}else R(s[a])&&(r[a]=s[a]);for(const a in r)r[a]!==0&&(t[i]+=a===i?r[a]:r[a]/this.matrix[i][a]);return Et(this.matrix,t),A(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const e={};for(const t of Object.keys(this.values))e[t]=this.values[t]===0?0:-this.values[t];return A(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function t(r,s){return r===void 0||r===0?s===void 0||s===0:r===s}for(const r of Y)if(!t(this.values[r],e.values[r]))return!1;return!0}}const B="Invalid Interval";function ps(n,e){return!n||!n.isValid?T.invalid("missing or invalid start"):!e||!e.isValid?T.invalid("missing or invalid end"):e<n?T.invalid("end before start",`The end of an interval must be after its start, but you had start=${n.toISO()} and end=${e.toISO()}`):null}class T{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new N("need to specify a reason the Interval is invalid");const r=e instanceof C?e:new C(e,t);if(S.throwOnInvalid)throw new Yn(r);return new T({invalid:r})}static fromDateTimes(e,t){const r=le(e),s=le(t),i=ps(r,s);return i??new T({start:r,end:s})}static after(e,t){const r=g.fromDurationLike(t),s=le(e);return T.fromDateTimes(s,s.plus(r))}static before(e,t){const r=g.fromDurationLike(t),s=le(e);return T.fromDateTimes(s.minus(r),s)}static fromISO(e,t){const[r,s]=(e||"").split("/",2);if(r&&s){let i,a;try{i=h.fromISO(r,t),a=i.isValid}catch{a=!1}let o,u;try{o=h.fromISO(s,t),u=o.isValid}catch{u=!1}if(a&&u)return T.fromDateTimes(i,o);if(a){const l=g.fromISO(s,t);if(l.isValid)return T.after(i,l)}else if(u){const l=g.fromISO(r,t);if(l.isValid)return T.before(o,l)}}return T.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){if(!this.isValid)return NaN;const r=this.start.startOf(e,t);let s;return t!=null&&t.useLocaleWeeks?s=this.end.reconfigure({locale:r.locale}):s=this.end,s=s.startOf(e,t),Math.floor(s.diff(r,e).get(e))+(s.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:t}={}){return this.isValid?T.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];const t=e.map(le).filter(a=>this.contains(a)).sort((a,o)=>a.toMillis()-o.toMillis()),r=[];let{s}=this,i=0;for(;s<this.e;){const a=t[i]||this.e,o=+a>+this.e?this.e:a;r.push(T.fromDateTimes(s,o)),s=o,i+=1}return r}splitBy(e){const t=g.fromDurationLike(e);if(!this.isValid||!t.isValid||t.as("milliseconds")===0)return[];let{s:r}=this,s=1,i;const a=[];for(;r<this.e;){const o=this.start.plus(t.mapUnits(u=>u*s));i=+o>+this.e?this.e:o,a.push(T.fromDateTimes(r,i)),r=i,s+=1}return a}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;const t=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return t>=r?null:T.fromDateTimes(t,r)}union(e){if(!this.isValid)return this;const t=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return T.fromDateTimes(t,r)}static merge(e){const[t,r]=e.sort((s,i)=>s.s-i.s).reduce(([s,i],a)=>i?i.overlaps(a)||i.abutsStart(a)?[s,i.union(a)]:[s.concat([i]),a]:[s,a],[[],null]);return r&&t.push(r),t}static xor(e){let t=null,r=0;const s=[],i=e.map(u=>[{time:u.s,type:"s"},{time:u.e,type:"e"}]),a=Array.prototype.concat(...i),o=a.sort((u,l)=>u.time-l.time);for(const u of o)r+=u.type==="s"?1:-1,r===1?t=u.time:(t&&+t!=+u.time&&s.push(T.fromDateTimes(t,u.time)),t=null);return T.merge(s)}difference(...e){return T.xor([this].concat(e)).map(t=>this.intersection(t)).filter(t=>t&&!t.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:B}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=De,t={}){return this.isValid?I.create(this.s.loc.clone(t),e).formatInterval(this):B}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:B}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:B}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:B}toFormat(e,{separator:t=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:B}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):g.invalid(this.invalidReason)}mapEndpoints(e){return T.fromDateTimes(e(this.s),e(this.e))}}class Se{static hasDST(e=S.defaultZone){const t=h.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return $.isValidZone(e)}static normalizeZone(e){return z(e,S.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||k.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||k.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||k.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:r=null,locObj:s=null,outputCalendar:i="gregory"}={}){return(s||k.create(t,r,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:s=null,outputCalendar:i="gregory"}={}){return(s||k.create(t,r,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:r=null,locObj:s=null}={}){return(s||k.create(t,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:r=null,locObj:s=null}={}){return(s||k.create(t,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return k.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return k.create(t,null,"gregory").eras(e)}static features(){return{relative:cn(),localeWeek:fn()}}}function Mt(n,e){const t=s=>s.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=t(e)-t(n);return Math.floor(g.fromMillis(r).as("days"))}function vs(n,e,t){const r=[["years",(u,l)=>l.year-u.year],["quarters",(u,l)=>l.quarter-u.quarter+(l.year-u.year)*4],["months",(u,l)=>l.month-u.month+(l.year-u.year)*12],["weeks",(u,l)=>{const f=Mt(u,l);return(f-f%7)/7}],["days",Mt]],s={},i=n;let a,o;for(const[u,l]of r)t.indexOf(u)>=0&&(a=u,s[u]=l(n,e),o=i.plus(s),o>e?(s[u]--,n=i.plus(s),n>e&&(o=n,s[u]--,n=i.plus(s))):n=o);return[n,s,o,a]}function Ns(n,e,t,r){let[s,i,a,o]=vs(n,e,t);const u=e-s,l=t.filter(y=>["hours","minutes","seconds","milliseconds"].indexOf(y)>=0);l.length===0&&(a<e&&(a=s.plus({[o]:1})),a!==s&&(i[o]=(i[o]||0)+u/(a-s)));const f=g.fromObject(i,r);return l.length>0?g.fromMillis(u,r).shiftTo(...l).plus(f):f}const Is="missing Intl.DateTimeFormat.formatToParts support";function w(n,e=t=>t){return{regex:n,deser:([t])=>e(mr(t))}}const Es=String.fromCharCode(160),xn=`[ ${Es}]`,bn=new RegExp(xn,"g");function Ms(n){return n.replace(/\./g,"\\.?").replace(bn,xn)}function Dt(n){return n.replace(/\./g,"").replace(bn," ").toLowerCase()}function V(n,e){return n===null?null:{regex:RegExp(n.map(Ms).join("|")),deser:([t])=>n.findIndex(r=>Dt(t)===Dt(r))+e}}function xt(n,e){return{regex:n,deser:([,t,r])=>Le(t,r),groups:e}}function Oe(n){return{regex:n,deser:([e])=>e}}function Ds(n){return n.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function xs(n,e){const t=F(e),r=F(e,"{2}"),s=F(e,"{3}"),i=F(e,"{4}"),a=F(e,"{6}"),o=F(e,"{1,2}"),u=F(e,"{1,3}"),l=F(e,"{1,6}"),f=F(e,"{1,9}"),y=F(e,"{2,4}"),O=F(e,"{4,6}"),m=L=>({regex:RegExp(Ds(L.val)),deser:([J])=>J,literal:!0}),G=(L=>{if(n.literal)return m(L);switch(L.val){case"G":return V(e.eras("short"),0);case"GG":return V(e.eras("long"),0);case"y":return w(l);case"yy":return w(y,je);case"yyyy":return w(i);case"yyyyy":return w(O);case"yyyyyy":return w(a);case"M":return w(o);case"MM":return w(r);case"MMM":return V(e.months("short",!0),1);case"MMMM":return V(e.months("long",!0),1);case"L":return w(o);case"LL":return w(r);case"LLL":return V(e.months("short",!1),1);case"LLLL":return V(e.months("long",!1),1);case"d":return w(o);case"dd":return w(r);case"o":return w(u);case"ooo":return w(s);case"HH":return w(r);case"H":return w(o);case"hh":return w(r);case"h":return w(o);case"mm":return w(r);case"m":return w(o);case"q":return w(o);case"qq":return w(r);case"s":return w(o);case"ss":return w(r);case"S":return w(u);case"SSS":return w(s);case"u":return Oe(f);case"uu":return Oe(o);case"uuu":return w(t);case"a":return V(e.meridiems(),0);case"kkkk":return w(i);case"kk":return w(y,je);case"W":return w(o);case"WW":return w(r);case"E":case"c":return w(t);case"EEE":return V(e.weekdays("short",!1),1);case"EEEE":return V(e.weekdays("long",!1),1);case"ccc":return V(e.weekdays("short",!0),1);case"cccc":return V(e.weekdays("long",!0),1);case"Z":case"ZZ":return xt(new RegExp(`([+-]${o.source})(?::(${r.source}))?`),2);case"ZZZ":return xt(new RegExp(`([+-]${o.source})(${r.source})?`),2);case"z":return Oe(/[a-z_+-/]{1,256}?/i);case" ":return Oe(/[^\S\n\r]/);default:return m(L)}})(n)||{invalidReason:Is};return G.token=n,G}const bs={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Fs(n,e,t){const{type:r,value:s}=n;if(r==="literal"){const u=/^\s+$/.test(s);return{literal:!u,val:u?" ":s}}const i=e[r];let a=r;r==="hour"&&(e.hour12!=null?a=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?a="hour12":a="hour24":a=t.hour12?"hour12":"hour24");let o=bs[a];if(typeof o=="object"&&(o=o[i]),o)return{literal:!1,val:o}}function Vs(n){return[`^${n.map(t=>t.regex).reduce((t,r)=>`${t}(${r.source})`,"")}$`,n]}function Cs(n,e,t){const r=n.match(e);if(r){const s={};let i=1;for(const a in t)if(te(t,a)){const o=t[a],u=o.groups?o.groups+1:1;!o.literal&&o.token&&(s[o.token.val[0]]=o.deser(r.slice(i,i+u))),i+=u}return[r,s]}else return[r,{}]}function Ws(n){const e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}};let t=null,r;return d(n.z)||(t=$.create(n.z)),d(n.Z)||(t||(t=new E(n.Z)),r=n.Z),d(n.q)||(n.M=(n.q-1)*3+1),d(n.h)||(n.h<12&&n.a===1?n.h+=12:n.h===12&&n.a===0&&(n.h=0)),n.G===0&&n.y&&(n.y=-n.y),d(n.u)||(n.S=et(n.u)),[Object.keys(n).reduce((i,a)=>{const o=e(a);return o&&(i[o]=n[a]),i},{}),t,r]}let ze=null;function Ls(){return ze||(ze=h.fromMillis(1555555555555)),ze}function $s(n,e){if(n.literal)return n;const t=I.macroTokenToFormatOpts(n.val),r=Wn(t,e);return r==null||r.includes(void 0)?n:r}function Fn(n,e){return Array.prototype.concat(...n.map(t=>$s(t,e)))}class Vn{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=Fn(I.parseFormat(t),e),this.units=this.tokens.map(r=>xs(r,e)),this.disqualifyingUnit=this.units.find(r=>r.invalidReason),!this.disqualifyingUnit){const[r,s]=Vs(this.units);this.regex=RegExp(r,"i"),this.handlers=s}}explainFromTokens(e){if(this.isValid){const[t,r]=Cs(e,this.regex,this.handlers),[s,i,a]=r?Ws(r):[null,null,void 0];if(te(r,"a")&&te(r,"H"))throw new K("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:r,result:s,zone:i,specificOffset:a}}else return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function Cn(n,e,t){return new Vn(n,t).explainFromTokens(e)}function Zs(n,e,t){const{result:r,zone:s,specificOffset:i,invalidReason:a}=Cn(n,e,t);return[r,s,i,a]}function Wn(n,e){if(!n)return null;const r=I.create(e,n).dtFormatter(Ls()),s=r.formatToParts(),i=r.resolvedOptions();return s.map(a=>Fs(a,n,i))}const Re="Invalid DateTime",bt=864e13;function fe(n){return new C("unsupported zone",`the zone "${n.name}" is not supported`)}function qe(n){return n.weekData===null&&(n.weekData=xe(n.c)),n.weekData}function He(n){return n.localWeekData===null&&(n.localWeekData=xe(n.c,n.loc.getMinDaysInFirstWeek(),n.loc.getStartOfWeek())),n.localWeekData}function H(n,e){const t={ts:n.ts,zone:n.zone,c:n.c,o:n.o,loc:n.loc,invalid:n.invalid};return new h({...t,...e,old:t})}function Ln(n,e,t){let r=n-e*60*1e3;const s=t.offset(r);if(e===s)return[r,e];r-=(s-e)*60*1e3;const i=t.offset(r);return s===i?[r,s]:[n-Math.min(s,i)*60*1e3,Math.max(s,i)]}function pe(n,e){n+=e*60*1e3;const t=new Date(n);return{year:t.getUTCFullYear(),month:t.getUTCMonth()+1,day:t.getUTCDate(),hour:t.getUTCHours(),minute:t.getUTCMinutes(),second:t.getUTCSeconds(),millisecond:t.getUTCMilliseconds()}}function Ie(n,e,t){return Ln(We(n),e,t)}function Ft(n,e){const t=n.o,r=n.c.year+Math.trunc(e.years),s=n.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...n.c,year:r,month:s,day:Math.min(n.c.day,be(r,s))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},a=g.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),o=We(i);let[u,l]=Ln(o,t,n.zone);return a!==0&&(u+=a,l=n.zone.offset(u)),{ts:u,o:l}}function j(n,e,t,r,s,i){const{setZone:a,zone:o}=t;if(n&&Object.keys(n).length!==0||e){const u=e||o,l=h.fromObject(n,{...t,zone:u,specificOffset:i});return a?l:l.setZone(o)}else return h.invalid(new C("unparsable",`the input "${s}" can't be parsed as ${r}`))}function ve(n,e,t=!0){return n.isValid?I.create(k.create("en-US"),{allowZ:t,forceSimple:!0}).formatDateTimeFromString(n,e):null}function Ye(n,e){const t=n.c.year>9999||n.c.year<0;let r="";return t&&n.c.year>=0&&(r+="+"),r+=p(n.c.year,t?6:4),e?(r+="-",r+=p(n.c.month),r+="-",r+=p(n.c.day)):(r+=p(n.c.month),r+=p(n.c.day)),r}function Vt(n,e,t,r,s,i){let a=p(n.c.hour);return e?(a+=":",a+=p(n.c.minute),(n.c.millisecond!==0||n.c.second!==0||!t)&&(a+=":")):a+=p(n.c.minute),(n.c.millisecond!==0||n.c.second!==0||!t)&&(a+=p(n.c.second),(n.c.millisecond!==0||!r)&&(a+=".",a+=p(n.c.millisecond,3))),s&&(n.isOffsetFixed&&n.offset===0&&!i?a+="Z":n.o<0?(a+="-",a+=p(Math.trunc(-n.o/60)),a+=":",a+=p(Math.trunc(-n.o%60))):(a+="+",a+=p(Math.trunc(n.o/60)),a+=":",a+=p(Math.trunc(n.o%60)))),i&&(a+="["+n.zone.ianaName+"]"),a}const $n={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},As={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Us={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Zn=["year","month","day","hour","minute","second","millisecond"],zs=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Rs=["year","ordinal","hour","minute","second","millisecond"];function qs(n){const e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[n.toLowerCase()];if(!e)throw new Zt(n);return e}function Ct(n){switch(n.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return qs(n)}}function Hs(n){return Me[n]||(Ee===void 0&&(Ee=S.now()),Me[n]=n.offset(Ee)),Me[n]}function Wt(n,e){const t=z(e.zone,S.defaultZone);if(!t.isValid)return h.invalid(fe(t));const r=k.fromObject(e);let s,i;if(d(n.year))s=S.now();else{for(const u of Zn)d(n[u])&&(n[u]=$n[u]);const a=un(n)||ln(n);if(a)return h.invalid(a);const o=Hs(t);[s,i]=Ie(n,o,t)}return new h({ts:s,zone:t,loc:r,o:i})}function Lt(n,e,t){const r=d(t.round)?!0:t.round,s=(a,o)=>(a=tt(a,r||t.calendary?0:2,!0),e.loc.clone(t).relFormatter(t).format(a,o)),i=a=>t.calendary?e.hasSame(n,a)?0:e.startOf(a).diff(n.startOf(a),a).get(a):e.diff(n,a).get(a);if(t.unit)return s(i(t.unit),t.unit);for(const a of t.units){const o=i(a);if(Math.abs(o)>=1)return s(o,a)}return s(n>e?-0:0,t.units[t.units.length-1])}function $t(n){let e={},t;return n.length>0&&typeof n[n.length-1]=="object"?(e=n[n.length-1],t=Array.from(n).slice(0,n.length-1)):t=Array.from(n),[e,t]}let Ee,Me={};class h{constructor(e){const t=e.zone||S.defaultZone;let r=e.invalid||(Number.isNaN(e.ts)?new C("invalid input"):null)||(t.isValid?null:fe(t));this.ts=d(e.ts)?S.now():e.ts;let s=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[s,i]=[e.old.c,e.old.o];else{const o=R(e.o)&&!e.old?e.o:t.offset(this.ts);s=pe(this.ts,o),r=Number.isNaN(s.year)?new C("invalid input"):null,s=r?null:s,i=r?null:o}this._zone=t,this.loc=e.loc||k.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=s,this.o=i,this.isLuxonDateTime=!0}static now(){return new h({})}static local(){const[e,t]=$t(arguments),[r,s,i,a,o,u,l]=t;return Wt({year:r,month:s,day:i,hour:a,minute:o,second:u,millisecond:l},e)}static utc(){const[e,t]=$t(arguments),[r,s,i,a,o,u,l]=t;return e.zone=E.utcInstance,Wt({year:r,month:s,day:i,hour:a,minute:o,second:u,millisecond:l},e)}static fromJSDate(e,t={}){const r=Tr(e)?e.valueOf():NaN;if(Number.isNaN(r))return h.invalid("invalid input");const s=z(t.zone,S.defaultZone);return s.isValid?new h({ts:r,zone:s,loc:k.fromObject(t)}):h.invalid(fe(s))}static fromMillis(e,t={}){if(R(e))return e<-bt||e>bt?h.invalid("Timestamp out of range"):new h({ts:e,zone:z(t.zone,S.defaultZone),loc:k.fromObject(t)});throw new N(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(R(e))return new h({ts:e*1e3,zone:z(t.zone,S.defaultZone),loc:k.fromObject(t)});throw new N("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};const r=z(t.zone,S.defaultZone);if(!r.isValid)return h.invalid(fe(r));const s=k.fromObject(t),i=Fe(e,Ct),{minDaysInFirstWeek:a,startOfWeek:o}=St(i,s),u=S.now(),l=d(t.specificOffset)?r.offset(u):t.specificOffset,f=!d(i.ordinal),y=!d(i.year),O=!d(i.month)||!d(i.day),m=y||O,v=i.weekYear||i.weekNumber;if((m||f)&&v)throw new K("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(O&&f)throw new K("Can't mix ordinal dates with month/day");const G=v||i.weekday&&!m;let L,J,ae=pe(u,l);G?(L=zs,J=As,ae=xe(ae,a,o)):f?(L=Rs,J=Us,ae=Ue(ae)):(L=Zn,J=$n);let it=!1;for(const ue of L){const qn=i[ue];d(qn)?it?i[ue]=J[ue]:i[ue]=ae[ue]:it=!0}const An=G?gr(i,a,o):f?wr(i):un(i),at=An||ln(i);if(at)return h.invalid(at);const Un=G?kt(i,a,o):f?Tt(i):i,[zn,Rn]=Ie(Un,l,r),oe=new h({ts:zn,zone:r,o:Rn,loc:s});return i.weekday&&m&&e.weekday!==oe.weekday?h.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${oe.toISO()}`):oe.isValid?oe:h.invalid(oe.invalid)}static fromISO(e,t={}){const[r,s]=us(e);return j(r,s,t,"ISO 8601",e)}static fromRFC2822(e,t={}){const[r,s]=ls(e);return j(r,s,t,"RFC 2822",e)}static fromHTTP(e,t={}){const[r,s]=cs(e);return j(r,s,t,"HTTP",t)}static fromFormat(e,t,r={}){if(d(e)||d(t))throw new N("fromFormat requires an input string and a format");const{locale:s=null,numberingSystem:i=null}=r,a=k.fromOpts({locale:s,numberingSystem:i,defaultToEN:!0}),[o,u,l,f]=Zs(a,e,t);return f?h.invalid(f):j(o,u,r,`format ${t}`,e,l)}static fromString(e,t,r={}){return h.fromFormat(e,t,r)}static fromSQL(e,t={}){const[r,s]=ws(e);return j(r,s,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new N("need to specify a reason the DateTime is invalid");const r=e instanceof C?e:new C(e,t);if(S.throwOnInvalid)throw new Hn(r);return new h({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){const r=Wn(e,k.fromObject(t));return r?r.map(s=>s?s.val:null).join(""):null}static expandFormat(e,t={}){return Fn(I.parseFormat(e),k.fromObject(t)).map(s=>s.val).join("")}static resetCache(){Ee=void 0,Me={}}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?qe(this).weekYear:NaN}get weekNumber(){return this.isValid?qe(this).weekNumber:NaN}get weekday(){return this.isValid?qe(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?He(this).weekday:NaN}get localWeekNumber(){return this.isValid?He(this).weekNumber:NaN}get localWeekYear(){return this.isValid?He(this).weekYear:NaN}get ordinal(){return this.isValid?Ue(this.c).ordinal:NaN}get monthShort(){return this.isValid?Se.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?Se.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?Se.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?Se.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const e=864e5,t=6e4,r=We(this.c),s=this.zone.offset(r-e),i=this.zone.offset(r+e),a=this.zone.offset(r-s*t),o=this.zone.offset(r-i*t);if(a===o)return[this];const u=r-a*t,l=r-o*t,f=pe(u,a),y=pe(l,o);return f.hour===y.hour&&f.minute===y.minute&&f.second===y.second&&f.millisecond===y.millisecond?[H(this,{ts:u}),H(this,{ts:l})]:[this]}get isInLeapYear(){return ye(this.year)}get daysInMonth(){return be(this.year,this.month)}get daysInYear(){return this.isValid?X(this.year):NaN}get weeksInWeekYear(){return this.isValid?he(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?he(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){const{locale:t,numberingSystem:r,calendar:s}=I.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:r,outputCalendar:s}}toUTC(e=0,t={}){return this.setZone(E.instance(e),t)}toLocal(){return this.setZone(S.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:r=!1}={}){if(e=z(e,S.defaultZone),e.equals(this.zone))return this;if(e.isValid){let s=this.ts;if(t||r){const i=e.offset(this.ts),a=this.toObject();[s]=Ie(a,i,e)}return H(this,{ts:s,zone:e})}else return h.invalid(fe(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:r}={}){const s=this.loc.clone({locale:e,numberingSystem:t,outputCalendar:r});return H(this,{loc:s})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;const t=Fe(e,Ct),{minDaysInFirstWeek:r,startOfWeek:s}=St(t,this.loc),i=!d(t.weekYear)||!d(t.weekNumber)||!d(t.weekday),a=!d(t.ordinal),o=!d(t.year),u=!d(t.month)||!d(t.day),l=o||u,f=t.weekYear||t.weekNumber;if((l||a)&&f)throw new K("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&a)throw new K("Can't mix ordinal dates with month/day");let y;i?y=kt({...xe(this.c,r,s),...t},r,s):d(t.ordinal)?(y={...this.toObject(),...t},d(t.day)&&(y.day=Math.min(be(y.year,y.month),y.day))):y=Tt({...Ue(this.c),...t});const[O,m]=Ie(y,this.o,this.zone);return H(this,{ts:O,o:m})}plus(e){if(!this.isValid)return this;const t=g.fromDurationLike(e);return H(this,Ft(this,t))}minus(e){if(!this.isValid)return this;const t=g.fromDurationLike(e).negate();return H(this,Ft(this,t))}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;const r={},s=g.normalizeUnit(e);switch(s){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break}if(s==="weeks")if(t){const i=this.loc.getStartOfWeek(),{weekday:a}=this;a<i&&(r.weekNumber=this.weekNumber-1),r.weekday=i}else r.weekday=1;if(s==="quarters"){const i=Math.ceil(this.month/3);r.month=(i-1)*3+1}return this.set(r)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?I.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):Re}toLocaleString(e=De,t={}){return this.isValid?I.create(this.loc.clone(t),e).formatDateTime(this):Re}toLocaleParts(e={}){return this.isValid?I.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:r=!1,includeOffset:s=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;const a=e==="extended";let o=Ye(this,a);return o+="T",o+=Vt(this,a,t,r,s,i),o}toISODate({format:e="extended"}={}){return this.isValid?Ye(this,e==="extended"):null}toISOWeekDate(){return ve(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:r=!0,includePrefix:s=!1,extendedZone:i=!1,format:a="extended"}={}){return this.isValid?(s?"T":"")+Vt(this,a==="extended",t,e,r,i):null}toRFC2822(){return ve(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return ve(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Ye(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:r=!0}={}){let s="HH:mm:ss.SSS";return(t||e)&&(r&&(s+=" "),t?s+="z":e&&(s+="ZZ")),ve(this,s,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():Re}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};const t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",r={}){if(!this.isValid||!e.isValid)return g.invalid("created by diffing an invalid DateTime");const s={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=Sr(t).map(g.normalizeUnit),a=e.valueOf()>this.valueOf(),o=a?this:e,u=a?e:this,l=Ns(o,u,i,s);return a?l.negate():l}diffNow(e="milliseconds",t={}){return this.diff(h.now(),e,t)}until(e){return this.isValid?T.fromDateTimes(this,e):this}hasSame(e,t,r){if(!this.isValid)return!1;const s=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(t,r)<=s&&s<=i.endOf(t,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;const t=e.base||h.fromObject({},{zone:this.zone}),r=e.padding?this<t?-e.padding:e.padding:0;let s=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(s=e.unit,i=void 0),Lt(t,this.plus(r),{...e,numeric:"always",units:s,unit:i})}toRelativeCalendar(e={}){return this.isValid?Lt(e.base||h.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(h.isDateTime))throw new N("min requires all arguments be DateTimes");return Ot(e,t=>t.valueOf(),Math.min)}static max(...e){if(!e.every(h.isDateTime))throw new N("max requires all arguments be DateTimes");return Ot(e,t=>t.valueOf(),Math.max)}static fromFormatExplain(e,t,r={}){const{locale:s=null,numberingSystem:i=null}=r,a=k.fromOpts({locale:s,numberingSystem:i,defaultToEN:!0});return Cn(a,e,t)}static fromStringExplain(e,t,r={}){return h.fromFormatExplain(e,t,r)}static buildFormatParser(e,t={}){const{locale:r=null,numberingSystem:s=null}=t,i=k.fromOpts({locale:r,numberingSystem:s,defaultToEN:!0});return new Vn(i,e)}static fromFormatParser(e,t,r={}){if(d(e)||d(t))throw new N("fromFormatParser requires an input string and a format parser");const{locale:s=null,numberingSystem:i=null}=r,a=k.fromOpts({locale:s,numberingSystem:i,defaultToEN:!0});if(!a.equals(t.locale))throw new N(`fromFormatParser called with a locale of ${a}, but the format parser was created for ${t.locale}`);const{result:o,zone:u,specificOffset:l,invalidReason:f}=t.explainFromTokens(e);return f?h.invalid(f):j(o,u,r,`format ${t.format}`,e,l)}static get DATE_SHORT(){return De}static get DATE_MED(){return At}static get DATE_MED_WITH_WEEKDAY(){return Gn}static get DATE_FULL(){return Ut}static get DATE_HUGE(){return zt}static get TIME_SIMPLE(){return Rt}static get TIME_WITH_SECONDS(){return qt}static get TIME_WITH_SHORT_OFFSET(){return Ht}static get TIME_WITH_LONG_OFFSET(){return Yt}static get TIME_24_SIMPLE(){return Pt}static get TIME_24_WITH_SECONDS(){return Gt}static get TIME_24_WITH_SHORT_OFFSET(){return Jt}static get TIME_24_WITH_LONG_OFFSET(){return _t}static get DATETIME_SHORT(){return Bt}static get DATETIME_SHORT_WITH_SECONDS(){return jt}static get DATETIME_MED(){return Qt}static get DATETIME_MED_WITH_SECONDS(){return Kt}static get DATETIME_MED_WITH_WEEKDAY(){return Jn}static get DATETIME_FULL(){return Xt}static get DATETIME_FULL_WITH_SECONDS(){return en}static get DATETIME_HUGE(){return tn}static get DATETIME_HUGE_WITH_SECONDS(){return nn}}function le(n){if(h.isDateTime(n))return n;if(n&&n.valueOf&&R(n.valueOf()))return h.fromJSDate(n);if(n&&typeof n=="object")return h.fromObject(n);throw new N(`Unknown datetime argument: ${n}, of type ${typeof n}`)}export{h as D};
