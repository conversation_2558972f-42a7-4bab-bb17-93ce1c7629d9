import{u as s}from"./useIntersect-6e15125e.js";import{e as n,l as c}from"./app-f0078ddb.js";function v(r,t=null){const e=n(r),a=n(e.value.data),u=c(()=>e.value.next_page_url!==null),l=()=>{u.value&&axios.get(e.value.next_page_url).then(o=>{a.value=[...a.value,...o.data.data],e.value=o.data})};return t!==null&&s(t,l,{rootMargin:"0px 0px 250px 0px"}),{items:a,dataObject:e,loadMoreItems:l,reset:()=>a.value=e.value.data,canLoadMoreItems:u}}export{v as u};
