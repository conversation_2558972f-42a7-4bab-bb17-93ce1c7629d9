import n from"./OptionsMode-26ccd316.js";import d from"./OptionsDifficulty-c63856d1.js";import{V as f,o as l,d as c,a as r,b as m,u}from"./app-f0078ddb.js";import"./radio-group-97521e36.js";import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";/* empty css            */const _={class:"flex-1 py-2"},y={class:"mt-8 bg-gray-100 rounded-lg p-6"},j={__name:"Options",props:{},emits:["update:options"],setup(g,{emit:p}){let i=f({time:0,difficulty:0});const a=p;function e(s,t){switch(s){case"time":i.time=t;break;case"difficulty":i.difficulty=t;break}a("update:options",i)}return(s,t)=>(l(),c("div",_,[t[2]||(t[2]=r("h1",{class:"text-4xl text-gray-900 font-bold"},"Options",-1)),r("div",y,[m(n,{"current-mode":u(i).time,"onUpdate:mode":t[0]||(t[0]=o=>e("time",o))},null,8,["current-mode"]),m(d,{class:"mt-12","onUpdate:difficulty":t[1]||(t[1]=o=>e("difficulty",o))})])]))}};export{j as default};
