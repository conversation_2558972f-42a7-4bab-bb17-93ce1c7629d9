import{_ as m,L as d,b as n}from"./AppLayout-33f062bc.js";import{_ as p}from"./Breadcrumbs-c96e9207.js";import{_ as r}from"./ButtonItem-718c0517.js";import{i as g,o as u,c as x,w as l,b as o,a as s,g as a}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";/* empty css            */const f={class:"lg:pr-96 2xl:pr-[32rem]"},y={class:"p-8"},b={class:"mt-8 w-full"},w={class:"mt-4 grid grid-cols-2 gap-4"},v={class:"rounded-lg bg-slate-900"},T={class:"mt-8 w-full"},z={class:"mt-4 grid grid-cols-2 items-center gap-4"},ot={__name:"Style",setup(k){const i=[{name:"Style",href:"#",current:!0}];return(L,t)=>{const e=g("Head");return u(),x(m,null,{default:l(()=>[o(e,null,{default:l(()=>t[0]||(t[0]=[s("title",null,"Style",-1)])),_:1}),s("main",f,[s("div",y,[o(p,{class:"lg:col-span-9 xl:grid-cols-10",pages:i}),t[21]||(t[21]=s("h1",{class:"mt-12 text-5xl font-bold text-gray-900"}," LatinTutorial Style ",-1)),t[22]||(t[22]=s("p",{class:"mt-4 text-gray-600"},"This is the style page.",-1)),s("section",b,[t[5]||(t[5]=s("h3",{class:"text-xl font-bold text-gray-900"},"Logo",-1)),s("div",w,[t[1]||(t[1]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Full width ",-1)),o(d,{class:"mt-4 w-full"}),t[2]||(t[2]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Dark",-1)),s("div",v,[o(n,{class:"mt-4 w-full"})]),t[3]||(t[3]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Icon Only",-1)),t[4]||(t[4]=s("img",{class:"mx-auto h-12 w-auto",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/img/latintutorial-colosseum.svg",alt:"LatinTutorial"},null,-1))])]),t[23]||(t[23]=s("section",{class:"mt-8 w-full"},[s("h3",{class:"text-xl font-bold text-gray-900"},"Colors"),s("div",{class:"mt-4 grid w-full grid-cols-5 overflow-hidden rounded-xl"},[s("div",{class:"h-36 bg-slate-900"}),s("div",{class:"h-36 bg-indigo-600"}),s("div",{class:"h-36 bg-sky-400"}),s("div",{class:"h-36 bg-teal-400"}),s("div",{class:"h-36 bg-slate-100"})])],-1)),s("section",T,[t[20]||(t[20]=s("h3",{class:"text-xl font-bold text-gray-900"},"Buttons",-1)),s("div",z,[t[13]||(t[13]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Primary",-1)),o(r,{size:"md",color:"purple"},{default:l(()=>t[6]||(t[6]=[a(" Primary ")])),_:1}),t[14]||(t[14]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Secondary",-1)),o(r,{size:"md",color:"sky"},{default:l(()=>t[7]||(t[7]=[a(" Secondary ")])),_:1}),t[15]||(t[15]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Tertiary",-1)),o(r,{size:"md",color:"gray"},{default:l(()=>t[8]||(t[8]=[a(" Tertiary ")])),_:1}),t[16]||(t[16]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Warning",-1)),o(r,{size:"md",color:"amber"},{default:l(()=>t[9]||(t[9]=[a(" Warning ")])),_:1}),t[17]||(t[17]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Danger",-1)),o(r,{size:"md",color:"red"},{default:l(()=>t[10]||(t[10]=[a(" Danger ")])),_:1}),t[18]||(t[18]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Vocabulary ",-1)),o(r,{size:"md",color:"orange"},{default:l(()=>t[11]||(t[11]=[a(" Vocabulary ")])),_:1}),t[19]||(t[19]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Grammar",-1)),o(r,{size:"md",color:"teal"},{default:l(()=>t[12]||(t[12]=[a(" Grammar ")])),_:1})])]),t[24]||(t[24]=s("section",{class:"mt-8 w-full"},[s("h3",{class:"text-xl font-bold text-gray-900"},"Typography")],-1))])]),t[25]||(t[25]=s("aside",{class:"bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},null,-1))]),_:1})}}};export{ot as default};
