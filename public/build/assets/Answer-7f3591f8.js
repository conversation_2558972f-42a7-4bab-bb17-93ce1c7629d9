import{e as b,A as x,B as h,o as r,d as i,a as s,r as l,F as k,h as v,f as p,n as a,u as q,z as B,t as C}from"./app-f0078ddb.js";/* empty css            */const w={key:0,class:"flex-1"},$={class:"mt-4 mb-4 text-center text-3xl font-semibold whitespace-pre-line text-gray-900"},D={class:"text-center text-lg font-medium text-gray-600"},A={class:"relative my-6 grid w-full grid-cols-1 justify-items-center select-none xl:px-16"},K={class:"grid w-full grid-cols-1 gap-4 sm:grid-cols-4"},_=["onClick"],j={class:"line-clamp-2"},E=["disabled"],z={__name:"Answer",props:{question:{type:Object,required:!0},isCorrect:{type:Boolean,required:!0},nextItemButtonDisabled:{type:<PERSON>olean,required:!0}},emits:["nextQuestion"],setup(t,{emit:y}){const u=t,f=y;function d(){f("nextQuestion")}function c(e){e.ctrlKey||e.metaKey||e.altKey||(e.preventDefault(),(e.which==13||e.which==32)&&!u.nextItemButtonDisabled&&B(()=>{d()}))}let m=b(!0);return u.isCorrect||setTimeout(()=>{m.value=!1},50),x(()=>{document.addEventListener("keydown",c)}),h(()=>{document.removeEventListener("keydown",c)}),(e,g)=>(r(),i("div",null,[t.question.type=="mc"?(r(),i("div",w,[s("h1",$,[l(e.$slots,"stem")]),s("h3",D,[l(e.$slots,"instructions")]),l(e.$slots,"latin"),l(e.$slots,"english"),s("div",A,[s("div",K,[(r(!0),i(k,null,v(t.question.options,(o,n)=>(r(),i("div",{key:o,class:a(["col-span-2 mt-2",{"col-start-2":n===2&&t.question.options.length===3}])},[s("div",{class:a(["flex h-24 w-full transform items-center justify-center rounded-lg border-2 px-6 py-4 text-xl font-semibold shadow-md duration-150 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden",[n===0&&t.question.key==o?"border-lime-400 bg-lime-100 text-gray-900":"border-gray-400 bg-gray-50 text-gray-400",n===1&&t.question.key==o?"border-teal-400 bg-teal-100 text-gray-900":"border-gray-400 bg-gray-50 text-gray-400",n===2&&t.question.key==o?"border-rose-400 bg-rose-100 text-gray-900":"border-gray-400 bg-gray-50 text-gray-400",n===3&&t.question.key==o?"border-sky-400 bg-sky-100 text-gray-900":"border-gray-400 bg-gray-50 text-gray-400"]]),onClick:I=>e.submit(o)},[s("p",j,C(o),1)],10,_)],2))),128))])])])):p("",!0),s("button",{class:a(["button-disabled mt-12 w-full transform items-center rounded-md border border-transparent px-6 py-2 text-lg font-semibold text-white shadow-xs duration-150 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden",{"button-animation":!q(m)||t.isCorrect}]),disabled:t.nextItemButtonDisabled,onClick:g[0]||(g[0]=o=>d())}," Continue ",10,E)]))}};export{z as default};
