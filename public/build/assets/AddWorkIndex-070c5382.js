import{_ as T}from"./AppLayout-33f062bc.js";import{_ as M}from"./Breadcrumbs-c96e9207.js";import{_ as A}from"./DropdownWorks-53c01eb4.js";import{_ as B}from"./DropdownGeneral-ce7a4558.js";import{_ as z}from"./ButtonItem-718c0517.js";import{_ as v}from"./_plugin-vue_export-helper-c27b6911.js";import{o as p,d as c,a as t,i as f,c as y,w as l,b as i,h as S,t as g,F as R,f as h,g as n,q as m,x as k}from"./app-f0078ddb.js";import{_ as U}from"./ProgressBar-b7203293.js";import{_ as L}from"./Modal-cd0db00e.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./listbox-f702e976.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";/* empty css            */const P={},V={class:"animate-spin",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48"};function j(s,o){return p(),c("svg",V,o[0]||(o[0]=[t("path",{fill:"#9ca3af",d:"M24,44C12.972,44,4,35.028,4,24S12.972,4,24,4s20,8.972,20,20S35.028,44,24,44z M24,8 C15.178,8,8,15.178,8,24s7.178,16,16,16s16-7.178,16-16S32.822,8,24,8z"},null,-1),t("path",{fill:"#d1d5db",d:"M24,44C12.972,44,4,35.028,4,24h4c0,8.822,7.178,16,16,16s16-7.178,16-16S32.822,8,24,8V4 c11.028,0,20,8.972,20,20S35.028,44,24,44z"},null,-1)]))}const D=v(P,[["render",j]]),O={components:{Breadcrumbs:M,DropdownWorks:A,DropdownGeneral:B,Button:z,SpinnerIcon:D,Progress:U,Modal:L,AppLayout:T},props:{works:Array,authors:Array,categories:Array},data(){return{breadcrumbs:[{name:"Dev",href:"/dev",current:!1},{name:"Add a Work to the Database",href:"#",current:!0}],tokenWork:this.works[0],tokenBook:"",tokenBookList:this.works[0].books,createTokensCount:0,currentAuthor:"",currentCategory:"",tokenizeResults:"",loadingToken:!1,tokenizeProgress:0,allWorks:this.works,allAuthors: <AUTHORS>
