import{o as a,d as i,a as e,e as l,A as D,g as k,t as u,u as o,f as _,q as V,x as C,j as B,c as w,b as x,w as f,F as E,h as U,n as N}from"./app-f0078ddb.js";import{k as z,E as A,O as F,h as L}from"./radio-group-97521e36.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";function S(r,n){return a(),i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"fill-rule":"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z","clip-rule":"evenodd"})])}const q={class:"ml-4"},I={class:"grid grid-cols-5 gap-4"},R=["for"],Z={key:0},G={key:0},H={class:"flex"},J={class:"relative rounded-l-md border border-gray-300 px-3 py-2 shadow-xs focus-within:border-indigo-600 focus-within:ring-1 focus-within:ring-indigo-600"},K=["for"],P=["id","name"],Q={key:1},W={class:"col-span-2 flex"},X={class:"relative grow rounded-l-md border border-gray-300 px-3 py-2 shadow-xs focus-within:border-indigo-600 focus-within:ring-1 focus-within:ring-indigo-600"},Y=["for"],$=["id","name"],ee={key:1},te={class:"mt-4 pl-8"},oe={class:"grid grid-cols-3 gap-4"},ae={class:"flex items-center"},se={class:"flex flex-col text-sm"},re={key:0},ne={key:1},ie={class:"block sm:inline"},le={key:0,class:"z-50 float-right text-green-500"},ve={__name:"TokenSyntax",props:{token:Object,alternates:Array},setup(r){const n=r,T=l(n.alternates);let m=l(n.token),h=l(!1),b=l(!1),y=l(!1);const d=l(n.alternates.filter(p=>p.grammar.replace(/ *\([^)]*\) */g,"")===n.token.grammar)[0]);let c=l(n.token.grammar),g=l(n.token.word_id);l(!1);const M=()=>{axios.post("/api/edit-syntax",{id:n.token.id,syntax:d.value.grammar.replace(/ *\([^)]*\) */g,"")}).then(()=>{h.value=!0,c.value=d.value.grammar.replace(/ *\([^)]*\) */g,"")}).catch(()=>{h.value=!1})},O=()=>{axios.post("/api/edit-syntax",{id:n.token.id,syntax:c.value}).then(()=>{y.value=!0}).catch(()=>{y.value=!1})},j=()=>{axios.post("/api/update-lexicon-id",{token_id:n.token.id,word_id:g.value}).then(p=>{b.value=!0,setTimeout(()=>{m.value=p.data.token},1e3)}).catch(()=>{b.value=!1})};return D(()=>{d.value&&(c.value=d.value.grammar.replace(/ *\([^)]*\) */g,""))}),(p,s)=>(a(),i("div",q,[e("div",I,[e("label",{for:r.token.id,class:"col-span-2 block py-1 text-xl font-semibold text-gray-700"},[k(u(r.token.token)+" ",1),o(m).word?(a(),i("span",Z,[k("("+u(o(m).word.display_word),1),o(m).word.gender?(a(),i("span",G,", "+u(o(m).word.gender),1)):_("",!0),s[5]||(s[5]=k(")"))])):_("",!0)],8,R),e("div",H,[e("div",J,[e("label",{for:"word_id"+r.token.id,class:"absolute -top-2 left-2 -mt-px inline-block bg-white px-1 text-xs font-medium text-gray-900"},"ID",8,K),V(e("input",{id:"word_id"+r.token.id,"onUpdate:modelValue":s[0]||(s[0]=t=>B(g)?g.value=t:g=t),type:"text",name:"word_id"+r.token.id,class:"block w-full border-0 p-0 text-gray-900 placeholder-gray-500 focus:ring-0 sm:text-sm"},null,8,P),[[C,o(g)]])]),e("button",{type:"button",class:"relative -ml-px inline-flex items-center space-x-2 rounded-r-md border border-gray-300 bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500",onClick:j},[o(b)?(a(),w(o(S),{key:0,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):(a(),i("span",Q,"Submit"))])]),e("div",W,[e("div",X,[e("label",{for:"word_id"+r.token.id,class:"absolute -top-2 left-2 -mt-px inline-block bg-white px-1 text-xs font-medium text-gray-900"},"Manually Change Token Syntax",8,Y),V(e("input",{id:r.token.id,"onUpdate:modelValue":s[1]||(s[1]=t=>B(c)?c.value=t:c=t),type:"text",name:"syntax"+r.token.id,class:"block w-full border-0 p-0 text-gray-900 placeholder-gray-500 focus:ring-0 sm:text-sm"},null,8,$),[[C,o(c)]])]),e("button",{type:"button",class:"relative -ml-px inline-flex items-center space-x-2 rounded-r-md border border-gray-300 bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500",onClick:s[2]||(s[2]=t=>O())},[o(y)?(a(),w(o(S),{key:0,class:"h-5 w-5 text-green-600","aria-hidden":"true"})):(a(),i("span",ee,"Submit"))])])]),e("div",te,[x(o(L),{modelValue:d.value,"onUpdate:modelValue":s[4]||(s[4]=t=>d.value=t)},{default:f(()=>[e("div",oe,[(a(!0),i(E,null,U(T.value,t=>(a(),w(o(F),{key:t.name,as:"template",value:t,onClick:s[3]||(s[3]=v=>M())},{default:f(({checked:v})=>[e("div",{class:N([v?"border-transparent":"border-gray-300","relative block cursor-pointer rounded-lg border bg-white px-6 py-4 shadow-xs focus:outline-hidden sm:flex sm:justify-between"])},[e("span",ae,[e("span",se,[x(o(z),{as:"span",class:"font-medium text-gray-900"},{default:f(()=>[t.grammar.replace(/ *\([^)]*\) */g,"").length>0?(a(),i("span",re,u(t.grammar.replace(/ *\([^)]*\) */g,"")),1)):(a(),i("span",ne,"No Syntax"))]),_:2},1024),x(o(A),{as:"span",class:"text-gray-500 line-clamp-1"},{default:f(()=>[e("span",ie,u(t.alt_display)+" - "+u(t.alt_def),1)]),_:2},1024)])]),e("span",{class:N([v?"border-indigo-500":"border-transparent","pointer-events-none absolute -inset-px rounded-lg border-2"]),"aria-hidden":"true"},null,2),o(h)&&d.value===t?(a(),i("div",le,[x(o(S),{class:"h-5 w-5"})])):_("",!0)],2)]),_:2},1032,["value"]))),128))])]),_:1},8,["modelValue"])])]))}};export{ve as default};
