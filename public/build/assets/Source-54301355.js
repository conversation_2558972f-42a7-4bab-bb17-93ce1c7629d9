import{l as P,e as p,J as z,o as a,d as r,a as t,g as b,j as v,n as u,c as y,G as D,t as n,b as R,w as g,u as m,Q as E,F as x,q as W,bP as j,h}from"./app-f0078ddb.js";import{_ as G}from"./ButtonItem-718c0517.js";import{_ as J}from"./Modal-cd0db00e.js";import{C as Q,T as U}from"./Colosseum-0e8d62a4.js";/* empty css            */import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./_plugin-vue_export-helper-c27b6911.js";const Y={class:"rounded-xl bg-white p-2 lg:p-4"},H=["src","alt"],I={key:2,class:"mx-auto font-intro text-2xl font-semibold text-gray-900"},K={class:"flex flex-col"},O={class:"text-base font-bold text-gray-900"},X={class:"-mt-1 text-sm text-gray-600"},Z={class:"mb-8"},ee={class:"mt-2"},te={class:"sm:hidden"},le=["value"],oe={class:"hidden sm:block"},ae={class:"border-b border-gray-200"},se={class:"-mb-px flex space-x-8","aria-label":"Tabs"},re=["onClick","aria-current"],ie={class:"mt-4 flex h-[29vh] flex-col gap-4 overflow-y-auto"},ne=["onClick"],de=["src","alt"],ue={class:"flex flex-col"},me={class:"text-base font-bold text-gray-900"},ce={class:"-mt-1 text-sm text-gray-600"},Be={__name:"Source",props:{source:String,works:Array},setup(A){const f=A,w=P(()=>[...f.works,{title:"LatinTutorial",color:"indigo",description:"Full Vocabulary by frequency",type:"syllabi",value:"latintutorial",image:null,icon:Q,image_text:null,padding:.5},{title:"AP Latin",color:"blue",description:"Required 1,000 Words",type:"syllabi",value:"ap_syllabus",image:null,icon:null,image_text:"AP",padding:null},{title:"Readings",color:"slate",description:"Words from recent reading sections",type:"syllabi",value:"readings",image:null,icon:U,image_text:null,padding:.5}]);let i=p(!1);const o=p(null);z(()=>{o.value=w.value.find(c=>c.value===f.source.split(":")[1])});const d=p(null);z(()=>{d.value=o.value});const F=()=>{d.value&&(o.value=d.value,axios.post("/api/infinitas/set-source",{source:`${d.value.type}:${d.value.value.toLowerCase()}`}),i.value=!1)},M=c=>{var l;return((l=d.value)==null?void 0:l.value)===c.value};let s=p(f.source.split(":")[0]);const _=[{name:"Syllabi"},{name:"Readings"}];return(c,l)=>{var k,C,S,$,L,T,q,B,V;return a(),r(x,null,[t("div",Y,[l[5]||(l[5]=t("div",{class:"flex flex-row items-center"},[t("h3",{class:"flex items-center text-2xl font-bold text-gray-900"},[b(" Source "),t("span",{class:"ml-2 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 px-2 py-px text-xs font-semibold text-white lowercase"},"new")])],-1)),l[6]||(l[6]=t("p",{class:"mt-2 text-sm text-gray-600"}," Select the primary source for the words you want to practice. ",-1)),t("div",{onClick:l[0]||(l[0]=e=>v(i)?i.value=!0:i=!0),class:"group mt-2 flex cursor-pointer items-center gap-3 rounded-xl border-2 border-dashed border-white px-4 py-4 transition duration-200 ease-in-out hover:border-gray-400 hover:bg-gray-50"},[t("div",{class:u(["relative flex size-12 items-center overflow-hidden rounded-full bg-white",[`p-${o.value.padding}`,o.value.image?"":`border-2 border-${o.value.color}-600`]])},[(k=o.value)!=null&&k.image?(a(),r("img",{key:0,class:"w-auto",src:(C=o.value)==null?void 0:C.image,alt:(S=o.value)==null?void 0:S.value},null,8,H)):($=o.value)!=null&&$.icon?(a(),y(D((L=o.value)==null?void 0:L.icon),{key:1,class:u(`self-center bg-transparent fill-${(T=o.value)==null?void 0:T.color}-600`)},null,8,["class"])):(a(),r("h1",I,n((q=o.value)==null?void 0:q.image_text),1))],2),t("div",K,[t("h4",O,n((B=o.value)==null?void 0:B.title),1),t("p",X,n((V=o.value)==null?void 0:V.description),1)]),l[4]||(l[4]=t("div",{class:"grow text-right"},[t("span",{class:"text-right text-sm text-white transition duration-200 ease-in-out group-hover:text-gray-600"},"Click to Change")],-1))])]),(a(),y(E,{to:"body"},[R(J,{open:m(i),onCloseModal:l[3]||(l[3]=e=>v(i)?i.value=!1:i=!1),modalTitle:"Select a Source"},{title:g(()=>l[7]||(l[7]=[b("Select a Source")])),content:g(()=>[t("div",Z,[l[10]||(l[10]=t("p",{class:"mt-2 w-full text-sm text-gray-600"}," You will see words from the full vocabulary once you have all words from this source added to your queue. ",-1)),t("div",ee,[t("div",te,[l[8]||(l[8]=t("label",{for:"tabs",class:"sr-only"},"Select a tab",-1)),W(t("select",{id:"tabs",name:"tabs","onUpdate:modelValue":l[1]||(l[1]=e=>v(s)?s.value=e:s=e),class:"block w-full rounded-md border-gray-300 py-2 pr-10 pl-3 text-base focus:border-indigo-500 focus:ring-indigo-500 focus:outline-hidden sm:text-sm"},[(a(),r(x,null,h(_,e=>t("option",{key:e.name,value:e.name.toLowerCase()},n(e.name),9,le)),64))],512),[[j,m(s)]])]),t("div",oe,[t("div",ae,[t("nav",se,[(a(),r(x,null,h(_,e=>t("div",{key:e.name,onClick:N=>v(s)?s.value=e.name.toLowerCase():s=e.name.toLowerCase(),class:u([m(s)===e.name.toLowerCase()?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700","cursor-pointer border-b-2 px-1 py-4 text-sm font-bold whitespace-nowrap"]),"aria-current":m(s)===e.name.toLowerCase()?"page":void 0},n(e.name),11,re)),64))])])])]),t("div",ie,[(a(!0),r(x,null,h(w.value.filter(e=>e.type===m(s)),e=>(a(),r("div",{key:e,class:u(["flex cursor-pointer items-center gap-3 rounded-xl border-2 px-2 py-2 transition duration-200 ease-in-out",[M(e)?"border-emerald-400 bg-emerald-50":"border-white bg-white hover:border-gray-300"]]),onClick:N=>d.value=e},[l[9]||(l[9]=t("div",{class:"hidden border-slate-600 fill-slate-600"},null,-1)),t("div",{class:u(["relative flex size-12 items-center overflow-hidden rounded-full bg-white",[`p-${e.padding}`,e.image?"":`border-2 border-${e.color}-600`]])},[e.image?(a(),r("img",{key:0,class:"w-auto",src:e.image,alt:e.value},null,8,de)):e!=null&&e.icon?(a(),y(D(e==null?void 0:e.icon),{key:1,class:u(`self-center bg-transparent fill-${e==null?void 0:e.color}-600`)},null,8,["class"])):(a(),r("h1",{key:2,class:u(["mx-auto font-intro text-2xl font-semibold",`text-${e==null?void 0:e.color}-600`])},n(e.image_text),3))],2),t("div",ue,[t("h4",me,n(e.title),1),t("p",ce,n(e.description),1)])],10,ne))),128))])])]),actionButton:g(()=>[R(G,{size:"md",color:"indigo",onClick:l[2]||(l[2]=e=>F())},{default:g(()=>l[11]||(l[11]=[b("Save ")])),_:1})]),_:1},8,["open"])]))],64)}}};export{Be as default};
