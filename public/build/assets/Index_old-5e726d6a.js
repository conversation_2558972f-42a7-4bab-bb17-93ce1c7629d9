import{_ as z,a as C}from"./AppLayout-33f062bc.js";import{_ as u}from"./ButtonItem-718c0517.js";import{_ as B}from"./ProgressBar-b7203293.js";import{l as g,i as A,o as l,c as v,w as p,b as n,a as t,u as a,k as d,t as c,g as m,d as f,h,F as w,f as P}from"./app-f0078ddb.js";import{_ as E}from"./Assignments-a7efae90.js";import M from"./ActivityItem-7b8627ed.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */import"./pluralize-d25a928b.js";import"./datetime-8ddd27a0.js";import"./AssignmentItemOwner-5f5b8c19.js";import"./ClassroomModal-05d10768.js";import"./UtilityDropdown-2d786282.js";import"./CopyToClipboard-21badf5d.js";import"./clipboard-a66b13b3.js";import"./AssignModule-74fae4e9.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";import"./Combobox-f427c07d.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./InfinitasIcon-1a3ae135.js";import"./AssignReading-df4942fb.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./DropdownBooks-b7db1f80.js";import"./RectangleGroupIcon-c6b3f31f.js";import"./AssignmentItemStudent-2daa0e28.js";import"./CheckBadgeIcon-e7792a17.js";const N={class:"2xl:pr-[32rem] lg:pr-96"},R={class:"p-8"},j={"aria-labelledby":"profile-overview-title"},I={class:"overflow-hidden"},O={class:"pb-6"},D={class:"gap-4 sm:flex sm:items-center sm:justify-between"},F={class:"grow items-stretch sm:flex sm:space-x-5"},H={class:"shrink-0"},T=["src","alt"],G={class:"mt-4 flex flex-col sm:mt-0 sm:pt-1 sm:text-left font-intro"},X={class:"mt-2"},q={class:"text-4xl font-bold text-gray-900 dark:text-gray-100 sm:text-5xl"},J={class:"mt-4 flex gap-2 font-sans"},K={class:"border-t border-gray-300 pt-2"},Q={class:"flex flex-row space-x-4 overflow-x-auto py-4"},U={key:0,class:"mt-12"},W={class:"group"},Y=["src"],Z={class:"mt-4 line-clamp-1 w-full text-xl font-bold text-gray-900 font-intro"},tt={class:"mt-2 text-sm text-gray-600 font-intro font-medium"},et={class:"mt-4 grid grid-cols-3 gap-8"},ot={class:"mt-12"},st={class:"grid grid-cols-2 gap-4 overflow-hidden"},rt={"aria-labelledby":"video"},it={class:"rounded-lg"},lt={class:"w-full overflow-hidden rounded-xl"},nt={class:"group relative"},at=["src"],dt=["src"],ct={"aria-labelledby":"description"},mt={class:"text-xl font-bold text-gray-900 font-intro"},pt={class:"mt-2 flex items-center text-sm font-medium leading-5 text-gray-500"},ut={class:"mr-8 flex items-center"},ft={class:"flex items-center"},gt={class:"mt-4 line-clamp-6 text-sm text-gray-600 font-intro font-medium"},vt={class:"mt-6 text-center"},xt={class:"bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},_t={class:"rounded-lg shadow-sm bg-white p-4"},ht={class:"mt-2 grid grid-cols-1 gap-2"},wt=["href"],be={__name:"Index_old",props:{achievements:Array,activities:Array,assignments:Array,recentSection:Object,latestVideo:Object},setup(r){const s=r,b=g(()=>s.recentSection?"works/"+s.recentSection.book.work.image_name:"works/eutropius.png"),k=g(()=>s.recentSection?y(s.recentSection):"/read/eutropius/breviarum/book-1/chapter-1/1-2"),y=o=>o.chapter?`/read/${o.book.work.author.url}/${o.book.work.url}/${o.book.url}/${o.book.work.l2.toLowerCase()}-${s.recentSection.chapter}/${o.line_start}-${o.line_end}`:`/read/${o.book.work.author.url}/${o.book.work.url}/${o.book.url}/${o.line_start}-${o.line_end}`,S=o=>{let e=o.level.max-o.level.min+1;return(o.xp-o.level.min)/e*100},V=g(()=>s.recentSection?s.recentSection.book.work.name+" "+s.recentSection.book.work.l1+" "+s.recentSection.book.book+(s.recentSection.chapter?" Chapter "+s.recentSection.chapter:"."+s.recentSection.line_start+"-"+s.recentSection.line_end):"Eutropius, Breviarium ab urbe condita"),x=g(()=>s.recentSection.chapter?s.recentSection.book_id+":"+s.recentSection.chapter+":"+s.recentSection.chapter:s.recentSection.book_id+":"+s.recentSection.line_start+":"+s.recentSection.line_end);return(o,e)=>{const _=A("Head");return l(),v(z,null,{default:p(()=>[n(_,{title:"Dashboard"}),t("main",N,[t("div",R,[t("section",j,[t("div",I,[e[12]||(e[12]=t("h2",{id:"profile-overview-title",class:"sr-only"}," Profile Overview ",-1)),t("div",O,[t("div",D,[t("div",F,[t("div",H,[t("img",{class:"mx-auto h-32 w-32 rounded-full",src:a(d)().props.user.profile_photo_url,alt:a(d)().props.user.name},null,8,T)]),t("div",G,[e[3]||(e[3]=t("div",null,[t("p",{class:"text-xl font-semibold text-gray-600 dark:text-gray-200"}," Salve, ")],-1)),t("div",X,[t("p",q,c(a(d)().props.user.name),1)]),t("div",J,[n(u,{link:"/user/profile",size:"sm",color:"black"},{default:p(()=>e[2]||(e[2]=[m(" View Profile ")])),_:1})])])])])]),t("div",K,[t("div",Q,[(l(!0),f(w,null,h(r.achievements,(i,$)=>(l(),v(C,{size:"sm",icon:i.icon,description:i.description,name:i.name,level:i.level,key:$,"is-earned":a(d)().props.user.achievements.filter(L=>i.id===L.id).length>0},null,8,["icon","description","name","level","is-earned"]))),128))]),n(B,{class:"mt-2",height:"h-3",size:"lg","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+a(d)().props.user.level.level,"post-text":a(d)().props.user.xp+" XP",progress:S(a(d)().props.user)},null,8,["pre-text","post-text","progress"])]),r.recentSection?(l(),f("div",U,[t("div",W,[e[7]||(e[7]=t("h5",{class:"mb-2 text-sm font-extrabold uppercase text-gray-500 duration-300"}," Continue Reading ",-1)),t("img",{src:b.value,class:"transition-shadow-md mt-2 w-full rounded-2xl shadow-md duration-300 ease-in-out"},null,8,Y),t("h2",Z,c(V.value),1),t("p",tt,c(r.recentSection.description),1),t("div",et,[n(u,{color:"gray",size:"md",link:k.value},{default:p(()=>e[4]||(e[4]=[m("Read this Section")])),_:1},8,["link"]),n(u,{color:"pink",size:"md",onClick:e[0]||(e[0]=i=>o.router.post("/practice/vocabulary/create",{sections:[x.value]}))},{default:p(()=>e[5]||(e[5]=[m("Learn Vocabulary")])),_:1}),n(u,{color:"indigo",size:"md",onClick:e[1]||(e[1]=i=>o.router.post("/practice/grammar/create",{sections:[x.value]}))},{default:p(()=>e[6]||(e[6]=[m("Practice Grammar")])),_:1})])])])):P("",!0),t("div",ot,[e[11]||(e[11]=t("h5",{class:"mb-2 text-sm font-extrabold uppercase text-gray-400"}," Latest Video ",-1)),t("div",st,[t("section",rt,[t("div",it,[t("div",lt,[t("div",nt,[o.$page.props.user?(l(),f("iframe",{key:0,class:"aspect-video w-full",src:"https://player.vimeo.com/video/"+r.latestVideo.vimeo_id,frameborder:"0",allow:"autoplay; fullscreen",allowfullscreen:""},null,8,at)):(l(),f("iframe",{key:1,class:"embed-responsive-item aspect-video w-full",src:"https://www.youtube.com/embed/"+r.latestVideo.youtube_id},null,8,dt))])])])]),t("section",ct,[t("h2",mt,c(r.latestVideo.title),1),t("div",pt,[t("span",ut,[e[8]||(e[8]=t("svg",{class:"mr-1 inline h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),t("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)),m(" "+c(r.latestVideo.views.toLocaleString())+" views ",1)]),t("span",ft,[e[9]||(e[9]=t("svg",{class:"mr-1 inline h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},[t("path",{"fill-rule":"evenodd",d:"M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z","clip-rule":"evenodd"})],-1)),m(" "+c(r.latestVideo.likes.toLocaleString())+" likes ",1)])]),t("p",gt,c(r.latestVideo.description),1)])]),t("div",vt,[n(u,{color:"white",size:"sm",link:"watch",class:"text-sm font-semibold text-gray-500 transition duration-300 hover:text-gray-600"},{default:p(()=>e[10]||(e[10]=[m(" View Videos")])),_:1})])])])])])]),t("aside",xt,[t("section",null,[n(E,{assignments:r.assignments},null,8,["assignments"])]),t("section",null,[t("div",_t,[e[13]||(e[13]=t("h5",{class:"text-sm font-bold uppercase text-gray-500"}," Recent Activities ",-1)),t("div",ht,[(l(!0),f(w,null,h(r.activities,i=>(l(),v(M,{key:i.id,activity:i},null,8,["activity"]))),128))])]),t("a",{href:o.route("practice.activities.index")},e[14]||(e[14]=[t("button",{class:"w-full mt-8 shadow-sm border-transparent hover:bg-gray-100 px-3 py-2 text-sm leading-4 text-gray-700 hover:text-gray-800 border bg-white duration-250 items-center rounded-lg text-center font-bold transition focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"}," View All Activities ",-1)]),8,wt)])])]),_:1})}}};export{be as default};
