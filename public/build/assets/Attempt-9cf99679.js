import{V as le,e as p,k as x,l as te,A as Ee,i as Be,o as s,c as f,w as l,b as v,a as o,t as d,j as _e,u as e,d as r,F as W,h as F,n as D,g as y,f as c,T as B,W as I}from"./app-f0078ddb.js";import{_ as Qe}from"./AppLayout-33f062bc.js";import{_ as qe}from"./Breadcrumbs-c96e9207.js";import{_ as ue}from"./ProgressBar-b7203293.js";import Ne from"./Options-d6b29c2b.js";import Re from"./OptionsSidebar-7953908b.js";import{_ as C}from"./ButtonItem-718c0517.js";import{T as ce}from"./easytimer-3d932146.js";import Oe from"./MultipleChoice-080711a4.js";import We from"./Matching-1875e265.js";import Fe from"./Type-dfa282d2.js";import De from"./Drag-75baa519.js";import Ie from"./DragWithWords-609f636f.js";import{r as Ce}from"./replaceMacra-3b9666ed.js";import Me from"./VideoItemSmall-c82820c8.js";import{P as Ue}from"./Promotion-3eee0057.js";import{_ as Xe}from"./Footer-0988dcd8.js";import{_ as Je}from"./MobileSidebar-5e21b4cd.js";import{_ as Te}from"./AssignmentModule-fc2620bb.js";import{r as Ge}from"./InformationCircleIcon-716f3ffb.js";import{r as $e}from"./PlusIcon-4ea782f1.js";import{r as Le,a as Ae}from"./PlayIcon-8c672cba.js";import{a as U}from"./animate.es-f1432f5f.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./OptionsMode-26ccd316.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";import"./OptionsDifficulty-c63856d1.js";import"./NextButtonSmall-9e6ffefc.js";import"./XCircleIcon-63af2b2a.js";import"./vuedraggable.umd-aab17b5c.js";import"./lodash-631955d9.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-6a201aa1.js";import"./ArrowPathIcon-f74cb8d6.js";const Ke={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Ye={class:"px-4 py-8 sm:px-8"},Ze={class:"flex flex-row items-center justify-between"},et={class:"mt-8"},tt={key:0},st={key:1},ot={key:0,class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},at={class:"mt-2 mb-8"},rt={class:"text-center text-xs font-bold text-gray-500 uppercase"},it={class:"mb-8"},nt={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},lt={key:0,class:"mb-8 text-left"},ut={class:"text-sm font-bold text-gray-500 uppercase"},ct={key:0},dt={key:1},mt={key:0,class:"mt-2 flex flex-row items-center"},pt={key:1,class:"w-full"},vt={key:0,class:"mx-auto"},ft={key:1,class:"mx-auto"},gt=["innerHTML"],yt=["innerHTML"],xt=["innerHTML"],ht=["innerHTML"],wt={key:1},kt=["innerHTML"],bt=["innerHTML"],_t={key:0,class:"lg:hidden"},Ct={class:"mt-2 text-left text-sm text-gray-800"},Mt={key:0},Tt={key:1,class:"mx-auto mt-8 text-center lg:hidden"},$t={key:0,class:"mx-auto"},Lt={key:1,class:"mx-auto"},At={key:0},Vt={key:0,class:"mx-auto"},St={key:1,class:"mx-auto"},zt={key:1,class:"mt-2"},jt={class:"text-center text-xs font-bold text-gray-500 uppercase"},Ht={class:"mt-8 font-intro text-2xl font-bold"},Pt={class:"mt-2 font-sans text-sm font-medium text-gray-600"},Et={key:2},Bt={key:3},Qt={key:0,class:"mt-8"},qt={class:"mt-2"},Nt={key:4},Rt={"aria-labelledby":"userStats"},Ot={class:"grid grid-cols-1 gap-px overflow-hidden bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},Wt={class:"text-sm leading-6 font-medium text-gray-500"},Ft={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},Dt={key:0},It={key:0,class:"ml-1 text-sm font-medium text-gray-500"},Ut={key:1},Xt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Jt={key:0,class:"mb-8"},Gt={key:1,class:"mt-2"},Kt={class:"text-center text-xs font-bold text-gray-500 uppercase"},Yt={class:"mt-8 font-intro text-2xl font-bold"},Zt={class:"mt-2 font-sans text-sm font-medium text-gray-600"},es={key:2,class:"mt-8"},ts={key:3},ss={key:1},os={class:"mt-2 text-left text-sm text-gray-800"},as={key:0},rs={class:"mx-auto mt-8 text-center"},is={key:0,class:"mx-auto"},ns={key:1,class:"mx-auto"},ls={key:0},us={key:0,class:"mx-auto"},cs={key:1,class:"mx-auto"},ds={key:4,class:"mt-8 grid grid-cols-1 gap-8"},ms={key:0,class:"text-left"},ps={class:"text-sm font-bold text-gray-500 uppercase"},vs={key:0},fs={key:1},gs={key:0,class:"mt-2 flex flex-row items-center"},ys={"aria-labelledby":"userStats"},xs={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},hs={class:"text-sm leading-6 font-medium text-gray-500"},ws={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},ks={key:0},bs={key:0,class:"ml-1 text-sm font-medium text-gray-500"},_s={key:1},Cs={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},Ms={key:1,class:"mt-8 w-full"},Ts={key:0,class:"mx-auto"},$s={key:1,class:"mx-auto"},Ls={key:5,class:"mt-8"},As={class:"mt-2"},qo={__name:"Attempt",props:{activity:{type:Object,required:!0},session:{type:Object,required:!1},question:{type:Object,required:!1},initialQuestion:{type:Object,required:!1},assignment:{type:Object,required:!1}},setup(m){const g=m,Ve=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:g.activity.name,href:"#",current:!0}];let b=le({time:0,difficulty:0}),z=p(x().props.authenticated?x().props.user:null),de=p(x().props.authenticated?z.value.xp:0),j=x().props.authenticated?p(z.value.xp):0,M=x().props.authenticated?p(z.value.level):p(0),Se=p(x().props.authenticated?z.value.level.next_level_max:null),Q=p(!1),se=p(!1),q=p(!1),u=le({correct:0,incorrect:0}),i=p(),oe=p(),R=p(!0),_=p(!1),w=p(!1),K=p(0),H=x().props.authenticated?p(z.value.level.max-z.value.xp+1):p(0),ae=p(0),k=p(),T=le([]),P=p(0),h=new ce,$=new ce,me=p([]),Y=p(0),N=p(!1),E=p(!0),L=p(!1);function pe(){_.value=!0,I.reload({only:["assignment"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{_.value=!1}})}const ve=n=>{switch(!0){case!n:return null;case Math.round(n/10)<15:return"A few seconds spent";case Math.round(n/10)<30:return"Less than 30 seconds  spent";case Math.round(n/10)<60:return"Less than a minute  spent";case Math.round(n/10)<90:return"About a minute  spent";case Math.round(n/10)<150:return"About 2 minutes spent";case Math.round(n/10)<210:return"About 3 minutes spent";case Math.round(n/10)<270:return"About 4 minutes spent";case Math.round(n/10)<330:return"About 5 minutes spent";default:return"About "+Math.round(n/600)+" minutes spent"}},fe=te(()=>u.correct+u.incorrect==0?-1:u.correct+u.incorrect<10?u.correct/10:Math.round(100*u.correct/(u.correct+u.incorrect))),Z=()=>{x().props.authenticated?(se.value=!0,ye()):I.get(`/login?redirect=/practice/grammar/a/${g.activity.id}`)};let re=p(!1);const ge=()=>{re.value=!0,ye(!0)},ye=(n=!1)=>{I.reload({only:["session","assignment"],data:{reattempt:!!n},preserveState:!0,preserveScroll:!0,onSuccess:()=>{u.correct=g.session.correct,u.incorrect=g.session.attempts-g.session.correct,P.value=g.session.streak,Y.value=g.session.xp_earned,K.value=g.session.correct?Math.round(g.session.correct/g.session.attempts*100):0,axios.post("/api/practice/grammar/update-session-options",{session:g.session.id,settings:b}).then(()=>{setTimeout(()=>{$.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),b.time>0?(h.start({countdown:!0,startValues:{seconds:b.time}}),k.value=O(h.getTimeValues()),h.addEventListener("secondsUpdated",function(){k.value=O(h.getTimeValues())}),h.addEventListener("targetAchieved",function(){ee()})):(k.value=O($.getTimeValues()),$.addEventListener("secondsUpdated",function(){k.value=O($.getTimeValues())})),I.reload({method:"get",data:{session:g.session.id},only:["initialQuestion"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{i.value=g.initialQuestion.first,oe.value=g.initialQuestion.second,q.value=!0}})},ze(200,500))})}})},ie=te(()=>{let n=M.value.max-M.value.min+1;return(j.value-M.value.min)/n*100}),O=n=>n.days>1?"more than "+n.days+" days":n.days>0?"more than "+n.days+" day":n.hours>1?"more than "+n.hours+" hours":n.hours>0?"more than "+n.hours+" hour":n.seconds<=9?n.minutes+":0"+n.seconds:n.minutes+":"+n.seconds,ze=(n,t)=>Math.floor(Math.random()*(t-n+1))+n,xe=()=>{b.time>0?h.isRunning()?(h.pause(),N.value=!0):(h.start(),N.value=!1):$.isRunning()?($.pause(),N.value=!0):($.start(),N.value=!1)},he=()=>{let n=h.getTimeValues();n.minutes+=1,h.removeAllEventListeners("targetAchieved"),h=new ce,h.start({countdown:!0,startValues:{minutes:n.minutes,seconds:n.seconds}}),N.value&&h.pause(),k.value=O(h.getTimeValues()),h.addEventListener("secondsUpdated",function(){k.value=O(h.getTimeValues())}),h.addEventListener("targetAchieved",function(){ee()})},we=te(()=>[{name:"Correct",value:u.correct,unit:"out of "+(u.correct+u.incorrect)},{name:"Current Streak",value:P.value,unit:"in a row"},{name:"XP Earned",value:Y.value},{name:"Accuracy",value:K.value,unit:"%"}]),ke=te(()=>u.correct+u.incorrect==0?-1:u.correct+u.incorrect<10?u.correct:Math.round(10*u.correct/(u.correct+u.incorrect))),be=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],X=()=>{if(w.value)switch(i.value.type){case"drag":switch(b.difficulty){case 1:return 3;case 2:return 4;default:return 2}case"type":switch(b.difficulty){case 1:return 4;case 2:return 5;default:return 3}case"match":switch(b.difficulty){case 1:return 3;case 2:return 4;default:return 2}default:return 1}return 0},J=n=>{if(E.value=!0,_.value)return;if(n.isArray)me.value=n.answer,w.value=JSON.stringify(n.answer)===JSON.stringify(n.key);else switch(!0){case i.value.type=="dragWithWords":w.value=je(n.answer,i.value.key.split(" "));break;default:w.value=Ce(n.answer).toUpperCase()==Ce(n.key).toUpperCase()}if(w.value){if(P.value<1)U(S=>{P.value=Math.round(S)},{duration:.3,easing:"ease-out"});else{let S=P.value;U(Pe=>{P.value=Math.round(Pe+S)},{duration:.3,easing:"ease-out"})}let a=u.correct;U(S=>{u.correct=Math.round(S+a)},{duration:.3,easing:"ease-out"});let ne=de.value;U(S=>{de.value=Math.round(ne+S)},{duration:.3,easing:"ease-out"});let He=Y.value;U(S=>{Y.value=Math.round(He+S*X())},{duration:.3,easing:"ease-out"})}else w.value=!1,P.value=0,u.incorrect++;let t=K.value;if(U(a=>{K.value=t+Math.round((Math.round(u.correct/(u.correct+u.incorrect)*100)-t)*a)},{duration:.3,easing:"ease-out"}),T.find(a=>a.id===i.value.id)){var V=T.findIndex(a=>a.id===i.value.id);T[V].correct=T[V].correct+w.value,T[V].total=T[V].total+1}else T.push({id:i.value.id,word:i.value.word,pos:i.value.pos,definition:i.value.short_definition,core:i.value.core,correct:w.value?1:0,gender:i.value.gender,total:1,lexicon_id:i.value.lexicon_id});Q.value=!0,w.value?E.value=!1:setTimeout(()=>{E.value=!1},2e3),j.value=j.value+X(),H.value<=X()?(M.value.level=M.value.level+1,H.value=Se.value-j.value+1):H.value=H.value-X(),ae.value=$.getTotalTimeValues().seconds,axios.post("/api/practice/grammar/add-attempt",{session:g.session.id,item_id:i.value.id,type:i.value.type,correct:w.value,attempt:Array.isArray(n.answer)?n.answer.join(" "):n.answer,options:i.value.options?i.value.options.join("|"):null,xp:X(),streak:P.value,time:$.getTotalTimeValues().secondTenths}).then(a=>{j.value=a.data.xp,M.value=a.data.level,H.value=a.data.next_level_xp})},je=(n,t)=>{const V=[...new Set(n).values()],a=[...new Set(t).values()];return V.length!=a.length?!1:V.every(ne=>a.includes(ne))};function G(){i.value=oe.value,R.value=!1,Q.value=!1,I.reload({data:{session:g.session.id},only:["question"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{oe.value=g.question,R.value=!0}})}const ee=()=>{_.value=!0,I.post("/api/practice/grammar/finish",{session:g.session.id})};let A=p(!1);return Ee(()=>{g.assignment&&(A.value=g.assignment.completed==0)}),(n,t)=>{const V=Be("Head");return s(),f(Qe,null,{default:l(()=>[v(V,null,{default:l(()=>[o("title",null,d(m.activity.name),1)]),_:1}),o("main",Ke,[o("div",Ye,[o("div",Ze,[v(qe,{class:"lg:col-span-9 xl:grid-cols-10",pages:Ve}),v(e(Ge),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=a=>_e(L)?L.value=!e(L):L=!e(L))})]),o("div",et,[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:l(()=>[e(q)?(s(),r("div",st,[e(x)().props.authenticated?(s(),r("div",ot,[v(ue,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(M).level,"post-text":e(j)+" XP",progress:ie.value},null,8,["pre-text","post-text","progress"]),o("div",at,[o("p",rt,d(e(H))+" xp to the next level ",1)]),o("section",it,[t[27]||(t[27]=o("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),o("div",nt,[(s(),r(W,null,F(be,a=>o("div",{class:D(["h-4 w-full rounded-sm shadow-sm",ke.value>=a.value?a.color:"bg-gray-300"]),key:a.value},null,2)),64))])]),e(k)?(s(),r("section",lt,[o("h5",ut,[t[28]||(t[28]=y(" Time ")),e(b).time>0?(s(),r("span",ct,"Remaining")):(s(),r("span",dt,"Elapsed"))]),e(k)?(s(),r("div",mt,[o("div",{class:D(["grow text-left font-medium text-gray-900",e(k).length>10?"text-2xl":"text-4xl"])},d(e(k)),3),e(b).time>0?(s(),r("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[2]||(t[2]=a=>he())},[v(e($e),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):c("",!0),o("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[3]||(t[3]=a=>xe())},[e(N)?(s(),f(e(Le),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(s(),f(e(Ae),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):c("",!0)])):c("",!0),e(q)?(s(),r("div",pt,[v(C,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(_),onClick:t[4]||(t[4]=a=>ee())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(_)?(s(),r("span",vt,t[29]||(t[29]=[o("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Finishing ...",-1)]))):(s(),r("span",ft,"Finish"))]),_:1})]),_:1},8,["disabled"])])):c("",!0)])):c("",!0),v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:l(()=>[e(i).type==="mc"?(s(),f(Oe,{key:`${e(i).id}-mc`,options:e(i).options,"question-key":e(i).key,"is-correct":e(w),"is-answer":e(Q),attempts:e(u).correct+e(u).incorrect,disabled:e(E)||e(_)||!e(R),onSubmit:t[5]||(t[5]=a=>J(a)),onNextQuestion:t[6]||(t[6]=a=>G())},{stem:l(()=>[o("div",{innerHTML:e(i).stem},null,8,gt)]),instructions:l(()=>[y(d(e(i).instructions),1)]),_:1},8,["options","question-key","is-correct","is-answer","attempts","disabled"])):e(i).type==="match"?(s(),f(We,{key:`${e(i).id}-match`,options:e(i).options,"is-answer":e(Q),"is-correct":e(w),"user-attempt":e(me),disabled:e(E)||e(_)||!e(R),onSubmit:t[7]||(t[7]=a=>J(a)),onNextQuestion:t[8]||(t[8]=a=>G())},{stem:l(()=>[o("div",{innerHTML:e(i).stem},null,8,yt)]),instructions:l(()=>[y(d(e(i).instructions),1)]),_:1},8,["options","is-answer","is-correct","user-attempt","disabled"])):e(i).type==="type"?(s(),f(Fe,{key:`${e(i).id}-type`,options:e(i).key,"is-answer":e(Q),"is-correct":e(w),difficulty:e(i).difficulty,disabled:e(E)||e(_)||!e(R),onSubmit:t[9]||(t[9]=a=>J(a)),onNextQuestion:t[10]||(t[10]=a=>G())},{stem:l(()=>[o("div",{innerHTML:e(i).stem},null,8,xt)]),word:l(()=>[y(d(e(i).word.display_word),1)]),instructions:l(()=>[y(d(e(i).instructions),1)]),_:1},8,["options","is-answer","is-correct","difficulty","disabled"])):e(i).type==="drag"?(s(),f(De,{key:`${e(i).id}-drag`,options:e(i).options,"question-key":e(i).key,"original-key":e(i).originalKey,stem:Array.isArray(e(i).stem)?e(i).stem[1]:"","is-answer":e(Q),"is-correct":e(w),difficulty:e(i).difficulty,disabled:e(E)||e(_)||!e(R),onSubmit:t[11]||(t[11]=a=>J(a)),onNextQuestion:t[12]||(t[12]=a=>G())},{stem:l(()=>[Array.isArray(e(i).stem)?(s(),r("span",{key:0,innerHTML:e(i).stem[0]},null,8,ht)):(s(),r("span",wt,[o("div",{innerHTML:e(i).stem},null,8,kt)]))]),instructions:l(()=>[y(d(e(i).instructions),1)]),_:1},8,["options","question-key","original-key","stem","is-answer","is-correct","difficulty","disabled"])):e(i).type==="dragWithWords"?(s(),f(Ie,{key:`${e(i).id}-drag-with-words`,options:e(i).options,"question-key":e(i).key,stem:e(i).stem,"is-answer":e(Q),"is-correct":e(w),difficulty:e(i).difficulty,disabled:e(E),onSubmit:t[13]||(t[13]=a=>J(a)),onNextQuestion:t[14]||(t[14]=a=>G())},{stem:l(()=>[o("div",{innerHTML:e(i).stem},null,8,bt)]),instructions:l(()=>[y(d(e(i).instructions),1)]),_:1},8,["options","question-key","stem","is-answer","is-correct","difficulty","disabled"])):c("",!0)]),_:1})])):(s(),r("div",tt,[v(Ne,{"onUpdate:options":t[1]||(t[1]=a=>Object.assign(e(b),a))})]))]),_:1})]),e(A)&&m.assignment.attempt?(s(),r("div",_t,[t[30]||(t[30]=o("h4",{class:"mt-8 text-left text-sm font-bold text-gray-500 uppercase"}," Assignment in Progress ",-1)),o("p",Ct,[y(d(m.assignment.attempt.correct)+" out of "+d(m.assignment.attempt.attempts)+" correct ",1),m.assignment.attempt.attempts>0?(s(),r("span",Mt,"("+d(Math.round(100*m.assignment.attempt.correct)/m.assignment.attempt.attempts)+"% accuracy)",1)):c("",!0),y(" | "+d(ve(m.assignment.attempt.time)),1)])])):c("",!0),e(q)?c("",!0):(s(),r("div",Tt,[e(x)().props.authenticated&&e(z).membership.subscribed?(s(),r("div",{key:0,class:D(["grid",e(A)?"grid-cols-2 gap-4":"grid-cols-1"])},[v(C,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:t[15]||(t[15]=a=>Z())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(se)?(s(),r("span",$t,t[31]||(t[31]=[o("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Loading ...",-1)]))):(s(),r("span",Lt,[t[32]||(t[32]=y("Continue")),e(A)?(s(),r("span",At," Attempt")):c("",!0)]))]),_:1})]),_:1}),e(A)?(s(),f(C,{key:0,color:"white",size:"md",class:"inline-flex w-full disabled:bg-gray-200",onClick:t[16]||(t[16]=a=>ge())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(re)?(s(),r("span",Vt,t[33]||(t[33]=[o("svg",{class:"inline h-5 w-5 animate-spin text-gray-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Loading ...",-1)]))):(s(),r("span",St,"Restart"))]),_:1})]),_:1})):c("",!0)],2)):e(x)().props.authenticated?(s(),f(C,{key:1,color:"indigo",size:"md",class:"flex w-full items-center justify-center",link:"/subscribe"},{default:l(()=>t[34]||(t[34]=[y("Join LatinTutorial Pro to Attempt")])),_:1})):(s(),f(C,{key:2,color:"indigo",size:"md",class:"flex w-full items-center justify-center",onClick:t[17]||(t[17]=a=>Z())},{default:l(()=>t[35]||(t[35]=[y("Log in to Continue")])),_:1}))]))]),v(Xe)]),v(Je,{class:"lg:hidden",show:e(L),onClose:t[19]||(t[19]=a=>_e(L)?L.value=!1:L=!1)},{default:l(()=>[o("div",null,[e(x)().props.authenticated?(s(),f(ue,{key:0,class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(M).level,"post-text":e(j)+" XP",progress:ie.value},null,8,["pre-text","post-text","progress"])):c("",!0),e(x)().props.authenticated?(s(),r("div",zt,[o("p",jt,d(e(H))+" xp to the next level ",1)])):c("",!0),o("h2",Ht,d(m.activity.name),1),o("h4",Pt,d(m.activity.description),1),m.assignment?(s(),r("section",Et,[v(Te,{assignment:m.assignment,"words-seen":e(T).length,"words-correct":e(u).correct,time:e(ae),accuracy:fe.value,onCompleted:t[18]||(t[18]=a=>pe())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):c("",!0),e(q)?(s(),r("div",Nt,[o("section",Rt,[o("div",null,[o("dl",Ot,[(s(!0),r(W,null,F(we.value,a=>(s(),r("div",{key:a.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[o("dt",Wt,d(a.name),1),o("dd",Ft,[a.value?(s(),r("span",Dt,[y(d(a.value)+" ",1),a.unit?(s(),r("span",It,d(a.unit),1)):c("",!0)])):(s(),r("span",Ut,"–"))])]))),128))])])])])):(s(),r("div",Bt,[m.activity.videos.length>0?(s(),r("div",Qt,[t[36]||(t[36]=o("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Video Review ",-1)),o("div",qt,[(s(!0),r(W,null,F(m.activity.videos,a=>(s(),f(Me,{key:a.id,item:a},null,8,["item"]))),128))])])):c("",!0)]))])]),_:1},8,["show"]),o("aside",Xt,[e(x)().props.authenticated?c("",!0):(s(),r("section",Jt,[v(Ue)])),o("div",null,[e(x)().props.authenticated?(s(),f(ue,{key:0,class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(M).level,"post-text":e(j)+" XP",progress:ie.value},null,8,["pre-text","post-text","progress"])):c("",!0),e(x)().props.authenticated?(s(),r("div",Gt,[o("p",Kt,d(e(H))+" xp to the next level ",1)])):c("",!0),o("h2",Yt,d(m.activity.name),1),o("h4",Zt,d(m.activity.description),1),m.assignment?(s(),r("section",es,[v(Te,{assignment:m.assignment,"words-seen":e(T).length,"words-correct":e(u).correct,time:e(ae),accuracy:fe.value,onCompleted:t[20]||(t[20]=a=>pe())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):c("",!0),e(q)?(s(),r("div",ds,[e(k)?(s(),r("section",ms,[o("h5",ps,[t[43]||(t[43]=y(" Time ")),e(b).time>0?(s(),r("span",vs,"Remaining")):(s(),r("span",fs,"Elapsed"))]),e(k)?(s(),r("div",gs,[o("div",{class:D(["grow text-left font-medium text-gray-900",e(k).length>10?"text-2xl":"text-4xl"])},d(e(k)),3),e(b).time>0?(s(),r("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[24]||(t[24]=a=>he())},[v(e($e),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):c("",!0),o("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[25]||(t[25]=a=>xe())},[e(N)?(s(),f(e(Le),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(s(),f(e(Ae),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):c("",!0)])):c("",!0),o("section",ys,[o("div",null,[o("dl",xs,[(s(!0),r(W,null,F(we.value,a=>(s(),r("div",{key:a.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[o("dt",hs,d(a.name),1),o("dd",ws,[a.value?(s(),r("span",ks,[y(d(a.value)+" ",1),a.unit?(s(),r("span",bs,d(a.unit),1)):c("",!0)])):(s(),r("span",_s,"–"))])]))),128))])])]),o("section",null,[t[44]||(t[44]=o("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),o("div",Cs,[(s(),r(W,null,F(be,a=>o("div",{class:D(["h-4 w-full rounded-sm shadow-sm",ke.value>=a.value?a.color:"bg-gray-300"]),key:a.value},null,2)),64))])]),e(q)?(s(),r("div",Ms,[v(C,{class:"inline-flex w-full disabled:bg-blue-400",size:"lg",color:"indigo",disabled:e(_),onClick:t[26]||(t[26]=a=>ee())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(_)?(s(),r("span",Ts,t[45]||(t[45]=[o("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Finishing ...",-1)]))):(s(),r("span",$s,"Finish"))]),_:1})]),_:1},8,["disabled"])])):c("",!0)])):(s(),r("div",ts,[e(x)().props.authenticated?(s(),f(Re,{key:0,sections:null,"current-list":null})):c("",!0),e(A)&&m.assignment.attempt?(s(),r("div",ss,[t[37]||(t[37]=o("h4",{class:"mt-8 text-left text-sm font-bold text-gray-500 uppercase"}," Assignment in Progress ",-1)),o("p",os,[y(d(m.assignment.attempt.correct)+" out of "+d(m.assignment.attempt.attempts)+" correct ",1),m.assignment.attempt.attempts>0?(s(),r("span",as,"("+d(Math.round(100*m.assignment.attempt.correct)/m.assignment.attempt.attempts)+"% accuracy)",1)):c("",!0),y(" | "+d(ve(m.assignment.attempt.time)),1)])])):c("",!0),o("div",rs,[e(x)().props.authenticated&&e(z).membership.subscribed?(s(),r("div",{key:0,class:D(["grid",e(A)?"grid-cols-2 gap-4":"grid-cols-1"])},[v(C,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:t[21]||(t[21]=a=>Z())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(se)?(s(),r("span",is,t[38]||(t[38]=[o("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Loading ...",-1)]))):(s(),r("span",ns,[t[39]||(t[39]=y("Continue")),e(A)?(s(),r("span",ls," Attempt")):c("",!0)]))]),_:1})]),_:1}),e(A)?(s(),f(C,{key:0,color:"white",size:"md",class:"inline-flex w-full disabled:bg-gray-200",onClick:t[22]||(t[22]=a=>ge())},{default:l(()=>[v(B,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:l(()=>[e(re)?(s(),r("span",us,t[40]||(t[40]=[o("svg",{class:"inline h-5 w-5 animate-spin text-gray-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[o("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),o("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),o("span",{class:"ml-2"},"Loading ...",-1)]))):(s(),r("span",cs,"Restart"))]),_:1})]),_:1})):c("",!0)],2)):e(x)().props.authenticated?(s(),f(C,{key:1,color:"indigo",size:"md",class:"flex w-full items-center justify-center",link:"/subscribe"},{default:l(()=>t[41]||(t[41]=[y("Join LatinTutorial Pro to Attempt")])),_:1})):(s(),f(C,{key:2,color:"indigo",size:"md",class:"flex w-full items-center justify-center",onClick:t[23]||(t[23]=a=>Z())},{default:l(()=>t[42]||(t[42]=[y("Log in to Continue")])),_:1}))])])),m.activity.videos.length>0?(s(),r("div",Ls,[t[46]||(t[46]=o("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Video Review ",-1)),o("div",As,[(s(!0),r(W,null,F(m.activity.videos,a=>(s(),f(Me,{key:a.id,item:a},null,8,["item"]))),128))])])):c("",!0)])])]),_:1})}}};export{qo as default};
