import{e as m,A as c,B as f,o as b,d as p,a as o,r as s,n as x,u as v,z as y}from"./app-f0078ddb.js";/* empty css            */const g={class:"flex-1 py-2 sm:px-16"},w=["disabled"],C={__name:"Answer",props:{top:String,bottom:String,nextItemButtonDisabled:Boolean,isCorrect:Boolean},emits:["nextQuestion"],setup(n,{emit:d}){const r=n,u=d;function i(){u("nextQuestion")}function a(t){t.ctrlKey||t.metaKey||t.altKey||(t.preventDefault(),(t.which==13||t.which==32)&&!r.nextItemButtonDisabled&&y(()=>{i()}))}let l=m(!0);return r.isCorrect||setTimeout(()=>{l.value=!1},50),c(()=>{document.addEventListener("keydown",a)}),f(()=>{document.removeEventListener("keydown",a)}),(t,e)=>(b(),p("div",null,[o("div",g,[s(t.$slots,"correct"),s(t.$slots,"top"),e[1]||(e[1]=o("div",{class:"border-1 mt-12 border border-gray-200"},null,-1)),s(t.$slots,"bottom"),o("button",{class:x(["button-disabled mt-12 w-full transform items-center rounded-md border border-transparent px-6 py-2 text-lg font-semibold text-white shadow-xs duration-150 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{"button-animation":!v(l)||n.isCorrect}]),disabled:n.nextItemButtonDisabled,onClick:e[0]||(e[0]=B=>i())}," Continue ",10,w)])]))}};export{C as default};
