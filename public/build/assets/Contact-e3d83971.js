import{o as r,d as a,a as e,e as v,k as p,C as q,A as F,B as N,i as _,b as n,w as m,F as y,u as s,j as C,g as d,s as D,q as h,x as b,t as u,f as x,T as I,h as M,n as L}from"./app-f0078ddb.js";import{_ as Z,L as A}from"./AppLayout-33f062bc.js";import{_ as z}from"./Breadcrumbs-c96e9207.js";import{_ as S}from"./ButtonItem-718c0517.js";import{_ as G}from"./Footer-0988dcd8.js";import{_ as R}from"./MobileSidebar-5e21b4cd.js";import{r as O}from"./InformationCircleIcon-716f3ffb.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";function T(c,f){return r(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z"})])}function $(c,f){return r(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"})])}function V(c,f){return r(),a("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"})])}const P={class:"lg:pr-96 2xl:pr-[32rem]"},Q={class:"px-4 py-8 sm:px-8"},W={class:"flex flex-row items-center justify-between"},Y={class:"mt-12 inline-block w-full lg:hidden"},J={class:"mt-10 space-y-4 text-base leading-7 text-gray-600"},K={class:"flex gap-x-4"},X={class:"flex-none"},ee={class:"flex gap-x-4"},te={class:"flex-none"},se={class:"flex gap-x-4"},oe={class:"flex-none"},le={class:"prose mt-12 w-full pb-24 sm:pb-32"},ne={class:"mx-auto max-w-xl lg:max-w-lg"},re={class:"grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2"},ae={class:"mt-2.5"},ie={key:0,class:"mt-1 text-sm text-red-600"},de={class:"mt-2.5"},me={key:0,class:"mt-1 text-sm text-red-600"},ue={class:"sm:col-span-2"},ce={class:"mt-2.5"},fe={key:0,class:"mt-1 text-sm text-red-600"},ge={key:0,class:"text-red-600"},pe={class:"mt-8 flex justify-between"},xe={key:0,class:"mx-auto mb-4 mt-4 flex w-full max-w-xl items-center rounded-xl bg-indigo-100 px-4 py-2 text-left lg:max-w-lg"},ve={class:"my-8 sm:mx-auto sm:w-full sm:max-w-md"},ye={class:"mt-8 flex-1 text-left"},he={class:"mt-2"},be={class:"hidden bg-slate-50 p-8 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},we={class:"mb-8 sm:mx-auto sm:w-full sm:max-w-md"},ke={class:"mt-10 space-y-4 text-base leading-7 text-gray-600"},_e={class:"flex gap-x-4"},Ce={class:"flex-none"},Me={class:"flex gap-x-4"},Le={class:"flex-none"},Ae={class:"flex gap-x-4"},Se={class:"flex-none"},Te={class:"mt-8 flex-1 text-left"},$e={class:"mt-2"},ct={__name:"Contact",props:{policy:String,errors:Object},setup(c){const f=[{name:"About",href:route("about"),current:!1},{name:"Contact",href:route("contact"),current:!0},{name:"Privacy Policy",href:"/privacy-policy",current:!1},{name:"Terms of Service",href:"/terms-of-service",current:!1},{name:"Frequently Asked Questions",href:"/faq",current:!1}],B=[{name:"Contact Us",href:"#",current:!0}];let g=v(!1),i=v(!1),j=v(!p().props.authenticated);const E=w=>{j.value=!1,l.token=w};let l=q({user_id:p().props.user?p().props.user.id:null,location:window.location.href,name:null,email:null,message:null,token:null});const U=()=>{p().props.authenticated?l.post("/submit-feedback",{preserveScroll:!0,onSuccess:()=>{l.reset(),g.value=!0,setTimeout(()=>{g.value=!1},5e3)}}):l.post("/send-contact-email",{preserveScroll:!0,onSuccess:()=>{l.reset(),g.value=!0,setTimeout(()=>{g.value=!1},5e3)}})};return window.onloadTurnstileCallback=function(){turnstile.render("#contact-container",{sitekey:"0x4AAAAAAAdb_RvUnMFgh0_B",callback:E})},F(()=>{p().props.authenticated||window.addEventListener("DOMContentLoaded",window.onloadTurnstileCallback)}),N(()=>{}),(w,t)=>{const H=_("Head"),k=_("Link");return r(),a(y,null,[n(H,{title:"Contact Us"}),n(Z,null,{default:m(()=>[e("main",P,[e("div",Q,[e("div",W,[n(z,{class:"lg:col-span-9 xl:grid-cols-10",pages:B}),n(s(O),{class:"ml-2 mt-1 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=o=>C(i)?i.value=!s(i):i=!s(i))})]),e("div",Y,[t[13]||(t[13]=e("h2",{class:"text-3xl font-bold tracking-tight text-gray-900"}," Get in touch ",-1)),t[14]||(t[14]=e("p",{class:"mt-6 text-base leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. If you have any questions, comments, or suggestions, please feel free to contact us. ",-1)),e("dl",J,[e("div",K,[e("dt",X,[t[7]||(t[7]=e("span",{class:"sr-only"},"Address",-1)),n(s(T),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[8]||(t[8]=e("dd",null,[d("155 Sidney Blvd"),e("br"),d("Hampden, ME 04444")],-1))]),e("div",ee,[e("dt",te,[t[9]||(t[9]=e("span",{class:"sr-only"},"Telephone",-1)),n(s(V),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[10]||(t[10]=e("dd",null,[e("a",{class:"hover:text-gray-900",href:"tel:+****************"},"+****************")],-1))]),e("div",se,[e("dt",oe,[t[11]||(t[11]=e("span",{class:"sr-only"},"Email",-1)),n(s($),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[12]||(t[12]=e("dd",null,[e("a",{class:"hover:text-gray-900",href:"mailto:<EMAIL>"},"<EMAIL>")],-1))])])]),e("div",le,[e("form",{onSubmit:t[5]||(t[5]=D(o=>U(),["prevent"]))},[e("div",ne,[e("div",re,[e("div",null,[t[15]||(t[15]=e("label",{for:"name",class:"block text-sm font-semibold leading-6 text-gray-900"},"Name",-1)),e("div",ae,[h(e("input",{"onUpdate:modelValue":t[1]||(t[1]=o=>s(l).name=o),type:"text",name:"name",id:"name",autocomplete:"given-name",class:"block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,512),[[b,s(l).name]])]),s(l).errors.name?(r(),a("div",ie,u(s(l).errors.name),1)):x("",!0)]),e("div",null,[t[16]||(t[16]=e("label",{for:"email",class:"block text-sm font-semibold leading-6 text-gray-900"},"Email",-1)),e("div",de,[h(e("input",{"onUpdate:modelValue":t[2]||(t[2]=o=>s(l).email=o),type:"email",name:"email",id:"email",autocomplete:"email",class:"block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,512),[[b,s(l).email]])]),s(l).errors.email?(r(),a("div",me,u(s(l).errors.email),1)):x("",!0)]),e("div",ue,[t[17]||(t[17]=e("label",{for:"message",class:"block text-sm font-semibold leading-6 text-gray-900"},"Message",-1)),e("div",ce,[h(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=o=>s(l).message=o),name:"message",id:"message",rows:"4",class:"block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,512),[[b,s(l).message]])]),s(l).errors.message?(r(),a("div",fe,u(s(l).errors.message),1)):x("",!0)]),c.errors.token?(r(),a("div",ge,u(c.errors.token),1)):x("",!0)]),t[20]||(t[20]=e("div",{id:"contact-container"},null,-1)),e("div",pe,[n(S,{type:"button",size:"sm",color:"white",onClick:t[4]||(t[4]=o=>s(l).reset())},{default:m(()=>t[18]||(t[18]=[d(" Reset Form ")])),_:1}),n(S,{type:"submit",size:"sm",color:"indigo",disabled:s(l).processing},{default:m(()=>t[19]||(t[19]=[d("Send Message")])),_:1},8,["disabled"])])])],32),n(I,{"enter-active-class":"transition ease-out duration-300","enter-from-class":"opacity-0","enter-to-class":"opacity-100","leave-active-class":"transition ease-in duration-300","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:m(()=>[s(g)?(r(),a("div",xe,t[21]||(t[21]=[e("div",{class:"text-sm leading-normal text-indigo-800"},[e("span",{class:"font-bold"},"Gratias tibi agimus!"),d(" Your message has been sent. We will get back to you as soon as possible. ")],-1)]))):x("",!0)]),_:1})])]),n(G)]),n(R,{class:"lg:hidden",show:s(i),onClose:t[6]||(t[6]=o=>C(i)?i.value=!1:i=!1)},{default:m(()=>[e("div",ve,[n(A,{class:"mx-auto w-full"})]),e("div",ye,[t[22]||(t[22]=e("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),e("div",he,[(r(),a(y,null,M(f,o=>n(k,{key:o.name,href:o.href,disabled:o.current,class:L(["block py-1 text-sm",o.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:m(()=>[d(u(o.name),1)]),_:2},1032,["href","disabled","class"])),64))])])]),_:1},8,["show"]),e("aside",be,[e("div",we,[n(A,{class:"mx-auto w-full"})]),t[30]||(t[30]=e("h2",{class:"text-3xl font-bold tracking-tight text-gray-900"}," Get in touch ",-1)),t[31]||(t[31]=e("p",{class:"mt-6 text-base leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. If you have any questions, comments, or suggestions, please feel free to contact us. ",-1)),e("dl",ke,[e("div",_e,[e("dt",Ce,[t[23]||(t[23]=e("span",{class:"sr-only"},"Address",-1)),n(s(T),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[24]||(t[24]=e("dd",null,[d("155 Sidney Blvd"),e("br"),d("Hampden, ME 04444")],-1))]),e("div",Me,[e("dt",Le,[t[25]||(t[25]=e("span",{class:"sr-only"},"Telephone",-1)),n(s(V),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[26]||(t[26]=e("dd",null,[e("a",{class:"hover:text-gray-900",href:"tel:+****************"},"+****************")],-1))]),e("div",Ae,[e("dt",Se,[t[27]||(t[27]=e("span",{class:"sr-only"},"Email",-1)),n(s($),{class:"h-7 w-6 text-gray-400","aria-hidden":"true"})]),t[28]||(t[28]=e("dd",null,[e("a",{class:"hover:text-gray-900",href:"mailto:<EMAIL>"},"<EMAIL>")],-1))])]),e("div",Te,[t[29]||(t[29]=e("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),e("div",$e,[(r(),a(y,null,M(f,o=>n(k,{key:o.name,href:o.href,disabled:o.current,class:L(["block py-1 text-sm",o.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:m(()=>[d(u(o.name),1)]),_:2},1032,["href","disabled","class"])),64))])])])]),_:1})],64)}}};export{ct as default};
