import{C as n,D as g,A as v}from"./chart-183f17ad.js";import{e as d,l as c,A as h,o as x,d as y,a as C}from"./app-f0078ddb.js";/* empty css            */const B={__name:"AccuracyDonutChart",props:{data:Object},setup(l){const t=l,o=d(null);let r;c(()=>(t.data.vocab_correct+t.data.grammar_correct)/(t.data.vocab_attempts+t.data.grammar_attempts));const m={id:"centerText",afterDraw:e=>{const a=e.ctx,_=Math.round((t.data.vocab_correct+t.data.grammar_correct)/(t.data.vocab_attempts+t.data.grammar_attempts)*100)+"%",f=e.width/2,b=e.height/2;a.save(),a.textAlign="center",a.textBaseline="middle",a.font="32px Inter",a.fillStyle="#111827",a.fillText(_,f,b),a.restore()}},s=c(()=>t.data.vocab_correct===0&&t.data.vocab_attempts===0&&t.data.grammar_correct===0&&t.data.grammar_attempts===0),u=c(()=>s.value?{labels:["No Data"],datasets:[{data:[1],backgroundColor:["#d1d5db"]}]}:{labels:["Vocabulary Correct","Vocabulary Incorrect","Grammar Correct","Grammar Incorrect"],datasets:[{data:[t.data.vocab_correct,t.data.vocab_attempts-t.data.vocab_correct,t.data.grammar_correct,t.data.grammar_attempts-t.data.grammar_correct],backgroundColor:["#fdba74","#f9a8d4","#d8b4fe","#67e8f9"]}]}),i=d({cutout:"50%",plugins:{tooltip:{enabled:!0},title:{display:!1}},responsive:!0,maintainAspectRatio:!1}),p=()=>{r&&r.destroy(),r=new n(o.value,{type:"doughnut",data:u.value,options:i.value,plugins:s.value?[]:[m]})};return h(()=>{p()}),n.register(g,v),(e,a)=>(x(),y("div",null,[C("canvas",{ref_key:"chartRef",ref:o,id:"accuracy-chart-id",height:"250"},null,512)]))}};export{B as default};
