import{V as X,e as i,k as I,l as Q,i as be,o as r,c as $,w as v,b as c,a as s,j as z,u as e,t as d,d as a,h as M,n as U,F as j,f as g,T as R,g as V,W as G,a3 as ce}from"./app-f0078ddb.js";import{_ as ke}from"./AppLayout-33f062bc.js";import{_ as _e}from"./Breadcrumbs-c96e9207.js";import{_ as me}from"./ButtonItem-718c0517.js";import{_ as pe}from"./ProgressBar-b7203293.js";import $e from"./Drag-9e816807.js";import Ce from"./MultipleChoice-080711a4.js";import Te from"./Type-dfa282d2.js";import Ve from"./Matching-1875e265.js";import{r as H}from"./replaceMacra-3b9666ed.js";import{_ as Se}from"./Footer-0988dcd8.js";import{_ as We}from"./MobileSidebar-5e21b4cd.js";import{T as qe}from"./easytimer-3d932146.js";import{g as ve}from"./index-794e919d.js";import{_ as fe}from"./AssignmentModule-fc2620bb.js";import{I as Ie}from"./InfinitasIcon-1a3ae135.js";import{_ as Ae}from"./TruncateVerse-a9219b3d.js";import{r as De}from"./InformationCircleIcon-716f3ffb.js";import{r as ge,a as xe}from"./PlayIcon-8c672cba.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./vuedraggable.umd-aab17b5c.js";import"./NextButtonSmall-9e6ffefc.js";import"./XCircleIcon-63af2b2a.js";import"./lodash-631955d9.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-6a201aa1.js";import"./ArrowPathIcon-f74cb8d6.js";import"./removePunctuation-702d8a66.js";const Le={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},ze={class:"px-4 py-8 sm:px-8"},Me={class:"flex flex-row items-center justify-between"},je={class:"mx-auto mt-12 w-full"},Ee={class:"flex flex-row items-center justify-center text-center"},Fe={class:"mt-12"},Ne={class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},Pe={class:"mt-2 mb-8"},Be={class:"text-center text-xs font-bold text-gray-500 uppercase"},Qe={class:"mb-8"},Ue={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},He={key:0,class:"mb-8 text-left"},Ke={key:0,class:"mt-2 flex flex-row items-center"},Oe={class:"w-full"},Xe={key:0,class:"mx-auto"},Re={key:1,class:"mx-auto"},Ge=["innerHTML"],Je={key:1},Ye={key:0,class:"text-xl"},Ze={key:1},et={key:0},tt={key:1},st={class:"mt-8 grid grid-cols-1 gap-8"},ot={key:0},rt={"aria-labelledby":"userStats"},lt={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},at={class:"text-sm leading-6 font-medium text-gray-500"},nt={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},it={key:0},ut={key:0,class:"ml-1 text-sm font-medium text-gray-500"},dt={key:1},ct={class:"mx-2"},mt={key:0},pt={class:"mt-4 grid w-full grid-cols-2 gap-2"},vt={class:"items-center truncate text-sm font-medium text-gray-800"},ft={key:0,class:"mt-4 cursor-pointer text-center text-sm font-medium text-blue-600"},gt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},xt={class:"mt-2"},yt={class:"text-center text-xs font-bold text-gray-500 uppercase"},wt={class:"mt-8 grid grid-cols-1 gap-8"},ht={key:0},bt={class:"mt-8 w-full"},kt={key:0,class:"mx-auto"},_t={key:1,class:"mx-auto"},$t={key:1,class:"text-left"},Ct={key:0,class:"mt-2 flex flex-row items-center"},Tt={"aria-labelledby":"userStats"},Vt={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},St={class:"text-sm leading-6 font-medium text-gray-500"},Wt={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},qt={key:0},It={key:0,class:"ml-1 text-sm font-medium text-gray-500"},At={key:1},Dt={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},Lt={key:0},zt={class:"mt-4 grid w-full grid-cols-2 gap-2"},Mt={class:"items-center truncate text-sm font-medium text-gray-800"},jt={key:0,class:"mt-4 cursor-pointer text-center text-sm font-medium text-blue-600"},Fs={__name:"Attempt",props:{initialData:{type:Object,required:!1},question:{type:Object,required:!1},assignment:{type:Object,required:!1},learnedWords:{type:Array,required:!1}},setup(m){const x=m,ye=[{name:"Infinitas",href:"#",current:!0}];let J=X({words:"all",time:0,difficulty:0,toggleLearned:!1,toggleNames:!1}),K=i(I().props.user.xp),S=i(I().props.user.xp),W=I().props.authenticated?i(I().props.user.level.max-I().props.user.xp+1):i(0),C=i(I().props.user.level),we=i(I().props.user.level.next_level_max),A=i(!1),n=X({correct:x.initialData.session.correct,incorrect:x.initialData.session.incorrect}),f=i(!1),w=i(),h=X(x.initialData.session.words_seen?x.initialData.session.words_seen:[]),l=i(x.initialData.question),Y=i(x.question),he=i([]),Z=i(x.initialData.session.id),T=i(!0),E=i(0),k=i(!1),O=i(0);function ee(){f.value=!0,G.reload({only:["assignment"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{f.value=!1}})}function F(){l.value=Y.value,q.value=!1,G.reload({only:["question","learnedWords"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{Y.value=x.question,q.value=!0}}),E.value++,A.value=!1}let D=i(x.initialData.session.streak),_=new qe,b=i(),q=i(!0),te=i(x.initialData.session.xp_earned);const N=u=>{if(T.value=!0,!f.value){if(console.log(H(u.answer).toUpperCase()==H(l.value.key).toUpperCase()),H(u.answer).toUpperCase()==H(l.value.key).toUpperCase()){w.value=!0;let y={value:n.correct};D.value<1?D.value=1:D.value++,ve.to(y,{value:n.correct+1,duration:.3,onUpdate:()=>{n.correct=Math.round(y.value)}});let o={value:K.value};ve.to(o,{value:K.value+L(),duration:.3,onUpdate:()=>{K.value=Math.round(o.value)}}),te.value+=L()}else w.value=!1,D.value=0,n.incorrect++;if(h.find(y=>y.id===l.value.id)){var t=h.findIndex(y=>y.id===l.value.id);h[t].correct=h[t].correct+w.value,h[t].total=h[t].total+1}else h.push({id:l.value.id,correct:w.value?1:0,total:1});A.value=!0,w.value?T.value=!1:setTimeout(()=>{T.value=!1},2e3),S.value=S.value+L(),W.value<=L()?(C.value.level=C.value.level+1,W.value=we.value-S.value+1):W.value=W.value-L(),O.value=_.getTotalTimeValues().seconds,ce.post("/api/infinitas/add-attempt",{session:Z.value,token_id:l.value.id,word_id:l.value.word_id,type:l.value.type,correct:w.value,xp:L(),streak:D.value,time:_.getTotalTimeValues().secondTenths}).then(y=>{S.value=y.data.xp,C.value=y.data.level,W.value=y.data.next_level_xp})}},P=()=>{if(f.value)return;q.value=!1,he.value.push(l.value.word_id);let u=l.value.word_id;T.value=!1,F(),ce.post("/api/practice/vocabulary/add-known-word",{word_id:u})},se=Q(()=>{let u=C.value.max-C.value.min+1;return(S.value-C.value.min)/u*100}),L=()=>{if(w.value)switch(l.value.type){case"drag":switch(J.difficulty){case 1:return 3;case 2:return 4;default:return 2}case"type":switch(J.difficulty){case 1:return 4;case 2:return 5;default:return 3}default:return 1}return 0},oe=()=>{f.value=!0,G.post("/api/practice/vocabulary/finish",{session:Z.value})},re=Q(()=>n.correct+n.incorrect==0?-1:n.correct+n.incorrect<10?n.correct:Math.round(10*n.correct/(n.correct+n.incorrect))),le=Q(()=>n.correct+n.incorrect==0?-1:n.correct+n.incorrect<10?n.correct/10:Math.round(100*n.correct/(n.correct+n.incorrect))),ae=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],ne=Q(()=>[{name:"Correct",value:n.correct,unit:"out of "+(n.correct+n.incorrect)},{name:"Current Streak",value:D.value,unit:"in a row"},{name:"XP Earned",value:te.value},{name:"Words Seen",value:h.length}]),ie=u=>u.days>1?"more than "+u.days+" days":u.days>0?"more than "+u.days+" day":u.hours>1?"more than "+u.hours+" hours":u.hours>0?"more than "+u.hours+" hour":u.seconds<=9?u.minutes+":0"+u.seconds:u.minutes+":"+u.seconds;_.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),b.value=ie(_.getTimeValues()),_.addEventListener("secondsUpdated",function(){b.value=ie(_.getTimeValues())});let B=i(!1);const ue=()=>{_.isRunning()?(_.pause(),B.value=!0):(_.start(),B.value=!1)};let p=i(!1);const de=()=>p.value?x.learnedWords:x.learnedWords.slice(0,6);return(u,t)=>{const y=be("Head");return r(),$(ke,null,{default:v(()=>[c(y,null,{default:v(()=>t[24]||(t[24]=[s("title",null,"Infinitas",-1)])),_:1}),s("main",Le,[s("div",ze,[s("div",Me,[c(_e,{class:"lg:col-span-9 xl:grid-cols-10",pages:ye}),c(e(De),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=o=>z(k)?k.value=!e(k):k=!e(k))})]),s("section",je,[s("div",Ee,[c(Ie,{class:"inline h-20"}),t[25]||(t[25]=s("span",{class:"ml-4 inline text-5xl font-semibold text-gray-900"},"Infinitas",-1))])]),s("div",Fe,[s("div",Ne,[c(pe,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(C).level,"post-text":e(S)+" XP",progress:se.value},null,8,["pre-text","post-text","progress"]),s("div",Pe,[s("p",Be,d(e(W))+" xp to the next level ",1)]),s("section",Qe,[t[26]||(t[26]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),s("div",Ue,[(r(),a(j,null,M(ae,o=>s("div",{class:U(["h-4 w-full rounded-sm shadow-sm",re.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),e(b)?(r(),a("section",He,[t[27]||(t[27]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Time Elapsed ",-1)),e(b)?(r(),a("div",Ke,[s("div",{class:U(["grow text-left font-medium text-gray-900",e(b).length>10?"text-2xl":"text-4xl"])},d(e(b)),3),s("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[1]||(t[1]=o=>ue())},[e(B)?(r(),$(e(ge),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(r(),$(e(xe),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):g("",!0)])):g("",!0),s("div",Oe,[c(me,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(f),onClick:t[2]||(t[2]=o=>oe())},{default:v(()=>[c(R,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:v(()=>[e(f)?(r(),a("span",Xe,t[28]||(t[28]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Finishing ...",-1)]))):(r(),a("span",Re,"Finish"))]),_:1})]),_:1},8,["disabled"])])]),c(R,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:v(()=>[e(l).type==="mc"&&m.question?(r(),$(Ce,{key:`${e(E)}-mc`,options:e(l).options,"question-key":e(l).key,"is-correct":e(w),"is-answer":e(A),attempts:e(n).correct+e(n).incorrect,disabled:e(T)||e(f)||!e(q),"is-vocabulary":!1,onSubmit:t[3]||(t[3]=o=>N(o)),onIKnowThis:t[4]||(t[4]=o=>P()),onNextQuestion:t[5]||(t[5]=o=>F())},{stem:v(()=>[e(l).is_translation?(r(),a("div",{key:0,innerHTML:e(l).instructions.replace("_____","<span class=text-blue-600>"+e(l).token.split(":")[0]+"</span>")},null,8,Ge)):(r(),a("div",Je,d(e(l).stem),1))]),instructions:v(()=>[e(l).is_translation?(r(),a("div",Ye,[c(Ae,{token:e(l).token,verse:e(l).verse},null,8,["token","verse"])])):(r(),a("div",Ze,d(e(l).instructions),1))]),_:1},8,["options","question-key","is-correct","is-answer","attempts","disabled"])):e(l).type==="match"&&m.question?(r(),$(Ve,{key:`${e(E)}-match`,options:e(l).options,"is-answer":e(A),"is-correct":e(w),"user-attempt":u.attemptValue,disabled:e(T)||e(f)||!e(q),"is-vocabulary":!1,onSubmit:t[6]||(t[6]=o=>N(o)),onIKnowThis:t[7]||(t[7]=o=>P()),onNextQuestion:t[8]||(t[8]=o=>F())},{instructions:v(()=>[V(d(e(l).instructions),1)]),_:1},8,["options","is-answer","is-correct","user-attempt","disabled"])):e(l).type==="type"&&m.question?(r(),$(Te,{key:`${e(E)}-type`,options:e(l).key,"is-answer":e(A),"is-correct":e(w),difficulty:e(l).difficulty,disabled:e(T)||e(f)||!e(q),"is-vocabulary":!1,onSubmit:t[9]||(t[9]=o=>N(o)),onIKnowThis:t[10]||(t[10]=o=>P()),onNextQuestion:t[11]||(t[11]=o=>F())},{stem:v(()=>[V(d(e(l).stem),1)]),instructions:v(()=>[V(d(e(l).instructions),1)]),_:1},8,["options","is-answer","is-correct","difficulty","disabled"])):e(l).type==="drag"&&m.question?(r(),$($e,{key:`${e(E)}-drag`,options:e(l).options,"question-key":e(l).key,syllabalized:e(l).syllabized,stem:Array.isArray(e(l).stem)?e(l).stem[1]:"","is-answer":e(A),"is-correct":e(w),difficulty:e(l).difficulty,disabled:e(T)||e(f)||!e(q),"is-vocabulary":!1,onSubmit:t[12]||(t[12]=o=>N(o)),onIKnowThis:t[13]||(t[13]=o=>P()),onNextQuestion:t[14]||(t[14]=o=>F())},{stem:v(()=>[Array.isArray(e(l).stem)?(r(),a("span",et,d(e(l).stem[0]),1)):(r(),a("span",tt,d(e(l).stem),1))]),instructions:v(()=>[V(d(e(l).instructions),1)]),_:1},8,["options","question-key","syllabalized","stem","is-answer","is-correct","difficulty","disabled"])):g("",!0)]),_:1})])]),c(Se)]),c(We,{class:"lg:hidden",show:e(k),onClose:t[18]||(t[18]=o=>z(k)?k.value=!1:k=!1)},{default:v(()=>[s("div",st,[t[30]||(t[30]=s("section",null,[s("div",{class:"mx-2 flex flex-row items-center text-sm font-medium text-gray-800"},[s("p",null,[s("span",{class:"font-bold uppercase"},"Infinitas"),V(" provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")])])],-1)),m.assignment?(r(),a("section",ot,[c(fe,{assignment:m.assignment,"words-seen":e(h).length,"words-correct":e(n).correct,time:e(O),accuracy:le.value,onCompleted:t[15]||(t[15]=o=>ee())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):g("",!0),s("section",rt,[s("div",null,[s("dl",lt,[(r(!0),a(j,null,M(ne.value,o=>(r(),a("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[s("dt",at,d(o.name),1),s("dd",nt,[o.value?(r(),a("span",it,[V(d(o.value)+" ",1),o.unit?(r(),a("span",ut,d(o.unit),1)):g("",!0)])):(r(),a("span",dt,"–"))])]))),128))])])]),s("section",ct,[t[29]||(t[29]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Completed Words ",-1)),m.learnedWords!=null?(r(),a("div",mt,[s("div",pt,[(r(!0),a(j,null,M(de(),o=>(r(),a("div",{key:o.id,class:"flex"},[s("span",vt,d(o.display_word),1)]))),128))]),m.learnedWords.length>6?(r(),a("div",ft,[e(p)?(r(),a("span",{key:0,onClick:t[16]||(t[16]=o=>z(p)?p.value=!1:p=!1)},"View Fewer")):(r(),a("span",{key:1,onClick:t[17]||(t[17]=o=>z(p)?p.value=!0:p=!0)},"View All ("+d(m.learnedWords.length)+" total)",1))])):g("",!0)])):g("",!0)])])]),_:1},8,["show"]),s("aside",gt,[c(pe,{class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(C).level,"post-text":e(S)+" XP",progress:se.value},null,8,["pre-text","post-text","progress"]),s("div",xt,[s("p",yt,d(e(W))+" xp to the next level ",1)]),s("div",wt,[t[35]||(t[35]=s("section",null,[s("div",{class:"flex flex-row items-center text-sm font-medium text-gray-800"},[s("p",null,[s("span",{class:"font-bold uppercase"},"Infinitas"),V(" provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")])])],-1)),m.assignment?(r(),a("section",ht,[c(fe,{assignment:m.assignment,"words-seen":e(h).length,"words-correct":e(n).correct,time:e(O),accuracy:le.value,onCompleted:t[19]||(t[19]=o=>ee())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):g("",!0),s("section",bt,[c(me,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(f),onClick:t[20]||(t[20]=o=>oe())},{default:v(()=>[c(R,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:v(()=>[e(f)?(r(),a("span",kt,t[31]||(t[31]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Finishing ...",-1)]))):(r(),a("span",_t,"Finish"))]),_:1})]),_:1},8,["disabled"])]),e(b)?(r(),a("section",$t,[t[32]||(t[32]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Time Elapsed ",-1)),e(b)?(r(),a("div",Ct,[s("div",{class:U(["grow text-left font-medium text-gray-900",e(b).length>10?"text-2xl":"text-4xl"])},d(e(b)),3),s("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[21]||(t[21]=o=>ue())},[e(B)?(r(),$(e(ge),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(r(),$(e(xe),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):g("",!0)])):g("",!0),s("section",Tt,[s("div",null,[s("dl",Vt,[(r(!0),a(j,null,M(ne.value,o=>(r(),a("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[s("dt",St,d(o.name),1),s("dd",Wt,[o.value?(r(),a("span",qt,[V(d(o.value)+" ",1),o.unit?(r(),a("span",It,d(o.unit),1)):g("",!0)])):(r(),a("span",At,"–"))])]))),128))])])]),s("section",null,[t[33]||(t[33]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Performance",-1)),s("div",Dt,[(r(),a(j,null,M(ae,o=>s("div",{class:U(["h-4 w-full rounded-sm shadow-sm",re.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),s("section",null,[t[34]||(t[34]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Completed Words ",-1)),m.learnedWords!=null?(r(),a("div",Lt,[s("div",zt,[(r(!0),a(j,null,M(de(),o=>(r(),a("div",{key:o.id,class:"flex"},[s("span",Mt,d(o.display_word),1)]))),128))]),m.learnedWords.length>6?(r(),a("div",jt,[e(p)?(r(),a("span",{key:0,onClick:t[22]||(t[22]=o=>z(p)?p.value=!1:p=!1)},"View Fewer")):(r(),a("span",{key:1,onClick:t[23]||(t[23]=o=>z(p)?p.value=!0:p=!0)},"View All ("+d(m.learnedWords.length)+" total)",1))])):g("",!0)])):g("",!0)])])])]),_:1})}}};export{Fs as default};
