import{e as V,V as v,i as U,o as d,c as A,w as f,b as m,a as t,j as D,u as g,d as u,h as _,t as x,q as h,x as w,n as k,F as C,z as S}from"./app-f0078ddb.js";import{_ as B}from"./AppLayout-33f062bc.js";import{_ as N}from"./Breadcrumbs-c96e9207.js";import{r as q}from"./InformationCircleIcon-5139ba20.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";const P={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},$={class:"px-4 py-8 sm:px-8"},j={class:"flex flex-row items-center justify-between"},z={class:"mt-6 grid grid-cols-1 gap-8 lg:grid-cols-2"},F={class:"w-full border-collapse border border-gray-300"},H={class:"border border-gray-300 p-2 text-center font-bold"},R={class:"border border-gray-300 p-2"},E=["onUpdate:modelValue","disabled","onInput"],G={class:"w-full border-collapse border border-gray-300"},L={class:"border border-gray-300 p-2 text-center font-bold"},M={class:"border border-gray-300 p-2"},O=["onUpdate:modelValue","disabled","onInput"],Vt={__name:"Decline",setup(J){const I=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:"Decline Nouns",href:"#",current:!0}];let n=V(!1);const c={singular:{nominative:"agricola",genitive:"agricolae",dative:"agricolae",accusative:"agricolam",ablative:"agricola",vocative:"agricola"},plural:{nominative:"agricolae",genitive:"agricolarum",dative:"agricolis",accusative:"agricolas",ablative:"agricolis",vocative:"agricolae"}},l=v({singular:{nominative:"",genitive:"",dative:"",accusative:"",ablative:"",vocative:""},plural:{nominative:"",genitive:"",dative:"",accusative:"",ablative:"",vocative:""}}),i=v({singular:{nominative:!1,genitive:!1,dative:!1,accusative:!1,ablative:!1,vocative:!1},plural:{nominative:!1,genitive:!1,dative:!1,accusative:!1,ablative:!1,vocative:!1}}),b=async(a,e,p)=>{if(l[a][e]===c[a][e]){i[a][e]=!0,await S();const s=[...document.querySelectorAll(".singular-input"),...document.querySelectorAll(".plural-input")],r=s.indexOf(p.target);r!==-1&&r+1<s.length&&s[r+1].focus()}},y=()=>{for(let a in l)for(let e in l[a])l[a][e]="",i[a][e]=!1};return(a,e)=>{const p=U("Head");return d(),A(B,null,{default:f(()=>[m(p,null,{default:f(()=>e[1]||(e[1]=[t("title",null,"Declining Nouns",-1)])),_:1}),t("main",P,[t("div",$,[t("div",j,[m(N,{class:"lg:col-span-9 xl:grid-cols-10",pages:I}),m(g(q),{class:"ml-2 mt-1 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:e[0]||(e[0]=s=>D(n)?n.value=!g(n):n=!g(n))})]),t("div",z,[t("div",null,[e[3]||(e[3]=t("h2",{class:"mb-4 text-center text-xl font-bold"},"Singular",-1)),t("table",F,[e[2]||(e[2]=t("thead",null,[t("tr",null,[t("th",{class:"border border-gray-300 p-2"},"Case"),t("th",{class:"border border-gray-300 p-2"},"Singular")])],-1)),t("tbody",null,[(d(!0),u(C,null,_(c.singular,(s,r)=>(d(),u("tr",{key:r},[t("td",H,x(r.charAt(0).toUpperCase()+r.slice(1)),1),t("td",R,[h(t("input",{"onUpdate:modelValue":o=>l.singular[r]=o,class:k(["singular-input w-full rounded-sm border border-gray-300 p-2",{"bg-green-200":i.singular[r]}]),disabled:i.singular[r],onInput:o=>b("singular",r,o)},null,42,E),[[w,l.singular[r]]])])]))),128))])])]),t("div",null,[e[5]||(e[5]=t("h2",{class:"mb-4 text-center text-xl font-bold"},"Plural",-1)),t("table",G,[e[4]||(e[4]=t("thead",null,[t("tr",null,[t("th",{class:"border border-gray-300 p-2"},"Case"),t("th",{class:"border border-gray-300 p-2"},"Plural")])],-1)),t("tbody",null,[(d(!0),u(C,null,_(c.plural,(s,r)=>(d(),u("tr",{key:r},[t("td",L,x(r.charAt(0).toUpperCase()+r.slice(1)),1),t("td",M,[h(t("input",{"onUpdate:modelValue":o=>l.plural[r]=o,class:k(["plural-input w-full rounded-sm border border-gray-300 p-2",{"bg-green-200":i.plural[r]}]),disabled:i.plural[r],onInput:o=>b("plural",r,o)},null,42,O),[[w,l.plural[r]]])])]))),128))])])])]),t("button",{onClick:y,class:"mx-auto mt-6 block rounded-sm bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700"}," Reset ")])])]),_:1})}}};export{Vt as default};
