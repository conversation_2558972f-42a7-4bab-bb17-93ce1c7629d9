import{C as B,e as p,o as c,c as S,w as r,g as i,d as v,a,b as o,q as y,M as h,E as U,s as g,f,u as s,P as z,n as E,W as O}from"./app-f0078ddb.js";import{_ as A}from"./ActionMessage-0a1272b7.js";import{_ as F}from"./FormSection-45b6b921.js";import{_ as k}from"./InputError-7edb5cf8.js";import{_ as w}from"./InputLabel-3b7f7747.js";import{_ as P}from"./ButtonItem-718c0517.js";import{_ as V}from"./TextInput-940981ae.js";/* empty css            */import"./SectionTitle-05f6d081.js";import"./_plugin-vue_export-helper-c27b6911.js";const R={key:0,class:"col-span-6 sm:col-span-4 text-center sm:text-left"},q={class:"my-2 flex justify-center sm:justify-start"},D=["src","alt"],L={class:"my-2 flex justify-center sm:justify-start"},M={class:"col-span-6 sm:col-span-4"},T={class:"col-span-6 sm:col-span-4"},W={key:0},Y={class:"text-sm mt-2"},G={class:"mt-2 font-medium text-sm text-green-600"},H={key:1,class:"col-span-6 sm:col-span-4"},le={__name:"UpdateProfileInformationForm",props:{user:{type:Object,required:!0},readOnly:{type:Boolean,default:!1}},setup(n){const _=n,t=B({_method:"PUT",name:_.user.name,email:_.user.email,photo:null}),b=p(null),u=p(null),m=p(null),C=()=>{m.value&&(t.photo=m.value.files[0]),t.post(route("user-profile-information.update"),{errorBag:"updateProfileInformation",preserveScroll:!0,onSuccess:()=>x()})},j=()=>{b.value=!0},$=()=>{m.value.click()},I=()=>{const l=m.value.files[0];if(!l)return;const e=new FileReader;e.onload=d=>{u.value=d.target.result},e.readAsDataURL(l)},N=()=>{O.delete(route("current-user-photo.destroy"),{preserveScroll:!0,onSuccess:()=>{u.value=null,x()}})},x=()=>{var l;(l=m.value)!=null&&l.value&&(m.value.value=null)};return(l,e)=>(c(),S(F,{onSubmitted:C},{title:r(()=>e[2]||(e[2]=[i(" Profile Information ")])),description:r(()=>e[3]||(e[3]=[i(" Update your account's profile information and email address. ")])),form:r(()=>[l.$page.props.jetstream.managesProfilePhotos?(c(),v("div",R,[a("input",{ref_key:"photoInput",ref:m,type:"file",class:"hidden",onChange:I},null,544),o(w,{for:"photo",value:"Photo"}),y(a("div",q,[a("img",{src:n.user.profile_photo_url,alt:n.user.name,class:"rounded-full h-20 w-20 object-cover"},null,8,D)],512),[[h,!u.value]]),y(a("div",L,[a("span",{class:"block rounded-full w-20 h-20 bg-cover bg-no-repeat bg-center",style:U("background-image: url('"+u.value+"');")},null,4)],512),[[h,u.value]]),o(P,{size:"sm",color:"white",class:"mt-2 mr-2",type:"button",onClick:g($,["prevent"])},{default:r(()=>e[4]||(e[4]=[i(" Select A New Photo ")])),_:1}),n.user.profile_photo_path?(c(),S(P,{key:0,size:"sm",color:"white",type:"button",class:"mt-2",onClick:g(N,["prevent"])},{default:r(()=>e[5]||(e[5]=[i(" Remove Photo ")])),_:1})):f("",!0),o(k,{message:s(t).errors.photo,class:"mt-2"},null,8,["message"])])):f("",!0),a("div",M,[o(w,{for:"name",value:"Name"}),o(V,{id:"name",modelValue:s(t).name,"onUpdate:modelValue":e[0]||(e[0]=d=>s(t).name=d),type:"text",class:"mt-1 block w-full",autocomplete:"name","read-only":n.readOnly},null,8,["modelValue","read-only"]),o(k,{message:s(t).errors.name,class:"mt-2"},null,8,["message"])]),a("div",T,[o(w,{for:"email",value:"Email"}),o(V,{id:"email",modelValue:s(t).email,"onUpdate:modelValue":e[1]||(e[1]=d=>s(t).email=d),type:"email",class:"mt-1 block w-full",autocomplete:"username","read-only":n.readOnly},null,8,["modelValue","read-only"]),o(k,{message:s(t).errors.email,class:"mt-2"},null,8,["message"]),l.$page.props.jetstream.hasEmailVerification&&n.user.email_verified_at===null?(c(),v("div",W,[a("p",Y,[e[7]||(e[7]=i(" Your email address is unverified. ")),o(s(z),{href:l.route("verification.send"),method:"post",as:"button",class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",onClick:g(j,["prevent"])},{default:r(()=>e[6]||(e[6]=[i(" Click here to re-send the verification email. ")])),_:1},8,["href"])]),y(a("div",G," A new verification link has been sent to your email address. ",512),[[h,b.value]])])):f("",!0)]),n.readOnly?(c(),v("div",H,e[8]||(e[8]=[a("p",{class:"text-sm mt-2 font-medium text-gray-600"}," Your name and email address are managed elsewhere. Please get in touch with us if you need help. ",-1)]))):f("",!0)]),actions:r(()=>[o(A,{on:s(t).recentlySuccessful,class:"mr-3"},{default:r(()=>e[9]||(e[9]=[i(" Saved. ")])),_:1},8,["on"]),o(P,{size:"sm",color:"black",class:E({"opacity-25":s(t).processing}),disabled:s(t).processing},{default:r(()=>e[10]||(e[10]=[i(" Save ")])),_:1},8,["class","disabled"])]),_:1}))}};export{le as default};
