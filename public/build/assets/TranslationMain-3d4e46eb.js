import{l as B,e as g,o as r,d as o,a as t,F as m,h as x,q as T,x as C,b as N,w as A,g as d,n as h,t as c,f as V,c as L}from"./app-f0078ddb.js";import{_ as E}from"./RenderToken-2b0a5644.js";import{_ as F}from"./ButtonItem-718c0517.js";import"./lodash-631955d9.js";/* empty css            */import"./removePunctuation-702d8a66.js";const P={class:"grid grid-cols-2 gap-4"},S={key:0,class:"col-span-2 mt-8"},U={class:"rounded-2xl border-8 border-gray-200 p-4"},D={class:"mt-2 text-sm"},z={__name:"TranslationMain",props:{verses:Array,tokens:Array,highlight:String,caseColor:<PERSON><PERSON><PERSON>,selectingText:<PERSON><PERSON><PERSON>,has<PERSON>rose:<PERSON><PERSON><PERSON>,sectionId:Number,verseName:String},emits:["update:highlighted","update:is-editing"],setup(l,{emit:f}){const u=l,p=B(()=>{var s=[];return u.verses.forEach((e,a)=>{e.prose==1&&u.verses.length>1?s.push(e):a==0?s[0]=e:s.push(e)}),s}),i=g(""),b=f,n=g({}),v=()=>{axios.post("/api/translation",{translation:i.value,section_id:u.sectionId}).then(s=>{s.data&&(n.value=s.data)})},y=s=>{switch(s){case 1:return"bg-red-100 text-red-800";case 2:return"bg-yellow-100 text-yellow-800";case 3:return"bg-green-100 text-green-800";case 4:return"bg-blue-100 text-blue-800";case 5:return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}},k=s=>{switch(s){case 1:return"Poor";case 2:return"Fair";case 3:return"Good";case 4:return"Great";case 5:return"Excellent";default:return"Unknown"}};return(s,e)=>(r(),o("div",null,[t("div",P,[t("div",null,[e[2]||(e[2]=t("h3",{class:"mt-2 text-base font-semibold mb-2"},"Latin Text",-1)),(r(!0),o(m,null,x(p.value,a=>(r(),L(E,{key:a.id,verse:a,tokens:l.tokens,highlighted:l.highlight,"case-color":l.caseColor,"selecting-text":l.selectingText,"show-all-line-numbers":!1,"has-prose":l.hasProse,"onUpdate:highlighted":e[0]||(e[0]=w=>b("update:highlighted",w)),class:h(["text-gray-900 font-intro break-normal text-base font-medium text-gray-900 sm:text-2xl lg:text-sm",[l.selectingText?"cursor-text select-text":"cursor-pointer select-none"]])},null,8,["verse","tokens","highlighted","case-color","selecting-text","has-prose","class"]))),128))]),t("div",null,[e[4]||(e[4]=t("h3",{class:"mt-2 text-base font-semibold mb-2"},"Your Translation",-1)),T(t("textarea",{class:"block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6","onUpdate:modelValue":e[1]||(e[1]=a=>i.value=a)},null,512),[[C,i.value]]),N(F,{class:"mt-2",onClick:v,disabled:!i.value},{default:A(()=>e[3]||(e[3]=[d("Check with Lingu.ai")])),_:1},8,["disabled"])])]),n.value?(r(),o("div",S,[t("div",U,[e[5]||(e[5]=t("h3",{class:"text-base font-semibold mb-2"},[d(" Lingu.ai "),t("span",{class:"uppercase align-[8px] text-[10px]"},"beta")],-1)),t("div",null,[t("p",null,[t("span",{class:h(["inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600 mr-4",y(n.value.score)])},c(k(n.value.score)),3),d(c(n.value.justification),1)]),t("ul",null,[(r(!0),o(m,null,x(n.value.improvements,a=>(r(),o("li",D," • "+c(a),1))),256))])])])])):V("",!0)]))}};export{z as default};
