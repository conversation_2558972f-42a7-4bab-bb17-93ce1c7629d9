import{_ as c}from"./AppLayout-33f062bc.js";import f from"./DeleteUserForm-bd489cef.js";import u from"./LogoutOtherBrowserSessionsForm-1ab7bb92.js";import{S as s}from"./SectionBorder-b49c1148.js";import d from"./TwoFactorAuthenticationForm-052be0d4.js";import _ from"./UpdatePasswordForm-c3c366e2.js";import g from"./UpdateProfileInformationForm-3ba66559.js";import h from"./SubscriptionInformation-d44dabeb.js";import{_ as $}from"./Breadcrumbs-c96e9207.js";import{_ as w}from"./Footer-0988dcd8.js";import y from"./CaseColors-f1eb7460.js";import{A as b,i as v,o as e,c as B,w as k,b as o,a as r,u as P,d as i,f as a,F as A}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./DialogModal-b2ffd0f0.js";import"./SectionTitle-05f6d081.js";import"./InputError-7edb5cf8.js";import"./TextInput-940981ae.js";/* empty css            */import"./ActionMessage-0a1272b7.js";import"./InputLabel-3b7f7747.js";import"./FormSection-45b6b921.js";import"./datetime-8ddd27a0.js";import"./ChevronRightIcon-a926c707.js";const F={class:"p-8 pb-16"},T={class:"mx-auto max-w-7xl py-10 pr-4"},j={key:0},C={key:1},V={id:"case-colors"},x={key:2},jo={__name:"Show",props:{confirmsTwoFactorAuthentication:Boolean,sessions:Array},setup(n){let l=[{name:"Profile",href:"/user/profile",current:!0}];return b(()=>{window.location.hash==="#case-colors"&&setTimeout(()=>{const t=document.getElementById("case-colors");if(t){const p=t.getBoundingClientRect().top+window.scrollY;window.scrollTo({top:p-100,behavior:"smooth"})}},5)}),(t,m)=>{const p=v("Head");return e(),B(c,{title:"Profile"},{default:k(()=>[o(p,{title:"Profile"}),r("div",F,[o($,{class:"lg:col-span-9 xl:grid-cols-10",pages:P(l)},null,8,["pages"]),m[0]||(m[0]=r("section",{class:"mt-8 w-full","aria-labelledby":"Description"},[r("h1",{class:"text-4xl font-bold text-gray-900"},"Profile"),r("p",{class:"mt-4 text-base font-normal text-gray-600"}," View and update your profile information. ")],-1)),r("div",T,[r("div",null,[o(h,{user:t.$page.props.user,"trial-ends-at":t.$page.props.auth.user.trial_ends_at},null,8,["user","trial-ends-at"]),o(s)]),t.$page.props.jetstream.canUpdateProfileInformation?(e(),i("div",j,[o(g,{user:t.$page.props.user,"read-only":t.$page.props.auth.user.google_id,class:"mt-10 sm:mt-0"},null,8,["user","read-only"]),o(s)])):a("",!0),t.$page.props.jetstream.canUpdatePassword?(e(),i("div",C,[o(_,{class:"mt-10 sm:mt-0"}),o(s)])):a("",!0),r("div",V,[o(y,{"case-colors":t.$page.props.user.case_colors,class:"mt-10 sm:mt-0"},null,8,["case-colors"]),o(s)]),t.$page.props.jetstream.canManageTwoFactorAuthentication?(e(),i("div",x,[o(d,{"requires-confirmation":n.confirmsTwoFactorAuthentication,class:"mt-10 sm:mt-0"},null,8,["requires-confirmation"]),o(s)])):a("",!0),o(u,{sessions:n.sessions,class:"mt-10 sm:mt-0"},null,8,["sessions"]),t.$page.props.jetstream.hasAccountDeletionFeatures?(e(),i(A,{key:3},[o(s),o(f,{class:"mt-10 sm:mt-0"})],64)):a("",!0)])]),o(w)]),_:1})}}};export{jo as default};
