import{e as u,A as l,o as s,d,n as i}from"./app-f0078ddb.js";const f=["value","readonly"],m={__name:"TextInput",props:{modelValue:String,readOnly:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(a,{expose:t}){const e=u(null);return l(()=>{e.value.hasAttribute("autofocus")&&e.value.focus()}),t({focus:()=>e.value.focus()}),(n,o)=>(s(),d("input",{ref_key:"input",ref:e,class:i(["border-gray-300 rounded-md shadow-xs",a.readOnly?"bg-gray-100 focus:border-gray-300 focus:ring-transparent cursor-not-allowed text-gray-600":"bg-white focus:border-indigo-500 focus:ring-indigo-500"]),value:a.modelValue,onInput:o[0]||(o[0]=r=>n.$emit("update:modelValue",r.target.value)),readonly:a.readOnly},null,42,f))}};export{m as _};
