import{_ as l}from"./ButtonItem-718c0517.js";import{r as j}from"./CheckCircleIcon-d86d1232.js";import{r as G}from"./StarIcon-155a2a28.js";import{r as T}from"./ArrowRightOnRectangleIcon-b404433d.js";import{p as Y,A as F,bb as O,o as d,d as m,b as o,w as a,u as s,z as U,a as r,g as f,t as i,f as y,n as N}from"./app-f0078ddb.js";import{Y as q,G as H}from"./dialog-86f7bd91.js";import{h as A,S as I}from"./transition-a0923044.js";/* empty css            */import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";const J={class:"fixed inset-0 z-50 w-screen overflow-y-auto lg:hidden"},K={class:"flex min-h-full items-end justify-center p-4 text-center sm:p-0"},P={class:"text-center"},Q={class:"text-lg leading-6 font-semibold text-gray-900"},R={key:0},X={class:"mt-1 text-sm text-gray-600"},Z={key:0,class:"mr-2 text-xs uppercase"},_={key:1},ee={class:"mt-2 text-base text-gray-900"},te={class:"mt-5 grid grid-cols-3 gap-2 sm:mt-6"},oe=1024,he={__name:"VocabularyDisplayMobile",props:{word:{type:Object,default:()=>({})},toggle:Boolean,displaySyntax:Boolean,isLearned:Boolean,isStarred:Boolean,verifiedSyntax:Boolean},emits:["update:learned-words","update:starred-words","closeModal"],setup(e,{emit:D}){const g=e,p=()=>{W("closeModal")},W=D,c=n=>{document.documentElement.style.overflow=n?"auto":""},u=()=>{window.innerWidth>=oe?U(()=>{c(g.toggle)}):c(!1)};return Y(()=>g.toggle,()=>{u()}),F(()=>{window.addEventListener("resize",u)}),O(()=>{window.removeEventListener("resize",u),c(!1)}),(n,t)=>(d(),m("div",null,[o(s(I),{as:"template",show:e.toggle},{default:a(()=>[o(s(q),{as:"div",class:"relative z-10 lg:z-0",onClose:t[3]||(t[3]=w=>p())},{default:a(()=>[o(s(A),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:a(()=>t[4]||(t[4]=[r("div",{class:"fixed inset-0 bg-gray-500 opacity-75 transition-opacity lg:hidden"},null,-1)])),_:1}),r("div",J,[r("div",K,[o(s(A),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:a(()=>[o(s(H),{class:"relative w-full transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6"},{default:a(()=>{var w,x,v,h,k,b,S,B,$,z,C,L,M,V;return[r("div",null,[r("div",P,[r("h3",Q,[f(i(((x=(w=e.word)==null?void 0:w.word)==null?void 0:x.display_word)||"Loading...")+" ",1),(h=(v=e.word)==null?void 0:v.word)!=null&&h.gender?(d(),m("span",R,", "+i(e.word.word.gender),1)):y("",!0)]),r("p",X,[((b=(k=e.word)==null?void 0:k.word)==null?void 0:b.core)==1?(d(),m("span",Z,"core")):y("",!0),f(" "+i(((B=(S=e.word)==null?void 0:S.word)==null?void 0:B.pos)||"—")+" ",1),((z=($=e.word)==null?void 0:$.grammar)==null?void 0:z.length)>0&&e.displaySyntax&&e.verifiedSyntax?(d(),m("span",_," | "+i(e.word.grammar),1)):y("",!0)]),r("p",ee,i(((L=(C=e.word)==null?void 0:C.word)==null?void 0:L.short_def)||"Definition unavailable"),1)])]),r("div",te,[o(l,{type:"button",color:"white",onClick:t[0]||(t[0]=E=>n.$emit("update:learned-words",e.word.word_id))},{default:a(()=>[o(s(j),{class:N(["mx-auto h-5 w-5 stroke-current stroke-2 transition duration-250",[e.isLearned?"text-blue-600":"text-gray-400"]])},null,8,["class"])]),_:1}),o(l,{type:"button",color:"white",onClick:t[1]||(t[1]=E=>n.$emit("update:starred-words",e.word.word_id))},{default:a(()=>[o(s(G),{class:N(["mx-auto h-5 w-5 stroke-current stroke-2 transition duration-250",[e.isStarred?"fill-current stroke-2 text-green-600":"stroke-current text-gray-400"]])},null,8,["class"])]),_:1}),o(l,{link:(V=(M=e.word)==null?void 0:M.word)!=null&&V.id?`/words/w/${e.word.word.id}`:"#",color:"white"},{default:a(()=>[o(s(T),{class:"mx-auto h-5 w-5 stroke-current text-gray-600 transition duration-250"})]),_:1},8,["link"]),o(l,{class:"col-span-3",type:"button",color:"indigo",onClick:t[2]||(t[2]=E=>p())},{default:a(()=>t[5]||(t[5]=[f(" Close ")])),_:1})])]}),_:1})]),_:1})])])]),_:1})]),_:1},8,["show"])]))}};export{he as default};
