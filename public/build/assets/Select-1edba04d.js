import{_ as m}from"./ButtonItem-718c0517.js";import{e as d,l as k,o as l,d as r,a as s,t as g,F as H,h as T,b as f,w as p,n as C,g as h}from"./app-f0078ddb.js";/* empty css            */const L={class:"flex-1"},S=["innerHTML"],z={class:"text-center text-lg font-medium text-gray-600"},M={class:"relative my-20 grid w-full touch-manipulation grid-cols-1 justify-items-center sm:px-16"},B={class:"text-4xl font-semibold text-gray-900"},N=["onClick","innerHTML"],V={class:"mt-2 text-2xl font-semibold text-gray-500"},$={class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8"},E={__name:"Select",props:{options:Object,stem:String,instructions:String},emits:["submit"],setup(i,{emit:x}){const a=i;let c=d(""),u=d("");const _=x;function b(){_("submit",{answer:c.value})}const v=k(()=>a.options.latin.match(/\w+|[^\s\w]+/g));function y(e,t){c.value=e,u.value=e+"["+t+"]"}function w(e,t){return u.value==e+"["+t+"]"}return(e,t)=>(l(),r("div",L,[s("h1",{class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900",innerHTML:i.stem},null,8,S),s("h3",z,g(a.instructions),1),s("div",M,[s("h2",B,[(l(!0),r(H,null,T(v.value,(n,o)=>(l(),r("span",{key:o,class:"inline-block cursor-pointer"},[s("span",{class:C({"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100 transition duration-250":w(n,o)}),onClick:j=>y(n,o),innerHTML:n},null,10,N),t[1]||(t[1]=s("span",null," ",-1))]))),128))]),s("h4",V,g(i.options.english),1)]),s("div",$,[f(m,{size:"lg",color:"indigo"},{default:p(()=>t[2]||(t[2]=[h("Hint")])),_:1}),f(m,{size:"lg",class:"w-full",color:"pink",onClick:t[0]||(t[0]=n=>b())},{default:p(()=>t[3]||(t[3]=[h("Submit")])),_:1})])]))}};export{E as default};
