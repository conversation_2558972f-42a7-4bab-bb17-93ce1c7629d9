import{e as l,l as $,V as q,p as N,A as O,o as r,d as u,a as t,F as g,h as f,b as c,u as U,q as b,v as x,t as y}from"./app-f0078ddb.js";import{_}from"./ToggleItem-94c3ab1e.js";import A from"./OptionsMode-26ccd316.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";import"./radio-group-97521e36.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";const C={class:"flex-1 py-2"},B={class:"mt-8 gap-8 rounded-lg bg-gray-100 p-6"},S={class:"grid grid-cols-2 gap-8"},z={class:"mt-4 ml-4 grid grid-cols-1 gap-2"},F=["id","value"],L=["for"],R={class:"mt-4 ml-4 grid grid-cols-1 gap-2"},E=["id","value"],G=["for"],P={class:"mt-12 rounded-lg bg-gray-100 p-6"},Z={class:"mt-12 grid grid-cols-1 gap-12 border-t border-gray-300 pt-12 pb-12 sm:grid-cols-2 sm:pt-8 sm:pb-6"},le={__name:"Options",emits:["update:options"],setup(j,{emit:h}){const D=h,V=[{id:1,name:"1st Declension",value:"first"},{id:2,name:"2nd Declension (Masculine)",value:"second_masc"},{id:3,name:"2nd Declension (Neuter)",value:"second_neut"},{id:4,name:"3rd Declension (Consonant)",value:"third_cons"},{id:5,name:"3rd Declension (i-stem)",value:"third_i"},{id:6,name:"3rd Declension (Neuter)",value:"third_neut"},{id:7,name:"4th Declension",value:"fourth"},{id:8,name:"5th Declension",value:"fifth"}],M=[{id:1,name:"Nominative",value:"nominative"},{id:2,name:"Genitive",value:"genitive"},{id:3,name:"Dative",value:"dative"},{id:4,name:"Accusative",value:"accusative"},{id:5,name:"Ablative",value:"ablative"},{id:6,name:"Vocative",value:"vocative"}],a=l([]),i=l(["nominative","genitive","dative","accusative","ablative"]),m=l(0),n=l(!1),o=l(!1),k=$(()=>a.value.length>0&&i.value.length>0),v=()=>{k.value&&D("update:options",{declensions:a.value,cases:i.value,difficulty:m.value,time:p.time,requireMacra:n.value,zenMode:o.value})};let p=q({time:0,difficulty:0});return N([a,i,m],()=>{v()}),O(()=>{v()}),(w,e)=>(r(),u("div",C,[e[7]||(e[7]=t("h1",{class:"text-4xl font-bold text-gray-900"}," Declension Practice Options ",-1)),e[8]||(e[8]=t("p",{class:"mt-1 text-sm text-gray-500"}," Select which declensions and cases you want to practice. ",-1)),t("div",B,[t("div",S,[t("div",null,[e[5]||(e[5]=t("h3",{class:"text-base font-medium text-gray-900"},"Declensions",-1)),t("div",z,[(r(),u(g,null,f(V,s=>t("div",{key:s.id,class:"flex items-center space-x-2"},[b(t("input",{type:"checkbox",id:`declension-${s.id}`,value:s.value,"onUpdate:modelValue":e[0]||(e[0]=d=>a.value=d),class:"h-4 w-4 rounded border-gray-300 text-indigo-600"},null,8,F),[[x,a.value]]),t("label",{for:`declension-${s.id}`,class:"text-sm text-gray-700"},y(s.name),9,L)])),64))])]),t("div",null,[e[6]||(e[6]=t("h3",{class:"text-base font-medium text-gray-900"},"Cases",-1)),t("div",R,[(r(),u(g,null,f(M,s=>t("div",{key:s.id,class:"flex items-center space-x-2"},[b(t("input",{type:"checkbox",id:`case-${s.id}`,value:s.value,"onUpdate:modelValue":e[1]||(e[1]=d=>i.value=d),class:"h-4 w-4 rounded border-gray-300 text-indigo-600"},null,8,E),[[x,i.value]]),t("label",{for:`case-${s.id}`,class:"text-sm text-gray-700"},y(s.name),9,G)])),64))])])]),t("div",P,[c(A,{"current-mode":U(p).time,"onUpdate:mode":e[2]||(e[2]=s=>w.updateSettings("time",s))},null,8,["current-mode"]),t("div",Z,[c(_,{order:"left",class:"self-top",label:"Require Long Marks",description:"Require correct placement of macrons.","button-color":"amber",modelValue:n.value,"onUpdate:modelValue":e[3]||(e[3]=s=>n.value=s)},null,8,["modelValue"]),c(_,{order:"left",class:"self-top",label:"Zen Mode",description:"Automatically move to the next word.","button-color":"amber",modelValue:o.value,"onUpdate:modelValue":e[4]||(e[4]=s=>o.value=s)},null,8,["modelValue"])])])])]))}};export{le as default};
