import{o as s,d as t,b as o,w as n,q as r,a as c,r as i,M as l,T as d}from"./app-f0078ddb.js";const _={class:"text-sm text-gray-600"},m={__name:"ActionMessage",props:{on:Boolean},setup(e){return(a,p)=>(s(),t("div",null,[o(d,{"leave-active-class":"transition ease-in duration-1000","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[r(c("div",_,[i(a.$slots,"default")],512),[[l,e.on]])]),_:3})]))}};export{m as _};
