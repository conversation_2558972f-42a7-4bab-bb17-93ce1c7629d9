import{e as b,V as M,l as k,p as _,a1 as T,o as d,c as R,w as y,d as m,b as g,a as i,f,u as r,j as x,t as F,q as h,x as S,F as N,h as z,n as $,W as C}from"./app-f0078ddb.js";import{_ as P}from"./ClassroomModal-05d10768.js";import j from"./SelectDate-fab0bbcb.js";import{_ as W}from"./Combobox-f427c07d.js";import{I as E}from"./InfinitasIcon-1a3ae135.js";import{r as G}from"./RectangleGroupIcon-04390470.js";import{r as L}from"./QueueListIcon-824b634b.js";import{r as V}from"./PlayCircleIcon-8bd12a30.js";/* empty css            */import"./ButtonItem-718c0517.js";import"./XMarkIcon-9bc7c0bd.js";import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./index-b0adb136.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./use-resolve-button-type-24d8b5c5.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./_plugin-vue_export-helper-c27b6911.js";const H={key:0,class:"flex flex-row items-center justify-center"},J={key:1,class:"flex flex-row items-center justify-center"},K={class:"inline h-10 w-10 rounded-full bg-orange-100 transition duration-150 group-hover:bg-orange-200"},Q={key:2,class:"flex flex-row items-center justify-center"},X={class:"inline h-10 w-10 rounded-full bg-emerald-100 transition duration-150 group-hover:bg-emerald-200"},Y={key:3,class:"flex flex-row items-center justify-center"},Z={class:"py-4"},ee={key:0,class:"flex h-12 w-full flex-row items-center"},te={class:"mb-4 flex w-full flex-col items-start sm:mb-0 sm:flex-row sm:items-center"},se={class:"flex text-xs font-bold text-gray-500 uppercase sm:w-1/3"},ie={key:0},le={key:1},ae={class:"flex h-12 w-full grow flex-row items-center"},oe={class:"flex h-12 w-full grow flex-row items-center"},ne={class:"flex grow text-xs font-bold text-gray-500 uppercase"},re={key:1,class:"text-right text-xs font-medium text-red-600"},ce={class:"mt-4 flex w-full flex-row items-center"},ue={class:"mx-auto h-8 items-center space-x-1 self-center rounded-lg bg-gray-200 p-0.5"},de={class:"mt-4 flex flex-row items-center"},me={class:"mx-auto h-8 items-center space-x-1 self-center rounded-lg p-0.5"},ge={class:"mt-4 flex flex-row items-center"},fe={class:"mx-auto h-8 w-1/2 items-center space-x-1 self-center rounded-lg p-0.5"},ve={class:"grid w-full grid-cols-10 gap-1 opacity-75"},xe=["onClick"],pe=["disabled"],be=["disabled"],Xe={__name:"AssignModule",props:{team:{type:Object,required:!0},open:{type:Boolean,required:!0},activities:{type:Array,required:!1},name:{type:String,required:!0},color:{type:String,required:!1,default:"indigo"},assignment:{type:Object,required:!1}},emits:["close-modal"],setup(c,{emit:D}){let e=c,o=b(e.assignment?e.assignment.due_at:""),n=b(e.assignment?e.assignment.published_at:""),s=M({time:e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.time/60:null,correct:e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.correct:null,accuracy:e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.accuracy:null}),u=b(e.assignment?e.assignment.activity_id:null);const p=D,I=()=>{setTimeout(()=>{o.value=e.assignment?e.assignment.due_at:"",n.value=e.assignment?e.assignment.published_at:"",s.time=e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.time/60:null,s.correct=e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.correct:null,s.accuracy=e.name=="video"?null:e.assignment&&e.assignment.targets?e.assignment.targets.accuracy:null,u.value=e.assignment?e.assignment.activity_id:null},250)},w=k(()=>e.activities?e.name=="video"?v.value||!o.value||!u.value:v.value||!o.value||!u.value||u.value&&!s.time&&!s.correct&&!s.accuracy:!s.time&&!s.correct&&!s.accuracy||v.value||!o.value);_(()=>e.open,a=>{a||I()}),_(()=>e.assignment,a=>{a&&(o.value=a.due_at,n.value=a.published_at,s.time=a.targets?a.targets.time/60:null,s.correct=a.targets?a.targets.correct:null,s.accuracy=a.targets?a.targets.accuracy:null,u.value=a.activity_id)});const A=()=>{C.put(`/assignments/${e.assignment.id}`,{due_at:o.value,publish_at:n.value?n.value:null,section:`${e.name}${u.value?":section-"+u.value:""}${s.time?":time-"+s.time:""}${s.correct?":correct-"+s.correct:""}${s.accuracy?":accuracy-"+s.accuracy:""}`},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{p("close-modal")}})},O=()=>{C.post("/assignments",{team_id:e.team.id,due_at:o.value,publish_at:n.value?n.value:null,section:`${e.name}${u.value?":section-"+u.value:""}${s.time?":time-"+s.time:""}${s.correct?":correct-"+s.correct:""}${s.accuracy?":accuracy-"+s.accuracy:""}`},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{p("close-modal")}})},q=a=>{if(s.accuracy==a){s.accuracy=null;return}s.accuracy=a},B=[{value:5,color:"bg-red-500"},{value:15,color:"bg-orange-600"},{value:25,color:"bg-amber-500"},{value:35,color:"bg-yellow-600"},{value:45,color:"bg-lime-600"},{value:55,color:"bg-green-600"},{value:65,color:"bg-teal-600"},{value:75,color:"bg-blue-600"},{value:85,color:"bg-indigo-600"},{value:95,color:"bg-purple-600"}],v=k(()=>new Date(n.value)>new Date(o.value));return(a,t)=>{const U=T("tippy");return d(),R(P,{open:c.open,onCloseModal:t[7]||(t[7]=l=>p("close-modal"))},{title:y(()=>[c.name=="infinitas"?(d(),m("div",H,[g(E,{class:"inline w-12"}),t[8]||(t[8]=i("span",{class:"ml-2 text-4xl font-semibold tracking-tight text-indigo-500"}," Infinitas ",-1))])):f("",!0),c.name=="grammar"?(d(),m("div",J,[i("div",K,[g(r(G),{class:"mt-2 ml-2 h-6 w-6 stroke-2 text-orange-600"})]),t[9]||(t[9]=i("div",{class:"ml-2 text-4xl font-semibold tracking-tight text-orange-500"}," Grammar ",-1))])):f("",!0),c.name=="vocabulary"?(d(),m("div",Q,[i("div",X,[g(r(L),{class:"mt-2 ml-2 h-6 w-6 stroke-2 text-emerald-600"})]),t[10]||(t[10]=i("div",{class:"ml-2 text-4xl font-semibold tracking-tight text-emerald-500"}," Vocabulary ",-1))])):f("",!0),c.name=="video"?(d(),m("div",Y,[g(r(V),{class:"h-10 w-10 stroke-2 text-slate-700"}),t[11]||(t[11]=i("div",{class:"ml-2 text-4xl font-semibold tracking-tight text-slate-700"}," Videos ",-1))])):f("",!0)]),main:y(()=>[i("div",Z,[c.activities?(d(),m("div",ee,[i("div",te,[i("div",se,[c.name!="video"?(d(),m("span",ie,"Activity")):(d(),m("span",le,"Video"))]),g(W,{class:"z-20 mt-2 w-full sm:mt-0",items:c.activities,"onUpdate:modelValue":t[0]||(t[0]=l=>x(u)?u.value=l.id:u=l.id),color:c.color,"default-item":r(u)},null,8,["items","color","default-item"])])])):f("",!0),i("div",ae,[t[12]||(t[12]=i("div",{class:"flex grow text-xs font-bold text-gray-500 uppercase"}," Due on ",-1)),g(j,{class:"inline pl-4",color:c.color,"onUpdate:selected":t[1]||(t[1]=l=>l?x(o)?o.value=l.toISOString():o=l.toISOString():x(o)?o.value="":o=""),"default-date":r(o)},null,8,["color","default-date"])]),i("div",oe,[i("div",ne,F(r(e).assignment?"Published":"Publish")+" on ",1),g(j,{class:"inline pl-4",publish:!0,color:c.color,"onUpdate:selected":t[2]||(t[2]=l=>l?x(n)?n.value=l.toISOString():n=l.toISOString():x(n)?n.value="":n=""),"default-date":r(n)},null,8,["color","default-date"])]),v.value?(d(),m("div",re," The due date must be after the publish date. ")):f("",!0),c.name!="video"?(d(),m("div",{key:2,class:$(["mb-0 text-left",v.value?"mt-4":"mt-8"])},[t[16]||(t[16]=i("p",{class:"text-xs font-bold text-gray-500 uppercase"}," Requirements (Select at least one) ",-1)),i("div",ce,[t[13]||(t[13]=i("div",{class:"flex grow flex-col text-left"},[i("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Time in Minutes"),i("span",{class:"text-sm text-gray-500"},"Require a certain amount of time.")],-1)),i("div",ue,[h(i("input",{class:"h-8 w-20 rounded-md py-1 text-center text-base leading-5 font-semibold ring-white/60 ring-offset-2 ring-offset-blue-400 placeholder:text-gray-300 focus:ring-2 focus:outline-hidden","onUpdate:modelValue":t[3]||(t[3]=l=>r(s).time=l),placeholder:"10"},null,512),[[S,r(s).time]])])]),i("div",de,[t[14]||(t[14]=i("div",{class:"flex grow flex-col text-left"},[i("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Total Correct"),i("span",{class:"text-sm text-gray-500"},"Require a certain number of correct words.")],-1)),i("div",me,[h(i("input",{class:"ml-2 h-8 w-20 rounded-md py-1 text-center text-base leading-5 font-semibold ring-white/60 ring-offset-2 ring-offset-blue-400 placeholder:text-gray-300 focus:ring-2 focus:outline-hidden","onUpdate:modelValue":t[4]||(t[4]=l=>r(s).correct=l),placeholder:"100"},null,512),[[S,r(s).correct]])])]),i("div",ge,[t[15]||(t[15]=i("div",{class:"flex grow flex-col text-left"},[i("span",{class:"text-sm leading-6 font-semibold text-gray-900"},"Minimum Accuracy")],-1)),i("div",fe,[i("div",ve,[(d(),m(N,null,z(B,l=>h(i("button",{class:$(["h-6 w-full rounded-sm shadow-sm",!r(s).accuracy||r(s).accuracy==l.value?l.color:"bg-gray-300"]),key:l.value,onClick:ye=>q(l.value)},null,10,xe),[[U,{content:l.value+"%",placement:"bottom",trigger:"mouseenter",hideOnClick:!1}]])),64))])])])],2)):f("",!0)])]),actionButton:y(()=>[r(e).assignment?(d(),m("button",{key:0,type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-2 py-1 text-base font-medium text-white shadow-xs transition duration-150 hover:bg-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75 sm:text-sm",onClick:t[5]||(t[5]=l=>A()),disabled:w.value}," Update ",8,pe)):(d(),m("button",{key:1,type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-2 py-1 text-base font-medium text-white shadow-xs transition duration-150 hover:bg-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75 sm:text-sm",onClick:t[6]||(t[6]=l=>O()),disabled:w.value}," Assign ",8,be))]),_:1},8,["open"])}}};export{Xe as default};
