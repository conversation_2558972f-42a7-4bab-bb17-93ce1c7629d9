import{e as m,l as $,p as C,o as f,c as q,w as h,a as s,b as x,j as p,u as l,t as _,g as M,d as v,q as S,x as T,n as A,f as z,W as D,a3 as H}from"./app-f0078ddb.js";import{_ as F}from"./ClassroomModal-05d10768.js";import I from"./SelectDate-fab0bbcb.js";import{_ as K}from"./DropdownWorks-53c01eb4.js";import R from"./DropdownBooks-b7db1f80.js";/* empty css            */import"./ButtonItem-718c0517.js";import"./XMarkIcon-9bc7c0bd.js";import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./index-b0adb136.js";import"./listbox-f702e976.js";import"./form-cb36670c.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";const E={class:"mt-8 text-left sm:pr-4"},P={class:"mt-4"},G={class:"block text-sm font-medium text-gray-700"},J={class:"mt-4 grid grid-cols-2 gap-8"},Q={for:"start",class:"block text-sm font-medium text-gray-700"},W=["innerHTML"],X=["innerHTML"],Y={class:"mt-1"},Z={for:"last",class:"block text-sm font-medium text-gray-700"},tt=["innerHTML"],et=["innerHTML"],st={class:"mt-1"},it={class:"mt-2 text-xs text-gray-600"},lt={class:"mt-4"},at={class:"flex items-center justify-between"},ot={class:"mt-2"},nt={class:"mt-4 flex h-12 w-full grow flex-row items-center"},rt={class:"flex h-12 w-full grow flex-row items-center"},dt={key:0,class:"text-right text-xs font-medium text-red-600"},ut=["disabled"],ct=["disabled"],$t={__name:"AssignReading",props:{team:Object,open:Boolean,works:Array,books:Array,assignment:{type:Object,required:!1}},emits:["close-modal"],setup(w,{emit:U}){let e=w;const y=U;let n=m(""),r=m(""),d=m(""),k=m(e.books),g=m(e.books[0]),u=m(""),c=m(""),a=m(e.works[0]),j=m(!1);const B=()=>{if(e.assignment){j.value=!0;let o=e.assignment.activity_id.split(":");n.value=e.assignment.due_at||"",r.value=e.assignment.published_at||"",d.value=e.assignment.description||"",a.value=e.works.find(t=>t.id===e.assignment.work_id)||e.works[0],H.post("/api/practice/vocabulary/get-books",{work:a.value.id}).then(t=>{k.value=t.data,g.value=t.data.find(i=>i.id===parseInt(o[0]))||t.data[0],setTimeout(()=>{u.value=o[1],c.value=o[2]},150)})}},V=()=>{D.post("/assignments",{team_id:e.team.id,due_at:n.value,publish_at:r.value||null,section:`read:${g.value.id}:${u.value}:${c.value}`,description:d.value},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{y("close-modal")}})},O=()=>{D.put(`/assignments/${e.assignment.id}`,{due_at:n.value,publish_at:r.value||null,section:`read:${g.value.id}:${u.value}:${c.value}`,description:d.value},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{y("close-modal")}})},L=o=>{let t=o.keyCode?o.keyCode:o.which;(t<48||t>57)&&o.preventDefault()},N=()=>{setTimeout(()=>{n.value="",r.value="",d.value="",a.value=e.works[0],g.value=e.books[0],u.value=e.assignment?e.assignment.activity_id.split(":")[1]:"",c.value=e.assignment?e.assignment.activity_id.split(":")[2]:""},250)},b=$(()=>new Date(r.value)>new Date(n.value));return C(()=>e.open,o=>{o?B():N()}),C(a,o=>{o&&H.post("/api/practice/vocabulary/get-books",{work:o.id}).then(t=>{k.value=t.data,g.value=t.data[0],e.assignment&&o.id==e.assignment.work_id?(u.value=e.assignment.activity_id.split(":")[1],c.value=e.assignment.activity_id.split(":")[2]):(u.value="",c.value="")})},{immediate:!0}),(o,t)=>(f(),q(F,{open:w.open,onCloseModal:t[9]||(t[9]=i=>y("close-modal"))},{icon:h(()=>t[10]||(t[10]=[s("div",{class:"flex justify-center -space-x-2"},[s("img",{class:"inline-block h-10 w-10 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/aeneid.png",alt:""}),s("img",{class:"inline-block h-10 w-10 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/metamorphoses.png",alt:""}),s("img",{class:"inline-block h-10 w-10 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/sulpicia.png",alt:""}),s("img",{class:"inline-block h-10 w-10 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/catullus.png",alt:""}),s("img",{class:"inline-block h-10 w-10 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/caesar-gallic-war.png",alt:""})],-1)])),main:h(()=>[s("div",E,[x(K,{list:w.works,"onUpdate:work":t[0]||(t[0]=i=>p(a)?a.value=i:a=i),current:l(e).assignment?l(e).works.find(i=>i.id===l(e).assignment.work_id):null},null,8,["list","current"]),s("div",P,[s("p",G," Select the "+_(l(a).l1),1),x(R,{items:l(k),"onUpdate:selected":t[1]||(t[1]=i=>p(g)?g.value=i:g=i),current:l(e).assignment?parseInt(l(e).assignment.activity_id.split(":")[0]):null},null,8,["items","current"])]),s("div",J,[s("div",null,[s("label",Q,[t[11]||(t[11]=M("First ")),l(a).l2?(f(),v("span",{key:0,innerHTML:l(a).l2},null,8,W)):(f(),v("span",{key:1,innerHTML:l(a).l4},null,8,X))]),s("div",Y,[S(s("input",{id:"start","onUpdate:modelValue":t[2]||(t[2]=i=>p(u)?u.value=i:u=i),type:"text",name:"start",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 101",onKeypress:L},null,544),[[T,l(u)]])])]),s("div",null,[s("label",Z,[t[12]||(t[12]=M("Last ")),l(a).l2?(f(),v("span",{key:0,innerHTML:l(a).l2},null,8,tt)):(f(),v("span",{key:1,innerHTML:l(a).l4},null,8,et))]),s("div",st,[S(s("input",{id:"last","onUpdate:modelValue":t[3]||(t[3]=i=>p(c)?c.value=i:c=i),type:"text",name:"last",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 157",onKeypress:L},null,544),[[T,l(c)]])])])]),s("p",it," Leaving the first or last blank will default to the start or end of this "+_(l(a).l1.toLowerCase())+". ",1),s("div",lt,[s("div",at,[t[13]||(t[13]=s("label",{for:"comment",class:"block text-sm leading-6 font-medium text-gray-700"},"Add a comment",-1)),s("span",{class:A(["text-xs",l(d).length>256?"text-red-500":"text-gray-500"])},_(256-l(d).length)+" characters remaining",3)]),s("div",ot,[S(s("textarea",{rows:"2",name:"comment",id:"comment","onUpdate:modelValue":t[4]||(t[4]=i=>p(d)?d.value=i:d=i),placeholder:"Tell your students what you want them to do.",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 focus:ring-inset sm:text-sm sm:leading-6"},null,512),[[T,l(d)]])])])]),s("div",{class:A(["pt-4",b.value?"pb-0":"pb-4"])},[s("div",nt,[t[14]||(t[14]=s("div",{class:"flex grow text-xs font-bold text-gray-500 uppercase"}," Due on ",-1)),x(I,{class:"inline pl-4",color:"indigo","onUpdate:selected":t[5]||(t[5]=i=>i?p(n)?n.value=i.toISOString():n=i.toISOString():p(n)?n.value="":n=""),"default-date":l(n)},null,8,["default-date"])]),s("div",rt,[t[15]||(t[15]=s("div",{class:"flex grow text-xs font-bold text-gray-500 uppercase"}," Publish on ",-1)),x(I,{class:"inline pl-4",publish:!0,color:"indigo","onUpdate:selected":t[6]||(t[6]=i=>i?p(r)?r.value=i.toISOString():r=i.toISOString():p(r)?r.value="":r=""),"default-date":l(r).value},null,8,["default-date"])]),b.value?(f(),v("div",dt," The due date must be after the publish date. ")):z("",!0)],2)]),actionButton:h(()=>[l(e).assignment?(f(),v("button",{key:0,type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-2 py-1 text-base font-medium text-white shadow-xs transition duration-150 hover:bg-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75 sm:text-sm",onClick:t[7]||(t[7]=i=>O()),disabled:!l(n)||l(d).length>256||b.value}," Update ",8,ut)):(f(),v("button",{key:1,type:"submit",onClick:t[8]||(t[8]=i=>V()),class:"inline-flex w-full justify-center rounded-md border border-transparent bg-indigo-500 px-2 py-1 text-base font-medium text-white shadow-xs transition duration-150 hover:bg-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75 sm:text-sm",disabled:!l(n)||l(d).length>256||b.value}," Assign ",8,ct))]),_:1},8,["open"]))}};export{$t as default};
