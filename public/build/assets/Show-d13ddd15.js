import{l as L,e as x,p as z,i as D,a1 as P,o,c as k,w as u,b as d,a as e,t as n,u as l,k as $,d as i,q as C,n as S,f as v,h,F as b,v as I,j as M,g as R,W as U}from"./app-f0078ddb.js";import{_ as Y}from"./AppLayout-33f062bc.js";import J from"./WordReference-fbb40385.js";import{_ as K}from"./Breadcrumbs-c96e9207.js";import{u as Q}from"./useInfiniteScroll-1e8e8e17.js";import{P as X}from"./Promotion-3eee0057.js";import{_ as Z}from"./Footer-0988dcd8.js";import{r as ee}from"./CheckCircleIcon-d86d1232.js";import{r as te}from"./StarIcon-155a2a28.js";import{r as re}from"./XMarkIcon-9bc7c0bd.js";import{Y as se,G as oe}from"./dialog-86f7bd91.js";import{h as T,S as le}from"./transition-a0923044.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./ChevronDownIcon-660c32b0.js";import"./render-c34c346a.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./open-closed-7f51e238.js";import"./keyboard-982fc047.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./Colosseum-0e8d62a4.js";import"./removePunctuation-702d8a66.js";import"./ChevronRightIcon-a926c707.js";import"./useIntersect-6e15125e.js";import"./description-cd3ec634.js";const ne={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},ie={class:"p-8"},ae={class:"mt-8 overflow-hidden border-b border-gray-300 pb-8"},de={class:"flex flex-row items-center"},ce={class:"grow"},ue={class:"text-3xl leading-tight font-bold text-gray-900"},me={key:0,class:"flex grow-0 flex-row"},fe={class:"align-center mt-2 flex"},pe={class:"text-base font-semibold text-gray-600"},ge={key:0,class:"ml-4 text-base font-semibold text-gray-600"},xe={class:"mt-2"},ve={class:"text-xl font-medium text-gray-600"},he={"aria-labelledby":"filter-heading",class:"block border-gray-200 py-6 lg:hidden"},be={class:"flex items-center justify-between"},_e={class:"fixed inset-0 z-40 flex"},ye={class:"flex items-center justify-between px-4"},we={class:"mt-8 px-4"},ke={class:"text-sm font-semibold"},$e={class:"max-w-[150px] truncate text-sm font-semibold"},Ce={class:"mt-4 grid grid-cols-1 border-b border-gray-300 px-4 pb-4 font-semibold text-gray-600"},Se={key:0,class:"mt-4"},qe={class:"mt-4"},Le={class:"mt-8 px-4 text-sm"},je={class:"mt-8 flex"},Oe={class:"flex-1 text-right"},Ve={class:"my-2 grid grid-cols-1 gap-2 py-2"},We={class:"relative flex items-center"},Fe={class:"flex h-5 items-center"},Be=["id","value","name"],Ne={class:"ml-3 text-sm"},ze=["for"],De=["src"],Ie={class:"mt-8 overflow-hidden"},Me={key:0,class:"mt-2"},Re={class:"grid divide-y"},Te={key:1,class:"relative mt-4 block h-20 w-full rounded-lg border-2 border-dashed border-gray-300 py-5 text-center"},Ae={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Ee={key:0},Ge={class:"flex"},He={class:"text-sm font-semibold"},Pe={class:"truncate text-sm font-semibold lg:max-w-[150px] xl:max-w-[180px] 2xl:max-w-[300px]"},Ue={class:"mt-8 grid grid-cols-1 border-b border-gray-300 pb-4 font-semibold text-gray-600"},Ye={key:0,class:"mt-4"},Je={class:"mt-4"},Ke={"aria-labelledby":"filter words"},Qe={class:"pt-8"},Xe={class:"flex text-sm"},Ze={class:"flex-1 text-right"},et={class:"mt-4"},tt={class:"my-2 grid grid-cols-1 gap-2 py-2"},rt={class:"relative flex items-center"},st={class:"flex h-5 items-center"},ot=["id","value","name"],lt={class:"ml-3 text-sm"},nt=["for"],it=["src"],Ut={__name:"Show",props:{word:{type:Object,required:!0},isStarred:{type:Boolean,required:!0},isLearned:{type:Boolean,required:!0},references:{type:Object,required:!1},filters:{type:Object,required:!1},referenceWorks:{type:Object,required:!1}},setup(m){const r=m,A=[{name:"Words",href:"/words",current:!1},{name:r.word.latin,href:"#",current:!0}],j=L(()=>{switch(!0){case r.word.core_frequency===null:return"border-purple-500 text-purple-500 hover:bg-purple-500 hover:text-white";case r.word.core_frequency<=250:return"border-emerald-500 text-emerald-500 hover:bg-emerald-500 hover:text-white";case r.word.core_frequency<=500:return"border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white";case r.word.core_frequency<=1e3:return"border-orange-500 text-orange-500 hover:bg-orange-500 hover:text-white";default:return"border-purple-500 text-purple-500 hover:bg-purple-500 hover:text-white"}}),_=L(()=>{switch(!0){case r.word.core_frequency===null:return"uncommon";case r.word.core_frequency<=250:return"basic";case r.word.core_frequency<=500:return"intermediate";case r.word.core_frequency<=1e3:return"advanced";default:return"uncommon"}}),O=L(()=>{switch(!0){case r.word.gender===null:return"";case r.word.gender==="m.":return"masculine";case r.word.gender==="f.":return"feminine";case r.word.gender==="n.":return"neuter";case r.word.gender==="m./f.":return"masculine/feminine";case r.word.gender==="m./n.":return"masculine/neuter";case r.word.gender==="m./f./n.":return"masculine/feminine/neuter";case r.word.gender==="f./n.":return"feminine/neuter";default:return r.word.gender}}),y=x(!1),V=x(null);let q=x(r.references);const{items:W}=Q(q.value,V);let p=x(r.isStarred),g=x(r.isLearned);const E=()=>{p.value=!p.value,axios.post("/words/"+r.word.id+"/star").then(a=>{a.data.status!="success"&&(p.value=!p.value)})},G=()=>{g.value=!g.value,axios.post("/words/"+r.word.id+"/learn").then(a=>{a.data.status!="success"&&(g.value=!g.value)})};let c=x(r.filters);const F=()=>{c.value=[]},B=a=>{if(a%100>=11&&a%100<=13)return a+"th";switch(a%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd";default:return a+"th"}};return z(()=>c,()=>{U.get(`/words/w/${r.word.id}`,{filters:c.value},{only:["references","filters"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),z(()=>r.references,a=>{q.value=a,W.value=q.value.data},{deep:!0}),(a,t)=>{const H=D("Head"),w=D("Link"),N=P("tippy");return o(),k(Y,null,{default:u(()=>[d(H,null,{default:u(()=>t[9]||(t[9]=[e("title",null,"Words",-1)])),_:1}),e("main",ne,[e("div",ie,[d(K,{class:"lg:col-span-9 xl:grid-cols-10",pages:A}),e("section",ae,[e("div",null,[e("div",de,[e("div",ce,[e("h3",ue,n(r.word.latin),1)]),l($)().props.authenticated?(o(),i("div",me,[C((o(),i("button",{class:"ml-4 rounded-lg",type:"button",color:"white",onClick:t[0]||(t[0]=s=>G())},[d(l(ee),{class:S(["mx-auto h-8 w-8 stroke-current stroke-2 transition duration-150 hover:text-blue-600",[l(g)?"text-blue-600":"text-gray-400"]])},null,8,["class"])])),[[N,{content:l(g)?"Mark as not learned":"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),C((o(),i("button",{class:"ml-4 rounded-lg",type:"button",color:"white",onClick:t[1]||(t[1]=s=>E())},[d(l(te),{class:S(["mx-auto h-8 w-8 stroke-current stroke-2 transition duration-150 hover:text-green-600",[l(p)?"text-green-600":"stroke-current text-gray-400"]])},null,8,["class"])])),[[N,{content:l(p)?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])):v("",!0)]),e("div",fe,[e("span",pe,n(r.word.pos),1),O.value?(o(),i("span",ge,n(O.value),1)):v("",!0)]),e("div",xe,[e("span",ve,n(r.word.definition),1)])])]),e("section",he,[t[10]||(t[10]=e("h2",{id:"filter-heading",class:"sr-only"},"Filters",-1)),e("div",be,[e("button",{type:"button",class:"inline-block text-sm font-medium text-gray-700 hover:text-gray-900 lg:hidden",onClick:t[2]||(t[2]=s=>y.value=!0)}," Filters ")])]),d(l(le),{as:"template",show:y.value},{default:u(()=>[d(l(se),{as:"div",class:"relative z-40 lg:hidden",onClose:t[6]||(t[6]=s=>y.value=!1)},{default:u(()=>[d(l(T),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:u(()=>t[11]||(t[11]=[e("div",{class:"fixed inset-0 bg-black opacity-25"},null,-1)])),_:1}),e("div",_e,[d(l(T),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:u(()=>[d(l(oe),{class:"relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl"},{default:u(()=>[e("div",ye,[t[13]||(t[13]=e("div",null,null,-1)),e("button",{type:"button",class:"-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:outline-hidden",onClick:t[3]||(t[3]=s=>y.value=!1)},[t[12]||(t[12]=e("span",{class:"sr-only"},"Close menu",-1)),d(l(re),{class:"h-6 w-6","aria-hidden":"true"})])]),e("div",we,[d(w,{href:`/words?filters[]=${_.value}`,class:S(["inline-flex items-center rounded-full border-2 px-3 py-1 transition duration-150",j.value])},{default:u(()=>[e("span",ke,n(_.value),1)]),_:1},8,["href","class"]),(o(!0),i(b,null,h(r.word.group,s=>(o(),k(w,{href:`/words?group=${s.id}`,class:"ml-4 inline-flex items-center truncate rounded-full border-2 border-gray-200 bg-gray-200 px-3 py-1 text-gray-600 transition duration-150 hover:border-gray-600 hover:bg-gray-600 hover:text-white"},{default:u(()=>[e("span",$e,n(s.name),1)]),_:2},1032,["href"]))),256))]),e("section",Ce,[e("div",null,n(m.word.text_count.toLocaleString())+" occurences ",1),l($)().props.authenticated?(o(),i("div",Se," Viewed "+n(m.word.user_clicks.toLocaleString())+" times by you ",1)):v("",!0),e("div",qe,n(B(m.word.site_rank))+" most common ",1)]),e("div",Le,[t[15]||(t[15]=e("h2",{class:"text-lg font-medium text-gray-900"},"Filters",-1)),e("div",je,[t[14]||(t[14]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Works")],-1)),e("div",Oe,[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:t[4]||(t[4]=s=>F())}," Clear ")])]),(o(!0),i(b,null,h(m.referenceWorks,s=>(o(),i("div",Ve,[e("div",We,[e("div",Fe,[C(e("input",{id:`${s.id}-mobile`,"onUpdate:modelValue":t[5]||(t[5]=f=>M(c)?c.value=f:c=f),value:s.id,name:s.id,type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,Be),[[I,l(c)]])]),e("div",Ne,[e("label",{for:`${s.id}-mobile`,class:"flex flex-row items-center font-intro text-lg font-semibold text-gray-700"},[e("img",{class:"mx-auto mr-2 h-8 w-8 rounded-full",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.image_name,alt:""},null,8,De),R(n(s.name),1)],8,ze)])])]))),256))])]),_:1})]),_:1})])]),_:1})]),_:1},8,["show"]),e("div",Ie,[e("div",null,[t[17]||(t[17]=e("h3",{class:"text-2xl leading-tight font-bold text-gray-900"}," Examples ",-1)),r.references.total>0?(o(),i("div",Me,[e("div",Re,[(o(!0),i(b,null,h(l(W),(s,f)=>(o(),k(J,{key:f,data:s,class:"py-6"},null,8,["data"]))),128)),e("div",{ref_key:"landmark",ref:V},null,512)])])):(o(),i("div",Te,t[16]||(t[16]=[e("span",{class:"mt-2 block text-sm font-medium text-gray-600"}," No Textual References ",-1)])))])])]),d(Z)]),e("aside",Ae,[l($)().props.authenticated?v("",!0):(o(),i("section",Ee,[d(X)])),e("section",Ge,[d(w,{href:`/words?filters[]=${_.value}`,class:S(["inline-flex items-center rounded-full border-2 px-3 py-1 transition duration-150",j.value])},{default:u(()=>[e("span",He,n(_.value),1)]),_:1},8,["href","class"]),(o(!0),i(b,null,h(r.word.group,s=>(o(),k(w,{href:`/words?group=${s.id}`,class:"ml-4 inline-flex items-center rounded-full border-2 border-gray-200 bg-gray-200 px-3 py-1 text-gray-600 transition duration-150 hover:border-gray-600 hover:bg-gray-600 hover:text-white"},{default:u(()=>[e("span",Pe,n(s.name),1)]),_:2},1032,["href"]))),256))]),e("section",Ue,[e("div",null,n(m.word.text_count.toLocaleString())+" occurences",1),l($)().props.authenticated?(o(),i("div",Ye," Viewed "+n(m.word.user_clicks.toLocaleString())+" times by you ",1)):v("",!0),e("div",Je,n(B(m.word.site_rank))+" most common",1)]),e("section",Ke,[e("div",Qe,[e("div",Xe,[t[18]||(t[18]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),e("div",Ze,[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:t[7]||(t[7]=s=>F())}," Clear ")])]),e("div",et,[(o(!0),i(b,null,h(m.referenceWorks,s=>(o(),i("div",tt,[e("div",rt,[e("div",st,[C(e("input",{id:s.id,"onUpdate:modelValue":t[8]||(t[8]=f=>M(c)?c.value=f:c=f),value:s.id,name:s.id,type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,ot),[[I,l(c)]])]),e("div",lt,[e("label",{for:s.id,class:"flex flex-row items-center font-intro text-lg font-semibold text-gray-700"},[e("img",{class:"mx-auto mr-2 h-8 w-8 rounded-full",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.image_name,alt:""},null,8,it),R(n(s.name),1)],8,nt)])])]))),256))])])])])]),_:1})}}};export{Ut as default};
