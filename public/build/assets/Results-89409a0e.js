import{_ as H}from"./AppLayout-33f062bc.js";import{_ as W}from"./Breadcrumbs-c96e9207.js";import{D as w}from"./datetime-8ddd27a0.js";import{_ as z}from"./ClassroomModal-05d10768.js";import k from"./AssignModule-74fae4e9.js";import G from"./AssignReading-df4942fb.js";import{e as f,D as J,i as C,o as a,c as $,w as d,b as l,a as e,t as m,d as n,u as r,j as p,f as c,h as D,g as M,n as S,F as I,Q,W as U}from"./app-f0078ddb.js";import{I as K}from"./InfinitasIcon-1a3ae135.js";import{r as P}from"./RectangleGroupIcon-04390470.js";import{r as X}from"./QueueListIcon-824b634b.js";import{r as Y}from"./PlayCircleIcon-8bd12a30.js";import{r as Z}from"./PencilSquareIcon-048eb348.js";import{r as O}from"./TrashIcon-31936c3d.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./BookOpenIcon-1746d343.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";/* empty css            */import"./Combobox-f427c07d.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./DropdownBooks-b7db1f80.js";const ee={class:"relative isolate bg-white px-6 pb-16 lg:px-8"},te={class:"py-2 md:py-8"},se={class:"flex flex-row items-center justify-between"},oe={class:"py-6"},ie={class:"sm:flex sm:items-center sm:justify-between"},ae={class:"w-full sm:flex sm:space-x-5"},le={class:"shrink-0"},ne={key:0},re={key:1},me={class:"flex h-24 w-24 items-center justify-center rounded-full bg-orange-100 transition duration-150"},ce={key:2},de={class:"flex h-24 w-24 items-center justify-center rounded-full bg-emerald-100 transition duration-150"},ue={key:3,class:"relative flex h-24 w-24 shrink-0 items-center justify-center overflow-hidden rounded-full"},fe=["src"],pe={key:4},ge={class:"flex h-24 w-24 items-center justify-center rounded-full bg-indigo-100 transition duration-150"},ve=["src"],ye={class:"mt-4 flex w-full flex-col sm:mt-0 sm:pt-1 sm:text-left"},he={class:"mt-2 flex w-full items-end"},be={class:"inline-block text-4xl font-bold text-gray-900 sm:text-5xl"},xe={class:"mb-1 flex grow items-end"},we={class:"ml-8 inline-block cursor-pointer text-sm font-medium text-gray-500 transition duration-150 ease-in-out hover:text-teal-600"},ke={class:"ml-4 inline-block cursor-pointer text-sm font-medium text-gray-500 transition duration-150 ease-in-out hover:text-red-600"},Me={class:"mt-2 flex gap-2"},_e={class:"inline text-xl font-semibold text-gray-500"},$e={class:"mt-8"},Ae={class:"mt-6 w-full whitespace-nowrap text-left"},je={class:"border-b border-white/10 text-sm leading-6 text-gray-600"},Ce={key:0,scope:"col",class:"hidden py-2 pl-0 pr-8 font-semibold md:table-cell lg:pr-20"},De={key:1,scope:"col",class:"hidden py-2 pl-0 pr-8 font-semibold sm:table-cell"},Se={class:"divide-y divide-white/5"},Ie={class:"py-4 pl-4 pr-8 sm:pl-6 lg:pl-8"},Oe={class:"flex items-center gap-x-4"},Ve=["src"],Be={class:"hidden py-4 pl-0 pr-4 sm:table-cell sm:pr-8"},Le={class:"flex items-center justify-end gap-x-2 sm:justify-start"},Te=["datetime"],Fe={class:"hidden text-sm font-medium text-gray-600 sm:block"},Ne=["datetime"],Re={key:0,class:"py-4 pl-0 pr-4 text-sm font-medium leading-6 sm:pr-8"},qe={key:0},Ee={key:1,class:"py-4 pl-0 pr-2 text-sm font-medium leading-6 2xl:pr-16"},He={key:0},We={class:"flex flex-row justify-between"},ze={class:"text-gray-900"},Ge={class:"w-20 pl-2"},Je={class:"flex justify-center gap-0.5 opacity-75"},Qe={class:"py-4 pl-0 pr-4 text-right text-sm font-medium leading-6 sm:pr-8"},Ue={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100"},Ht={__name:"Results",props:{team:Object,assignment:Object,students:Array,works:{type:Array,required:!1,default:()=>[]},books:{type:Array,required:!1,default:()=>[]},vocabActivities:{type:Array,required:!1,default:()=>[]},grammarActivities:{type:Array,required:!1,default:()=>[]},videos:{type:Array,required:!1,default:()=>[]}},setup(o){const u=o,V=[{name:"Classes",href:"/classes",current:!1},{name:u.team.name,href:`/classes/${u.team.slug}`,current:!1},{name:u.assignment.title+" Results",href:"#",current:!0}],B=i=>{switch(u.assignment.section){case"infinitas":return`/practice/vocabulary/attempt/${i.id}`;case"grammar":return`/practice/grammar/attempt/${i.id}`;case"vocabulary":return`/practice/vocabulary/attempt/${i.id}`;case"video":return`/watch/${i.url_slug}`;case"read":return`${i.url}`}};let g=f(!1);const L=function(){U.delete("/assignments/"+u.assignment.id,{preserveScroll:!0,preserveState:!0})},T=()=>{g.value=!1};let v=f(!1),y=f(!1),h=f(!1),b=f(!1),x=f(!1);const F=()=>{switch(u.assignment.section){case"infinitas":h.value=!0;break;case"grammar":y.value=!0;break;case"vocabulary":v.value=!0;break;case"video":x.value=!0;break;case"read":b.value=!0;break}},A=(i,s)=>w.fromISO(i).ts<=w.fromISO(s).ts,N=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],R=i=>{switch(!0){case!i:return null;case Math.round(i/10)<15:return"Just a few seconds";case Math.round(i/10)<30:return"Less than 30 seconds";case Math.round(i/10)<60:return"Less than a minute";case Math.round(i/10)<90:return"About a minute";case Math.round(i/10)<150:return"About 2 minutes";case Math.round(i/10)<210:return"About 3 minutes";case Math.round(i/10)<270:return"About 4 minutes";case Math.round(i/10)<330:return"About 5 minutes";default:return"About "+Math.round(i/600)+" minutes"}},q=i=>{if(i.length===0)return"Not yet started";if(i.completed==0)return"In progress";if(i.completed==1)return A(i.completed_at,u.assignment.due_at)?"Completed":"Completed late"};return J(2e3),(i,s)=>{const E=C("Head"),j=C("Link");return a(),$(H,null,{default:d(()=>[l(E,null,{default:d(()=>[e("title",null,m(o.team.name),1)]),_:1}),e("div",ee,[e("div",te,[e("div",se,[l(W,{class:"lg:col-span-9 xl:grid-cols-10",pages:V})])]),e("section",null,[e("div",oe,[e("div",ie,[e("div",ae,[e("div",le,[o.assignment.image=="infinitas"?(a(),n("div",ne,[l(K,{class:"inline w-24"})])):o.assignment.image=="grammar"?(a(),n("div",re,[e("div",me,[l(r(P),{class:"h-16 w-16 stroke-2 text-orange-600"})])])):o.assignment.image=="vocabulary"?(a(),n("div",ce,[e("div",de,[l(r(X),{class:"h-16 w-16 stroke-2 text-emerald-600"})])])):o.assignment.section=="video"?(a(),n("div",ue,[l(r(Y),{class:"absolute mx-auto h-16 w-16 text-slate-400"}),s[9]||(s[9]=e("div",{class:"absolute inset-0 bg-gray-200 opacity-25"},null,-1)),e("img",{src:o.assignment.image,class:"inset-0 h-full w-full object-cover"},null,8,fe)])):(a(),n("div",pe,[e("div",ge,[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${o.assignment.image}`,class:"h-24 w-24 stroke-2 text-indigo-600"},null,8,ve)])]))]),e("div",ye,[e("div",he,[e("h5",be,m(o.assignment.title),1),e("div",xe,[e("span",we,[l(r(Z),{class:"h-5 w-5",onClick:s[0]||(s[0]=t=>F())})]),e("span",ke,[l(r(O),{class:"h-5 w-5",onClick:s[1]||(s[1]=t=>p(g)?g.value=!0:g=!0)})])])]),e("div",Me,[e("div",_e," Due "+m(r(w).fromISO(o.assignment.due_at).toFormat("MMMM dd, yyyy 'at' hh:mm a")),1)])])])])])]),e("section",$e,[s[15]||(s[15]=e("div",{class:"text-xs font-semibold uppercase leading-6 text-gray-500"}," Student Results ",-1)),e("table",Ae,[s[14]||(s[14]=e("colgroup",null,[e("col",{class:"w-full sm:w-4/12"}),e("col",{class:"lg:w-2/12"}),e("col",{class:"lg:w-2/12"}),e("col",{class:"lg:w-3/12"}),e("col",{class:"lg:w-1/12"})],-1)),e("thead",je,[e("tr",null,[s[10]||(s[10]=e("th",{scope:"col",class:"py-2 pl-4 pr-8 font-semibold sm:pl-6 lg:pl-8"}," User ",-1)),s[11]||(s[11]=e("th",{scope:"col",class:"py-2 pl-0 pr-4 text-right font-semibold sm:pr-8 sm:text-left lg:pr-20"}," Status ",-1)),o.assignment.section!="video"&&o.assignment.section!="read"?(a(),n("th",Ce," Duration ")):c("",!0),o.assignment.section!="video"&&o.assignment.section!="read"?(a(),n("th",De," Accuracy ")):c("",!0),s[12]||(s[12]=e("th",{scope:"col",class:"py-2 pl-0 pr-4 font-semibold sm:pr-8 lg:pr-20"},null,-1))])]),e("tbody",Se,[(a(!0),n(I,null,D(o.students,t=>(a(),n("tr",{key:t.id,class:"group bg-transparent transition duration-150 ease-in-out even:bg-slate-50"},[e("td",Ie,[e("div",Oe,[e("img",{class:"h-8 w-8 rounded-full",src:t.photo_url,alt:""},null,8,Ve),l(j,{href:`/classes/${o.team.slug}/students/${t.slug}`,class:"truncate text-base font-medium leading-6 text-gray-800 hover:text-blue-700 hover:underline"},{default:d(()=>[M(m(t.name),1)]),_:2},1032,["href"])])]),e("td",Be,[e("div",Le,[t.completion&&t.completion.completed==1?(a(),n("time",{key:0,class:"text-gray-400 sm:hidden",datetime:t.completion.completed_at},m(r(w).fromISO(t.completion.completed_at).toFormat("MMMM dd, yyyy 'at' hh:mm a")),9,Te)):c("",!0),e("div",null,[e("span",{class:S(["right-0 top-0 mx-auto block h-2 w-2 rounded-full",t.completion.id?t.completion.completed==1?A(t.completion.completed_at,o.assignment.due_at)?"bg-green-500":"bg-amber-500":"bg-sky-500":"bg-gray-300"])},null,2)]),e("div",Fe,m(q(t.completion)),1)]),t.completion&&t.completion.completed==1?(a(),n("time",{key:0,class:"hidden text-xs font-medium text-gray-400 sm:block",datetime:t.completion.completed_at},m(r(w).fromISO(t.completion.completed_at).toFormat("MMMM dd, yyyy 'at' hh:mm a")),9,Ne)):c("",!0)]),o.assignment.section!="video"?(a(),n("td",Re,[t.completion&&t.completion.activity?(a(),n("div",qe,m(R(t.completion.activity.time)),1)):c("",!0)])):c("",!0),o.assignment.section!="video"?(a(),n("td",Ee,[t.completion&&t.completion.activity?(a(),n("div",He,[e("div",We,[e("h2",ze,m(t.completion.activity.correct)+" out of "+m(t.completion.activity.attempts)+" ("+m(Math.round(1e3*t.completion.activity.correct/t.completion.activity.attempts)/10)+"%) ",1),e("div",Ge,[e("div",Je,[(a(),n(I,null,D(N,_=>e("div",{class:S(["h-6 w-full rounded-sm shadow-sm",t.completion.activity.correct/t.completion.activity.attempts*10>=_.value?t.completion.completed===1?_.color:"bg-gray-600":"bg-gray-300"]),key:_.value},null,2)),64))])])])])):c("",!0)])):c("",!0),e("td",Qe,[t.completion.activity?(a(),$(j,{key:0,class:"text-sm font-semibold text-blue-500 transition duration-150 hover:text-blue-600",href:B(t.completion.activity)},{default:d(()=>s[13]||(s[13]=[M(" View ")])),_:2},1032,["href"])):c("",!0)])]))),128))])])]),(a(),$(Q,{to:"body"},[l(z,{open:r(g),onCloseModal:s[3]||(s[3]=t=>T())},{title:d(()=>s[16]||(s[16]=[M(" Delete this Assignment ")])),icon:d(()=>[e("div",Ue,[l(r(O),{class:"h-6 w-6 text-red-600","aria-hidden":"true"})])]),main:d(()=>s[17]||(s[17]=[M(" Are you sure you wish to delete this assignment? This action cannot be reversed. ")])),actionButton:d(()=>[e("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:text-sm",onClick:s[2]||(s[2]=t=>L())}," Delete ")]),_:1},8,["open"]),l(G,{open:r(b),onCloseModal:s[4]||(s[4]=t=>p(b)?b.value=!1:b=!1),team:o.team,works:o.works,books:o.books,assignment:o.assignment},null,8,["open","team","works","books","assignment"]),l(k,{open:r(h),onCloseModal:s[5]||(s[5]=t=>p(h)?h.value=!1:h=!1),team:o.team,color:"indigo",name:"infinitas",assignment:o.assignment},null,8,["open","team","assignment"]),l(k,{open:r(x),onCloseModal:s[6]||(s[6]=t=>p(x)?x.value=!1:x=!1),team:o.team,color:"slate",name:"video",activities:o.videos,assignment:o.assignment},null,8,["open","team","activities","assignment"]),l(k,{open:r(y),onCloseModal:s[7]||(s[7]=t=>p(y)?y.value=!1:y=!1),activities:o.grammarActivities,team:o.team,color:"orange",name:"grammar",assignment:o.assignment},null,8,["open","activities","team","assignment"]),l(k,{open:r(v),onCloseModal:s[8]||(s[8]=t=>p(v)?v.value=!1:v=!1),activities:o.vocabActivities,team:o.team,color:"emerald",name:"vocabulary",assignment:o.assignment},null,8,["open","activities","team","assignment"])]))])]),_:1})}}};export{Ht as default};
