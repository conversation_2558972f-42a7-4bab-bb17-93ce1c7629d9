import"./SortableEvent-286d5fef.js";import{S as u}from"./Sortable-ff8f985a.js";import{_ as m}from"./ButtonItem-718c0517.js";import{i as b,o,d as a,a as r,q as g,M as f,b as p,F as _,h as v}from"./app-f0078ddb.js";import{_ as w}from"./_plugin-vue_export-helper-c27b6911.js";import{r as y}from"./CheckCircleIcon-d86d1232.js";/* empty css            */const x={props:{options:Array,stem:String,instructions:String},components:{ButtonItem:m,CheckCircleIcon:y},data(){return{draggableList:[],answerList:[],sortable:"",isCorrect:!1}},methods:{addToken(t){var e=document.getElementById(t.id),s=document.getElementById("answerBench"),i=document.getElementById("workBench");e.parentNode.id=="workBench"?(s.appendChild(e),s.classList.add("border-green-500"),s.classList.remove("border-gray-400"),this.answerList.push(t.data),this.submit(),this.sortable.destroy()):(i.appendChild(e),s.classList.remove("border-green-500"),s.classList.add("border-gray-400"),this.answerList=[])},createSortable(){this.sortable&&this.sortable.destroy(),this.sortable=new u(document.querySelectorAll("[sortableContainer]"),{draggable:"[sortable]",plugins:[],mirror:{constrainDimensions:!1},classes:{mirror:["bg-blue-400"]}}),this.sortable.on("mirror:attached",function(t){t.mirror.style.position="fixed"}).on("drag:over:container",function(t){var e=t.overContainer;e.id==="answerBench"&&(e.classList.add("border-green-500"),e.classList.remove("border-gray-400"))}).on("drag:out:container",function(t){var e=t.overContainer;e.id==="answerBench"&&(e.classList.remove("border-green-500"),e.classList.add("border-gray-400"))}).on("drag:stop",()=>{this.sortable.containers.length,this.answerList=[],setTimeout(()=>{this.sortable.getDraggableElementsForContainer(this.sortable.containers[0])[0]&&(this.answerList.push(this.sortable.getDraggableElementsForContainer(this.sortable.containers[0])[0].innerText),this.submit(),this.sortable.destroy())},0)}).on("hook:destroyed",()=>{this.sortable.destroy()})},resetChunks(){var t=document.querySelectorAll("div#answerBench [sortable]");t.forEach(e=>{document.getElementById("workBench").appendChild(e)})},createIndex(t){const e=[];for(let s=0;s<t.length;s++)e.push({id:s,data:t[s]});return e},submit(){this.isCorrect=!0,setTimeout(()=>{this.$emit("update:addItem")},1e3)}},mounted(){this.draggableList=this.createIndex(this.options),this.createSortable()},watch:{options(){var t=document.getElementById("answerBench");t.classList.remove("border-green-500"),t.classList.add("border-gray-400"),this.isCorrect=!1;var e=[];e.push(this.options),this.draggableList=this.createIndex(e),this.createSortable(),this.answerList=[],this.resetChunks()}}},L={class:"relative flex-1"},C=["innerHTML"],B=["innerHTML"],k={class:"absolute z-50 w-full"},I={class:"relative mt-12 grid w-full grid-cols-1 justify-items-center px-16"},T={class:"mt-8 h-16 w-full"},S={class:"flex h-full w-full items-start justify-center",sortableContainer:"",id:"workBench"},E=["id","onDblclick","innerHTML"];function M(t,e,s,i,l,d){const c=b("CheckCircleIcon");return o(),a("div",L,[r("h1",{class:"mt-4 mb-4 text-center text-3xl font-semibold text-blue-700",innerHTML:s.stem},null,8,C),r("h3",{class:"text-center text-lg font-medium text-gray-600",innerHTML:s.instructions},null,8,B),g(r("div",k,[p(c,{class:"mx-auto h-48 w-48 text-blue-500 opacity-75"})],512),[[f,l.isCorrect]]),r("div",I,[e[0]||(e[0]=r("div",{class:"h-24 w-full"},[r("div",{class:"flex h-full w-full items-center justify-center rounded-2xl border-4 border-dashed border-gray-400 py-2",sortableContainer:"",id:"answerBench"})],-1)),r("div",T,[r("div",S,[(o(!0),a(_,null,v(l.draggableList,(n,h)=>(o(),a("div",{class:"mx-px inline-flex cursor-move items-center rounded-l-full rounded-r-full bg-purple-600 px-4 py-1 py-2 text-xl font-medium text-white select-none",draggable:"",sortable:"",key:h,id:n.id,onDblclick:D=>d.addToken(n),innerHTML:n.data},null,40,E))),128))])])])])}const $=w(x,[["render",M],["__scopeId","data-v-a72583a0"]]);export{$ as default};
