import{d as Q,e as J}from"./form-cb36670c.js";import{u as D,o as x,E as W,A as B,T as G,i as F,N}from"./render-c34c346a.js";import{w as X}from"./use-outside-click-484df218.js";import{s as Y}from"./use-resolve-button-type-24d8b5c5.js";import{p as Z}from"./use-text-value-2c18b2b1.js";import{f as _,c as O,u as $}from"./calculate-active-index-51ff911b.js";import{f as ee,u as te,o as f}from"./keyboard-982fc047.js";import{t as le,i as j,l as ae}from"./open-closed-7f51e238.js";import{w as oe,h as ie,O as ne}from"./focus-management-8406d052.js";import{H as C,e as R,l as m,I as ue,A as M,p as K,L as U,F as re,B as se,J as ve,z as V,K as de,a0 as S}from"./app-f0078ddb.js";function pe(t,b){return t===b}var ce=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(ce||{}),fe=(t=>(t[t.Single=0]="Single",t[t.Multi=1]="Multi",t))(fe||{}),be=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(be||{});function me(t){requestAnimationFrame(()=>requestAnimationFrame(t))}let H=Symbol("ListboxContext");function E(t){let b=de(H,null);if(b===null){let y=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(y,E),y}return b}let ke=C({name:"Listbox",emits:{"update:modelValue":t=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>pe},horizontal:{type:[Boolean],default:!1},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},multiple:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(t,{slots:b,attrs:y,emit:h}){let r=R(1),s=R(null),e=R(null),d=R(null),v=R([]),p=R(""),c=R(null),a=R(1);function i(l=o=>o){let o=c.value!==null?v.value[c.value]:null,n=ne(l(v.value.slice()),g=>x(g.dataRef.domRef)),u=o?n.indexOf(o):null;return u===-1&&(u=null),{options:n,activeOptionIndex:u}}let w=m(()=>t.multiple?1:0),[P,L]=Q(m(()=>t.modelValue),l=>h("update:modelValue",l),m(()=>t.defaultValue)),T=m(()=>P.value===void 0?D(w.value,{1:[],0:void 0}):P.value),k={listboxState:r,value:T,mode:w,compare(l,o){if(typeof t.by=="string"){let n=t.by;return(l==null?void 0:l[n])===(o==null?void 0:o[n])}return t.by(l,o)},orientation:m(()=>t.horizontal?"horizontal":"vertical"),labelRef:s,buttonRef:e,optionsRef:d,disabled:m(()=>t.disabled),options:v,searchQuery:p,activeOptionIndex:c,activationTrigger:a,closeListbox(){t.disabled||r.value!==1&&(r.value=1,c.value=null)},openListbox(){t.disabled||r.value!==0&&(r.value=0)},goToOption(l,o,n){if(t.disabled||r.value===1)return;let u=i(),g=_(l===O.Specific?{focus:O.Specific,id:o}:{focus:l},{resolveItems:()=>u.options,resolveActiveIndex:()=>u.activeOptionIndex,resolveId:A=>A.id,resolveDisabled:A=>A.dataRef.disabled});p.value="",c.value=g,a.value=n??1,v.value=u.options},search(l){if(t.disabled||r.value===1)return;let o=p.value!==""?0:1;p.value+=l.toLowerCase();let n=(c.value!==null?v.value.slice(c.value+o).concat(v.value.slice(0,c.value+o)):v.value).find(g=>g.dataRef.textValue.startsWith(p.value)&&!g.dataRef.disabled),u=n?v.value.indexOf(n):-1;u===-1||u===c.value||(c.value=u,a.value=1)},clearSearch(){t.disabled||r.value!==1&&p.value!==""&&(p.value="")},registerOption(l,o){let n=i(u=>[...u,{id:l,dataRef:o}]);v.value=n.options,c.value=n.activeOptionIndex},unregisterOption(l){let o=i(n=>{let u=n.findIndex(g=>g.id===l);return u!==-1&&n.splice(u,1),n});v.value=o.options,c.value=o.activeOptionIndex,a.value=1},theirOnChange(l){t.disabled||L(l)},select(l){t.disabled||L(D(w.value,{0:()=>l,1:()=>{let o=S(k.value.value).slice(),n=S(l),u=o.findIndex(g=>k.compare(n,S(g)));return u===-1?o.push(n):o.splice(u,1),o}}))}};X([e,d],(l,o)=>{var n;k.closeListbox(),oe(o,ie.Loose)||(l.preventDefault(),(n=x(e))==null||n.focus())},m(()=>r.value===0)),ue(H,k),le(m(()=>D(r.value,{0:j.Open,1:j.Closed})));let I=m(()=>{var l;return(l=x(e))==null?void 0:l.closest("form")});return M(()=>{K([I],()=>{if(!I.value||t.defaultValue===void 0)return;function l(){k.theirOnChange(t.defaultValue)}return I.value.addEventListener("reset",l),()=>{var o;(o=I.value)==null||o.removeEventListener("reset",l)}},{immediate:!0})}),()=>{let{name:l,modelValue:o,disabled:n,form:u,...g}=t,A={open:r.value===0,disabled:n,value:T.value};return U(re,[...l!=null&&T.value!=null?J({[l]:T.value}).map(([z,q])=>U(ee,W({features:te.Hidden,key:z,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:u,disabled:n,name:z,value:q}))):[],B({ourProps:{},theirProps:{...y,...G(g,["defaultValue","onUpdate:modelValue","horizontal","multiple","by"])},slot:A,slots:b,attrs:y,name:"Listbox"})])}}}),De=C({name:"ListboxLabel",props:{as:{type:[Object,String],default:"label"},id:{type:String,default:null}},setup(t,{attrs:b,slots:y}){var h;let r=(h=t.id)!=null?h:`headlessui-listbox-label-${F()}`,s=E("ListboxLabel");function e(){var d;(d=x(s.buttonRef))==null||d.focus({preventScroll:!0})}return()=>{let d={open:s.listboxState.value===0,disabled:s.disabled.value},{...v}=t,p={id:r,ref:s.labelRef,onClick:e};return B({ourProps:p,theirProps:v,slot:d,attrs:b,slots:y,name:"ListboxLabel"})}}}),Te=C({name:"ListboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(t,{attrs:b,slots:y,expose:h}){var r;let s=(r=t.id)!=null?r:`headlessui-listbox-button-${F()}`,e=E("ListboxButton");h({el:e.buttonRef,$el:e.buttonRef});function d(a){switch(a.key){case f.Space:case f.Enter:case f.ArrowDown:a.preventDefault(),e.openListbox(),V(()=>{var i;(i=x(e.optionsRef))==null||i.focus({preventScroll:!0}),e.value.value||e.goToOption(O.First)});break;case f.ArrowUp:a.preventDefault(),e.openListbox(),V(()=>{var i;(i=x(e.optionsRef))==null||i.focus({preventScroll:!0}),e.value.value||e.goToOption(O.Last)});break}}function v(a){switch(a.key){case f.Space:a.preventDefault();break}}function p(a){e.disabled.value||(e.listboxState.value===0?(e.closeListbox(),V(()=>{var i;return(i=x(e.buttonRef))==null?void 0:i.focus({preventScroll:!0})})):(a.preventDefault(),e.openListbox(),me(()=>{var i;return(i=x(e.optionsRef))==null?void 0:i.focus({preventScroll:!0})})))}let c=Y(m(()=>({as:t.as,type:b.type})),e.buttonRef);return()=>{var a,i;let w={open:e.listboxState.value===0,disabled:e.disabled.value,value:e.value.value},{...P}=t,L={ref:e.buttonRef,id:s,type:c.value,"aria-haspopup":"listbox","aria-controls":(a=x(e.optionsRef))==null?void 0:a.id,"aria-expanded":e.listboxState.value===0,"aria-labelledby":e.labelRef.value?[(i=x(e.labelRef))==null?void 0:i.id,s].join(" "):void 0,disabled:e.disabled.value===!0?!0:void 0,onKeydown:d,onKeyup:v,onClick:p};return B({ourProps:L,theirProps:P,slot:w,attrs:b,slots:y,name:"ListboxButton"})}}}),Ie=C({name:"ListboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(t,{attrs:b,slots:y,expose:h}){var r;let s=(r=t.id)!=null?r:`headlessui-listbox-options-${F()}`,e=E("ListboxOptions"),d=R(null);h({el:e.optionsRef,$el:e.optionsRef});function v(a){switch(d.value&&clearTimeout(d.value),a.key){case f.Space:if(e.searchQuery.value!=="")return a.preventDefault(),a.stopPropagation(),e.search(a.key);case f.Enter:if(a.preventDefault(),a.stopPropagation(),e.activeOptionIndex.value!==null){let i=e.options.value[e.activeOptionIndex.value];e.select(i.dataRef.value)}e.mode.value===0&&(e.closeListbox(),V(()=>{var i;return(i=x(e.buttonRef))==null?void 0:i.focus({preventScroll:!0})}));break;case D(e.orientation.value,{vertical:f.ArrowDown,horizontal:f.ArrowRight}):return a.preventDefault(),a.stopPropagation(),e.goToOption(O.Next);case D(e.orientation.value,{vertical:f.ArrowUp,horizontal:f.ArrowLeft}):return a.preventDefault(),a.stopPropagation(),e.goToOption(O.Previous);case f.Home:case f.PageUp:return a.preventDefault(),a.stopPropagation(),e.goToOption(O.First);case f.End:case f.PageDown:return a.preventDefault(),a.stopPropagation(),e.goToOption(O.Last);case f.Escape:a.preventDefault(),a.stopPropagation(),e.closeListbox(),V(()=>{var i;return(i=x(e.buttonRef))==null?void 0:i.focus({preventScroll:!0})});break;case f.Tab:a.preventDefault(),a.stopPropagation();break;default:a.key.length===1&&(e.search(a.key),d.value=setTimeout(()=>e.clearSearch(),350));break}}let p=ae(),c=m(()=>p!==null?(p.value&j.Open)===j.Open:e.listboxState.value===0);return()=>{var a,i;let w={open:e.listboxState.value===0},{...P}=t,L={"aria-activedescendant":e.activeOptionIndex.value===null||(a=e.options.value[e.activeOptionIndex.value])==null?void 0:a.id,"aria-multiselectable":e.mode.value===1?!0:void 0,"aria-labelledby":(i=x(e.buttonRef))==null?void 0:i.id,"aria-orientation":e.orientation.value,id:s,onKeydown:v,role:"listbox",tabIndex:0,ref:e.optionsRef};return B({ourProps:L,theirProps:P,slot:w,attrs:b,slots:y,features:N.RenderStrategy|N.Static,visible:c.value,name:"ListboxOptions"})}}}),Ae=C({name:"ListboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(t,{slots:b,attrs:y,expose:h}){var r;let s=(r=t.id)!=null?r:`headlessui-listbox-option-${F()}`,e=E("ListboxOption"),d=R(null);h({el:d,$el:d});let v=m(()=>e.activeOptionIndex.value!==null?e.options.value[e.activeOptionIndex.value].id===s:!1),p=m(()=>D(e.mode.value,{0:()=>e.compare(S(e.value.value),S(t.value)),1:()=>S(e.value.value).some(l=>e.compare(S(l),S(t.value)))})),c=m(()=>D(e.mode.value,{1:()=>{var l;let o=S(e.value.value);return((l=e.options.value.find(n=>o.some(u=>e.compare(S(u),S(n.dataRef.value)))))==null?void 0:l.id)===s},0:()=>p.value})),a=Z(d),i=m(()=>({disabled:t.disabled,value:t.value,get textValue(){return a()},domRef:d}));M(()=>e.registerOption(s,i)),se(()=>e.unregisterOption(s)),M(()=>{K([e.listboxState,p],()=>{e.listboxState.value===0&&p.value&&D(e.mode.value,{1:()=>{c.value&&e.goToOption(O.Specific,s)},0:()=>{e.goToOption(O.Specific,s)}})},{immediate:!0})}),ve(()=>{e.listboxState.value===0&&v.value&&e.activationTrigger.value!==0&&V(()=>{var l,o;return(o=(l=x(d))==null?void 0:l.scrollIntoView)==null?void 0:o.call(l,{block:"nearest"})})});function w(l){if(t.disabled)return l.preventDefault();e.select(t.value),e.mode.value===0&&(e.closeListbox(),V(()=>{var o;return(o=x(e.buttonRef))==null?void 0:o.focus({preventScroll:!0})}))}function P(){if(t.disabled)return e.goToOption(O.Nothing);e.goToOption(O.Specific,s)}let L=$();function T(l){L.update(l)}function k(l){L.wasMoved(l)&&(t.disabled||v.value||e.goToOption(O.Specific,s,0))}function I(l){L.wasMoved(l)&&(t.disabled||v.value&&e.goToOption(O.Nothing))}return()=>{let{disabled:l}=t,o={active:v.value,selected:p.value,disabled:l},{value:n,disabled:u,...g}=t,A={id:s,ref:d,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":p.value,disabled:void 0,onClick:w,onFocus:P,onPointerenter:T,onMouseenter:T,onPointermove:k,onMousemove:k,onPointerleave:I,onMouseleave:I};return B({ourProps:A,theirProps:g,slot:o,attrs:y,slots:b,name:"ListboxOption"})}}});export{Ie as A,De as E,Ae as F,ke as I,Te as j};
