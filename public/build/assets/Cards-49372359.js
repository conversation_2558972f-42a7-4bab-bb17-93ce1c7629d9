import{_ as l}from"./ButtonItem-718c0517.js";import{I as b}from"./InfinitasIcon-1a3ae135.js";import{D as y}from"./datetime-8ddd27a0.js";/* empty css                                                              */import{_ as v}from"./_plugin-vue_export-helper-c27b6911.js";import{o as d,d as c,a as t,e as g,p as h,b as s,w as o,u as m,g as i,t as r,F as _,h as k,f as L}from"./app-f0078ddb.js";import{r as p}from"./RectangleGroupIcon-04390470.js";/* empty css            */function A(n,f){return d(),c("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"fill-rule":"evenodd",d:"M8 1.75a.75.75 0 0 1 .692.462l1.41 3.393 3.664.293a.75.75 0 0 1 .428 1.317l-2.791 2.39.853 3.575a.75.75 0 0 1-1.12.814L7.998 12.08l-3.135 1.915a.75.75 0 0 1-1.12-.814l.852-3.574-2.79-2.39a.75.75 0 0 1 .427-1.318l3.663-.293 1.41-3.393A.75.75 0 0 1 8 1.75Z","clip-rule":"evenodd"})])}const I={class:"h-80 w-48 min-w-48 snap-start bg-indigo-100 rounded-xl px-4 py-3 shadow-lg grid content-between"},S={class:"w-full flex flex-col"},j={class:"h-80 w-48 min-w-48 snap-start bg-white rounded-xl px-4 py-3 shadow-lg grid content-between"},z={class:"flex gap-1 items-center flex-row justify-center text-gray-600 text-xs font-medium rounded-full py-0.5 px-2 bg-gray-100"},V=["src"],R={class:"font-bold text-xl text-center text-gray-900 line-clamp-2 leading-6 mt-2"},C={class:"text-xs text-gray-600 mt-2 line-clamp-3 text-center"},N={class:"h-80 w-48 min-w-48 snap-start bg-white rounded-xl px-4 py-3 shadow-lg grid content-between"},B=["src"],O={class:"font-bold text-xl text-center text-gray-900 line-clamp-2 leading-5 mt-2"},P={class:"mt-1"},$={key:0,class:"mx-auto font-bold text-xs text-gray-500 py-1 px-3 rounded-full bg-gray-200 dark:bg-gray-700 dark:text-gray-300"},D={class:"text-xs text-gray-600 mt-2 line-clamp-3 text-center"},F={class:"h-80 w-48 min-w-48 snap-start bg-orange-100 rounded-xl px-4 py-3 shadow-lg grid content-between"},M={class:"font-bold text-xl text-center text-orange-600 line-clamp-2 leading-5 mt-2"},W={class:"text-xs text-orange-600 mt-4 line-clamp-3 text-center"},E={class:"h-80 w-48 min-w-48 snap-start bg-orange-100 rounded-xl px-4 py-3 shadow-lg grid content-between"},T={__name:"Cards",props:{recentSection:Object,dailyActivity:Object,assignments:Object},setup(n){const f=g(0),x=g(null);g(0);const w=()=>{const a=x.value.children[0].offsetWidth;f.value=Math.round(x.value.scrollLeft/a)};return h(x,a=>{a&&a.addEventListener("scroll",w)}),(a,e)=>(d(),c("section",null,[t("div",{ref_key:"carouselRef",ref:x,class:"flex gap-8 overflow-x-auto scrollbar-none py-4 scroll-ps-6 snap-x"},[t("div",I,[t("div",S,[e[0]||(e[0]=t("p",{class:"text-center text-indigo-600 text-xs font-medium rounded-full py-0.5 px-2 bg-indigo-50"}," Grow Vocabulary ",-1)),s(b,{class:"inline w-24 mx-auto"}),e[1]||(e[1]=t("h3",{class:"font-bold text-2xl text-center text-indigo-600"}," Infinitas ",-1)),e[2]||(e[2]=t("p",{class:"text-xs text-indigo-600 mt-2 line-clamp-3 text-center"}," Infinitas helps you learn new words and improve your vocabulary. ",-1))]),t("div",null,[s(l,{color:"white",size:"xs",class:"w-full mt-4"},{default:o(()=>e[3]||(e[3]=[i(" Learn More ")])),_:1}),s(l,{color:"white",size:"xs",class:"w-full mt-2 border-indigo-600"},{default:o(()=>e[4]||(e[4]=[t("span",{class:"text-indigo-600 hover:text-indigo-600 transition duration-150"},"Practice Vocab",-1)])),_:1})])]),t("div",j,[t("div",null,[t("div",z,[s(m(A),{class:"text-green-500 h-3 inline"}),e[5]||(e[5]=i(" Pinned Work "))]),t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${n.recentSection.book.work.image_name}`,alt:"",class:"rounded-full bg-gray-900/5 shadow-lg w-16 mx-auto border border-gray-300 mt-4"},null,8,V),t("h3",R,r(n.recentSection.name),1),t("p",C,r(n.recentSection.description),1)]),t("div",null,[s(l,{color:"white",size:"xs",class:"w-full mt-2 border-indigo-600"},{default:o(()=>e[6]||(e[6]=[i(" Practice Vocab ")])),_:1}),s(l,{color:"white",size:"xs",class:"w-full mt-2 border-indigo-600"},{default:o(()=>e[7]||(e[7]=[i(" Read Again ")])),_:1})])]),(d(!0),c(_,null,k(n.assignments,u=>(d(),c("div",N,[t("div",null,[e[8]||(e[8]=t("div",{class:"flex gap-1 items-center flex-row justify-center text-blue-600 text-xs font-medium rounded-full py-0.5 px-2 bg-blue-100"}," Assignment ",-1)),t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${n.recentSection.book.work.image_name}`,alt:"",class:"rounded-full bg-gray-900/5 shadow-lg w-16 mx-auto border border-gray-300 mt-6"},null,8,B),t("h3",O,r(u.title),1),t("div",P,[u.due_at?(d(),c("span",$,"due "+r(m(y).fromISO(u.due_at).toFormat("LLL d, yyyy")),1)):L("",!0)]),t("p",D,r(u.description),1)]),t("div",null,[s(l,{color:"blue",size:"xs",class:"w-full mt-8 border-indigo-600"},{default:o(()=>e[9]||(e[9]=[i(" Attempt ")])),_:1})])]))),256)),t("div",F,[t("div",null,[e[10]||(e[10]=t("div",{class:"flex gap-1 items-center flex-row justify-center text-orange-600 text-xs font-medium rounded-full py-0.5 px-2 bg-orange-50"}," Practice the Language ",-1)),s(m(p),{class:"rounded-full w-16 mx-auto mt-6 text-orange-600"}),t("h3",M,r(n.dailyActivity.name),1),t("p",W,r(n.dailyActivity.description),1)]),t("div",null,[s(l,{color:"white",size:"xs",class:"w-full mt-8 border-indigo-600"},{default:o(()=>e[11]||(e[11]=[t("span",{class:"text-orange-600"},"Attempt",-1)])),_:1})])]),t("div",E,[t("div",null,[e[12]||(e[12]=t("div",{class:"flex gap-1 items-center flex-row justify-center text-orange-600 text-xs font-medium rounded-full py-0.5 px-2 bg-orange-50"}," Language Review ",-1)),s(m(p),{class:"rounded-full w-16 mx-auto mt-6 text-orange-600"}),e[13]||(e[13]=t("h3",{class:"font-bold text-xl text-center text-orange-600 line-clamp-2 leading-5 mt-2"}," Noun Review ",-1)),e[14]||(e[14]=t("p",{class:"text-xs text-orange-600 mt-4 line-clamp-3 text-center"}," Review nouns, their forms, and their usage. ",-1))]),t("div",null,[s(l,{color:"white",size:"xs",class:"w-full mt-8"},{default:o(()=>e[15]||(e[15]=[t("span",{class:"text-orange-600"},"Attempt",-1)])),_:1})])]),e[16]||(e[16]=t("div",{class:"h-80 min-w-48 snap-start w-48 bg-fuchsia-200 rounded-xl"},null,-1))],512)]))}},X=v(T,[["__scopeId","data-v-4a53711e"]]);export{X as default};
