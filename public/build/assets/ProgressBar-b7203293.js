import{l as d,o as a,d as l,a as t,t as s,E as u,n as o,f as i}from"./app-f0078ddb.js";const x={key:0,class:"grid grid-cols-2"},g={class:"text-left self-center"},m={class:"text-gray-500 font-bold text-base mx-auto"},h={class:"text-right self-center"},f={class:"text-gray-500 font-bold text-base mx-auto"},y=["aria-valuenow"],v={key:0,class:"absolute w-full self-center"},b={class:"text-center ml-2 text-gray-500 font-bold mx-auto"},w={key:0,class:"text-center self-center mr-4"},p={class:"text-gray-500 font-bold text-sm mx-auto"},k=["aria-valuenow"],T={key:0,class:"absolute w-full self-center"},S={class:"text-center ml-2 text-gray-500 font-bold mx-auto"},z={key:1,class:"text-center self-center ml-4"},B={class:"text-gray-500 font-bold text-sm mx-auto"},C={__name:"ProgressBar",props:{progress:{type:Number,default:0},maximum:{type:Number,default:100},color:{type:String,default:"green"},height:{type:String,default:"h-6"},displayProgress:{type:Boolean,default:!0},preText:{type:String,required:!1},postText:{type:String,required:!1},size:{type:String,default:"lg"}},setup(e){const r=e,n=d(()=>({green:"bg-emerald-400",indigo:"bg-indigo-500",pink:"bg-pink-500",purple:"bg-purple-500",blue:"bg-blue-500",amber:"bg-amber-500",rose:"bg-rose-400"})[r.color]),c=d(()=>r.progress/r.maximum*100);return(N,P)=>e.size==="sm"?(a(),l("div",x,[t("div",g,[t("h5",m,s(e.preText),1)]),t("div",h,[t("h5",f,s(e.postText),1)]),t("div",{class:o(["mt-4 col-span-2 flex overflow-hidden rounded-xl bg-gray-200 text-xs grow self-center relative",[r.height]])},[t("div",{style:u([{width:`${c.value}%`},{transition:"width 0.25s ease"}]),class:o(["flex flex-col justify-center whitespace-nowrap rounded-xl text-center text-white shadow-none",n.value]),"aria-valuenow":e.progress,"aria-valuemin":"0","aria-valuemax":"100"},null,14,y),e.displayProgress?(a(),l("div",v,[t("h5",b,s(e.progress)+" / "+s(e.maximum),1)])):i("",!0)],2)])):(a(),l("div",{key:1,class:o(["pt-1",{"flex flex-row justify-center":e.preText||e.postText}])},[e.preText&&e.size!="sm"?(a(),l("div",w,[t("h5",p,s(e.preText),1)])):i("",!0),t("div",{class:o(["flex overflow-hidden rounded-xl bg-gray-200 text-xs grow self-center relative",[r.height]])},[t("div",{style:u([{width:`${c.value}%`},{transition:"width 0.25s ease"}]),class:o(["flex flex-col justify-center whitespace-nowrap rounded-xl text-center text-white shadow-none",n.value]),"aria-valuenow":e.progress,"aria-valuemin":"0","aria-valuemax":"100"},null,14,k),e.displayProgress?(a(),l("div",T,[t("h5",S,s(e.progress)+" / "+s(e.maximum),1)])):i("",!0)],2),e.postText&&e.size!="sm"?(a(),l("div",z,[t("h5",B,s(e.postText),1)])):i("",!0)],2))}};export{C as _};
