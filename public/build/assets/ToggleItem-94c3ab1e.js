import{d as G,p as O}from"./form-cb36670c.js";import{A as $,i as R,o as _,E as H,T as z}from"./render-c34c346a.js";import{s as M}from"./use-resolve-button-type-24d8b5c5.js";import{f as U,u as q,o as C}from"./keyboard-982fc047.js";import{k as J,K as Q}from"./description-cd3ec634.js";import{E as W,K as X}from"./label-6c8c1cbc.js";import{H as E,e as y,l as c,I as Y,K as Z,A as ee,p as D,L as V,F as te,o as ae,c as le,w as m,b as g,a as h,n as v,u as b,t as B,g as re}from"./app-f0078ddb.js";let K=Symbol("GroupContext"),oe=E({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(t,{slots:u,attrs:l}){let r=y(null),o=W({name:"SwitchLabel",props:{htmlFor:c(()=>{var a;return(a=r.value)==null?void 0:a.id}),onClick(a){r.value&&(a.currentTarget.tagName==="LABEL"&&a.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),s=J({name:"SwitchDescription"});return Y(K,{switchRef:r,labelledby:o,describedby:s}),()=>$({theirProps:t,ourProps:{},slot:{},slots:u,attrs:l,name:"SwitchGroup"})}}),ne=E({name:"Switch",emits:{"update:modelValue":t=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(t,{emit:u,attrs:l,slots:r,expose:o}){var s;let a=(s=t.id)!=null?s:`headlessui-switch-${R()}`,n=Z(K,null),[d,x]=G(c(()=>t.modelValue),e=>u("update:modelValue",e),c(()=>t.defaultChecked));function k(){x(!d.value)}let L=y(null),p=n===null?L:n.switchRef,T=M(c(()=>({as:t.as,type:l.type})),p);o({el:p,$el:p});function I(e){e.preventDefault(),k()}function N(e){e.key===C.Space?(e.preventDefault(),k()):e.key===C.Enter&&O(e.currentTarget)}function A(e){e.preventDefault()}let f=c(()=>{var e,i;return(i=(e=_(p))==null?void 0:e.closest)==null?void 0:i.call(e,"form")});return ee(()=>{D([f],()=>{if(!f.value||t.defaultChecked===void 0)return;function e(){x(t.defaultChecked)}return f.value.addEventListener("reset",e),()=>{var i;(i=f.value)==null||i.removeEventListener("reset",e)}},{immediate:!0})}),()=>{let{name:e,value:i,form:P,tabIndex:S,...w}=t,j={checked:d.value},F={id:a,ref:p,role:"switch",type:T.value,tabIndex:S===-1?0:S,"aria-checked":d.value,"aria-labelledby":n==null?void 0:n.labelledby.value,"aria-describedby":n==null?void 0:n.describedby.value,onClick:I,onKeyup:N,onKeypress:A};return V(te,[e!=null&&d.value!=null?V(U,H({features:q.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:d.value,form:P,disabled:w.disabled,name:e,value:i})):null,$({ourProps:F,theirProps:{...l,...z(w,["modelValue","defaultChecked"])},slot:j,attrs:l,slots:r,name:"Switch"})])}}}),se=X,ie=Q;const be={__name:"ToggleItem",props:{label:String,description:String,enabled:Boolean,textColor:String,order:{type:String,default:"right"}},emits:["update:toggle"],setup(t,{emit:u}){const l=t,r=u,o=y(l.enabled);return D(o,s=>{r("update:toggle",s)}),(s,a)=>(ae(),le(b(oe),{as:"div",class:"flex items-center"},{default:m(()=>[g(b(ne),{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=n=>o.value=n),class:v([o.value?"bg-blue-600":"bg-gray-200","relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-blue-600 focus:ring-offset-2",,t.order=="left"?"order-2":"order-1"])},{default:m(()=>[h("span",{"aria-hidden":"true",class:v([o.value?"translate-x-5":"translate-x-0","pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out"])},null,2)]),_:1},8,["modelValue","class"]),h("span",{class:v(["flex grow flex-col",[t.order=="left"?"order-1 mr-3":"order-2 ml-3 "]])},[g(b(se),{as:"span"},{default:m(()=>[h("span",{class:v(["text-sm font-semibold",[t.textColor=="iris"?"bg-linear-to-r from-blue-600 via-green-600 to-pink-600 bg-clip-text text-transparent":"text-gray-900"]])},B(l.label),3)]),_:1}),g(b(ie),{as:"span",class:"text-xs text-gray-500"},{default:m(()=>[re(B(l.description),1)]),_:1})],2)]),_:1}))}};export{be as _};
