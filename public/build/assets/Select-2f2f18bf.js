import{_}from"./ButtonItem-718c0517.js";import{e as u,l as b,o as i,d as o,a as e,r as d,F as v,h as w,t as k,f as S,b as C,w as $,n as N,g as T}from"./app-f0078ddb.js";/* empty css            */const V={class:"flex-1"},z={class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900"},B={class:"text-center text-lg font-medium text-gray-600"},H={class:"relative my-20 grid w-full grid-cols-1 justify-items-center sm:px-16"},L={class:"text-4xl font-semibold text-gray-900"},j=["onClick","innerHTML"],q={key:0,class:"mt-2 text-2xl font-semibold text-gray-500"},A={class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8"},E={__name:"Select",props:{options:Object,stem:String,instructions:String,questionKey:String},emits:["submit"],setup(r,{emit:g}){const a=r;let c=u(""),m=u("");const f=g;function h(){f("submit",{answer:c.value,key:a.questionKey,isArray:!1})}const p=b(()=>a.options.latin.match(/\w+|[^\s\w]+/g));function y(s,t){c.value=s,m.value=s+"["+t+"]"}function x(s,t){return m.value==s+"["+t+"]"}return(s,t)=>(i(),o("div",V,[e("h1",z,[d(s.$slots,"stem")]),e("h3",B,[d(s.$slots,"instructions")]),e("div",H,[e("h2",L,[(i(!0),o(v,null,w(p.value,(n,l)=>(i(),o("span",{key:l,class:"inline-block cursor-pointer"},[e("span",{class:N({"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100 transition duration-250":x(n,l)}),onClick:F=>y(n,l),innerHTML:n},null,10,j),t[1]||(t[1]=e("span",null," ",-1))]))),128))]),r.options.english?(i(),o("h4",q,k(r.options.english),1)):S("",!0)]),e("div",A,[C(_,{size:"md",class:"w-full",color:"pink",onClick:t[0]||(t[0]=n=>h())},{default:$(()=>t[2]||(t[2]=[T("Submit ")])),_:1})])]))}};export{E as default};
