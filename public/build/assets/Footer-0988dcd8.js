import{_ as f}from"./ButtonItem-718c0517.js";import{e as w,C as h,k as g,o as n,c as p,w as r,b as i,u as s,a as t,d as u,s as T,g as c,q as S,x as $,t as b,f as _,y as C,j as y,Q as V}from"./app-f0078ddb.js";import{Y as z,G as B,V as G}from"./dialog-86f7bd91.js";import{r as N}from"./CheckCircleIcon-d86d1232.js";import{h as v,S as Y}from"./transition-a0923044.js";const j={class:"fixed inset-0 z-10 w-screen overflow-y-auto"},D={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},L={key:0},M={class:"mt-3"},q={class:"mt-4"},A={key:0,class:"mt-2 text-xs text-red-500"},F={class:"mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3"},I={key:1},E={class:"flex flex-col items-center"},O={__name:"GetInTouch",props:{toggle:{type:Boolean,required:!0,default:!1}},emits:["close:modal"],setup(x,{emit:a}){let d=w(!1);const o=a,l=h({user_id:g().props.user?g().props.user.id:null,location:window.location.href,message:null}),k=()=>{d.value=!0,setTimeout(()=>{o("close:modal"),l.message=null,setTimeout(()=>{d.value=!1},300)},2e3)};return(W,e)=>(n(),p(s(Y),{as:"template",show:x.toggle},{default:r(()=>[i(s(z),{as:"div",class:"relative z-10",onClose:e[3]||(e[3]=m=>o("close:modal"))},{default:r(()=>[i(s(v),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:r(()=>e[4]||(e[4]=[t("div",{class:"fixed inset-0 bg-gray-500 opacity-75 transition-opacity"},null,-1)])),_:1}),t("div",j,[t("div",D,[i(s(v),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:r(()=>[i(s(B),{class:"relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all duration-300 sm:my-8 sm:w-full sm:max-w-lg sm:p-6"},{default:r(()=>[s(d)?(n(),u("div",I,[t("div",E,[i(s(N),{class:"h-24 w-24 text-emerald-500"}),e[11]||(e[11]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"}," Thanks for your feedback! ",-1)),e[12]||(e[12]=t("div",{class:"mt-2 text-sm text-gray-500"},[t("p",null," We'll take a look and get back to you as soon as possible. ")],-1))])])):(n(),u("div",L,[t("form",{onSubmit:e[2]||(e[2]=T(m=>{s(l).post("/submit-feedback",{preserveScroll:!0,onSuccess:()=>{k()}})},["prevent"]))},[e[10]||(e[10]=t("img",{class:"mx-auto h-10 w-auto",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/img/latintutorial-colosseum.svg",alt:"LatinTutorial"},null,-1)),t("div",M,[i(s(G),{as:"h3",class:"text-center text-base leading-6 font-semibold text-gray-900"},{default:r(()=>e[5]||(e[5]=[c("Get in touch with LatinTutorial")])),_:1}),t("div",q,[e[6]||(e[6]=t("label",{for:"comment",class:"sr-only"},"Comment",-1)),t("div",null,[S(t("textarea",{"onUpdate:modelValue":e[0]||(e[0]=m=>s(l).message=m),rows:"5",name:"message",id:"message",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 focus:ring-inset sm:text-sm sm:leading-6",placeholder:"Add your message..."},null,512),[[$,s(l).message]]),s(l).errors.message?(n(),u("div",A,b(s(l).errors.message),1)):_("",!0)])]),e[7]||(e[7]=t("div",{class:"mt-2"},[t("p",{class:"text-xs text-gray-500"}," Your user information, including name and email, will be sent with your message. ")],-1))]),t("div",F,[i(f,{size:"xs",color:"white",onClick:e[1]||(e[1]=m=>o("close:modal"))},{default:r(()=>e[8]||(e[8]=[c(" Cancel ")])),_:1}),i(f,{size:"xs",color:"indigo",type:"submit",disabled:s(l).processing},{default:r(()=>e[9]||(e[9]=[c(" Send ")])),_:1},8,["disabled"])])],32)]))]),_:1})]),_:1})])])]),_:1})]),_:1},8,["show"]))}},P={class:"mx-8 mt-16 border-t border-gray-900/10 pt-8 sm:mt-20 items-center grid grid-cols-2 sm:grid-cols-4 gap-8"},Q={class:"order-3 sm:order-1 col-span-2 text-left"},R={class:"text-xs leading-5 text-gray-500"},U={class:"order-2 md:order-3 justify-end flex"},ee={__name:"Footer",setup(x){let a=w(!1);return(d,o)=>(n(),u("div",P,[t("div",Q,[t("p",R," © 2023-"+b(new Date().getFullYear())+" LatinTutorial, Inc. All rights reserved. ",1)]),o[3]||(o[3]=C('<div class="order-1 md:order-2"><p class="text-xs leading-5 text-gray-500 text-left sm:text-center"><a href="/terms-of-service" class="hover:text-gray-900"> Terms </a><span class="mx-3">•</span><a href="/privacy-policy" class="hover:text-gray-900"> Privacy </a></p></div>',1)),t("div",U,[s(g)().props.authenticated?(n(),p(f,{key:0,color:"indigoOutline",size:"xs",class:"flex items-center space-x-2",onClick:o[0]||(o[0]=l=>y(a)?a.value=!0:a=!0)},{default:r(()=>o[2]||(o[2]=[c("Get in Touch")])),_:1})):_("",!0)]),(n(),p(V,{to:"body"},[i(O,{toggle:s(a),"onClose:modal":o[1]||(o[1]=l=>y(a)?a.value=!1:a=!1)},null,8,["toggle"])]))]))}};export{ee as _};
