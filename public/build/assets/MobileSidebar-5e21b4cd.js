import{r as p}from"./XMarkIcon-9bc7c0bd.js";import{o as c,c as m,w as o,a as t,b as a,u as s,r as x}from"./app-f0078ddb.js";import{h as r,S as v}from"./transition-a0923044.js";const y={class:"fixed inset-0 z-40 flex"},h={class:"relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl"},w={class:"flex items-center justify-end px-4"},b={class:"px-4"},B={__name:"MobileSidebar",props:{open:Boolean},emits:["close"],setup(l,{emit:i}){const d=l,n=i;return(u,e)=>(c(),m(s(v),{as:"template",show:d.open},{default:o(()=>[t("div",{class:"relative z-40 lg:hidden",onClose:e[1]||(e[1]=f=>n("close"))},[a(s(r),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:o(()=>e[2]||(e[2]=[t("div",{class:"fixed inset-0 bg-black opacity-25"},null,-1)])),_:1}),t("div",y,[a(s(r),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:o(()=>[t("div",h,[t("div",w,[t("button",{type:"button",class:"-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-slate-100 focus:ring-2 focus:ring-indigo-500 focus:outline-hidden",onClick:e[0]||(e[0]=f=>n("close"))},[e[3]||(e[3]=t("span",{class:"sr-only"},"Close menu",-1)),a(s(p),{class:"h-6 w-6","aria-hidden":"true"})])]),t("div",b,[x(u.$slots,"default")])])]),_:3})])],32)]),_:3},8,["show"]))}};export{B as _};
