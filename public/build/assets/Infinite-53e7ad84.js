import{V as Q,e as n,k as $,l as K,i as pe,o as a,c as w,w as c,b as m,a as s,j as se,u as e,t as u,d as l,h as P,n as D,F as M,f as C,T as U,g as x,W as oe,a3 as re}from"./app-f0078ddb.js";import{_ as fe}from"./AppLayout-33f062bc.js";import{_ as ve}from"./Breadcrumbs-c96e9207.js";import{_ as ae}from"./ButtonItem-718c0517.js";import{_ as le}from"./ProgressBar-b7203293.js";import ge from"./Drag-9e816807.js";import xe from"./MultipleChoice-080711a4.js";import ye from"./Type-dfa282d2.js";import we from"./Matching-1875e265.js";import{r as ie}from"./replaceMacra-3b9666ed.js";import{_ as he}from"./Footer-0988dcd8.js";import{_ as be}from"./MobileSidebar-5e21b4cd.js";import{T as _e}from"./easytimer-3d932146.js";import{g as ne}from"./index-794e919d.js";import{I as ke}from"./InfinitasIcon-1a3ae135.js";import{r as $e}from"./InformationCircleIcon-716f3ffb.js";import{r as ue,a as de}from"./PlayIcon-8c672cba.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./vuedraggable.umd-aab17b5c.js";import"./NextButtonSmall-9e6ffefc.js";import"./XCircleIcon-63af2b2a.js";import"./lodash-631955d9.js";const Ce={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Te={class:"px-4 py-8 sm:px-8"},Ie={class:"flex flex-row items-center justify-between"},Ve={class:"mx-auto mt-12 w-full"},Se={class:"flex flex-row items-center justify-center text-center"},qe={class:"mt-12"},ze={class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},Le={class:"mt-2 mb-8"},je={class:"text-center text-xs font-bold text-gray-500 uppercase"},Ae={class:"mb-8"},Ee={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},Ne={key:0,class:"mb-8 text-left"},Pe={key:0,class:"mt-2 flex flex-row items-center"},De={class:"w-full"},Me={key:0,class:"mx-auto"},Be={key:1,class:"mx-auto"},Fe={key:0},Qe={key:1},Ke={class:"mt-8 grid grid-cols-1 gap-8"},Ue={"aria-labelledby":"userStats"},We={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},He={class:"text-sm leading-6 font-medium text-gray-500"},Xe={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},Oe={key:0},Re={key:0,class:"ml-1 text-sm font-medium text-gray-500"},Ge={key:1},Je={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Ye={class:"mt-2"},Ze={class:"text-center text-xs font-bold text-gray-500 uppercase"},et={class:"mt-8 grid grid-cols-1 gap-8"},tt={key:0,class:"text-left"},st={key:0,class:"mt-2 flex flex-row items-center"},ot={"aria-labelledby":"userStats"},rt={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},at={class:"text-sm leading-6 font-medium text-gray-500"},lt={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},it={key:0},nt={key:0,class:"ml-1 text-sm font-medium text-gray-500"},ut={key:1},dt={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},ct={class:"mt-8 w-full"},mt={key:0,class:"mx-auto"},pt={key:1,class:"mx-auto"},us={__name:"Infinite",props:{initialData:{type:Object,required:!1},question:{type:Object,required:!1}},setup(S){const L=S,ce=[{name:"Practice",href:"/practice",current:!1},{name:"Infinitas",href:"#",current:!0}];let W=Q({words:"all",time:0,difficulty:0,toggleLearned:!1,toggleNames:!1}),B=n($().props.user.xp),j=n($().props.user.xp),T=n($().props.user.level),I=n(!1),d=Q({correct:0,incorrect:0}),p=n(!1),f=n(),h=Q([]),r=n(L.initialData.question),H=n(L.question),me=n([]),X=n(L.initialData.session),b=n(!0),q=n(0),y=n(!1);function z(){r.value=H.value,k.value=!1,oe.reload({only:["question"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{H.value=L.question,k.value=!0}}),q.value++,I.value=!1}let V=n(0),_=new _e,g=n(),k=n(!0),O=n(0);const A=i=>{if(b.value=!0,!p.value){if(ie(i.answer).toUpperCase()==ie(r.value.key).toUpperCase()){f.value=!0;let v={value:d.correct};V.value<1?V.value=1:V.value++,ne.to(v,{value:d.correct+1,duration:.3,onUpdate:()=>{d.correct=Math.round(v.value)}});let o={value:B.value};ne.to(o,{value:B.value+F(),duration:.3,onUpdate:()=>{B.value=Math.round(o.value)}}),O.value+=F()}else f.value=!1,V.value=0,d.incorrect++;if(h.find(v=>v.id===r.value.id)){var t=h.findIndex(v=>v.id===r.value.id);h[t].correct=h[t].correct+f.value,h[t].total=h[t].total+1}else h.push({id:r.value.id,word:r.value.word,pos:r.value.pos,definition:r.value.short_definition,core:r.value.core,correct:f.value?1:0,gender:r.value.gender,total:1,lexicon_id:r.value.lexicon_id});I.value=!0,f.value?b.value=!1:setTimeout(()=>{b.value=!1},2e3),re.post("/api/infinitas/add-attempt",{session:X.value,token_id:r.value.id,word_id:r.value.word_id,type:r.value.type,correct:f.value,xp:F(),streak:V.value,time:_.getTotalTimeValues().secondTenths}).then(v=>{j.value=v.data.xp,T.value=v.data.level})}},E=()=>{if(p.value)return;k.value=!1,me.value.push(r.value.word_id);let i=r.value.word_id;b.value=!1,z(),re.post("/api/practice/vocabulary/add-known-word",{word_id:i})},R=K(()=>{let i=T.value.max-T.value.min+1;return(j.value-T.value.min)/i*100}),F=()=>{if(f.value)switch(r.value.type){case"drag":switch(W.difficulty){case 1:return 3;case 2:return 4;default:return 2}case"type":switch(W.difficulty){case 1:return 4;case 2:return 5;default:return 3}default:return 1}return 0},G=()=>{p.value=!0,oe.post("/api/practice/vocabulary/finish",{session:X.value})},J=K(()=>d.correct+d.incorrect==0?-1:d.correct+d.incorrect<10?d.correct:Math.round(10*d.correct/(d.correct+d.incorrect))),Y=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],Z=K(()=>[{name:"Correct",value:d.correct,unit:"out of "+(d.correct+d.incorrect)},{name:"Current Streak",value:V.value,unit:"in a row"},{name:"XP Earned",value:O.value},{name:"Words Seen",value:h.length}]),ee=i=>i.days>1?"more than "+i.days+" days":i.days>0?"more than "+i.days+" day":i.hours>1?"more than "+i.hours+" hours":i.hours>0?"more than "+i.hours+" hour":i.seconds<=9?i.minutes+":0"+i.seconds:i.minutes+":"+i.seconds;_.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),g.value=ee(_.getTimeValues()),_.addEventListener("secondsUpdated",function(){g.value=ee(_.getTimeValues())});let N=n(!1);const te=()=>{_.isRunning()?(_.pause(),N.value=!0):(_.start(),N.value=!1)};return(i,t)=>{const v=pe("Head");return a(),w(fe,null,{default:c(()=>[m(v,null,{default:c(()=>t[18]||(t[18]=[s("title",null,"Infinitas",-1)])),_:1}),s("main",Ce,[s("div",Te,[s("div",Ie,[m(ve,{class:"lg:col-span-9 xl:grid-cols-10",pages:ce}),m(e($e),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=o=>se(y)?y.value=!e(y):y=!e(y))})]),s("section",Ve,[s("div",Se,[m(ke,{class:"inline"}),t[19]||(t[19]=s("span",{class:"ml-4 inline text-4xl font-semibold text-gray-900"},"Infinitas",-1))])]),s("div",qe,[s("div",ze,[m(le,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(T).level,"post-text":e(j)+" XP",progress:R.value},null,8,["pre-text","post-text","progress"]),s("div",Le,[s("p",je,u(e($)().props.user.level.max-e($)().props.user.xp+1)+" xp to the next level ",1)]),s("section",Ae,[t[20]||(t[20]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),s("div",Ee,[(a(),l(M,null,P(Y,o=>s("div",{class:D(["h-4 w-full rounded-sm shadow-sm",J.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),e(g)?(a(),l("section",Ne,[t[21]||(t[21]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Time Elapsed ",-1)),e(g)?(a(),l("div",Pe,[s("div",{class:D(["grow text-left font-medium text-gray-900",e(g).length>10?"text-2xl":"text-4xl"])},u(e(g)),3),s("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[1]||(t[1]=o=>te())},[e(N)?(a(),w(e(ue),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(a(),w(e(de),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):C("",!0)])):C("",!0),s("div",De,[m(ae,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(p),onClick:t[2]||(t[2]=o=>G())},{default:c(()=>[m(U,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(p)?(a(),l("span",Me,t[22]||(t[22]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Finishing ...",-1)]))):(a(),l("span",Be,"Finish"))]),_:1})]),_:1},8,["disabled"])])]),m(U,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:c(()=>[e(r).type==="mc"&&S.question?(a(),w(xe,{key:`${e(q)}-mc`,options:e(r).options,"question-key":e(r).key,"is-correct":e(f),"is-answer":e(I),attempts:e(d).correct+e(d).incorrect,disabled:e(b)||e(p)||!e(k),"is-vocabulary":!0,onSubmit:t[3]||(t[3]=o=>A(o)),onIKnowThis:t[4]||(t[4]=o=>E()),onNextQuestion:t[5]||(t[5]=o=>z())},{stem:c(()=>[x(u(e(r).stem),1)]),instructions:c(()=>[x(u(e(r).instructions),1)]),_:1},8,["options","question-key","is-correct","is-answer","attempts","disabled"])):e(r).type==="match"&&S.question?(a(),w(we,{key:`${e(q)}-match`,options:e(r).options,"is-answer":e(I),"is-correct":e(f),"user-attempt":i.attemptValue,disabled:e(b)||e(p)||!e(k),"is-vocabulary":!0,onSubmit:t[6]||(t[6]=o=>A(o)),onIKnowThis:t[7]||(t[7]=o=>E()),onNextQuestion:t[8]||(t[8]=o=>z())},{instructions:c(()=>[x(u(e(r).instructions),1)]),_:1},8,["options","is-answer","is-correct","user-attempt","disabled"])):e(r).type==="type"&&S.question?(a(),w(ye,{key:`${e(q)}-type`,options:e(r).key,"is-answer":e(I),"is-correct":e(f),difficulty:e(r).difficulty,disabled:e(b)||e(p)||!e(k),"is-vocabulary":!0,onSubmit:t[9]||(t[9]=o=>A(o)),onIKnowThis:t[10]||(t[10]=o=>E()),onNextQuestion:t[11]||(t[11]=o=>z())},{stem:c(()=>[x(u(e(r).stem),1)]),instructions:c(()=>[x(u(e(r).instructions),1)]),_:1},8,["options","is-answer","is-correct","difficulty","disabled"])):e(r).type==="drag"&&S.question?(a(),w(ge,{key:`${e(q)}-drag`,options:e(r).options,"question-key":e(r).key,syllabalized:e(r).syllabized,stem:Array.isArray(e(r).stem)?e(r).stem[1]:"","is-answer":e(I),"is-correct":e(f),difficulty:e(r).difficulty,disabled:e(b)||e(p)||!e(k),"is-vocabulary":!0,onSubmit:t[12]||(t[12]=o=>A(o)),onIKnowThis:t[13]||(t[13]=o=>E()),onNextQuestion:t[14]||(t[14]=o=>z())},{stem:c(()=>[Array.isArray(e(r).stem)?(a(),l("span",Fe,u(e(r).stem[0]),1)):(a(),l("span",Qe,u(e(r).stem),1))]),instructions:c(()=>[x(u(e(r).instructions),1)]),_:1},8,["options","question-key","syllabalized","stem","is-answer","is-correct","difficulty","disabled"])):C("",!0)]),_:1})])]),m(he)]),m(be,{class:"lg:hidden",show:e(y),onClose:t[15]||(t[15]=o=>se(y)?y.value=!1:y=!1)},{default:c(()=>[s("div",Ke,[t[23]||(t[23]=s("section",null,[s("div",{class:"mx-2 flex flex-row items-center text-sm font-medium text-gray-800"},[s("p",null,[s("span",{class:"font-bold uppercase"},"Infinitas"),x(" provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")])])],-1)),s("section",Ue,[s("div",null,[s("dl",We,[(a(!0),l(M,null,P(Z.value,o=>(a(),l("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[s("dt",He,u(o.name),1),s("dd",Xe,[o.value?(a(),l("span",Oe,[x(u(o.value)+" ",1),o.unit?(a(),l("span",Re,u(o.unit),1)):C("",!0)])):(a(),l("span",Ge,"–"))])]))),128))])])])])]),_:1},8,["show"]),s("aside",Je,[m(le,{class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+e(T).level,"post-text":e(j)+" XP",progress:R.value},null,8,["pre-text","post-text","progress"]),s("div",Ye,[s("p",Ze,u(e($)().props.user.level.max-e($)().props.user.xp+1)+" xp to the next level ",1)]),s("div",et,[t[27]||(t[27]=s("section",null,[s("div",{class:"flex flex-row items-center text-sm font-medium text-gray-800"},[s("p",null,[s("span",{class:"font-bold uppercase"},"Infinitas"),x(" provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")])])],-1)),e(g)?(a(),l("section",tt,[t[24]||(t[24]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Time Elapsed ",-1)),e(g)?(a(),l("div",st,[s("div",{class:D(["grow text-left font-medium text-gray-900",e(g).length>10?"text-2xl":"text-4xl"])},u(e(g)),3),s("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:t[16]||(t[16]=o=>te())},[e(N)?(a(),w(e(ue),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(a(),w(e(de),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):C("",!0)])):C("",!0),s("section",ot,[s("div",null,[s("dl",rt,[(a(!0),l(M,null,P(Z.value,o=>(a(),l("div",{key:o.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[s("dt",at,u(o.name),1),s("dd",lt,[o.value?(a(),l("span",it,[x(u(o.value)+" ",1),o.unit?(a(),l("span",nt,u(o.unit),1)):C("",!0)])):(a(),l("span",ut,"–"))])]))),128))])])]),s("section",null,[t[25]||(t[25]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Performance",-1)),s("div",dt,[(a(),l(M,null,P(Y,o=>s("div",{class:D(["h-4 w-full rounded-sm shadow-sm",J.value>=o.value?o.color:"bg-gray-300"]),key:o.value},null,2)),64))])]),s("div",ct,[m(ae,{class:"inline-flex w-full disabled:bg-indigo-500",size:"lg",color:"indigo",disabled:e(p),onClick:t[17]||(t[17]=o=>G())},{default:c(()=>[m(U,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:c(()=>[e(p)?(a(),l("span",mt,t[26]||(t[26]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Finishing ...",-1)]))):(a(),l("span",pt,"Finish"))]),_:1})]),_:1},8,["disabled"])])])])]),_:1})}}};export{us as default};
