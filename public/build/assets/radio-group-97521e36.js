import{d as _,e as M,p as U}from"./form-cb36670c.js";import{i as L,o as O,E as W,A as N,T as J}from"./render-c34c346a.js";import{i as q}from"./use-tree-walker-100527b8.js";import{f as z,u as Q,o as R}from"./keyboard-982fc047.js";import{O as X,i as w,P,N as E,T as V}from"./focus-management-8406d052.js";import{k as B,K as Y}from"./description-cd3ec634.js";import{E as C,K as Z}from"./label-6c8c1cbc.js";import{H as x,e as $,l as n,a0 as f,I as ee,A as I,p as ae,L as F,F as le,B as re,K as te}from"./app-f0078ddb.js";function oe(a,v){return a===v}let K=Symbol("RadioGroupContext");function j(a){let v=te(K,null);if(v===null){let b=new Error(`<${a} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(b,j),b}return v}let fe=x({name:"RadioGroup",emits:{"update:modelValue":a=>!0},props:{as:{type:[Object,String],default:"div"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],default:()=>oe},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},id:{type:String,default:null}},inheritAttrs:!1,setup(a,{emit:v,attrs:b,slots:k,expose:h}){var g;let u=(g=a.id)!=null?g:`headlessui-radiogroup-${L()}`,d=$(null),o=$([]),y=C({name:"RadioGroupLabel"}),S=B({name:"RadioGroupDescription"});h({el:d,$el:d});let[p,A]=_(n(()=>a.modelValue),e=>v("update:modelValue",e),n(()=>a.defaultValue)),s={options:o,value:p,disabled:n(()=>a.disabled),firstOption:n(()=>o.value.find(e=>!e.propsRef.disabled)),containsCheckedOption:n(()=>o.value.some(e=>s.compare(f(e.propsRef.value),f(a.modelValue)))),compare(e,r){if(typeof a.by=="string"){let l=a.by;return(e==null?void 0:e[l])===(r==null?void 0:r[l])}return a.by(e,r)},change(e){var r;if(a.disabled||s.compare(f(p.value),f(e)))return!1;let l=(r=o.value.find(i=>s.compare(f(i.propsRef.value),f(e))))==null?void 0:r.propsRef;return l!=null&&l.disabled?!1:(A(e),!0)},registerOption(e){o.value.push(e),o.value=X(o.value,r=>r.element)},unregisterOption(e){let r=o.value.findIndex(l=>l.id===e);r!==-1&&o.value.splice(r,1)}};ee(K,s),q({container:n(()=>O(d)),accept(e){return e.getAttribute("role")==="radio"?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});function c(e){if(!d.value||!d.value.contains(e.target))return;let r=o.value.filter(l=>l.propsRef.disabled===!1).map(l=>l.element);switch(e.key){case R.Enter:U(e.currentTarget);break;case R.ArrowLeft:case R.ArrowUp:if(e.preventDefault(),e.stopPropagation(),P(r,E.Previous|E.WrapAround)===V.Success){let l=o.value.find(i=>{var t;return i.element===((t=w(d))==null?void 0:t.activeElement)});l&&s.change(l.propsRef.value)}break;case R.ArrowRight:case R.ArrowDown:if(e.preventDefault(),e.stopPropagation(),P(r,E.Next|E.WrapAround)===V.Success){let l=o.value.find(i=>{var t;return i.element===((t=w(i.element))==null?void 0:t.activeElement)});l&&s.change(l.propsRef.value)}break;case R.Space:{e.preventDefault(),e.stopPropagation();let l=o.value.find(i=>{var t;return i.element===((t=w(i.element))==null?void 0:t.activeElement)});l&&s.change(l.propsRef.value)}break}}let m=n(()=>{var e;return(e=O(d))==null?void 0:e.closest("form")});return I(()=>{ae([m],()=>{if(!m.value||a.defaultValue===void 0)return;function e(){s.change(a.defaultValue)}return m.value.addEventListener("reset",e),()=>{var r;(r=m.value)==null||r.removeEventListener("reset",e)}},{immediate:!0})}),()=>{let{disabled:e,name:r,form:l,...i}=a,t={ref:d,id:u,role:"radiogroup","aria-labelledby":y.value,"aria-describedby":S.value,onKeydown:c};return F(le,[...r!=null&&p.value!=null?M({[r]:p.value}).map(([G,T])=>F(z,W({features:Q.Hidden,key:G,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:l,disabled:e,name:G,value:T}))):[],N({ourProps:t,theirProps:{...b,...J(i,["modelValue","defaultValue","by"])},slot:{},attrs:b,slots:k,name:"RadioGroup"})])}}});var ie=(a=>(a[a.Empty=1]="Empty",a[a.Active=2]="Active",a))(ie||{});let be=x({name:"RadioGroupOption",props:{as:{type:[Object,String],default:"div"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(a,{attrs:v,slots:b,expose:k}){var h;let g=(h=a.id)!=null?h:`headlessui-radiogroup-option-${L()}`,u=j("RadioGroupOption"),d=C({name:"RadioGroupLabel"}),o=B({name:"RadioGroupDescription"}),y=$(null),S=n(()=>({value:a.value,disabled:a.disabled})),p=$(1);k({el:y,$el:y});let A=n(()=>O(y));I(()=>u.registerOption({id:g,element:A,propsRef:S})),re(()=>u.unregisterOption(g));let s=n(()=>{var t;return((t=u.firstOption.value)==null?void 0:t.id)===g}),c=n(()=>u.disabled.value||a.disabled),m=n(()=>u.compare(f(u.value.value),f(a.value))),e=n(()=>c.value?-1:m.value||!u.containsCheckedOption.value&&s.value?0:-1);function r(){var t;u.change(a.value)&&(p.value|=2,(t=O(y))==null||t.focus())}function l(){p.value|=2}function i(){p.value&=-3}return()=>{let{value:t,disabled:G,...T}=a,D={checked:m.value,disabled:c.value,active:!!(p.value&2)},H={id:g,ref:y,role:"radio","aria-checked":m.value?"true":"false","aria-labelledby":d.value,"aria-describedby":o.value,"aria-disabled":c.value?!0:void 0,tabIndex:e.value,onClick:c.value?void 0:r,onFocus:c.value?void 0:l,onBlur:c.value?void 0:i};return N({ourProps:H,theirProps:T,slot:D,attrs:v,slots:b,name:"RadioGroupOption"})}}}),ge=Z,ye=Y;export{ye as E,be as O,fe as h,ge as k};
