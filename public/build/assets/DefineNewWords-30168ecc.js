import{_ as x}from"./Breadcrumbs-c96e9207.js";import{_ as w}from"./AppLayout-33f062bc.js";import{i as l,o as y,d as h,b as n,w as u,F as _,a as t,t as p,s as v,q as r,x as i}from"./app-f0078ddb.js";import{_ as k}from"./_plugin-vue_export-helper-c27b6911.js";import"./ChevronRightIcon-a926c707.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */const V={components:{Breadcrumbs:x,AppLayout:w},props:{word:Object,data:Object,words_remaining:Number},setup(){return{breadcrumbs:[{name:"Developer",href:"/dev",current:!1},{name:"Define New Vocabulary",href:"#",current:!0}]}},data(){return{form:{display_word:this.data.orthography+", "+this.data.endings,gender:this.data.gender,pos:this.data.pos,url_slug:this.data.orthography+", "+this.data.endings,definition:this.data.definition,id:this.data.id}}},computed:{display_word(){return this.form.display_word}},watch:{data(){this.form.display_word=this.data.orthography+", "+this.data.endings,this.form.gender=this.data.gender,this.form.pos=this.data.pos,this.form.url_slug=this.data.orthography+", "+this.data.endings,this.form.definition=this.data.definition,this.form.id=this.data.id},display_word(){var d={Ā:"A",Ē:"E",Ī:"I",Ō:"O",Ū:"U",Ȳ:"Y",ā:"a",ē:"e",ī:"i",ō:"o",ū:"u",ȳ:"y"};this.form.url_slug=this.form.display_word.replace(/-/g,"").replace(/, /g,"-").replace(/Ā|Ē|Ī|Ō|Ū|Ȳ|ā|ē|ī|ō|ū|ȳ/gi,function(o){return d[o]}).replace(/,/g,"")}},methods:{submit(){this.$inertia.post("/fill-blank-words",this.form)}}},D={class:"p-8"},U={class:"mt-8"},B={class:"text-sm font-medium text-gray-700"},N={class:"mt-4 block text-sm font-medium leading-5 text-gray-700"},S={class:"relative mt-2 rounded-md shadow-xs"},j={class:"relative mt-2 rounded-md shadow-xs"},A={class:"relative mt-2 rounded-md shadow-xs"},O={class:"relative mt-2 rounded-md shadow-xs"},L={class:"sm:col-span-2"},W={class:"relative mt-12 rounded-md shadow-xs"};function C(d,o,a,f,s,m){const b=l("Head"),g=l("Breadcrumbs"),c=l("AppLayout");return y(),h(_,null,[n(b,null,{default:u(()=>o[7]||(o[7]=[t("title",null,"Define New Vocabulary",-1)])),_:1}),n(c,null,{default:u(()=>[t("div",D,[n(g,{class:"lg:col-span-9 xl:grid-cols-10",pages:f.breadcrumbs},null,8,["pages"]),t("div",U,[t("p",B,p(a.words_remaining)+" words remaining. ",1),t("p",N," Word ID: "+p(a.word.id),1),t("form",{class:"mt-4 grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8",onSubmit:o[6]||(o[6]=v((...e)=>m.submit&&m.submit(...e),["prevent"]))},[r(t("input",{id:"id","onUpdate:modelValue":o[0]||(o[0]=e=>s.form.id=e),type:"hidden"},null,512),[[i,s.form.id]]),t("div",null,[o[8]||(o[8]=t("label",{for:"first_name",class:"block text-sm font-bold leading-5 text-gray-700"},"Display Word",-1)),t("div",S,[r(t("input",{id:"first_name","onUpdate:modelValue":o[1]||(o[1]=e=>s.form.display_word=e),class:"form-input block w-full py-2 px-4 transition duration-150 ease-in-out rounded-md border border-gray-400 focus:border-blue-600 focus:shadow-outline-blue sm:text-sm sm:leading-5"},null,512),[[i,s.form.display_word]])])]),t("div",null,[o[9]||(o[9]=t("label",{for:"gender",class:"block text-sm font-bold leading-5 text-gray-700"},"Gender",-1)),t("div",j,[r(t("input",{id:"gender","onUpdate:modelValue":o[2]||(o[2]=e=>s.form.gender=e),class:"form-input block w-full py-2 px-4 transition duration-150 ease-in-out rounded-md border border-gray-400 focus:border-blue-600 focus:shadow-outline-blue sm:text-sm sm:leading-5"},null,512),[[i,s.form.gender]])])]),t("div",null,[o[10]||(o[10]=t("label",{for:"pos",class:"block text-sm font-bold leading-5 text-gray-700"},"Part of Speech",-1)),t("div",A,[r(t("input",{id:"pos","onUpdate:modelValue":o[3]||(o[3]=e=>s.form.pos=e),class:"form-input block w-full py-2 px-4 transition duration-150 ease-in-out rounded-md border border-gray-400 focus:border-blue-600 focus:shadow-outline-blue sm:text-sm sm:leading-5"},null,512),[[i,s.form.pos]])])]),t("div",null,[o[11]||(o[11]=t("label",{for:"pos",class:"block text-sm font-bold leading-5 text-gray-700"},"url Slug",-1)),t("div",O,[r(t("input",{id:"url","onUpdate:modelValue":o[4]||(o[4]=e=>s.form.url_slug=e),class:"form-input block w-full py-2 px-4 transition duration-150 ease-in-out rounded-md border border-gray-400 focus:border-blue-600 focus:shadow-outline-blue sm:text-sm sm:leading-5"},null,512),[[i,s.form.url_slug]])])]),t("div",L,[o[12]||(o[12]=t("label",{for:"company",class:"block text-sm font-bold leading-5 text-gray-700"},"Definition",-1)),t("div",W,[r(t("textarea",{id:"message","onUpdate:modelValue":o[5]||(o[5]=e=>s.form.definition=e),rows:"4",class:"form-textarea block w-full py-2 px-4 transition duration-150 ease-in-out rounded-md border border-gray-400 focus:border-blue-600 focus:shadow-outline-blue sm:text-sm sm:leading-5"},null,512),[[i,s.form.definition]])])]),o[13]||(o[13]=t("div",{class:"sm:col-span-2"},[t("span",{class:"inline-flex w-full rounded-md shadow-xs"},[t("button",{type:"submit",class:"focus:shadow-outline-blue inline-flex w-full items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-1 text-base font-semibold leading-6 text-white transition duration-150 ease-in-out hover:bg-blue-500 focus:border-blue-700 focus:outline-hidden active:bg-blue-700"}," Submit ")])],-1))],32)])])]),_:1})],64)}const go=k(V,[["render",C]]);export{go as default};
