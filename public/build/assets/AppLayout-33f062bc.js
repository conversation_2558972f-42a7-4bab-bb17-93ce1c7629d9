import{o as l,d as o,a as e,y as E,H as h3,e as B,l as N,I as B3,J as f3,L as K,F as H,A as C3,B as v3,K as g3,k as y,p as H3,b as h,w as x,u as n,h as O,n as S,c as T,G as e3,g as D,t as P,P as J,f as z,T as j3,q as r1,M as s1,N as l3,O as E3,i as l1,j as n1,Q as V3,r as o1,R as i1}from"./app-f0078ddb.js";import{_ as M}from"./_plugin-vue_export-helper-c27b6911.js";import{r as F3}from"./PlayCircleIcon-8bd12a30.js";import{r as z3}from"./BookOpenIcon-1746d343.js";import{r as O3}from"./RectangleGroupIcon-04390470.js";import{r as I3}from"./QueueListIcon-824b634b.js";import{q as c1,N as d1,E as u1,n as N3,d as G,Y as D3,G as W3}from"./dialog-86f7bd91.js";import{h as t3,S as R3}from"./transition-a0923044.js";import{r as p1}from"./XMarkIcon-9bc7c0bd.js";import{S as h1,M as f1,b as b3,g as C1,r as v1}from"./ChevronDownIcon-660c32b0.js";import{o as L,u as Q,A as m3,i as a3,N as _3}from"./render-c34c346a.js";import{w as g1}from"./use-outside-click-484df218.js";import{s as m1}from"./use-resolve-button-type-24d8b5c5.js";import{f as d3,u as u3,o as U}from"./keyboard-982fc047.js";import{t as x1,i as i3,l as L1}from"./open-closed-7f51e238.js";import{i as x3,E as L3,w as w1,h as y1,P as Y,N as q,T as p3}from"./focus-management-8406d052.js";import{_ as $3}from"./ButtonItem-718c0517.js";import{l as b1,i as _1,u as k3,r as n3}from"./combobox-4401f443.js";import{C as o3,T as $1}from"./Colosseum-0e8d62a4.js";import{r as k1}from"./CheckCircleIcon-d86d1232.js";function M1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m15 11.25-3-3m0 0-3 3m3-3v7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Z1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.182 16.318A4.486 4.486 0 0 0 12.016 15a4.486 4.486 0 0 0-3.198 1.318M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0ZM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75Zm-.375 0h.008v.015h-.008V9.75Z"})])}function G3(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"})])}function A1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"})])}const S1={},P1={xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 0 54 50",fill:"currentColor",stroke:"currentColor"};function T1(t,a){return l(),o("svg",P1,a[0]||(a[0]=[e("path",{d:"M37.156,37.969c-7.816,0-11.399-6.687-14.56-12.585c-3.127-5.837-5.311-9.382-9.253-9.382c-5.343,0-9.372,3.881-9.372,9.027c0,1.104-0.896,2-2,2s-2-0.896-2-2c0-7.305,5.874-13.027,13.372-13.027c6.621,0,9.751,5.842,12.779,11.493c2.887,5.387,5.612,10.475,11.034,10.475c4.733,0,8.885-4.178,8.885-8.94c0-1.104,0.896-2,2-2s2,0.896,2,2C50.041,32.043,44.141,37.969,37.156,37.969z"},null,-1),e("path",{d:"M21.054 26.739c-1.971 4.331-3.799 7.341-7.835 7.341-5.382 0-9.288-3.807-9.288-9.052v-1H.012v1C.012 32.424 5.689 38 13.219 38c5.415 0 8.18-3.449 10.172-7.144C22.541 29.485 21.775 28.083 21.054 26.739zM36.875 12c-5.491 0-8.484 3.673-10.609 7.596.563.984 1.099 1.981 1.621 2.957.234.437.461.854.685 1.264 1.98-4.329 3.986-7.897 8.304-7.897 5.335 0 9.207 3.831 9.207 9.109v1H50v-1C50 17.723 44.234 12 36.875 12z"},null,-1)]))}const B1=M(S1,[["render",T1]]),H1={},j1={viewBox:"0 0 707 84",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"};function E1(t,a){return l(),o("svg",j1,a[0]||(a[0]=[E('<g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Risorsa-7l" transform="translate(0.000000, -0.003131)" fill-rule="nonzero"><path id="Path" class="fill-current text-[#2C275B] dark:text-white" d="M163.62,8.83313051 C164.09,8.83313051 164.46,9.21313051 164.46,9.67313051 L164.46,63.7331305 C164.46,70.7831305 167.09,72.4831305 173.14,71.6731305 C173.63,71.6031305 174.08,71.9631305 174.11,72.4631305 L174.53,81.2831305 C174.55,81.7131305 174.24,82.0931305 173.82,82.1631305 C159.36,84.6031305 150.66,80.7431305 150.66,63.7331305 L150.66,9.67313051 C150.66,9.20313051 151.04,8.83313051 151.5,8.83313051 L163.61,8.83313051 L163.62,8.83313051 Z"></path><path id="Shape" d="M225.57,66.5331305 C225.57,70.3731305 225.39,72.5531305 229.32,72.5631305 C229.78,72.5631305 230.17,72.9431305 230.17,73.4031305 L230.17,80.8431305 C230.17,81.2431305 229.89,81.5931305 229.5,81.6731305 C223.68,82.8331305 215.98,83.0631305 214.52,76.0931305 C214.37,75.3631305 213.42,75.1831305 213,75.8031305 C209.9,80.3631305 204.51,82.8331305 198.97,82.8331305 C184.07,82.5331305 177.17,71.0331305 177.17,57.3331305 C177.67,41.0331305 188.77,32.4331305 205.07,32.7331305 C211.98,32.8331305 218.79,34.4431305 225.05,37.0131305 C225.37,37.1431305 225.57,37.4531305 225.57,37.8031305 L225.57,66.5331305 Z M211.97,45.9131305 C211.97,45.5631305 211.75,45.2331305 211.42,45.1131305 C208.88,44.1831305 206.21,43.9231305 203.97,43.9231305 C196.37,43.9231305 191.47,48.1231305 191.17,57.3231305 C191.17,64.9231305 194.77,70.5231305 201.97,70.4231305 C208.67,70.4231305 211.67,65.1231305 211.97,57.8231305 L211.97,45.9131305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M257.31,33.9331305 L271.32,33.9331305 C271.79,33.9331305 272.16,34.3131305 272.16,34.7731305 L272.16,43.5831305 C272.16,44.0531305 271.78,44.4231305 271.32,44.4231305 L257.31,44.4231305 C256.84,44.4231305 256.47,44.8031305 256.47,45.2631305 L256.47,63.1231305 C256.47,72.7131305 264.11,73.5631305 271.79,71.0631305 C272.3,70.8931305 272.83,71.2231305 272.9,71.7531305 L273.98,80.5931305 C274.03,81.0131305 273.76,81.4131305 273.36,81.5131305 C257.05,85.8431305 242.77,82.9331305 242.77,63.4231305 L242.77,45.1831305 C242.77,44.7531305 242.44,44.3831305 242.01,44.3431305 L234.43,43.5931305 C234,43.5531305 233.67,43.1831305 233.67,42.7531305 L233.67,34.7631305 C233.67,34.2931305 234.05,33.9231305 234.51,33.9231305 L243,33.9231305 C243.43,33.9231305 243.8,33.5931305 243.84,33.1631305 L245.19,19.6831305 C245.23,19.2531305 245.6,18.9231305 246.03,18.9231305 L255.62,18.9231305 C256.09,18.9231305 256.46,19.3031305 256.46,19.7631305 L256.46,33.0731305 C256.46,33.5431305 256.84,33.9131305 257.3,33.9131305 L257.31,33.9331305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Shape" d="M296.37,19.9331305 C296.37,30.7331305 279.97,30.7331305 279.97,19.9331305 C279.97,9.13313051 296.37,9.23313051 296.37,19.9331305 Z M294.33,82.0331305 L282.02,82.0331305 C281.55,82.0331305 281.18,81.6531305 281.18,81.1931305 L281.18,35.2731305 C281.18,34.8031305 281.56,34.4331305 282.02,34.4331305 L294.33,34.4331305 C294.8,34.4331305 295.17,34.8131305 295.17,35.2731305 L295.17,81.1931305 C295.17,81.6631305 294.79,82.0331305 294.33,82.0331305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M303.46,33.5131305 C309.94,32.4131305 319.29,32.0031305 320.92,38.7231305 C321.08,39.3931305 321.98,39.5731305 322.39,39.0231305 C325.64,34.6731305 330.73,32.7331305 336.37,32.7331305 C349.97,32.7331305 355.67,42.0331305 355.37,54.0331305 L355.37,67.1331305 C355.37,71.3631305 355.55,73.5631305 359.64,73.5631305 C360.1,73.5631305 360.47,73.9431305 360.47,74.4031305 L360.47,81.0731305 C360.47,81.4631305 360.2,81.8131305 359.82,81.9031305 C350.54,84.0831305 341.47,82.6731305 341.47,70.9431305 L341.47,54.0431305 C341.57,47.3431305 337.27,43.8431305 332.17,43.8431305 C326.97,43.5431305 322.07,47.6431305 322.07,54.3431305 L322.07,81.2031305 C322.07,81.6731305 321.69,82.0431305 321.23,82.0431305 L308.92,82.0431305 C308.45,82.0431305 308.08,81.6631305 308.08,81.2031305 L308.08,48.7331305 C308.08,43.7331305 308.17,41.7531305 303.63,41.8031305 C303.16,41.8031305 302.78,41.4331305 302.78,40.9631305 L302.78,34.3431305 C302.78,33.9331305 303.07,33.5831305 303.47,33.5131305 L303.46,33.5131305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M400.31,75.0231305 C400.24,74.5031305 399.71,74.1831305 399.21,74.3331305 C393.31,76.1931305 389.05,76.1031305 386.22,74.1131305 C383.61,72.2631305 382.29,68.7331305 382.29,63.6231305 L382.29,42.0131305 C382.29,41.5431305 382.67,41.1731305 383.13,41.1731305 L398.33,41.1731305 C398.8,41.1731305 399.17,40.7931305 399.17,40.3331305 L399.17,35.1431305 C399.17,34.6731305 398.79,34.3031305 398.33,34.3031305 L383.13,34.3031305 C382.66,34.3031305 382.29,33.9231305 382.29,33.4631305 L382.29,20.0831305 C382.29,19.6131305 381.91,19.2431305 381.45,19.2431305 L374.34,19.2431305 C373.9,19.2431305 373.53,19.5831305 373.5,20.0231305 L372.5,33.5331305 C372.47,33.9731305 372.1,34.3131305 371.66,34.3131305 L363.42,34.3131305 C362.95,34.3131305 362.58,34.6931305 362.58,35.1531305 L362.58,39.7331305 C362.58,40.1731305 362.92,40.5431305 363.36,40.5731305 L371.7,41.1731305 C372.14,41.2031305 372.48,41.5731305 372.48,42.0131305 L372.48,63.8431305 C372.48,71.5731305 374.62,77.0831305 378.87,80.2031305 C383.66,83.7331305 391.11,84.1731305 400.44,81.4931305 C400.84,81.3731305 401.1,80.9731305 401.05,80.5631305 L400.31,75.0331305 L400.31,75.0231305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M456.85,76.8831305 C456.85,76.4231305 456.46,76.0531305 456,76.0531305 C454.63,76.0631305 453.73,75.7931305 453.07,75.2031305 C451.88,74.1331305 451.66,72.1931305 451.66,68.4731305 L451.66,35.0431305 C451.66,34.5731305 451.28,34.2031305 450.82,34.2031305 L442.49,34.2031305 C442.02,34.2031305 441.65,34.5831305 441.65,35.0431305 L441.65,61.6031305 C441.65,69.4731305 436.37,74.9531305 428.81,74.9531305 C425.76,74.9531305 423.13,73.9331305 421.2,71.9831305 C418.9,69.6631305 417.72,66.1731305 417.78,61.9131305 L417.78,35.0431305 C417.78,34.5731305 417.4,34.2031305 416.94,34.2031305 L408.51,34.2031305 C408.04,34.2031305 407.67,34.5831305 407.67,35.0431305 L407.67,61.9131305 C407.67,71.4231305 410.73,82.7431305 425.37,82.7431305 C430.91,82.7431305 438.27,81.2831305 441.8,74.3331305 L442.76,72.4531305 L442.76,74.5531305 C442.76,77.6431305 443.57,79.8931305 445.16,81.2231305 C447.74,83.3731305 451.95,83.0431305 456.2,82.3431305 C456.61,82.2731305 456.91,81.9131305 456.900243,81.5031305 L456.84,76.8831305 L456.85,76.8831305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M498.32,75.0231305 C498.25,74.5031305 497.72,74.1831305 497.22,74.3331305 C491.32,76.1931305 487.07,76.1031305 484.24,74.1131305 C481.63,72.2631305 480.31,68.7331305 480.31,63.6231305 L480.31,42.0131305 C480.31,41.5431305 480.69,41.1731305 481.15,41.1731305 L496.35,41.1731305 C496.82,41.1731305 497.19,40.7931305 497.19,40.3331305 L497.19,35.1431305 C497.19,34.6731305 496.81,34.3031305 496.35,34.3031305 L481.15,34.3031305 C480.68,34.3031305 480.31,33.9231305 480.31,33.4631305 L480.31,20.0831305 C480.31,19.6131305 479.93,19.2431305 479.47,19.2431305 L472.36,19.2431305 C471.92,19.2431305 471.55,19.5831305 471.52,20.0231305 L470.52,33.5331305 C470.49,33.9731305 470.12,34.3131305 469.68,34.3131305 L461.43,34.3131305 C460.96,34.3131305 460.59,34.6931305 460.59,35.1531305 L460.59,39.7331305 C460.59,40.1731305 460.93,40.5431305 461.37,40.5731305 L469.72,41.1731305 C470.16,41.2031305 470.5,41.5731305 470.5,42.0131305 L470.5,63.8431305 C470.5,71.5731305 472.64,77.0831305 476.89,80.2031305 C481.67,83.7331305 489.13,84.1731305 498.46,81.4931305 C498.86,81.3731305 499.12,80.9731305 499.07,80.5631305 L498.32,75.0331305 L498.32,75.0231305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Shape" d="M546.82,40.0931305 C542.21,35.5631305 535.56,32.9831305 528.5,32.9831305 L528.04,32.9831305 C516.11,33.1731305 503.25,41.0431305 503.25,57.8631305 C503.25,74.6831305 516.31,82.8431305 528.44,82.8431305 C540.57,82.8431305 553.72,75.0231305 553.72,57.8631305 C553.72,50.6931305 551.32,44.5531305 546.8,40.0931305 L546.82,40.0931305 Z M528.66,74.1431305 C528.59,74.1531305 528.52,74.1531305 528.45,74.1531305 C524.39,74.1531305 520.51,72.6031305 517.76,69.8831305 C514.77,66.9331305 513.18,62.7731305 513.18,57.8631305 C513.18,47.0331305 520.89,41.8831305 528.04,41.6831305 C532.21,41.5831305 536.24,43.1131305 539.1,45.9031305 C542.12,48.8431305 543.73,52.9831305 543.73,57.8631305 C543.73,68.9031305 535.92,74.0431305 528.66,74.1431305 L528.66,74.1431305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M588.85,32.5831305 L588.83,32.5831305 C583.51,32.5831305 576.9,35.2031305 574.95,40.9331305 L574.13,43.3531305 L573.97,40.8031305 C573.81,38.2931305 572.85,36.3631305 571.12,35.0731305 C568.69,33.2531305 564.72,32.7131305 560.11,33.5631305 C559.71,33.6331305 559.42,34.0031305 559.42,34.4131305 L559.49,39.4331305 C559.49,39.8731305 559.85,40.2431305 560.29,40.2631305 C561.81,40.3131305 562.83,40.6431305 563.54,41.3131305 C564.76,42.4631305 564.76,44.3931305 564.76,47.0531305 L564.76,81.1031305 C564.76,81.5731305 565.14,81.9431305 565.6,81.9431305 L573.82,81.9431305 C574.29,81.9431305 574.66,81.5631305 574.66,81.1031305 L574.66,54.2331305 C574.66,45.5431305 581.38,41.5931305 587.61,41.5931305 C590.61,41.5931305 593.45,42.3031305 596.09,43.7131305 C596.57,43.9731305 597.17,43.7431305 597.32,43.2131305 L599.3,36.3231305 C599.4,35.9731305 599.27,35.5831305 598.96,35.3831305 C596.06,33.4931305 592.51,32.5131305 588.84,32.5931305 L588.85,32.5831305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Shape" d="M605.84,35.5431305 L605.84,81.0831305 C605.84,81.5531305 606.22,81.9231305 606.68,81.9231305 L615.01,81.9231305 C615.48,81.9231305 615.85,81.5431305 615.85,81.0831305 L615.85,35.5431305 C615.85,35.0731305 615.47,34.7031305 615.01,34.7031305 L606.68,34.7031305 C606.21,34.7031305 605.84,35.0831305 605.84,35.5431305 Z M610.9,12.6531305 C607.94,12.6531305 604.73,14.5231305 604.73,18.6231305 C604.73,20.4031305 605.33,21.9231305 606.45,23.0131305 C607.6,24.1331305 609.25,24.7631305 610.99,24.6931305 C613.96,24.6031305 616.97,22.6931305 616.97,18.6231305 C616.97,14.5531305 613.82,12.6531305 610.9,12.6531305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Shape" d="M673.34,74.2231305 C671.98,73.0031305 671.98,70.8431305 671.98,68.1131305 L671.98,38.1231305 C671.98,37.7831305 671.77,37.4731305 671.46,37.3431305 C664.94,34.5531305 658.71,33.1931305 652.46,33.1931305 C652.21,33.1931305 651.94,33.1831305 651.68,33.1831305 C644.16,33.1831305 637.74,35.2831305 633.09,39.2831305 C628.1,43.5631305 625.39,49.8531305 625.25,57.4731305 C625.25,69.0831305 630.89,82.6531305 646.79,82.6531305 C654.18,82.6531305 660.59,78.6031305 663.11,72.3431305 L664.08,69.9231305 L664.08,74.5431305 C664.08,77.1231305 664.85,79.0631305 666.37,80.3131305 C668.88,82.4031305 673.09,82.3431305 676.52,81.8231305 C676.93,81.7631305 677.23,81.4031305 677.23,80.9931305 L677.23,76.0431305 C677.23,75.5831305 676.86,75.2131305 676.41,75.2031305 C675.02,75.1831305 674.03,74.8631305 673.33,74.2131305 L673.34,74.2231305 Z M662.38,57.9631305 C662.05,67.9131305 657.09,73.8431305 649.13,73.8431305 C640.58,73.8431305 635.27,67.5731305 635.27,57.4631305 C635.57,47.0431305 641.54,41.0831305 651.65,41.0831305 C654.33,41.0831305 658.31,41.4431305 662.08,43.1531305 L662.37,43.2831305 L662.37,57.9631305 L662.38,57.9631305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><path id="Path" d="M705.95,75.7431305 C705.93,75.2531305 705.49,74.8931305 705,74.9431305 C701.53,75.3331305 699.28,74.9131305 697.8,73.6331305 C696.09,72.1731305 695.36,69.5731305 695.36,64.9331305 L695.36,9.76313051 C695.36,9.29313051 694.98,8.92313051 694.52,8.92313051 L686.3,8.92313051 C685.83,8.92313051 685.46,9.30313051 685.46,9.76313051 L685.46,65.0531305 C685.46,72.6631305 686.94,77.4531305 690.11,80.1531305 C693.22,82.8031305 698,83.5131305 705.51,82.4431305 C705.94,82.3831305 706.26,82.0031305 706.24,81.5631305 L705.97,75.7531305 L705.95,75.7431305 Z" class="fill-current text-[#2C275B] dark:text-white"></path><g id="Group"><path id="Shape" d="M78.01,79.0531305 L52.01,2.72313051 C51.44,1.04313051 49.82,-0.0668694893 48.05,0.00313051067 C21.52,1.11313051 4.56,16.7531305 0.9,21.1731305 C0.31,21.8931305 0,22.7831305 0,23.7131305 L0,80.1331305 C0,81.5831305 1.18,82.7631305 2.63,82.7631305 L11.42,82.7631305 C12.32,82.7631305 13.06,82.0331305 13.06,81.1231305 L13.06,75.8531305 C13.06,72.2331305 15.64,68.6631305 19.23,67.8331305 C23.52,66.8331305 27.17,69.8631305 27.17,74.0631305 L27.17,81.1231305 C27.17,82.0231305 27.9,82.7631305 28.81,82.7631305 L40.88,82.7631305 C41.78,82.7631305 42.52,82.0331305 42.52,81.1231305 L42.52,72.5931305 C42.52,68.5031305 45.84,65.0131305 49.94,64.8931305 C54.04,64.7731305 57.37,68.0631305 57.37,72.1531305 L57.37,81.1231305 C57.37,82.0231305 58.1,82.7631305 59.01,82.7631305 L75.57,82.7631305 C78.31,82.7631305 78.65,80.8931305 78.02,79.0531305 L78.01,79.0531305 Z M25.28,57.4531305 C21.67,57.7931305 18.07,58.1731305 14.47,58.6131305 C13.5,58.7331305 12.64,57.9731305 12.64,56.9931305 L12.64,48.2231305 C12.64,44.6131305 15.23,41.0331305 18.81,40.2031305 C23.1,39.2131305 26.76,42.2331305 26.76,46.4331305 L26.76,55.8331305 C26.76,56.6731305 26.12,57.3831305 25.28,57.4531305 Z M55.33,55.8231305 C51.49,55.8831305 47.64,55.9931305 43.81,56.1731305 C42.88,56.2131305 42.1,55.4731305 42.1,54.5431305 L42.1,44.9631305 C42.1,40.8731305 45.42,37.3831305 49.52,37.2531305 C53.62,37.1331305 56.94,40.4231305 56.94,44.5131305 L56.94,54.1931305 C56.94,55.0831305 56.22,55.8131305 55.33,55.8231305 L55.33,55.8231305 Z" fill="#5A43FF"></path><path id="Shape" d="M124.76,41.9431305 C110.58,30.7631305 83.95,29.6131305 74.31,29.5731305 C72.52,29.5731305 71.25,31.3131305 71.8,33.0131305 L86.86,80.0531305 C87.38,81.6831305 88.87,82.7931305 90.57,82.8331305 L94.56,82.8331305 C95.46,82.8331305 96.19,82.0931305 96.19,81.1931305 L96.19,74.2431305 C96.19,70.4031305 99.3,67.5631305 103.12,67.9831305 C106.95,68.3931305 110.05,71.9031305 110.05,75.7331305 L110.05,81.1531305 C110.05,82.0631305 110.79,82.7931305 111.69,82.7931305 L122.86,82.7631305 C124.69,82.7631305 125.72,81.7631305 125.72,80.1131305 L125.72,43.9831305 C125.72,43.1931305 125.38,42.4331305 124.76,41.9431305 L124.76,41.9431305 Z M107.8,58.6031305 C104.29,58.1731305 100.77,57.8031305 97.25,57.4631305 C96.41,57.3831305 95.77,56.6831305 95.77,55.8431305 L95.77,46.6031305 C95.77,42.7631305 98.88,39.9231305 102.71,40.3431305 C106.54,40.7531305 109.63,44.2631305 109.63,48.1031305 L109.63,56.9831305 C109.63,57.9631305 108.77,58.7231305 107.8,58.6031305 Z" fill="#7469FF"></path></g></g></g>',1)]))}const V1=M(H1,[["render",E1]]),F1={},z1={viewBox:"0 0 707 84",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"};function O1(t,a){return l(),o("svg",z1,a[0]||(a[0]=[E('<g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g id="Risorsa-7l" transform="translate(0.000000, -0.003131)" fill-rule="nonzero"><path id="Path" class="fill-current text-white" d="M163.62,8.83313051 C164.09,8.83313051 164.46,9.21313051 164.46,9.67313051 L164.46,63.7331305 C164.46,70.7831305 167.09,72.4831305 173.14,71.6731305 C173.63,71.6031305 174.08,71.9631305 174.11,72.4631305 L174.53,81.2831305 C174.55,81.7131305 174.24,82.0931305 173.82,82.1631305 C159.36,84.6031305 150.66,80.7431305 150.66,63.7331305 L150.66,9.67313051 C150.66,9.20313051 151.04,8.83313051 151.5,8.83313051 L163.61,8.83313051 L163.62,8.83313051 Z"></path><path id="Shape" d="M225.57,66.5331305 C225.57,70.3731305 225.39,72.5531305 229.32,72.5631305 C229.78,72.5631305 230.17,72.9431305 230.17,73.4031305 L230.17,80.8431305 C230.17,81.2431305 229.89,81.5931305 229.5,81.6731305 C223.68,82.8331305 215.98,83.0631305 214.52,76.0931305 C214.37,75.3631305 213.42,75.1831305 213,75.8031305 C209.9,80.3631305 204.51,82.8331305 198.97,82.8331305 C184.07,82.5331305 177.17,71.0331305 177.17,57.3331305 C177.67,41.0331305 188.77,32.4331305 205.07,32.7331305 C211.98,32.8331305 218.79,34.4431305 225.05,37.0131305 C225.37,37.1431305 225.57,37.4531305 225.57,37.8031305 L225.57,66.5331305 Z M211.97,45.9131305 C211.97,45.5631305 211.75,45.2331305 211.42,45.1131305 C208.88,44.1831305 206.21,43.9231305 203.97,43.9231305 C196.37,43.9231305 191.47,48.1231305 191.17,57.3231305 C191.17,64.9231305 194.77,70.5231305 201.97,70.4231305 C208.67,70.4231305 211.67,65.1231305 211.97,57.8231305 L211.97,45.9131305 Z" class="fill-current text-white"></path><path id="Path" d="M257.31,33.9331305 L271.32,33.9331305 C271.79,33.9331305 272.16,34.3131305 272.16,34.7731305 L272.16,43.5831305 C272.16,44.0531305 271.78,44.4231305 271.32,44.4231305 L257.31,44.4231305 C256.84,44.4231305 256.47,44.8031305 256.47,45.2631305 L256.47,63.1231305 C256.47,72.7131305 264.11,73.5631305 271.79,71.0631305 C272.3,70.8931305 272.83,71.2231305 272.9,71.7531305 L273.98,80.5931305 C274.03,81.0131305 273.76,81.4131305 273.36,81.5131305 C257.05,85.8431305 242.77,82.9331305 242.77,63.4231305 L242.77,45.1831305 C242.77,44.7531305 242.44,44.3831305 242.01,44.3431305 L234.43,43.5931305 C234,43.5531305 233.67,43.1831305 233.67,42.7531305 L233.67,34.7631305 C233.67,34.2931305 234.05,33.9231305 234.51,33.9231305 L243,33.9231305 C243.43,33.9231305 243.8,33.5931305 243.84,33.1631305 L245.19,19.6831305 C245.23,19.2531305 245.6,18.9231305 246.03,18.9231305 L255.62,18.9231305 C256.09,18.9231305 256.46,19.3031305 256.46,19.7631305 L256.46,33.0731305 C256.46,33.5431305 256.84,33.9131305 257.3,33.9131305 L257.31,33.9331305 Z" class="fill-current text-white"></path><path id="Shape" d="M296.37,19.9331305 C296.37,30.7331305 279.97,30.7331305 279.97,19.9331305 C279.97,9.13313051 296.37,9.23313051 296.37,19.9331305 Z M294.33,82.0331305 L282.02,82.0331305 C281.55,82.0331305 281.18,81.6531305 281.18,81.1931305 L281.18,35.2731305 C281.18,34.8031305 281.56,34.4331305 282.02,34.4331305 L294.33,34.4331305 C294.8,34.4331305 295.17,34.8131305 295.17,35.2731305 L295.17,81.1931305 C295.17,81.6631305 294.79,82.0331305 294.33,82.0331305 Z" class="fill-current text-white"></path><path id="Path" d="M303.46,33.5131305 C309.94,32.4131305 319.29,32.0031305 320.92,38.7231305 C321.08,39.3931305 321.98,39.5731305 322.39,39.0231305 C325.64,34.6731305 330.73,32.7331305 336.37,32.7331305 C349.97,32.7331305 355.67,42.0331305 355.37,54.0331305 L355.37,67.1331305 C355.37,71.3631305 355.55,73.5631305 359.64,73.5631305 C360.1,73.5631305 360.47,73.9431305 360.47,74.4031305 L360.47,81.0731305 C360.47,81.4631305 360.2,81.8131305 359.82,81.9031305 C350.54,84.0831305 341.47,82.6731305 341.47,70.9431305 L341.47,54.0431305 C341.57,47.3431305 337.27,43.8431305 332.17,43.8431305 C326.97,43.5431305 322.07,47.6431305 322.07,54.3431305 L322.07,81.2031305 C322.07,81.6731305 321.69,82.0431305 321.23,82.0431305 L308.92,82.0431305 C308.45,82.0431305 308.08,81.6631305 308.08,81.2031305 L308.08,48.7331305 C308.08,43.7331305 308.17,41.7531305 303.63,41.8031305 C303.16,41.8031305 302.78,41.4331305 302.78,40.9631305 L302.78,34.3431305 C302.78,33.9331305 303.07,33.5831305 303.47,33.5131305 L303.46,33.5131305 Z" class="fill-current text-white"></path><path id="Path" d="M400.31,75.0231305 C400.24,74.5031305 399.71,74.1831305 399.21,74.3331305 C393.31,76.1931305 389.05,76.1031305 386.22,74.1131305 C383.61,72.2631305 382.29,68.7331305 382.29,63.6231305 L382.29,42.0131305 C382.29,41.5431305 382.67,41.1731305 383.13,41.1731305 L398.33,41.1731305 C398.8,41.1731305 399.17,40.7931305 399.17,40.3331305 L399.17,35.1431305 C399.17,34.6731305 398.79,34.3031305 398.33,34.3031305 L383.13,34.3031305 C382.66,34.3031305 382.29,33.9231305 382.29,33.4631305 L382.29,20.0831305 C382.29,19.6131305 381.91,19.2431305 381.45,19.2431305 L374.34,19.2431305 C373.9,19.2431305 373.53,19.5831305 373.5,20.0231305 L372.5,33.5331305 C372.47,33.9731305 372.1,34.3131305 371.66,34.3131305 L363.42,34.3131305 C362.95,34.3131305 362.58,34.6931305 362.58,35.1531305 L362.58,39.7331305 C362.58,40.1731305 362.92,40.5431305 363.36,40.5731305 L371.7,41.1731305 C372.14,41.2031305 372.48,41.5731305 372.48,42.0131305 L372.48,63.8431305 C372.48,71.5731305 374.62,77.0831305 378.87,80.2031305 C383.66,83.7331305 391.11,84.1731305 400.44,81.4931305 C400.84,81.3731305 401.1,80.9731305 401.05,80.5631305 L400.31,75.0331305 L400.31,75.0231305 Z" class="fill-current text-white"></path><path id="Path" d="M456.85,76.8831305 C456.85,76.4231305 456.46,76.0531305 456,76.0531305 C454.63,76.0631305 453.73,75.7931305 453.07,75.2031305 C451.88,74.1331305 451.66,72.1931305 451.66,68.4731305 L451.66,35.0431305 C451.66,34.5731305 451.28,34.2031305 450.82,34.2031305 L442.49,34.2031305 C442.02,34.2031305 441.65,34.5831305 441.65,35.0431305 L441.65,61.6031305 C441.65,69.4731305 436.37,74.9531305 428.81,74.9531305 C425.76,74.9531305 423.13,73.9331305 421.2,71.9831305 C418.9,69.6631305 417.72,66.1731305 417.78,61.9131305 L417.78,35.0431305 C417.78,34.5731305 417.4,34.2031305 416.94,34.2031305 L408.51,34.2031305 C408.04,34.2031305 407.67,34.5831305 407.67,35.0431305 L407.67,61.9131305 C407.67,71.4231305 410.73,82.7431305 425.37,82.7431305 C430.91,82.7431305 438.27,81.2831305 441.8,74.3331305 L442.76,72.4531305 L442.76,74.5531305 C442.76,77.6431305 443.57,79.8931305 445.16,81.2231305 C447.74,83.3731305 451.95,83.0431305 456.2,82.3431305 C456.61,82.2731305 456.91,81.9131305 456.900243,81.5031305 L456.84,76.8831305 L456.85,76.8831305 Z" class="fill-current text-white"></path><path id="Path" d="M498.32,75.0231305 C498.25,74.5031305 497.72,74.1831305 497.22,74.3331305 C491.32,76.1931305 487.07,76.1031305 484.24,74.1131305 C481.63,72.2631305 480.31,68.7331305 480.31,63.6231305 L480.31,42.0131305 C480.31,41.5431305 480.69,41.1731305 481.15,41.1731305 L496.35,41.1731305 C496.82,41.1731305 497.19,40.7931305 497.19,40.3331305 L497.19,35.1431305 C497.19,34.6731305 496.81,34.3031305 496.35,34.3031305 L481.15,34.3031305 C480.68,34.3031305 480.31,33.9231305 480.31,33.4631305 L480.31,20.0831305 C480.31,19.6131305 479.93,19.2431305 479.47,19.2431305 L472.36,19.2431305 C471.92,19.2431305 471.55,19.5831305 471.52,20.0231305 L470.52,33.5331305 C470.49,33.9731305 470.12,34.3131305 469.68,34.3131305 L461.43,34.3131305 C460.96,34.3131305 460.59,34.6931305 460.59,35.1531305 L460.59,39.7331305 C460.59,40.1731305 460.93,40.5431305 461.37,40.5731305 L469.72,41.1731305 C470.16,41.2031305 470.5,41.5731305 470.5,42.0131305 L470.5,63.8431305 C470.5,71.5731305 472.64,77.0831305 476.89,80.2031305 C481.67,83.7331305 489.13,84.1731305 498.46,81.4931305 C498.86,81.3731305 499.12,80.9731305 499.07,80.5631305 L498.32,75.0331305 L498.32,75.0231305 Z" class="fill-current text-white"></path><path id="Shape" d="M546.82,40.0931305 C542.21,35.5631305 535.56,32.9831305 528.5,32.9831305 L528.04,32.9831305 C516.11,33.1731305 503.25,41.0431305 503.25,57.8631305 C503.25,74.6831305 516.31,82.8431305 528.44,82.8431305 C540.57,82.8431305 553.72,75.0231305 553.72,57.8631305 C553.72,50.6931305 551.32,44.5531305 546.8,40.0931305 L546.82,40.0931305 Z M528.66,74.1431305 C528.59,74.1531305 528.52,74.1531305 528.45,74.1531305 C524.39,74.1531305 520.51,72.6031305 517.76,69.8831305 C514.77,66.9331305 513.18,62.7731305 513.18,57.8631305 C513.18,47.0331305 520.89,41.8831305 528.04,41.6831305 C532.21,41.5831305 536.24,43.1131305 539.1,45.9031305 C542.12,48.8431305 543.73,52.9831305 543.73,57.8631305 C543.73,68.9031305 535.92,74.0431305 528.66,74.1431305 L528.66,74.1431305 Z" class="fill-current text-white"></path><path id="Path" d="M588.85,32.5831305 L588.83,32.5831305 C583.51,32.5831305 576.9,35.2031305 574.95,40.9331305 L574.13,43.3531305 L573.97,40.8031305 C573.81,38.2931305 572.85,36.3631305 571.12,35.0731305 C568.69,33.2531305 564.72,32.7131305 560.11,33.5631305 C559.71,33.6331305 559.42,34.0031305 559.42,34.4131305 L559.49,39.4331305 C559.49,39.8731305 559.85,40.2431305 560.29,40.2631305 C561.81,40.3131305 562.83,40.6431305 563.54,41.3131305 C564.76,42.4631305 564.76,44.3931305 564.76,47.0531305 L564.76,81.1031305 C564.76,81.5731305 565.14,81.9431305 565.6,81.9431305 L573.82,81.9431305 C574.29,81.9431305 574.66,81.5631305 574.66,81.1031305 L574.66,54.2331305 C574.66,45.5431305 581.38,41.5931305 587.61,41.5931305 C590.61,41.5931305 593.45,42.3031305 596.09,43.7131305 C596.57,43.9731305 597.17,43.7431305 597.32,43.2131305 L599.3,36.3231305 C599.4,35.9731305 599.27,35.5831305 598.96,35.3831305 C596.06,33.4931305 592.51,32.5131305 588.84,32.5931305 L588.85,32.5831305 Z" class="fill-current text-white"></path><path id="Shape" d="M605.84,35.5431305 L605.84,81.0831305 C605.84,81.5531305 606.22,81.9231305 606.68,81.9231305 L615.01,81.9231305 C615.48,81.9231305 615.85,81.5431305 615.85,81.0831305 L615.85,35.5431305 C615.85,35.0731305 615.47,34.7031305 615.01,34.7031305 L606.68,34.7031305 C606.21,34.7031305 605.84,35.0831305 605.84,35.5431305 Z M610.9,12.6531305 C607.94,12.6531305 604.73,14.5231305 604.73,18.6231305 C604.73,20.4031305 605.33,21.9231305 606.45,23.0131305 C607.6,24.1331305 609.25,24.7631305 610.99,24.6931305 C613.96,24.6031305 616.97,22.6931305 616.97,18.6231305 C616.97,14.5531305 613.82,12.6531305 610.9,12.6531305 Z" class="fill-current text-white"></path><path id="Shape" d="M673.34,74.2231305 C671.98,73.0031305 671.98,70.8431305 671.98,68.1131305 L671.98,38.1231305 C671.98,37.7831305 671.77,37.4731305 671.46,37.3431305 C664.94,34.5531305 658.71,33.1931305 652.46,33.1931305 C652.21,33.1931305 651.94,33.1831305 651.68,33.1831305 C644.16,33.1831305 637.74,35.2831305 633.09,39.2831305 C628.1,43.5631305 625.39,49.8531305 625.25,57.4731305 C625.25,69.0831305 630.89,82.6531305 646.79,82.6531305 C654.18,82.6531305 660.59,78.6031305 663.11,72.3431305 L664.08,69.9231305 L664.08,74.5431305 C664.08,77.1231305 664.85,79.0631305 666.37,80.3131305 C668.88,82.4031305 673.09,82.3431305 676.52,81.8231305 C676.93,81.7631305 677.23,81.4031305 677.23,80.9931305 L677.23,76.0431305 C677.23,75.5831305 676.86,75.2131305 676.41,75.2031305 C675.02,75.1831305 674.03,74.8631305 673.33,74.2131305 L673.34,74.2231305 Z M662.38,57.9631305 C662.05,67.9131305 657.09,73.8431305 649.13,73.8431305 C640.58,73.8431305 635.27,67.5731305 635.27,57.4631305 C635.57,47.0431305 641.54,41.0831305 651.65,41.0831305 C654.33,41.0831305 658.31,41.4431305 662.08,43.1531305 L662.37,43.2831305 L662.37,57.9631305 L662.38,57.9631305 Z" class="fill-current text-white"></path><path id="Path" d="M705.95,75.7431305 C705.93,75.2531305 705.49,74.8931305 705,74.9431305 C701.53,75.3331305 699.28,74.9131305 697.8,73.6331305 C696.09,72.1731305 695.36,69.5731305 695.36,64.9331305 L695.36,9.76313051 C695.36,9.29313051 694.98,8.92313051 694.52,8.92313051 L686.3,8.92313051 C685.83,8.92313051 685.46,9.30313051 685.46,9.76313051 L685.46,65.0531305 C685.46,72.6631305 686.94,77.4531305 690.11,80.1531305 C693.22,82.8031305 698,83.5131305 705.51,82.4431305 C705.94,82.3831305 706.26,82.0031305 706.24,81.5631305 L705.97,75.7531305 L705.95,75.7431305 Z" class="fill-current text-white"></path><g id="Group"><path id="Shape" d="M78.01,79.0531305 L52.01,2.72313051 C51.44,1.04313051 49.82,-0.0668694893 48.05,0.00313051067 C21.52,1.11313051 4.56,16.7531305 0.9,21.1731305 C0.31,21.8931305 0,22.7831305 0,23.7131305 L0,80.1331305 C0,81.5831305 1.18,82.7631305 2.63,82.7631305 L11.42,82.7631305 C12.32,82.7631305 13.06,82.0331305 13.06,81.1231305 L13.06,75.8531305 C13.06,72.2331305 15.64,68.6631305 19.23,67.8331305 C23.52,66.8331305 27.17,69.8631305 27.17,74.0631305 L27.17,81.1231305 C27.17,82.0231305 27.9,82.7631305 28.81,82.7631305 L40.88,82.7631305 C41.78,82.7631305 42.52,82.0331305 42.52,81.1231305 L42.52,72.5931305 C42.52,68.5031305 45.84,65.0131305 49.94,64.8931305 C54.04,64.7731305 57.37,68.0631305 57.37,72.1531305 L57.37,81.1231305 C57.37,82.0231305 58.1,82.7631305 59.01,82.7631305 L75.57,82.7631305 C78.31,82.7631305 78.65,80.8931305 78.02,79.0531305 L78.01,79.0531305 Z M25.28,57.4531305 C21.67,57.7931305 18.07,58.1731305 14.47,58.6131305 C13.5,58.7331305 12.64,57.9731305 12.64,56.9931305 L12.64,48.2231305 C12.64,44.6131305 15.23,41.0331305 18.81,40.2031305 C23.1,39.2131305 26.76,42.2331305 26.76,46.4331305 L26.76,55.8331305 C26.76,56.6731305 26.12,57.3831305 25.28,57.4531305 Z M55.33,55.8231305 C51.49,55.8831305 47.64,55.9931305 43.81,56.1731305 C42.88,56.2131305 42.1,55.4731305 42.1,54.5431305 L42.1,44.9631305 C42.1,40.8731305 45.42,37.3831305 49.52,37.2531305 C53.62,37.1331305 56.94,40.4231305 56.94,44.5131305 L56.94,54.1931305 C56.94,55.0831305 56.22,55.8131305 55.33,55.8231305 L55.33,55.8231305 Z" fill="#5A43FF"></path><path id="Shape" d="M124.76,41.9431305 C110.58,30.7631305 83.95,29.6131305 74.31,29.5731305 C72.52,29.5731305 71.25,31.3131305 71.8,33.0131305 L86.86,80.0531305 C87.38,81.6831305 88.87,82.7931305 90.57,82.8331305 L94.56,82.8331305 C95.46,82.8331305 96.19,82.0931305 96.19,81.1931305 L96.19,74.2431305 C96.19,70.4031305 99.3,67.5631305 103.12,67.9831305 C106.95,68.3931305 110.05,71.9031305 110.05,75.7331305 L110.05,81.1531305 C110.05,82.0631305 110.79,82.7931305 111.69,82.7931305 L122.86,82.7631305 C124.69,82.7631305 125.72,81.7631305 125.72,80.1131305 L125.72,43.9831305 C125.72,43.1931305 125.38,42.4331305 124.76,41.9431305 L124.76,41.9431305 Z M107.8,58.6031305 C104.29,58.1731305 100.77,57.8031305 97.25,57.4631305 C96.41,57.3831305 95.77,56.6831305 95.77,55.8431305 L95.77,46.6031305 C95.77,42.7631305 98.88,39.9231305 102.71,40.3431305 C106.54,40.7531305 109.63,44.2631305 109.63,48.1031305 L109.63,56.9831305 C109.63,57.9631305 108.77,58.7231305 107.8,58.6031305 Z" fill="#7469FF"></path></g></g></g>',1)]))}const I1=M(F1,[["render",O1]]);var N1=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(N1||{});let q3=Symbol("PopoverContext");function w3(t){let a=g3(q3,null);if(a===null){let d=new Error(`<${t} /> is missing a parent <${Y3.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(d,w3),d}return a}let D1=Symbol("PopoverGroupContext");function K3(){return g3(D1,null)}let U3=Symbol("PopoverPanelContext");function W1(){return g3(U3,null)}let Y3=h3({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(t,{slots:a,attrs:d,expose:u}){var m;let g=B(null);u({el:g,$el:g});let r=B(1),s=B(null),i=B(null),b=B(null),f=B(null),$=N(()=>x3(g)),v=N(()=>{var c,p;if(!L(s)||!L(f))return!1;for(let s3 of document.querySelectorAll("body > *"))if(Number(s3==null?void 0:s3.contains(L(s)))^Number(s3==null?void 0:s3.contains(L(f))))return!0;let C=L3(),k=C.indexOf(L(s)),V=(k+C.length-1)%C.length,R=(k+1)%C.length,r3=C[V],a1=C[R];return!((c=L(f))!=null&&c.contains(r3))&&!((p=L(f))!=null&&p.contains(a1))}),w={popoverState:r,buttonId:B(null),panelId:B(null),panel:f,button:s,isPortalled:v,beforePanelSentinel:i,afterPanelSentinel:b,togglePopover(){r.value=Q(r.value,{0:1,1:0})},closePopover(){r.value!==1&&(r.value=1)},close(c){w.closePopover();let p=(()=>c?c instanceof HTMLElement?c:c.value instanceof HTMLElement?L(c):L(w.button):L(w.button))();p==null||p.focus()}};B3(q3,w),x1(N(()=>Q(r.value,{0:i3.Open,1:i3.Closed})));let W={buttonId:w.buttonId,panelId:w.panelId,close(){w.closePopover()}},j=K3(),I=j==null?void 0:j.registerPopover,[F,Z]=c1(),A=d1({mainTreeNodeRef:j==null?void 0:j.mainTreeNodeRef,portals:F,defaultContainers:[s,f]});function _(){var c,p,C,k;return(k=j==null?void 0:j.isFocusWithinPopoverGroup())!=null?k:((c=$.value)==null?void 0:c.activeElement)&&(((p=L(s))==null?void 0:p.contains($.value.activeElement))||((C=L(f))==null?void 0:C.contains($.value.activeElement)))}return f3(()=>I==null?void 0:I(W)),u1((m=$.value)==null?void 0:m.defaultView,"focus",c=>{var p,C;c.target!==window&&c.target instanceof HTMLElement&&r.value===0&&(_()||s&&f&&(A.contains(c.target)||(p=L(w.beforePanelSentinel))!=null&&p.contains(c.target)||(C=L(w.afterPanelSentinel))!=null&&C.contains(c.target)||w.closePopover()))},!0),g1(A.resolveContainers,(c,p)=>{var C;w.closePopover(),w1(p,y1.Loose)||(c.preventDefault(),(C=L(s))==null||C.focus())},N(()=>r.value===0)),()=>{let c={open:r.value===0,close:w.close};return K(H,[K(Z,{},()=>m3({theirProps:{...t,...d},ourProps:{ref:g},slot:c,slots:a,attrs:d,name:"Popover"})),K(A.MainTreeNode)])}}}),R1=h3({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{attrs:a,slots:d,expose:u}){var m;let g=(m=t.id)!=null?m:`headlessui-popover-button-${a3()}`,r=w3("PopoverButton"),s=N(()=>x3(r.button));u({el:r.button,$el:r.button}),C3(()=>{r.buttonId.value=g}),v3(()=>{r.buttonId.value=null});let i=K3(),b=i==null?void 0:i.closeOthers,f=W1(),$=N(()=>f===null?!1:f.value===r.panelId.value),v=B(null),w=`headlessui-focus-sentinel-${a3()}`;$.value||f3(()=>{r.button.value=L(v)});let W=m1(N(()=>({as:t.as,type:a.type})),v);function j(c){var p,C,k,V,R;if($.value){if(r.popoverState.value===1)return;switch(c.key){case U.Space:case U.Enter:c.preventDefault(),(C=(p=c.target).click)==null||C.call(p),r.closePopover(),(k=L(r.button))==null||k.focus();break}}else switch(c.key){case U.Space:case U.Enter:c.preventDefault(),c.stopPropagation(),r.popoverState.value===1&&(b==null||b(r.buttonId.value)),r.togglePopover();break;case U.Escape:if(r.popoverState.value!==0)return b==null?void 0:b(r.buttonId.value);if(!L(r.button)||(V=s.value)!=null&&V.activeElement&&!((R=L(r.button))!=null&&R.contains(s.value.activeElement)))return;c.preventDefault(),c.stopPropagation(),r.closePopover();break}}function I(c){$.value||c.key===U.Space&&c.preventDefault()}function F(c){var p,C;t.disabled||($.value?(r.closePopover(),(p=L(r.button))==null||p.focus()):(c.preventDefault(),c.stopPropagation(),r.popoverState.value===1&&(b==null||b(r.buttonId.value)),r.togglePopover(),(C=L(r.button))==null||C.focus()))}function Z(c){c.preventDefault(),c.stopPropagation()}let A=N3();function _(){let c=L(r.panel);if(!c)return;function p(){Q(A.value,{[G.Forwards]:()=>Y(c,q.First),[G.Backwards]:()=>Y(c,q.Last)})===p3.Error&&Y(L3().filter(C=>C.dataset.headlessuiFocusGuard!=="true"),Q(A.value,{[G.Forwards]:q.Next,[G.Backwards]:q.Previous}),{relativeTo:L(r.button)})}p()}return()=>{let c=r.popoverState.value===0,p={open:c},{...C}=t,k=$.value?{ref:v,type:W.value,onKeydown:j,onClick:F}:{ref:v,id:g,type:W.value,"aria-expanded":r.popoverState.value===0,"aria-controls":L(r.panel)?r.panelId.value:void 0,disabled:t.disabled?!0:void 0,onKeydown:j,onKeyup:I,onClick:F,onMousedown:Z};return K(H,[m3({ourProps:k,theirProps:{...a,...C},slot:p,attrs:a,slots:d,name:"PopoverButton"}),c&&!$.value&&r.isPortalled.value&&K(d3,{id:w,features:u3.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:_})])}}}),G1=h3({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(t,{attrs:a,slots:d,expose:u}){var m;let g=(m=t.id)!=null?m:`headlessui-popover-panel-${a3()}`,{focus:r}=t,s=w3("PopoverPanel"),i=N(()=>x3(s.panel)),b=`headlessui-focus-sentinel-before-${a3()}`,f=`headlessui-focus-sentinel-after-${a3()}`;u({el:s.panel,$el:s.panel}),C3(()=>{s.panelId.value=g}),v3(()=>{s.panelId.value=null}),B3(U3,s.panelId),f3(()=>{var Z,A;if(!r||s.popoverState.value!==0||!s.panel)return;let _=(Z=i.value)==null?void 0:Z.activeElement;(A=L(s.panel))!=null&&A.contains(_)||Y(L(s.panel),q.First)});let $=L1(),v=N(()=>$!==null?($.value&i3.Open)===i3.Open:s.popoverState.value===0);function w(Z){var A,_;switch(Z.key){case U.Escape:if(s.popoverState.value!==0||!L(s.panel)||i.value&&!((A=L(s.panel))!=null&&A.contains(i.value.activeElement)))return;Z.preventDefault(),Z.stopPropagation(),s.closePopover(),(_=L(s.button))==null||_.focus();break}}function W(Z){var A,_,c,p,C;let k=Z.relatedTarget;k&&L(s.panel)&&((A=L(s.panel))!=null&&A.contains(k)||(s.closePopover(),((c=(_=L(s.beforePanelSentinel))==null?void 0:_.contains)!=null&&c.call(_,k)||(C=(p=L(s.afterPanelSentinel))==null?void 0:p.contains)!=null&&C.call(p,k))&&k.focus({preventScroll:!0})))}let j=N3();function I(){let Z=L(s.panel);if(!Z)return;function A(){Q(j.value,{[G.Forwards]:()=>{var _;Y(Z,q.First)===p3.Error&&((_=L(s.afterPanelSentinel))==null||_.focus())},[G.Backwards]:()=>{var _;(_=L(s.button))==null||_.focus({preventScroll:!0})}})}A()}function F(){let Z=L(s.panel);if(!Z)return;function A(){Q(j.value,{[G.Forwards]:()=>{let _=L(s.button),c=L(s.panel);if(!_)return;let p=L3(),C=p.indexOf(_),k=p.slice(0,C+1),V=[...p.slice(C+1),...k];for(let R of V.slice())if(R.dataset.headlessuiFocusGuard==="true"||c!=null&&c.contains(R)){let r3=V.indexOf(R);r3!==-1&&V.splice(r3,1)}Y(V,q.First,{sorted:!1})},[G.Backwards]:()=>{var _;Y(Z,q.Previous)===p3.Error&&((_=L(s.button))==null||_.focus())}})}A()}return()=>{let Z={open:s.popoverState.value===0,close:s.close},{focus:A,..._}=t,c={ref:s.panel,id:g,onKeydown:w,onFocusout:r&&s.popoverState.value===0?W:void 0,tabIndex:-1};return m3({ourProps:c,theirProps:{...a,..._},attrs:a,slot:Z,slots:{...d,default:(...p)=>{var C;return[K(H,[v.value&&s.isPortalled.value&&K(d3,{id:b,ref:s.beforePanelSentinel,features:u3.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:I}),(C=d.default)==null?void 0:C.call(d,...p),v.value&&s.isPortalled.value&&K(d3,{id:f,ref:s.afterPanelSentinel,features:u3.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:F})])]}},features:_3.RenderStrategy|_3.Static,visible:v.value,name:"PopoverPanel"})}}});function q1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"fill-rule":"evenodd",d:"M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Zm0 5.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z","clip-rule":"evenodd"})])}function K1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{d:"M10 3a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM10 8.5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM11.5 15.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0Z"})])}function J3(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z","clip-rule":"evenodd"})])}function U1(t,a){return l(),o("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{d:"M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"})])}const Y1={class:"fixed inset-0 flex"},J1={class:"absolute top-0 left-full flex w-16 justify-center pt-5"},Q1={class:"flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 ring-1 ring-white/10"},X1={class:"flex h-16 shrink-0 items-center"},e0={class:"flex flex-1 flex-col"},t0={role:"list",class:"flex flex-1 flex-col gap-y-7"},a0={role:"list",class:"-mx-2 space-y-1"},r0=["href"],s0={role:"list",class:"-mx-2 mt-2 space-y-1"},l0=["src"],n0={class:"truncate"},o0={key:0,class:"-mx-6 mt-auto"},i0={href:"/user/profile",class:"flex items-center gap-x-4 px-6 py-3 text-sm leading-6 font-semibold text-white hover:bg-gray-800"},c0=["src","alt"],d0={"aria-hidden":"true"},u0={class:"z-0 hidden xl:fixed xl:inset-y-0 xl:flex xl:w-72 xl:flex-col"},p0={class:"flex grow flex-col gap-y-5 overflow-y-auto bg-slate-50 px-6 ring-1 ring-white/5 lg:border-r lg:border-slate-300"},h0={class:"flex flex-1 flex-col"},f0={role:"list",class:"flex flex-1 flex-col gap-y-7"},C0={role:"list",class:"-mx-2 mt-2 space-y-1"},v0={key:0,class:"group"},g0={class:"flex justify-between text-xs leading-6 font-semibold text-gray-500 uppercase"},m0={class:"opacity-0 transition duration-250 group-hover:opacity-100"},x0={role:"list",class:"-mx-2 mt-2"},L0=["src"],w0={class:"truncate"},y0={key:1,class:"group -mx-2 mt-2 space-y-1"},b0={key:2,class:"-mx-6 mt-auto"},_0={class:"flex items-center gap-x-4 px-6 py-3 text-sm leading-6 font-semibold text-gray-900 hover:bg-slate-100"},$0=["src","alt"],k0={"aria-hidden":"true"},M0={__name:"SideNav",props:["sidebarOpen","authenticated"],emits:["update:sidebar"],setup(t,{emit:a}){const d=[{name:y().props.authenticated?"Dashboard":"Home",href:y().props.authenticated?"/dashboard":"/",icon:G3,current:y().url.startsWith("/dashboard"),color:"indigo"},{name:"Watch",href:"/watch",icon:F3,current:y().url.startsWith("/watch"),color:"pink"},{name:"Read",href:"/read",icon:z3,current:y().url.startsWith("/read"),color:"sky"},{name:"Practice",href:"/practice",icon:O3,current:y().url.startsWith("/practice"),color:"amber"},{name:"Words",href:"/words",icon:I3,current:y().url.startsWith("/words"),color:"teal"},{name:"Infinitas",href:"/infinitas",icon:B1,current:y().url.startsWith("/infinitas"),color:"purple"}],u=a;let m=B(y().props.user?y().props.user.classes:[]);const g=()=>{axios.post(route("logout")).then(()=>{window.location.href="/"}).catch(r=>{})};return H3(()=>y().props.user,r=>{r&&(m.value=r.classes)},{deep:!0}),(r,s)=>(l(),o(H,null,[h(n(R3),{as:"template",show:t.sidebarOpen},{default:x(()=>[h(n(D3),{as:"div",class:"relative z-40 xl:hidden",onClose:s[1]||(s[1]=i=>u("update:sidebar",!1))},{default:x(()=>[h(n(t3),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:x(()=>s[3]||(s[3]=[e("div",{class:"fixed inset-0 bg-gray-900/80"},null,-1)])),_:1}),e("div",Y1,[h(n(t3),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"-translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"-translate-x-full"},{default:x(()=>[h(n(W3),{class:"relative mr-16 flex w-full max-w-xs flex-1"},{default:x(()=>[h(n(t3),{as:"template",enter:"ease-in-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in-out duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:x(()=>[e("div",J1,[e("button",{type:"button",class:"-m-2.5 p-2.5",onClick:s[0]||(s[0]=i=>u("update:sidebar",!1))},[s[4]||(s[4]=e("span",{class:"sr-only"},"Close sidebar",-1)),h(n(p1),{class:"h-6 w-6 text-white","aria-hidden":"true"})])])]),_:1}),e("div",Q1,[e("div",X1,[h(I1,{class:"h-6"})]),e("nav",e0,[e("ul",t0,[e("li",null,[e("ul",a0,[(l(),o(H,null,O(d,i=>e("li",{key:i.name},[e("a",{href:i.href,class:S([i.current?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white","group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold"])},[(l(),T(e3(i.icon),{class:"h-6 w-6 shrink-0","aria-hidden":"true"})),D(" "+P(i.name),1)],10,r0)])),64))])]),e("li",null,[s[5]||(s[5]=e("div",{class:"text-xs leading-6 font-semibold text-gray-400 uppercase"}," Your Classes ",-1)),e("ul",s0,[(l(!0),o(H,null,O(n(m),i=>(l(),o("li",{key:i.name},[h(n(J),{prefetch:"",href:`/classes/${i.slug}`,class:S([n(y)().url.startsWith(`/classes/${i.slug}`)?"bg-gray-800 text-white":"text-gray-400 hover:bg-gray-800 hover:text-white","group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold"])},{default:x(()=>[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/${i.image}`,class:"mr-2 flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-[0.625rem] font-medium text-gray-400 group-hover:text-gray-500"},null,8,l0),e("span",n0,P(i.name),1)]),_:2},1032,["href","class"])]))),128))])]),t.authenticated?(l(),o("li",o0,[e("a",i0,[e("img",{class:"h-8 w-8 rounded-full bg-gray-800",src:n(y)().props.user.profile_photo_url,alt:n(y)().props.user.name},null,8,c0),s[6]||(s[6]=e("span",{class:"sr-only"},"Your profile",-1)),e("span",d0,P(n(y)().props.user.name),1)])])):z("",!0)])])])]),_:1})]),_:1})])]),_:1})]),_:1},8,["show"]),e("div",u0,[e("div",p0,[h(n(J),{href:"/dashboard",prefetch:"",class:"flex h-16 shrink-0 items-center"},{default:x(()=>[h(V1,{class:"h-6"})]),_:1}),e("nav",h0,[e("ul",f0,[e("li",null,[s[8]||(s[8]=e("div",{class:"text-xs leading-6 font-semibold text-gray-500 uppercase"}," Navigation ",-1)),e("ul",C0,[(l(),o(H,null,O(d,i=>e("li",{key:i.name},[h(n(J),{prefetch:"",href:i.href,class:S([i.current?"bg-white text-gray-800":"text-gray-500 hover:bg-slate-100 hover:text-gray-700","group flex gap-x-3 rounded-lg p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out"])},{default:x(()=>[(l(),T(e3(i.icon),{class:S(["h-6 w-6 shrink-0 stroke-2",[i.current?"":`group-hover:text-${i.color}-600`,`text-${i.color}-500`]]),"aria-hidden":"true"},null,8,["class"])),s[7]||(s[7]=e("div",{class:"hidden text-amber-500 text-amber-600 text-indigo-500 text-indigo-600 text-pink-500 text-pink-600 text-purple-500 text-purple-600 text-sky-500 text-sky-600 text-teal-500 text-teal-600"},null,-1)),D(" "+P(i.name),1)]),_:2},1032,["href","class"])])),64))])]),n(m).length>0?(l(),o("li",v0,[e("div",g0,[s[10]||(s[10]=e("span",null,"Your Classes",-1)),e("span",m0,[h(n(J),{prefetch:"",href:"/classes",class:"text-blue-600 transition duration-250 hover:text-blue-700"},{default:x(()=>s[9]||(s[9]=[D("View All")])),_:1})])]),e("ul",x0,[(l(!0),o(H,null,O(n(m),i=>(l(),o("li",{key:i.name},[h(n(J),{prefetch:"",href:`/classes/${i.slug}`,class:S([n(y)().url.startsWith(`/classes/${i.slug}`)?"bg-white text-gray-800":"text-gray-500 hover:bg-slate-100 hover:text-gray-700","group flex gap-x-1 rounded-md p-2 text-sm leading-6 font-semibold"])},{default:x(()=>[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/${i.image}`,class:"mr-2 flex h-6 w-6 shrink-0 items-center justify-center rounded-full text-[0.625rem] font-medium text-gray-400 group-hover:text-gray-500"},null,8,L0),e("span",w0,P(i.name),1)]),_:2},1032,["href","class"])]))),128))])])):t.authenticated?(l(),o("li",y0,[h(n(J),{prefetch:"",href:"/classes",class:S(["group flex gap-x-3 rounded-lg p-2 text-sm leading-6 font-semibold text-gray-500 transition duration-150 ease-in-out hover:bg-slate-100 hover:text-gray-700",[n(y)().url.startsWith("/classes")?"bg-white text-gray-800":""]])},{default:x(()=>[(l(),T(e3(n(A1)),{class:"h-6 w-6 shrink-0 stroke-2 text-slate-500 group-hover:text-slate-600","aria-hidden":"true"})),s[11]||(s[11]=D("Join or Make a Class "))]),_:1},8,["class"])])):z("",!0),t.authenticated?(l(),o("li",b0,[e("div",_0,[e("img",{class:"h-8 w-8 rounded-full bg-gray-800",src:n(y)().props.user.profile_photo_url,alt:n(y)().props.user.name},null,8,$0),s[13]||(s[13]=e("span",{class:"sr-only"},"Your profile",-1)),e("span",k0,P(n(y)().props.user.name),1),h(n(C1),{as:"div",class:"relative ml-auto"},{default:x(()=>[h(n(h1),{class:"-m-2.5 block pl-2.5 text-gray-400 hover:text-gray-500"},{default:x(()=>[s[12]||(s[12]=e("span",{class:"sr-only"},"Open options",-1)),h(n(K1),{class:"h-5 w-5","aria-hidden":"true"})]),_:1}),h(j3,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:x(()=>[h(n(f1),{class:"absolute -top-24 right-0 z-10 -mt-2 -mr-4 w-32 origin-top-right rounded-md bg-white py-2 ring-1 shadow-lg ring-gray-900/5 focus:outline-hidden"},{default:x(()=>[h(n(b3),null,{default:x(({active:i})=>[e("a",{href:"/user/profile",class:S([i?"bg-gray-100":"","block px-3 py-1 text-sm leading-6 text-gray-500 hover:text-gray-700"])},"View Profile",2)]),_:1}),h(n(b3),null,{default:x(({active:i})=>[e("p",{onClick:s[2]||(s[2]=b=>g()),class:S([i?"bg-gray-100":"","block cursor-pointer px-3 py-1 text-sm leading-6 text-gray-500 hover:text-gray-700"])}," Logout ",2)]),_:1})]),_:1})]),_:1})]),_:1})])])):z("",!0)])])])])],64))}},Z0={class:"w-56 shrink flex-auto rounded-xl bg-white py-2 text-sm leading-6 shadow-lg ring-1 ring-gray-900/5"},A0=["href"],S0={class:"border-t border-gray-200 py-2 my-2"},P0=["href"],T0={key:0,class:"border-y border-gray-200 py-2 my-2"},B0={__name:"TopMenu",setup(t){const a=[{name:"About",href:route("about")},{name:"Contact",href:route("contact")},{name:"Privacy Policy",href:"/privacy-policy"},{name:"Terms of Service",href:"/terms-of-service"}],d=[{name:"Achievements",href:route("achievements.index"),show:!0},{name:"Activities",href:route("practice.activities.index"),show:!0},{name:"Assignments",href:route("assignments.index"),show:y().props.user.classes.length>0},{name:"Classes",href:route("classes.index"),show:!0},{name:"Profile",href:"/user/profile",show:!0}],u=()=>{axios.post(route("logout")).then(()=>{window.location.href="/"}).catch(m=>{console.log(m)})};return(m,g)=>(l(),T(n(Y3),{class:"relative"},{default:x(()=>[h(n(R1),{class:"border border-gray-300 inline-flex items-center gap-x-1 text-sm font-semibold leading-6 text-gray-500 hover:text-gray-700 px-3 py-0.5 rounded-xl focus:outline-hidden"},{default:x(()=>[g[0]||(g[0]=e("span",null,"Resources",-1)),h(n(v1),{class:"h-5 w-5","aria-hidden":"true"})]),_:1}),h(j3,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 translate-y-1","enter-to-class":"opacity-100 translate-y-0","leave-active-class":"transition ease-in duration-150","leave-from-class":"opacity-100 translate-y-0","leave-to-class":"opacity-0 translate-y-1"},{default:x(()=>[h(n(G1),{class:"absolute right-0 z-10 mt-5 w-56 flex max-w-min pr-4 w-screen"},{default:x(()=>[e("div",Z0,[(l(),o(H,null,O(d,r=>r1(e("a",{key:r.name,href:r.href,class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,P(r.name),1)],8,A0),[[s1,r.show]])),64)),e("div",S0,[(l(),o(H,null,O(a,r=>e("a",{key:r.name,href:r.href,class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,P(r.name),1)],8,P0)),64))]),m.$page.props.user.id===1?(l(),o("div",T0,g[1]||(g[1]=[e("a",{href:"/admin",target:"_blank",class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,"Filament")],-1),e("a",{href:"/pulse",target:"_blank",class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,"Pulse")],-1),e("a",{href:"/telescope",target:"_blank",class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,"Telescope")],-1),e("a",{href:"/dev",class:"relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"},[e("p",null,"Developer")],-1)]))):z("",!0),e("div",{onClick:u,class:"cursor-pointer relative block font-semibold px-4 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-slate-100 dark:text-gray-300 dark:hover:bg-gray-800"}," Sign Out ")])]),_:1})]),_:1})]),_:1}))}};function H0(t){var a=typeof t;return t!=null&&(a=="object"||a=="function")}var y3=H0,j0=typeof l3=="object"&&l3&&l3.Object===Object&&l3,E0=j0,V0=E0,F0=typeof self=="object"&&self&&self.Object===Object&&self,z0=V0||F0||Function("return this")(),Q3=z0,O0=Q3,I0=function(){return O0.Date.now()},N0=I0,D0=/\s/;function W0(t){for(var a=t.length;a--&&D0.test(t.charAt(a)););return a}var R0=W0,G0=R0,q0=/^\s+/;function K0(t){return t&&t.slice(0,G0(t)+1).replace(q0,"")}var U0=K0,Y0=Q3,J0=Y0.Symbol,X3=J0,M3=X3,e1=Object.prototype,Q0=e1.hasOwnProperty,X0=e1.toString,X=M3?M3.toStringTag:void 0;function e5(t){var a=Q0.call(t,X),d=t[X];try{t[X]=void 0;var u=!0}catch{}var m=X0.call(t);return u&&(a?t[X]=d:delete t[X]),m}var t5=e5,a5=Object.prototype,r5=a5.toString;function s5(t){return r5.call(t)}var l5=s5,Z3=X3,n5=t5,o5=l5,i5="[object Null]",c5="[object Undefined]",A3=Z3?Z3.toStringTag:void 0;function d5(t){return t==null?t===void 0?c5:i5:A3&&A3 in Object(t)?n5(t):o5(t)}var u5=d5;function p5(t){return t!=null&&typeof t=="object"}var h5=p5,f5=u5,C5=h5,v5="[object Symbol]";function g5(t){return typeof t=="symbol"||C5(t)&&f5(t)==v5}var m5=g5,x5=U0,S3=y3,L5=m5,P3=0/0,w5=/^[-+]0x[0-9a-f]+$/i,y5=/^0b[01]+$/i,b5=/^0o[0-7]+$/i,_5=parseInt;function $5(t){if(typeof t=="number")return t;if(L5(t))return P3;if(S3(t)){var a=typeof t.valueOf=="function"?t.valueOf():t;t=S3(a)?a+"":a}if(typeof t!="string")return t===0?t:+t;t=x5(t);var d=y5.test(t);return d||b5.test(t)?_5(t.slice(2),d?2:8):w5.test(t)?P3:+t}var k5=$5,M5=y3,c3=N0,T3=k5,Z5="Expected a function",A5=Math.max,S5=Math.min;function P5(t,a,d){var u,m,g,r,s,i,b=0,f=!1,$=!1,v=!0;if(typeof t!="function")throw new TypeError(Z5);a=T3(a)||0,M5(d)&&(f=!!d.leading,$="maxWait"in d,g=$?A5(T3(d.maxWait)||0,a):g,v="trailing"in d?!!d.trailing:v);function w(p){var C=u,k=m;return u=m=void 0,b=p,r=t.apply(k,C),r}function W(p){return b=p,s=setTimeout(F,a),f?w(p):r}function j(p){var C=p-i,k=p-b,V=a-C;return $?S5(V,g-k):V}function I(p){var C=p-i,k=p-b;return i===void 0||C>=a||C<0||$&&k>=g}function F(){var p=c3();if(I(p))return Z(p);s=setTimeout(F,j(p))}function Z(p){return s=void 0,v&&u?w(p):(u=m=void 0,r)}function A(){s!==void 0&&clearTimeout(s),b=0,u=i=m=s=void 0}function _(){return s===void 0?r:Z(c3())}function c(){var p=c3(),C=I(p);if(u=arguments,m=this,i=p,C){if(s===void 0)return W(i);if($)return clearTimeout(s),s=setTimeout(F,a),w(i)}return s===void 0&&(s=setTimeout(F,a)),r}return c.cancel=A,c.flush=_,c}var t1=P5;const S7=E3(t1);var T5=t1,B5=y3,H5="Expected a function";function j5(t,a,d){var u=!0,m=!0;if(typeof t!="function")throw new TypeError(H5);return B5(d)&&(u="leading"in d?!!d.leading:u,m="trailing"in d?!!d.trailing:m),T5(t,a,{leading:u,maxWait:a,trailing:m})}var E5=j5;const V5=E3(E5),F5={class:"fixed inset-0 z-10 overflow-y-auto p-4 sm:p-6 md:p-20"},z5={class:"relative"},O5={key:0},I5={class:"ml-4 flex-auto"},N5={key:1},D5={key:0,class:"mb-4"},W5={class:"mt-2 text-gray-800"},R5={class:"flex grow flex-col"},G5={class:"text-sm font-semibold text-gray-800"},q5={class:"line-clamp-1 text-xs text-gray-500"},K5={key:1,class:"mb-4 border-t border-gray-300"},U5={class:"mt-2 text-gray-800"},Y5={class:"flex w-24 shrink-0 items-center rounded-lg"},J5=["src"],Q5={class:"flex grow flex-col font-intro"},X5={class:"text-base font-semibold text-gray-800"},e2={class:"line-clamp-3 text-xs text-gray-500"},t2={key:2,class:"mb-4 border-t border-gray-300"},a2={class:"mt-2 text-gray-800"},r2={class:"flex h-12 w-12 shrink-0 items-center rounded-lg"},s2=["src"],l2={class:"ml-2 flex grow flex-col font-intro"},n2={class:"text-base font-semibold text-gray-800"},o2={class:"line-clamp-3 text-xs text-gray-500"},i2={key:2,class:"border-t border-gray-100 px-6 py-14 text-center text-sm sm:px-14"},c2={key:0,class:"mt-2 flex w-full justify-end border-t border-gray-100 px-4 py-4 text-xs font-medium text-gray-400"},d2={__name:"SearchPalette",props:{open:Boolean},emits:["update:close"],setup(t,{emit:a}){const d=B("");let u={videos:[],words:[],sections:[]},m=B(!1);const g=[{name:"Dashboard",description:"Return to the dashboard",url:"/dashboard",icon:G3,color:"bg-indigo-500"},{name:"Videos",description:"Find videos on LatinTutorial",url:"/watch",icon:F3,color:"bg-pink-500"},{name:"Read",description:"Put your Latin to use in reading",url:"/read",icon:z3,color:"bg-sky-500"},{name:"Practice",description:"Keep your Latin skills sharp",url:"/practice",icon:O3,color:"bg-amber-500"},{name:"Words",description:"Search through the list of Latin words on this site",url:"/words",icon:I3,color:"bg-teal-500"}],r=a;function s(b){window.location=b.url}const i=()=>{d.value="",u.value={videos:[],words:[],sections:[]},r("update:close")};return H3(()=>d.value,V5(b=>{b.length>2?(m.value=!0,axios.post("/search",{query:b}).then(f=>{u=f.data,m.value=!1}).catch(f=>{console.log(f)})):u={videos:[],words:[],sections:[]}},250)),(b,f)=>{const $=l1("Link");return l(),T(n(R3),{show:t.open,as:"template",onAfterLeave:f[2]||(f[2]=v=>d.value=""),appear:""},{default:x(()=>[h(n(D3),{as:"div",class:"relative z-10",onClose:f[1]||(f[1]=v=>i())},{default:x(()=>[h(n(t3),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:x(()=>f[3]||(f[3]=[e("div",{class:"fixed inset-0 bg-gray-500/75 transition-opacity"},null,-1)])),_:1}),e("div",F5,[h(n(t3),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:x(()=>[h(n(W3),{class:"mx-auto max-w-xl transform overflow-hidden rounded-xl bg-white shadow-2xl transition-all"},{default:x(()=>[h(n(b1),{"onUpdate:modelValue":s},{default:x(()=>[e("div",z5,[h(n(J3),{class:"pointer-events-none absolute top-3.5 left-4 h-5 w-5 text-gray-400","aria-hidden":"true"}),h(n(_1),{class:"h-12 w-full border-0 bg-transparent pr-4 pl-11 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",onChange:f[0]||(f[0]=v=>d.value=v.target.value)})]),d.value.length<=2?(l(),o("div",O5,[g?(l(),T(n(k3),{key:0,static:"",class:"max-h-96 scroll-py-3 overflow-y-auto p-3"},{default:x(()=>[(l(),o(H,null,O(g,v=>h(n(n3),{key:v.id,value:v,as:"template"},{default:x(({active:w})=>[e("li",{class:S(["flex cursor-pointer rounded-xl p-3 select-none",w&&"bg-slate-200"])},[e("div",{class:S(["flex h-10 w-10 flex-none items-center justify-center rounded-lg",v.color])},[(l(),T(e3(v.icon),{class:"h-6 w-6 text-white","aria-hidden":"true"}))],2),e("div",I5,[e("p",{class:S(["text-sm font-medium",w?"text-gray-900":"text-gray-700"])},P(v.name),3),e("p",{class:S(["text-sm",w?"text-gray-700":"text-gray-500"])},P(v.description),3)])],2)]),_:2},1032,["value"])),64))]),_:1})):z("",!0)])):(l(),o("div",N5,[h(n(k3),{static:"",class:"max-h-96 scroll-py-3 overflow-y-auto p-3"},{default:x(()=>[n(u).words.length>0?(l(),o("li",D5,[f[5]||(f[5]=e("h2",{class:"px-4 py-2.5 text-xs font-semibold text-gray-900"}," Words ",-1)),e("ul",W5,[(l(!0),o(H,null,O(n(u).words.slice(0,5),v=>(l(),T(n(n3),{key:v.id,value:v,as:"template"},{default:x(({active:w})=>[e("li",{class:S(["flex cursor-default cursor-pointer gap-4 rounded-xl p-3 select-none",w&&"bg-slate-200"])},[e("div",R5,[e("h4",G5,P(v.display_word),1),e("p",q5,P(v.short_def),1)])],2)]),_:2},1032,["value"]))),128)),e("li",{class:S(["flex cursor-default cursor-pointer gap-4 rounded-xl py-3 text-center select-none",b.active&&"bg-slate-200"])},[h($,{href:`/words?search=${d.value}&sort=relevance`,class:"mx-auto"},{default:x(()=>f[4]||(f[4]=[e("a",{class:"block text-center text-sm font-semibold text-blue-600 hover:underline"}," See more results ",-1)])),_:1},8,["href"])],2)])])):z("",!0),n(u).videos.length>0?(l(),o("li",K5,[f[7]||(f[7]=e("h2",{class:"px-4 py-2.5 text-xs font-semibold text-gray-900"}," Videos ",-1)),e("ul",U5,[(l(!0),o(H,null,O(n(u).videos.slice(0,3),v=>(l(),T(n(n3),{key:v.id,value:v,as:"template"},{default:x(({active:w})=>[e("li",{class:S(["flex cursor-default cursor-pointer gap-4 rounded-xl p-3 select-none",w&&"bg-slate-200"])},[e("div",Y5,[e("img",{src:v.thumbnail,class:"self-center rounded-lg bg-gray-300"},null,8,J5)]),e("div",Q5,[e("h4",X5,P(v.title),1),e("p",e2,P(v.description),1)])],2)]),_:2},1032,["value"]))),128)),e("li",{class:S(["flex cursor-default cursor-pointer gap-4 rounded-xl py-3 text-center select-none",b.active&&"bg-slate-200"])},[h($,{href:`/watch?search=${d.value}&liked=false&unrated=false&sort=newest`,class:"mx-auto"},{default:x(()=>f[6]||(f[6]=[e("a",{class:"block text-center text-sm font-semibold text-blue-600 hover:underline"}," See more results ",-1)])),_:1},8,["href"])],2)])])):z("",!0),n(u).sections.length>0?(l(),o("li",t2,[f[9]||(f[9]=e("h2",{class:"px-4 py-2.5 text-xs font-semibold text-gray-900"}," Readings ",-1)),e("ul",a2,[(l(!0),o(H,null,O(n(u).sections.slice(0,3),v=>(l(),T(n(n3),{key:v.id,value:v,as:"template"},{default:x(({active:w})=>[e("li",{class:S(["flex cursor-default cursor-pointer items-center gap-4 rounded-xl p-3 select-none",w&&"bg-slate-200"])},[e("div",r2,[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${v.icon}`,class:"self-center rounded-full bg-gray-300"},null,8,s2)]),e("div",l2,[e("h4",n2,P(v.title),1),e("p",o2,P(v.description),1)])],2)]),_:2},1032,["value"]))),128)),e("li",{class:S(["flex cursor-default cursor-pointer gap-4 rounded-xl py-3 text-center select-none",b.active&&"bg-slate-200"])},[h($,{href:"/read",class:"mx-auto"},{default:x(()=>f[8]||(f[8]=[e("a",{class:"block text-center text-sm font-semibold text-blue-600 hover:underline"}," See all readings ",-1)])),_:1})],2)])])):z("",!0)]),_:1})])),d.value.length>2&&n(u).sections.length===0&&n(u).videos.length===0&&n(u).words.length===0&&!n(m)?(l(),o("div",i2,[h(n(Z1),{class:"mx-auto h-6 w-6 text-gray-400","aria-hidden":"true"}),f[10]||(f[10]=e("p",{class:"mt-4 font-semibold text-gray-900"},"No results found",-1)),f[11]||(f[11]=e("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ",-1))])):z("",!0)]),_:1}),d.value.length>2?(l(),o("div",c2,f[12]||(f[12]=[e("p",null,[D(" Search powered by "),e("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/img/algolia-logo-blue.svg",class:"ml-2 inline h-4"})],-1)]))):z("",!0)]),_:1})]),_:1})])]),_:1})]),_:1},8,["show"])}}},u2={},p2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function h2(t,a){return l(),o("svg",p2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M59.93,49.34h.22a5.23,5.23,0,0,1,5.08-4,4.47,4.47,0,0,1,.91.08,13.27,13.27,0,0,1-5-8.75c-.29-2.71.41-8.07,4.85-11a3.19,3.19,0,0,1,.38-.26v-.24H33a7.35,7.35,0,0,1,.85.51c5.56,3.72,4.87,10.73,4.84,11a13.28,13.28,0,0,1-4.95,8.68h.3a5.23,5.23,0,0,1,5.07,4h.22a5.22,5.22,0,0,1,10.17,0h.22a5.22,5.22,0,0,1,10.17,0Z"></path><path d="M30.55,50.56V84.22H37.6V50.56a3.53,3.53,0,0,0-7,0Z"></path><path d="M40.94,50.56V84.22H48V50.56a3.53,3.53,0,0,0-7.05,0Z"></path><path d="M54.85,47a3.53,3.53,0,0,0-3.53,3.52V84.22h7V50.56A3.52,3.52,0,0,0,54.85,47Z"></path><path d="M68.76,50.56a3.53,3.53,0,0,0-7.05,0V84.22h7.05Z"></path><path d="M24.1,47h.5C30.66,47,36.4,42.17,37,36.45c0-.25.62-6.25-4.08-9.41a9.62,9.62,0,0,0-11.62.83,7.91,7.91,0,0,0-2.49,6.6,6.15,6.15,0,0,0,2.25,4c2.21,1.73,6,2,7.94-.41A5.14,5.14,0,0,0,30,34a2.31,2.31,0,1,0-4.56.72c0,.26,0,.45,0,.42a1.8,1.8,0,0,1-1.53-.29,1.62,1.62,0,0,1-.53-1.09,3.48,3.48,0,0,1,1.11-2.58,5,5,0,0,1,5.85-.33c2.38,1.59,2.07,5.05,2.07,5.08-.38,3.52-4.26,6.59-8.12,6.4-4.21-.18-7.24-4.31-7.75-8.18A9.89,9.89,0,0,1,27.87,23.36l43.82,0,.31,0A9.89,9.89,0,0,1,83.32,34.18c-.5,3.87-3.54,8-7.75,8.18-3.87.18-7.74-2.88-8.12-6.39,0,0-.31-3.5,2.07-5.09a5,5,0,0,1,5.85.33,3.48,3.48,0,0,1,1.11,2.58A1.62,1.62,0,0,1,76,34.88a2.13,2.13,0,0,1-1.48.36,1.13,1.13,0,0,1,0-.49A2.31,2.31,0,0,0,69.9,34a5.14,5.14,0,0,0,1,4.07c1.93,2.39,5.73,2.14,7.94.41a6.26,6.26,0,0,0,2.25-4,7.88,7.88,0,0,0-2.49-6.6A9.6,9.6,0,0,0,67,27c-3.75,2.51-4.34,7.09-4.09,9.41.63,5.88,6.66,10.77,12.91,10.52,6.89-.3,11.39-6.51,12.12-12.2a14.48,14.48,0,0,0-16.34-16H28.32A14.48,14.48,0,0,0,12,34.77C12.71,40.46,17.21,46.67,24.1,47Z"></path></g></g>',1)]))}const f2=M(u2,[["render",h2]]),C2={},v2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function g2(t,a){return l(),o("svg",v2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><rect x="31.2" y="73.35" width="37.56" height="4.41"></rect><path d="M55.35,62.53a2.21,2.21,0,0,0,0-4.41H44.6a2.21,2.21,0,0,0,0,4.41Z"></path><path d="M7,29.49a2,2,0,0,0-2.33,1.7A2.14,2.14,0,0,0,6.21,33.7,64,64,0,0,1,15,36.16c.88.31,1.76.66,2.63,1a60,60,0,0,1-4.21-6A64.74,64.74,0,0,0,7,29.49Z"></path><path d="M50,29.28,16.14,29A54.12,54.12,0,0,0,29.19,45.43,49.65,49.65,0,0,0,47,55.1h6a49.65,49.65,0,0,0,17.79-9.67A54.19,54.19,0,0,0,83.82,29Z"></path><path d="M48.43,70.6v0h3.09v0l16.9-.14-13.93-4.9L50,65.68l-4.51-.12-13.94,4.9Z"></path><path d="M92.93,29.49a64.92,64.92,0,0,0-6.52,1.67,58.15,58.15,0,0,1-4.2,6c.87-.35,1.74-.7,2.63-1a63.2,63.2,0,0,1,8.83-2.46,2.14,2.14,0,0,0,1.57-2.51A2,2,0,0,0,92.93,29.49Z"></path></g></g>',1)]))}const m2=M(C2,[["render",g2]]),x2={},L2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function w2(t,a){return l(),o("svg",L2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M28.88,39.24a24.81,24.81,0,0,1,7.44-7.58,24.55,24.55,0,0,1,13.75-3.77,24.38,24.38,0,0,1,13.61,3.77,25.41,25.41,0,0,1,7.44,7.58,27.26,27.26,0,0,1,4.2,11.08L86.23,48a39.59,39.59,0,0,0-1.3-7.64A38.59,38.59,0,0,0,81.52,32L75.89,34c1.32-1.59,2.66-3.16,4-4.73,0,0-3.1-3.5-4.69-5.09a30.13,30.13,0,0,0-8.81-6c-1.67,1.46-3.34,2.94-5,4.4.21-2.16.42-4.33.63-6.48a36.59,36.59,0,0,0-24,0c.21,2.15.42,4.32.63,6.48-1.68-1.46-3.35-2.94-5-4.4a30.13,30.13,0,0,0-8.81,6c-1.59,1.59-4.69,5.09-4.69,5.09,1.33,1.57,2.67,3.14,4,4.73L18.48,32a38.59,38.59,0,0,0-3.41,8.37A39.59,39.59,0,0,0,13.77,48l10.91,2.36A27.26,27.26,0,0,1,28.88,39.24Z"></path><path d="M51.62,37.65V46A15.82,15.82,0,0,1,65.71,60,133.28,133.28,0,0,0,49.85,59h-.36a135.53,135.53,0,0,0-15.18.87A15.78,15.78,0,0,1,48.4,46V37.68a20.43,20.43,0,0,0-6.22,1.57,18.58,18.58,0,0,0-10,12l-4.72-.6c.33-1.85,2-10.9,10-16.08a23.15,23.15,0,0,1,10.09-3.66,22.45,22.45,0,0,1,4.89,0,23.14,23.14,0,0,1,10.08,3.66c8,5.18,9.71,14.23,10,16.08l-4.71.6a23.8,23.8,0,0,0-2.61-5.46,18.52,18.52,0,0,0-7.42-6.55A16.8,16.8,0,0,0,51.62,37.65Z"></path><path d="M47.77,86.18c-.13,1.79-4,3.67-7.37,3a8.08,8.08,0,0,1-4.74-3.32c-1.53-2-1.82-4.09-2.12-6.34A74.48,74.48,0,0,1,33.15,72c0-1.14-.1-2.19-.14-3.1.67-.08,1.36-.18,2.09-.26,1-.13,2.11-.24,3.17-.36-.1,4.11.95,5.87,2.08,6.64,1,.69,2.3.84,3,2.17s0,2.06.29,3.61C44.24,83.89,47.91,84.39,47.77,86.18Z"></path><path d="M31.11,76.54c-1.18.57-8.48,4.12-9,5.49a.35.35,0,0,0,0,.25c.27.58,3-.44,6.53-.79a26.89,26.89,0,0,1,3.08-.12c-.18-.88-.33-1.85-.45-2.88Q31.16,77.47,31.11,76.54Z"></path><path d="M67,69c0,.9-.09,1.92-.13,3-.16,4.21-.11,5.25-.4,7.49s-.59,4.35-2.12,6.34a8.08,8.08,0,0,1-4.74,3.32c-3.33.71-7.24-1.17-7.37-3s3.53-2.29,4.11-5.49c.28-1.55-.41-2.34.28-3.61s2.05-1.48,3-2.17c1.13-.77,2.16-2.51,2.09-6.57,1,.1,2.08.21,3.11.34l2,.25A.68.68,0,0,0,67,69Z"></path><path d="M68.89,76.54q0,.93-.15,1.95c-.12,1-.27,2-.45,2.88a26.89,26.89,0,0,1,3.08.12c3.53.35,6.26,1.37,6.53.79a.35.35,0,0,0,0-.25C77.37,80.66,70.07,77.11,68.89,76.54Z"></path><path d="M68.12,63.44v2.24a.86.86,0,0,1-.87.87h-.11c-.65-.09-1.31-.19-2-.27a122.61,122.61,0,0,0-15.39-1h-.34c-4,0-7.76.21-11.22.55-1.16.11-2.29.23-3.39.37-.82.08-1.62.19-2.41.31h-.13a.85.85,0,0,1-.85-.86V63.44a.87.87,0,0,1,.74-.86l2-.26a140.56,140.56,0,0,1,15.29-.89h.34a132.21,132.21,0,0,1,16,1c.53.07,1,.15,1.58.2A.86.86,0,0,1,68.12,63.44Z"></path></g></g>',1)]))}const y2=M(x2,[["render",w2]]),b2={},_2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function $2(t,a){return l(),o("svg",_2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M45,51.68c-.54,0-1.12.08-1.7.08a12.28,12.28,0,0,1-2.69-.26,20.1,20.1,0,0,0,3,.23C44.1,51.73,44.54,51.71,45,51.68Z"></path><path d="M28.4,50.27l.4.14Z"></path><path d="M45.22,51.65c.32,0,.64-.07,1-.1a.08.08,0,0,1-.06,0H46Z"></path><path d="M54.34,48.9c-2.29.77-4.07,2-7.23,2.51a6.27,6.27,0,0,1-.93.14A15.67,15.67,0,0,0,49,50.91a16.83,16.83,0,0,0,2.13-.85,21.37,21.37,0,0,1,2.65-1C54,49,54.15,49,54.34,48.9Z"></path><path d="M39.11,49.21a1.25,1.25,0,0,1-.07.26l-.22.83A1.49,1.49,0,0,1,39.11,49.21Z"></path><path d="M63.26,38.77l-.31,6H60.74A26.87,26.87,0,0,1,61,41.56c.17-1.35.37-2.63.45-4.2.22.19.43.37.65.54s.74.56,1.1.82Z"></path><path d="M69.76,91h3.07V44.78H69.76Z"></path><path d="M41.92,83.36c-1.19,1.15-1.78,1.73-1.73,1.91.24,1,5.4,1.25,7.41.17a1.53,1.53,0,0,0,.69-.64c.3-.67-.32-1.36-.69-1.94s-.44-1.47-.64-3.35a32.67,32.67,0,0,1,0-6.22c.25-2.54.56-2.86.41-4.35a11.37,11.37,0,0,0-1.09-3.77c.07-.35.11-.72.18-1.09-2.21.42-4.14.77-5.82,1,.09,2.79-.48,3.08,0,5.5a30.11,30.11,0,0,0,2.18,7.12,2.66,2.66,0,0,1,.51,1.91A9.28,9.28,0,0,0,43,80.83c0,.57.07.67,0,1C42.91,82.4,42.56,82.72,41.92,83.36Z"></path><path d="M53.4,82.86c-.37.58-1,1.27-.67,1.94a1.32,1.32,0,0,0,.67.64c2,1.08,7.17.85,7.43-.17,0-.18-.56-.76-1.73-1.91-.65-.64-1-1-1.08-1.51-.06-.35,0-.45,0-1a9.28,9.28,0,0,0-.29-1.22,2.53,2.53,0,0,1,.52-1.91,22.76,22.76,0,0,0,1.66-4.83c0-.05,0-.11,0-.16a5.14,5.14,0,0,1,.48-3.33c.06-.15.14-.34.19-.5l-.75-.1a6.83,6.83,0,0,1-4.22-1.57,5,5,0,0,1-1.12-1.39,10.92,10.92,0,0,0-.81,3.12c-.14,1.49.16,1.81.4,4.35a31.08,31.08,0,0,1,0,6.22C53.84,81.39,53.76,82.33,53.4,82.86Z"></path><rect x="23.51" y="92.82" width="52.97" height="4.99"></rect><rect x="38.6" y="87.78" width="22.81" height="3.21"></rect><path d="M33.09,58.82a3.22,3.22,0,0,0-1.38,2.5,3,3,0,0,0,.5,1.63h-2V91H27.18V49.72l.14.07a6.46,6.46,0,0,0,1.07.48l.41.14a9.58,9.58,0,0,0,1.09.24,3.59,3.59,0,0,0,.35.08v5.46h2.9a1.09,1.09,0,0,0-.11.27A3.6,3.6,0,0,0,33.09,58.82Z"></path><path d="M64.49,30.49l-.35,6.93c-.34-.24-.69-.5-1-.77a19.44,19.44,0,0,1-3.25-3.29c0,.29,0,.74,0,1.29a37.83,37.83,0,0,1-.49,6.69,29.15,29.15,0,0,0-.29,5.19,23.84,23.84,0,0,0-5.82,1c-2.23.67-3.1,1.31-4.81,1.88a16,16,0,0,1-4.86.73,18.46,18.46,0,0,1-3-.24,24,24,0,0,0,.88-7.12c-.07-1.84-.34-3.25-.34-6.44,0-1.43.08-2.75.08-3-.11.58-.83,7.44-.82,9.14a5.89,5.89,0,0,1-.32,1.92,1.64,1.64,0,0,1-.16.41c-1.25,2.66-5.08,4.37-8.42,4.37a6.83,6.83,0,0,1-3.62-.91c-1.16-.74-2-2-1.7-2.41.06-.09.17-.12.43-.12s1,.08,2.15.22c0,0,0,0,0,0a3.59,3.59,0,0,0,.89.3.08.08,0,0,0,.07,0,4.43,4.43,0,0,0,.9.1,4.21,4.21,0,0,0,.51,0,2,2,0,0,0,.5-.09s0,0,0,0h.1A2.3,2.3,0,0,0,33,46a2.58,2.58,0,0,0,.77-.87l0,0a2.73,2.73,0,0,0,.41-.48,4,4,0,0,0,.34-.53s0,0,.05-.05a13.63,13.63,0,0,1,1.34-1.52c0-1.51,0-3.27.1-5.26.15-2.53.26-2.07.48-5,.24-3.38.1-4.3.95-5.51a5,5,0,0,1,2.18-1.88c1.17-.5,1.94-.24,3.88-.45a18.49,18.49,0,0,0,3.32-.67c0-.31.13-1,.18-1.27s.09-.83.12-1a1.54,1.54,0,0,1-.86-.45c-.4-.4-.23-.82-.32-2.18a20.34,20.34,0,0,0-.39-2.31c-.25-1-.41-1.06-.48-1.49-.17-1.38,1.14-3.11,2.81-3.79a4.39,4.39,0,0,1,1.67-.32A6.49,6.49,0,0,1,54.1,13a5.87,5.87,0,0,1,1.19,2.28c.09.34,0,.75,0,1.6,0,.47.08.84.11,1.09a21.5,21.5,0,0,1,1.19,5.3c0,.24,0,1.23.38,1.34h.18a1.14,1.14,0,0,1,.3,0,11.56,11.56,0,0,1,2.58.52c1.43.49,2,2.44,3.87,4.78A7.52,7.52,0,0,0,64.49,30.49Z"></path><path d="M63.55,49.1,62.3,73.73a2.82,2.82,0,0,1-.85-1.38c-.44-1.78,1.25-3,.62-4.28-.29-.58-.88-.74-1.62-.82A6.27,6.27,0,0,1,56.64,66C55.45,65,55.56,63.9,55,63a2.42,2.42,0,0,0-.62-.77,1.25,1.25,0,0,0-.18-.11,3.13,3.13,0,0,0-2-.56,31.67,31.67,0,0,0-5,.8l-.51.1L43.85,63c-1.17.21-2.27.39-3.3.53a24.51,24.51,0,0,1-3.11.23A5.16,5.16,0,0,1,34.23,63a2.11,2.11,0,0,1-.22-.17l-.18-.23A2.25,2.25,0,0,1,33.64,61a2.49,2.49,0,0,1,1.88-1.69c-1-1.12-1.06-2.55-.49-3.15a2.58,2.58,0,0,1,1.46-.61,2.15,2.15,0,0,1,.45,0,3.38,3.38,0,0,1-1-1.76,2.79,2.79,0,0,1-.07-.58c0-1.58,1.25-3.32,3.34-4.41.08,0,.16-.09.25-.13-.12.15-.22.28-.33.44v0a1.49,1.49,0,0,0-.29,1.09v0a.27.27,0,0,0,0,.12c.17.52.81.85,1.7,1.05h.06a12.28,12.28,0,0,0,2.69.26c.58,0,1.16,0,1.7-.08a.3.3,0,0,0,.13,0h.1l.82-.07h.08a.08.08,0,0,0,.06,0,6.27,6.27,0,0,0,.93-.14c3.16-.56,4.94-1.74,7.23-2.51h0a14.12,14.12,0,0,1,4.14-.7h.11l1,0a8.49,8.49,0,0,1,3.18.47A2.87,2.87,0,0,1,63.55,49.1Z"></path><path d="M33.52,41.9a2.93,2.93,0,0,1-.25,1.2l-.05,0a2.92,2.92,0,0,1-.29.48,2.55,2.55,0,0,1-.35.38s0,.05,0,0a.62.62,0,0,1-.25.19.54.54,0,0,1-.18.11.24.24,0,0,1-.16.1,3.19,3.19,0,0,1-.48.19h0s0,0,0,0a1.15,1.15,0,0,1-.29.08,2,2,0,0,1-.48.05,2.24,2.24,0,0,1-.58-.06,2.13,2.13,0,0,1-.43-.13,1.28,1.28,0,0,1-.24-.11,1.78,1.78,0,0,1-.36-.19,2.49,2.49,0,0,1-.4-.34s-.05,0-.06-.06a1.28,1.28,0,0,1-.24-.29A2,2,0,0,1,28,43a3,3,0,0,1-.2-1.05A2.86,2.86,0,0,1,30.64,39a1.74,1.74,0,0,1,.51.06,2.35,2.35,0,0,1,.74.22,1.55,1.55,0,0,1,.32.2,1.06,1.06,0,0,1,.29.22l.19.16a1.28,1.28,0,0,1,.24.29,2.4,2.4,0,0,1,.21.31A2.78,2.78,0,0,1,33.52,41.9Z"></path><path d="M64.83,7.93l-1,19a2.92,2.92,0,0,1-.23-.34c-.88-1.38-1.62-2.56-3.06-3.06A16.74,16.74,0,0,0,58.2,23,23.75,23.75,0,0,0,57,17.62c0-.26-.06-.53-.09-.87a5.17,5.17,0,0,1,0-.79,3.65,3.65,0,0,0-.09-1.15l0-.1a7.23,7.23,0,0,0-1.5-2.8,8.08,8.08,0,0,0-5.71-2.63,5.75,5.75,0,0,0-2.3.45c-2.37,1-4,3.36-3.78,5.46a2.93,2.93,0,0,0,.27.9,5.46,5.46,0,0,1,.26.79,17,17,0,0,1,.33,2c0,.38,0,.69,0,.93a2.87,2.87,0,0,0,.72,2.24,2.54,2.54,0,0,0,.29.28.26.26,0,0,1,0,.11,18.52,18.52,0,0,1-2,.35,14.49,14.49,0,0,1-1.62.08,6.93,6.93,0,0,0-2.72.48,6.61,6.61,0,0,0-2.89,2.47c-.91,1.32-1,2.46-1.09,4.56,0,.48-.06,1.06-.11,1.73-.11,1.35-.19,1.94-.26,2.47s-.14,1.15-.22,2.53c0,.76-.08,1.51-.08,2.26a4.36,4.36,0,0,0-1.22-1.2V8.65a38.32,38.32,0,0,1,31.69-.72Z"></path><path d="M69.27,15.19l.49-9.62-2.4-.11-.47,9.19,1.46.3C68.67,15,69,15.1,69.27,15.19Z"></path><path d="M65.43,91.05,68.35,33.3C68,29.16,68,28.15,68,27.75v-.16c0-.32,0-.7,0-1.19a12.27,12.27,0,0,0-.22-1.81A2.7,2.7,0,0,1,67,24l0,0,0-.05a1.86,1.86,0,0,1-.22-.35c0-.06-.1-.11-.15-.18a.32.32,0,0,1-.06-.08L63,90.94Z"></path><path d="M67.94,17.1v.42a1.84,1.84,0,0,1,.32.27A3.21,3.21,0,0,1,69.2,19c.31.68.35,1.67-.13,2a.78.78,0,0,1-.37.09h-.11c-.11,0-.19,0-.22-.11s.32-.54.3-.94c0-.23-.19-.52-.72-.81l-.37-.2-.29-.12a4.24,4.24,0,0,0-.17,2.1,2.56,2.56,0,0,0,.09.45A3.35,3.35,0,0,0,68,22.62a3.53,3.53,0,0,0,.19.3,1.24,1.24,0,0,0,.64.37c.16.07.58.34.75,3,0,.66,0,1.06,0,1.31a1.35,1.35,0,0,0,0,.2c0,.44.08,2,.42,5.74.14,2,.27,4.07.41,6.11,2.51-3.92,2.73-6.77,2.44-8.68-.13-.93-1.26-7.52-1.26-9.44A8.72,8.72,0,0,0,71,18.48a3,3,0,0,0-.24-.4,2.25,2.25,0,0,0-.42-.48A4.44,4.44,0,0,0,68,16.53C68,16.72,68,16.91,67.94,17.1Z"></path></g></g>',1)]))}const k2=M(b2,[["render",$2]]),M2={},Z2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function A2(t,a){return l(),o("svg",Z2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M70.4,57c-1.09-1.16-1.72-1.76-3.09-1.76s-2,.6-3.09,1.76-2.68,2.85-5.61,2.85S54.22,58.26,53,57c-1-1.1-1.65-1.7-2.88-1.75h-.26c-1.33,0-2,.63-3,1.76-1.19,1.28-2.68,2.85-5.6,2.85s-4.4-1.57-5.6-2.85-1.73-1.76-3.08-1.76-2,.6-3.1,1.76a7.76,7.76,0,0,1-4.15,2.71c1,4.28,2.51,8.72,4,13.19a109.39,109.39,0,0,0,7.48,17.25H63.22A109.39,109.39,0,0,0,70.7,72.88c1.49-4.46,3-8.9,4-13.17A7.65,7.65,0,0,1,70.4,57Z"></path><path d="M73.08,38.81c-1.45-2.17-4.21-5.22-9.74-7.43l-13.21-.14h-.26l-13.2.14c-5.53,2.21-8.3,5.26-9.74,7.43-3.32,5-3.54,11-2.35,17.5A4.55,4.55,0,0,0,27,54.62c1.19-1.26,2.67-2.85,5.6-2.85s4.39,1.59,5.6,2.85,1.72,1.77,3.08,1.77,2-.6,3.1-1.77S47,51.8,49.87,51.77h.06a.68.68,0,0,1,.2,0,7.15,7.15,0,0,1,5.39,2.83c1.09,1.17,1.73,1.77,3.09,1.77s2-.6,3.1-1.77,2.68-2.85,5.6-2.85,4.4,1.59,5.61,2.85a4.25,4.25,0,0,0,2.51,1.72C76.61,49.81,76.39,43.79,73.08,38.81Z"></path><path d="M17.2,27.82a14,14,0,0,0,6.3,11.67,15.91,15.91,0,0,1,1.13-2,16.5,16.5,0,0,1,1.46-1.85,9.36,9.36,0,0,1,8.66-16.52,20.2,20.2,0,0,0-2-5.22c-.51,0-1-.09-1.58-.09A14,14,0,0,0,17.2,27.82Z"></path><path d="M35.28,12.81c2.13,3.07,3.5,7.8,3.5,13.11,0,1-.05,1.93-.14,2.85H61.36c-.09-.92-.13-1.88-.13-2.85,0-5.31,1.36-10,3.49-13.11Z"></path><path d="M82.8,27.82a14,14,0,0,0-14-14c-.54,0-1.07,0-1.58.09a20.2,20.2,0,0,0-2,5.22,9.36,9.36,0,0,1,8.66,16.52,16.5,16.5,0,0,1,1.46,1.85,15.91,15.91,0,0,1,1.13,2A14,14,0,0,0,82.8,27.82Z"></path></g></g>',1)]))}const S2=M(M2,[["render",A2]]),P2={},T2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function B2(t,a){return l(),o("svg",T2,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("g",null,[e("path",{d:"M6.56,69c2.36,4,6.45,6.26,10.52,5.89a10.39,10.39,0,0,0,6-2.71c.42.54.85,1.08,1.31,1.59a32.93,32.93,0,0,0,8.05,6.73c-5.63.53-8.76,5.38-9,5.84,3.72,2.7,8.39,3.22,12,1.27a10.09,10.09,0,0,0,4-4,34.05,34.05,0,0,0,6.23,1.41h0a1.67,1.67,0,1,0,.39-3.31,29,29,0,0,1-4.7-1,10.28,10.28,0,0,0,3.17-5.54c.78-4-1.11-8.32-4.8-11.06-.37.43-4.34,5.18-2.72,11a11.29,11.29,0,0,0,3.28,5.27,30.71,30.71,0,0,1-13.44-8.79c-.3-.33-.58-.66-.86-1a10.08,10.08,0,0,0,4.43-3.58c2.3-3.39,2.25-8.08-.06-12.06-.51.25-6,3.06-6.83,9a10.9,10.9,0,0,0,.06,3.25,31.1,31.1,0,0,1-4.32-11.88,10.26,10.26,0,0,0,6-.78c3.69-1.75,6-5.81,6.07-10.41-.57,0-6.75-.43-10.48,4.31a11,11,0,0,0-1.89,3.69c0-.3,0-.6,0-.9a30.9,30.9,0,0,1,2.45-12.32,9.19,9.19,0,0,0,5.4,1.23A10.2,10.2,0,0,0,35,33.37c-.47-.21-5.56-2.41-10.14.45a9.75,9.75,0,0,0-1.39,1.06,30.24,30.24,0,0,1,3-4.06,31.31,31.31,0,0,1,5.51-5,7.88,7.88,0,0,0,3.19,3.27,8.86,8.86,0,0,0,9.33-1c-.24-.38-2.86-4.42-7.55-4.56a9.07,9.07,0,0,0-1.4.09A46.85,46.85,0,0,1,45.8,20a.67.67,0,0,0,.49-.64.66.66,0,0,0-.65-.66,25.67,25.67,0,0,0-11.51,3.17c-.85.43-1.67.91-2.47,1.42a8.67,8.67,0,0,0,2.23-3.81C35.13,15,32,11.27,31.74,10.93A8.85,8.85,0,0,0,28,19.55,8.12,8.12,0,0,0,30.66,24a33.59,33.59,0,0,0-6,5.26,33.09,33.09,0,0,0-4.9,7.15,9.91,9.91,0,0,0-.38-4.55c-1.7-5.13-7-6.69-7.52-6.82a10.2,10.2,0,0,0,1.95,10.62,9.24,9.24,0,0,0,5.1,2.53A33.78,33.78,0,0,0,16,51.39c0,.24,0,.47,0,.71a10.91,10.91,0,0,0-1.82-3.5c-3.74-4.74-9.91-4.36-10.48-4.31,0,4.6,2.37,8.66,6.07,10.41a10.37,10.37,0,0,0,6.44.71,34,34,0,0,0,4.95,14.17,10.86,10.86,0,0,0-3.43-2.16C12.15,65.23,7,68.7,6.56,69Z"}),e("path",{d:"M85.77,48.6A10.91,10.91,0,0,0,84,52.1c0-.24,0-.47,0-.71a33.78,33.78,0,0,0-2.83-13.22,9.24,9.24,0,0,0,5.1-2.53,10.2,10.2,0,0,0,2-10.62c-.5.13-5.82,1.69-7.52,6.82a9.91,9.91,0,0,0-.38,4.55,33.09,33.09,0,0,0-4.9-7.15,33.59,33.59,0,0,0-6-5.26A8.12,8.12,0,0,0,72,19.55a8.85,8.85,0,0,0-3.7-8.62c-.29.34-3.39,4-2.15,8.55a8.67,8.67,0,0,0,2.23,3.81c-.8-.51-1.62-1-2.47-1.42A25.67,25.67,0,0,0,54.36,18.7.66.66,0,0,0,54.2,20a46.85,46.85,0,0,1,10.3,3.64,9.07,9.07,0,0,0-1.4-.09c-4.69.14-7.31,4.18-7.55,4.56a8.86,8.86,0,0,0,9.33,1,7.88,7.88,0,0,0,3.19-3.27,31.31,31.31,0,0,1,5.51,5,30.24,30.24,0,0,1,3,4.06,9.75,9.75,0,0,0-1.39-1.06C70.59,31,65.5,33.16,65,33.37a10.2,10.2,0,0,0,8.29,6.93,9.19,9.19,0,0,0,5.4-1.23,30.9,30.9,0,0,1,2.45,12.32c0,.3,0,.6,0,.9a11,11,0,0,0-1.89-3.69c-3.73-4.74-9.91-4.36-10.48-4.31,0,4.6,2.38,8.66,6.08,10.41a10.22,10.22,0,0,0,6,.78,31.1,31.1,0,0,1-4.32,11.88,10.9,10.9,0,0,0,.06-3.25c-.8-6-6.31-8.79-6.83-9-2.31,4-2.36,8.67-.06,12.06a10.08,10.08,0,0,0,4.43,3.58c-.28.34-.56.67-.86,1A30.71,30.71,0,0,1,59.85,80.5a11.29,11.29,0,0,0,3.28-5.27c1.62-5.82-2.35-10.57-2.72-11-3.69,2.74-5.58,7-4.8,11.06a10.28,10.28,0,0,0,3.17,5.54,29,29,0,0,1-4.7,1h0a1.66,1.66,0,1,0,.41,3.3h0a34.05,34.05,0,0,0,6.23-1.41,10.09,10.09,0,0,0,4,4c3.6,2,8.27,1.43,12-1.27-.29-.46-3.42-5.31-9-5.84a32.93,32.93,0,0,0,8-6.73c.46-.51.89-1,1.31-1.59a10.39,10.39,0,0,0,6,2.71C87,75.29,91.08,73,93.44,69c-.47-.33-5.59-3.8-11.22-1.61a10.86,10.86,0,0,0-3.43,2.16,34,34,0,0,0,4.95-14.17,10.37,10.37,0,0,0,6.44-.71c3.7-1.75,6-5.81,6.07-10.41C95.68,44.24,89.51,43.86,85.77,48.6Z"})])],-1)]))}const H2=M(P2,[["render",B2]]),j2={},E2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function V2(t,a){return l(),o("svg",E2,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("g",null,[e("path",{d:"M45,19.63H18.21V41.3h8.36V36.18a5,5,0,1,1,10,0V41.3H45V36.18a5,5,0,0,1,5-5,4.92,4.92,0,0,1,3.54,1.48l.13.13a4.81,4.81,0,0,1,.52.67,2.2,2.2,0,0,1,.19.3,5.28,5.28,0,0,1,.42.94,3.76,3.76,0,0,1,.16.74,3.84,3.84,0,0,1,.07.76V41.3h8.35V36.18A5,5,0,0,1,64,33.8a4.76,4.76,0,0,1,.87-1.16,5,5,0,0,1,3.56-1.48A4.92,4.92,0,0,1,72,32.64a3.94,3.94,0,0,1,.61.74,2.22,2.22,0,0,1,.25.42,1.91,1.91,0,0,1,.21.43c.06.14.12.29.17.43v0a4.13,4.13,0,0,1,.19,1c0,.17,0,.33,0,.51v-.26a2.26,2.26,0,0,1,0,.26V41.3h8.36V19.63H45Z"}),e("path",{d:"M45,44.79H18.21V84.56h8.36V57.74a5,5,0,1,1,10,0V84.56H45V57.74a5,5,0,0,1,5-5,4.92,4.92,0,0,1,3.54,1.48l.13.13a5.38,5.38,0,0,1,.52.66c.07.1.13.2.19.3a5.72,5.72,0,0,1,.42,1A3.61,3.61,0,0,1,55,57a3.77,3.77,0,0,1,.07.76V84.56h8.35V57.74a5.12,5.12,0,0,1,.61-2.4,5.18,5.18,0,0,1,.87-1.16,5.05,5.05,0,0,1,3.56-1.46A4.92,4.92,0,0,1,72,54.2a3.65,3.65,0,0,1,.61.74,2.22,2.22,0,0,1,.25.42,1.72,1.72,0,0,1,.21.43c.06.14.12.29.17.43v0a4.14,4.14,0,0,1,.19,1c0,.18,0,.34,0,.52v-.26a2.26,2.26,0,0,1,0,.26V84.56h8.36V44.79H45Z"})])],-1)]))}const F2=M(j2,[["render",V2]]),z2={},O2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function I2(t,a){return l(),o("svg",O2,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M37.71,67c1.9.12,3.77.63,8.7,3.1,1.35.68,2.7,1.4,4,2.14l.77-5.72a2.21,2.21,0,0,1,.52-1.14c-3.55-1.15-4.19-1.43-4.8-2.13s-1.42-2.32-.93-5a.74.74,0,0,1,.85-.59.73.73,0,0,1,.59.85A4.56,4.56,0,0,0,48,62.32c.35.4.88.65,4.2,1.72l3,1,4,.85a2.94,2.94,0,0,0,3.51-2.47l1.39-4.73.18-2.61a.73.73,0,0,1,.58-.66l2.66-.55a.45.45,0,0,0,.33-.25.44.44,0,0,0,0-.42l-3-5.93a.72.72,0,0,1-.08-.35l.11-8.15a9,9,0,0,0-1.56-5.31c-.15-.21-.32-.4-.48-.6a14.88,14.88,0,0,1-2.59,2.84l-.85.7a3.14,3.14,0,0,1,1.1,0,4.07,4.07,0,0,1,2.8,2.32,3.65,3.65,0,0,1-3.87-.15,3.2,3.2,0,0,1-1-1.38c-.48.36-1,.71-1.46,1a27,27,0,0,1-2.68,1.5,4,4,0,0,1,.75.08,4.68,4.68,0,0,1,3.14,2.77,4.2,4.2,0,0,1-4.44-.32,3.65,3.65,0,0,1-1.2-1.74,20.54,20.54,0,0,1-3.26,1,16.3,16.3,0,0,1-2.09.36l-1,.12a4.67,4.67,0,0,1,2.2.48,5.38,5.38,0,0,1,2.7,4,4.84,4.84,0,0,1-4.86-1.64,4.35,4.35,0,0,1-.84-2.77l.38-.05-.66,0a18.68,18.68,0,0,1-4.22-.31A21.93,21.93,0,0,1,37.94,42a4.87,4.87,0,0,1,.86,1.13,6,6,0,0,1-.08,5.37,5.4,5.4,0,0,1-3.41-4.6A4.82,4.82,0,0,1,36,41.23a25.26,25.26,0,0,1-2.79-1.43c-.6,3.08-.44,6.56,1.17,9.23.16.25.33.52.52.8A17.49,17.49,0,0,1,37.09,54a18.53,18.53,0,0,1,1,4.45.69.69,0,0,1,0,.25L35.88,67A14,14,0,0,1,37.71,67Z"></path><path d="M33.8,38.31h0a21.85,21.85,0,0,0,2.87,1.52,4.91,4.91,0,0,1,1.5-2.65,5.41,5.41,0,0,1,5.68-.69A6,6,0,0,1,40,40.25a5.27,5.27,0,0,1-2,.12,20.32,20.32,0,0,0,3.07.88,17.43,17.43,0,0,0,3.82.37,4.17,4.17,0,0,1-.07-2.17A4.83,4.83,0,0,1,48.67,36a5.41,5.41,0,0,1-.89,4.74,4.27,4.27,0,0,1-.86.76H47a16.28,16.28,0,0,0,2-.3,19.68,19.68,0,0,0,3-.87,3.87,3.87,0,0,1-.78-2.2,4.2,4.2,0,0,1,2.47-3.7A4.69,4.69,0,0,1,54,38.63a4,4,0,0,1-1.19,1.45,23.58,23.58,0,0,0,3.57-1.83c.54-.34,1.08-.7,1.61-1.08a3.37,3.37,0,0,1-.81-2,3.67,3.67,0,0,1,2-3.3,4.11,4.11,0,0,1,.35,3.63,3.21,3.21,0,0,1-.47.75l.51-.4c1-.83,1.87-1.75,2.85-2.59-4.75-5.37-14.5-8.15-21.78-4a15.69,15.69,0,0,0-6.45,7.27,16.13,16.13,0,0,0-.58,1.7A.77.77,0,0,1,33.8,38.31Z"></path><path d="M50,82.77A32.11,32.11,0,1,0,17.89,50.66,32.15,32.15,0,0,0,50,82.77ZM30.8,68.27a16.43,16.43,0,0,1,3.52-1l2.29-8.8a17.19,17.19,0,0,0-.89-4,16.28,16.28,0,0,0-2-3.81c-.2-.31-.39-.59-.56-.87-2.48-4.11-1.94-9.9-.3-13.81a16.81,16.81,0,0,1,7.07-8c8.43-4.78,19.93-1,24.57,5.56a10.39,10.39,0,0,1,1.86,6.21l-.11,8,3,5.77a1.93,1.93,0,0,1-1.33,2.77l-2.11.44-.15,2.13,0,.16L64.2,63.69a4.23,4.23,0,0,1-1.79,2.89,4.41,4.41,0,0,1-3.44.71l-5.44-1.17a.73.73,0,0,0-.56.12.7.7,0,0,0-.3.5l-.78,5.74a.66.66,0,0,1-.25.46c1.2.68,2.39,1.36,3.56,2.08a.72.72,0,0,1,.25,1,.73.73,0,0,1-1,.24c-2.83-1.73-5.76-3.36-8.69-4.83-4.7-2.36-6.43-2.84-8.13-2.94a13.77,13.77,0,0,0-6.26,1.13.73.73,0,0,1-.56-1.35Z"></path><path d="M50,89.7a39,39,0,1,0-39-39A39,39,0,0,0,50,89.7Zm0-72.61A33.57,33.57,0,1,1,16.43,50.66,33.61,33.61,0,0,1,50,17.09Z"></path></g></g>',1)]))}const N2=M(z2,[["render",I2]]),D2={},W2={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function R2(t,a){return l(),o("svg",W2,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("path",{d:"M73.74,82.38V17.8c0-7.46-10.66-13.52-23.81-13.52s-23.8,6.06-23.8,13.52V82.38c0,7.47,10.65,13.52,23.8,13.52S73.74,89.85,73.74,82.38ZM30,49.56a1.43,1.43,0,0,1,1.43-1.43H42a7.61,7.61,0,0,0-.13,1.43A7.79,7.79,0,0,0,42,51H31.41A1.43,1.43,0,0,1,30,49.56ZM43.55,62.8a34.41,34.41,0,0,1-3.43,4.28,18,18,0,0,0-3.76,5.41,17.65,17.65,0,0,0-1.28,8.17.34.34,0,0,1-.25.39.36.36,0,0,1-.43-.2c-4.25-7.6-4.08-16.4.44-22.43a17.46,17.46,0,0,1,7.67-5.72,8.08,8.08,0,0,0,4.06,4.2A29.73,29.73,0,0,1,43.55,62.8Zm-1-16.38a17.46,17.46,0,0,1-7.67-5.72c-4.52-6-4.69-14.83-.44-22.44a.37.37,0,0,1,.32-.2l.11,0a.34.34,0,0,1,.25.38,17.69,17.69,0,0,0,1.28,8.18A18.06,18.06,0,0,0,40.11,32a34.56,34.56,0,0,1,3.44,4.29,29.79,29.79,0,0,1,3,5.9A8.06,8.06,0,0,0,42.51,46.42Zm8.87,41.24a1.44,1.44,0,0,1-2.87,0V57.52a8.64,8.64,0,0,0,2.87,0Zm0-46A8.54,8.54,0,0,0,50,41.49a8.75,8.75,0,0,0-1.44.12V11.46a1.44,1.44,0,1,1,2.87,0Zm5-5.28A34.79,34.79,0,0,1,59.79,32a18,18,0,0,0,3.75-5.39,17.56,17.56,0,0,0,1.27-8.17.37.37,0,0,1,.7-.21c4.25,7.62,4.07,16.43-.44,22.44h0a17.56,17.56,0,0,1-7.68,5.73,8,8,0,0,0-4.06-4.2A29.35,29.35,0,0,1,56.36,36.33Zm9.15,44.52h0a.37.37,0,0,1-.44.2.36.36,0,0,1-.26-.39,17.58,17.58,0,0,0-1.27-8.18,17.88,17.88,0,0,0-3.75-5.39,33.69,33.69,0,0,1-3.43-4.29,29.81,29.81,0,0,1-3-5.9,8.12,8.12,0,0,0,4.06-4.2,17.39,17.39,0,0,1,7.68,5.72C69.58,64.44,69.76,73.24,65.51,80.85ZM68.5,51H57.9A8.76,8.76,0,0,0,58,49.56a8.54,8.54,0,0,0-.13-1.43H68.5a1.44,1.44,0,0,1,0,2.87Z"})],-1)]))}const G2=M(D2,[["render",R2]]),q2={},K2={viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg"};function U2(t,a){return l(),o("svg",K2,a[0]||(a[0]=[e("path",{d:"m82.73 79a33.45 33.45 0 0 1 -12.48 6.88c-3.91 1.14-9.38 2.73-14.52-.16-4.41-2.48-6.31-7-7.16-10.34a8.25 8.25 0 0 1 1.25-6.73 7.78 7.78 0 0 0 1.28-5.68c-.13-.87-.3-1.82-.54-2.84s-.47-2-.7-2.85l.76-.11a137.62 137.62 0 0 0 15.83-3.3c.72-.19 1.48-.42 2.24-.64v.72a5.07 5.07 0 0 0 .95 1.85c1.7 2 4.25 1.36 7.34 3.27 1.43.9 2.16 1.34 2.49 2.19 1.4 3.41-4.92 7.39-3.38 11.32a4.2 4.2 0 0 0 1.36 1.8 4.58 4.58 0 0 0 3.42.55 2.36 2.36 0 0 1 1.86 4.07z"},null,-1),e("path",{d:"m75.47 38.38-5 1.78c-2.39.91-4.79 1.6-7 2.21-3.92 1.05-7.86 1.9-11.71 2.56a18.43 18.43 0 0 0 -2.37-1.58 15.71 15.71 0 0 0 -6.68-2h-1.92c-.69.07-1.21.15-1.79.26a13.94 13.94 0 0 0 -3.36 1.08 10.36 10.36 0 0 0 -3.49 2.56 9.73 9.73 0 0 0 -1.26 1.64 11.45 11.45 0 0 0 -.89 1.61 18 18 0 0 0 -1.35 6.12 25.52 25.52 0 0 0 .35 5.49 49.05 49.05 0 0 0 2.69 9.65c.7 1.91 1.35 3.46 1.8 4.49a48.29 48.29 0 0 1 -9.24 4c-3.44 1.07-12.2 3.36-13.12 1.42-1.36-2.83 13.24-13.41 12.77-18.67 0-.23-.13-1.76-.4-4.19-.1-.89-.19-2-.29-3.43a74.24 74.24 0 0 1 -.21-9.38c.15-1.79.68-8.5 4.86-14 6.16-8.1 16.78-8.79 20.07-9a33.57 33.57 0 0 1 18.17 4.15l-9.1 4.19-7 3.18a2.41 2.41 0 0 0 -1.25 3l1.5 4a2.42 2.42 0 0 0 2.27 1.58 2.27 2.27 0 0 0 .67-.1l7.81-2.25 13.37-3.9c.33 1.15.71 2.35 1.1 3.53z"},null,-1),e("path",{d:"m78.11 31.23q-12.82 3.77-25.61 7.49c-.5-1.34-1-2.68-1.5-4l24-11a24.22 24.22 0 0 1 2 3.83 27.2 27.2 0 0 1 1.11 3.68z"},null,-1),e("path",{d:"m80.93 46.44-7.49 2.68c-2.48.93-5 1.65-7.58 2.35a135.88 135.88 0 0 1 -15.55 3.25l-2.05.3-1.26-1.59a9.61 9.61 0 0 0 -2.34-2.29 6.7 6.7 0 0 0 -2.73-1.14 5.88 5.88 0 0 0 -.9-.08 4 4 0 0 0 -.56 0 5.67 5.67 0 0 0 -1.48.32 2.22 2.22 0 0 0 -.94.53 2.25 2.25 0 0 0 -.41.45 3.15 3.15 0 0 0 -.41.6c-1 1.83-1.29 4.65-1.12 7.36a35.25 35.25 0 0 0 1.62 8.24 13.08 13.08 0 0 1 -.67 2.13 13.28 13.28 0 0 1 -1.63 2.85 52.31 52.31 0 0 1 -4.09-12.72 23.26 23.26 0 0 1 -.26-5 15.19 15.19 0 0 1 1.16-5.3 8.77 8.77 0 0 1 .76-1.26 8.29 8.29 0 0 1 1-1.29 8.13 8.13 0 0 1 2.72-2 11.51 11.51 0 0 1 2.84-.9 12 12 0 0 1 1.44-.19h1a4.52 4.52 0 0 1 .52 0 13.24 13.24 0 0 1 5.63 1.71 15.79 15.79 0 0 1 2.87 2 129.9 129.9 0 0 0 13-2.8c2.42-.66 4.83-1.35 7.18-2.24l7.1-2.53z"},null,-1)]))}const Y2=M(q2,[["render",U2]]),J2={},Q2={viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg"};function X2(t,a){return l(),o("svg",Q2,a[0]||(a[0]=[E('<path d="m49.93 31.61-33.57 15.88h67.14z"></path><path d="m21.18 49.97h57.51v3.15h-57.51z"></path><path d="m83.23 87.64h-4.47v-3.72h-57.65v3.72h-4.48v4.29h66.6z"></path><path d="m23.15 81.33h7.31l-.56-25.74h-6.19z"></path><path d="m38.57 81.33h7.31l-.56-25.74h-6.19z"></path><path d="m53.98 81.33h7.32l-.56-25.74h-6.19z"></path><path d="m69.4 81.33h7.32l-.56-25.74h-6.19z"></path><path d="m23.86 33.07v8.51l17.99-8.51z"></path><path d="m76 41.58v-8.51h-17.99z"></path><path d="m49.93 29.09 3.57 1.91h20.91c-5.19-8.89-14.21-14.72-24.48-14.72s-19.27 5.83-24.46 14.72h20.89z"></path>',10)]))}const e4=M(J2,[["render",X2]]),t4={},a4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function r4(t,a){return l(),o("svg",a4,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("path",{d:"M31.67,57.84a2.12,2.12,0,0,1,.61,1.88L29.06,78.51a2.12,2.12,0,0,0,3.08,2.24L49,71.88a2.1,2.1,0,0,1,2,0l16.87,8.87a2.12,2.12,0,0,0,3.08-2.24L67.72,59.72a2.12,2.12,0,0,1,.61-1.88L82,44.53a2.12,2.12,0,0,0-1.17-3.62L61.94,38.17A2.14,2.14,0,0,1,60.34,37l-8.43-17.1a2.13,2.13,0,0,0-3.82,0L39.66,37a2.14,2.14,0,0,1-1.6,1.16L19.19,40.91A2.12,2.12,0,0,0,18,44.53Z"})],-1)]))}const s4=M(t4,[["render",r4]]),l4={},n4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function o4(t,a){return l(),o("svg",n4,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("path",{d:"M37.72,52.06,26.11,43,8.79,66.49H93.21L62.11,30.83Z"})],-1)]))}const i4=M(l4,[["render",o4]]),c4={},d4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function u4(t,a){return l(),o("svg",d4,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M50.12,92.71a1.29,1.29,0,0,1-.53.62c-1.56,1.07-5.59.84-5.78-.17,0-.17.43-.74,1.35-1.89A3,3,0,0,0,46,89.79c0-.35,0-.45,0-1s.18-.86.22-1.21a3.1,3.1,0,0,0-.4-1.88,36.53,36.53,0,0,1-1.69-7c-.39-2.64.21-2.73,0-6.4-.06-1.12-.18-2.14-.3-3A39.29,39.29,0,0,0,49,69.6l-.48,3.74a13.23,13.23,0,0,1,.85,3.72,26.8,26.8,0,0,1-.32,4.28,41.21,41.21,0,0,0,0,6.13,11.35,11.35,0,0,0,.5,3.32C49.88,91.35,50.36,92,50.12,92.71Z"></path><path d="M60.89,36.79a14.06,14.06,0,0,1-1.27-3.43c0,.5.06,1.34,0,2.37s-.13,1.78-.21,2.48a19.12,19.12,0,0,0-2.14,2,9.49,9.49,0,0,1-1.71,1.53,6.3,6.3,0,0,1-3.33,1.24,31.69,31.69,0,0,0-7,1.88l-.11,0a7.62,7.62,0,0,1-2.13.55.52.52,0,0,1-.32-.07s0,0,0,0a22,22,0,0,0,.21-5.25,24.12,24.12,0,0,0-1.52-6.63,37.23,37.23,0,0,0-4.83-2.77c-3.35-1.58-4.92-1.69-6.63-2.59s-4-2.95-5.62-7.47A2.8,2.8,0,0,0,22.75,19a3.05,3.05,0,0,1-1.92-2.25,3.18,3.18,0,0,1,.84-2.51c-2-1.84-2.91-2.93-2.67-3.29.08-.13.32-.14.74-.06L22.8,13.1a.91.91,0,0,1,.58-.45c.75-.12,1.5,1.21,1.69,1.57a5.29,5.29,0,0,1,.67,2.54c.26.27.66.72,1.16,1.26,2,2.09,2.74,2.73,3.9,4.12.37.43.9,1.09,1.51,2a18.2,18.2,0,0,0,6.09.58,9.83,9.83,0,0,1,2-.1,18.38,18.38,0,0,1,1.84.33,10,10,0,0,0,3.27-.28,27.57,27.57,0,0,1,3.7-.29c.11-.85.21-1.69.32-2.54a5.83,5.83,0,0,1-1.16-2.42c-.16-.74-.09-1.19-.35-2.31s-.4-1.07-.45-1.5c-.18-1.39,1.06-3.13,2.64-3.8,2.27-1,4.73.48,5.84,1.79a5.76,5.76,0,0,1,1.11,2.28,2.58,2.58,0,0,1,0,1.61,2.08,2.08,0,0,1-.48.81A4.63,4.63,0,0,1,55,21.67c-.06.34-.4,2.09.6,2.91a1.71,1.71,0,0,0,.53.33,5.38,5.38,0,0,0,2.69.3,7,7,0,0,1,3.59.76Z"></path><path d="M63.85,92.88a2.61,2.61,0,0,1-3.39.5c-.61-.43-.41-.9-1.26-2.64-.64-1.34-.92-1.37-1.33-2.37-.5-1.16-.29-1.43-.68-3.26a30.3,30.3,0,0,0-1.56-4.88,14.08,14.08,0,0,1-.86-2.42A10.94,10.94,0,0,1,54.55,75c-.29-.7-.58-1.44-.86-2.23-.38-1.11-.69-2.18-1-3.21,1.35,0,2.61-.09,3.65-.16v.19l-.23,1.6,1.6.22.79.1a14.33,14.33,0,0,1,1.2,2.87,18.65,18.65,0,0,1,.5,6.66,36.52,36.52,0,0,1-.4,4,16.74,16.74,0,0,0,2.49,3.57,6,6,0,0,1,1.36,2A2.73,2.73,0,0,1,63.85,92.88Z"></path><path d="M60.54,39.38,56.61,67.77c-1.76.11-4.28.22-6.78.22-3.38,0-6.73-.21-8.07-.95a2.94,2.94,0,0,1-1.48-1.58A3.27,3.27,0,0,1,41,62.79,2.61,2.61,0,0,1,40.11,58a3.22,3.22,0,0,1-1.57-2.27A3.25,3.25,0,0,1,39.48,53a5,5,0,0,1-1-2.28c-.52-3.08,2.28-5.4,2.46-5.54a2.32,2.32,0,0,0,.81,1.43A2,2,0,0,0,43,47a8.25,8.25,0,0,0,2.8-.71c7.07-2.62,7.59-1.11,10.69-3.3C58.35,41.73,58.44,41,60.54,39.38Z"></path><path d="M68.09,58.69c-.31,2,2.72,3.25,2.38,5.31s-3.22,2-3.57,4,2,3,1.67,4.6c-.19,1-1.4,2.27-5.91,3.22q.3-7.56.61-15.08l1.15-8.39a5.39,5.39,0,0,0,1.93-.84,4.8,4.8,0,0,0,.76-.67,4.1,4.1,0,0,0,1-2c.12-.43.26-1,.58-1.84l.07-.16a23.55,23.55,0,0,1,1.12-2.6,101,101,0,0,1,1.53,11.54C70.07,56.33,68.3,57.25,68.09,58.69Z"></path><path d="M68.51,11l-2.68,19.4,0,0-.61,4.09-.39,2.64-.4,2.61c-.08.63-.16,1.09-.22,1.43a9.93,9.93,0,0,1-.45,1.79,2.72,2.72,0,0,0-.84.5,2.12,2.12,0,0,0-.37.42,1.16,1.16,0,0,1-.15.13,3.91,3.91,0,0,0-1,1.33s0,.07,0,.08l4.79-34.71Z"></path><path d="M62.76,52.63l-2.42,17.5L58,69.81l2.95-21.36c0,.14.05.3.06.43l.36,1.71,0,0v2.16Z"></path><path d="M69.6,40.38a4,4,0,0,1-.83,2.51,21.1,21.1,0,0,0-1.62,3.57c-.83,2.16-.59,2.63-1.25,3.35a2.49,2.49,0,0,1-.48.42A4.58,4.58,0,0,1,63,51c0-.2,0-.39,0-.6L63,50l.37-.24a3.71,3.71,0,0,0,1.08-1c.38-.65.56-1.63.12-2a.7.7,0,0,0-.46-.16.28.28,0,0,0-.24.08c-.16.2.25.58.17,1,0,.23-.25.49-.82.71l-.38.15-.31.08a4.19,4.19,0,0,1,.1-2.11,2,2,0,0,1,.16-.44,3.21,3.21,0,0,1,.9-1.07c.16-.18.14-.2.24-.29a1.31,1.31,0,0,1,.68-.28c.17,0,.63-.26,1.14-2.91.06-.36.15-.87.23-1.5.27-1.75.53-3.51.78-5.25.52.76.89,1.34.91,1.39a.45.45,0,0,0,0,.08.82.82,0,0,0,.08.22,20.56,20.56,0,0,0,1.5,3.05A2.46,2.46,0,0,1,69.6,40.38Z"></path></g></g>',1)]))}const p4=M(c4,[["render",u4]]),h4={},f4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function C4(t,a){return l(),o("svg",f4,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><polygon points="14.91 28.39 85.09 28.39 50 11.78 14.91 28.39"></polygon><rect x="19.94" y="30.98" width="60.12" height="3.29"></rect><path d="M80.13,70H19.87q-1,6.12-2,12.24H82.09Q81.11,76.15,80.13,70Z"></path><polygon points="22 67.33 29.65 67.33 29.06 36.86 22.59 36.86 22 67.33"></polygon><polygon points="38.12 67.33 45.77 67.33 45.18 36.86 38.71 36.86 38.12 67.33"></polygon><polygon points="54.23 67.33 61.88 67.33 61.29 36.86 54.82 36.86 54.23 67.33"></polygon><polygon points="70.35 67.33 78 67.33 77.41 36.86 70.94 36.86 70.35 67.33"></polygon></g></g>',1)]))}const v4=M(h4,[["render",C4]]),g4={},m4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function x4(t,a){return l(),o("svg",m4,a[0]||(a[0]=[E('<g id="Livello_2" data-name="Livello 2"><g><path d="M90.59,51.56a8.46,8.46,0,0,0-8.45-8.44,1.68,1.68,0,0,0,0,3.36,5.1,5.1,0,0,1,5.09,5.08,5,5,0,0,1-.33,1.76c0,.06,0,.13-.06.2l0,0a5.06,5.06,0,0,1-2.6,2.63,5,5,0,0,1-2.07.45H53v-10H69.74a23.8,23.8,0,0,0,0-27.52H53V10.05a1.68,1.68,0,0,0-3.36,0v9.07H28.24a23.29,23.29,0,0,1,0,27.52H49.63v10H22.73a7.37,7.37,0,0,1-7.36-7.36,1.68,1.68,0,1,0-3.36,0A10.73,10.73,0,0,0,22.73,60h.44v3.3a12.83,12.83,0,0,1-8.36,12,1.71,1.71,0,0,0-1.08,1.88,1.69,1.69,0,0,0,1.66,1.39H25.58L31.37,70a1.71,1.71,0,0,1,2.34-.45,1.69,1.69,0,0,1,.46,2.33l-4.53,6.69H36.3L42.09,70a1.69,1.69,0,0,1,2.33-.45,1.66,1.66,0,0,1,.45,2.33l-4.52,6.69H47L52.79,70a1.72,1.72,0,0,1,2.35-.45,1.7,1.7,0,0,1,.45,2.33l-4.53,6.69h6.66L63.51,70a1.69,1.69,0,0,1,2.33-.45,1.66,1.66,0,0,1,.45,2.33l-4.52,6.69h6.65L74.21,70a1.72,1.72,0,0,1,2.35-.45A1.7,1.7,0,0,1,77,71.89l-4.52,6.69a18.34,18.34,0,0,0,18.1-18.32v-8.7Z"></path><path d="M18.15,89.56a1.67,1.67,0,0,0,.45,2.34,1.69,1.69,0,0,0,2.33-.46l8.71-12.86H25.58Z"></path><path d="M28.85,89.56a1.67,1.67,0,0,0,1.39,2.62,1.67,1.67,0,0,0,1.4-.74l8.71-12.86H36.3Z"></path><path d="M39.57,89.56A1.67,1.67,0,0,0,40,91.9a1.69,1.69,0,0,0,2.33-.46l8.71-12.86H47Z"></path><path d="M50.27,89.56a1.68,1.68,0,0,0,1.4,2.62,1.67,1.67,0,0,0,1.39-.74l8.71-12.86H57.72Z"></path><path d="M61,89.56a1.68,1.68,0,1,0,2.78,1.88l8.72-12.86H68.42Z"></path></g></g>',1)]))}const L4=M(g4,[["render",x4]]),w4={},y4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function b4(t,a){return l(),o("svg",y4,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("g",null,[e("path",{d:"M26.36,83.63a4.54,4.54,0,0,0,.81-4.76l9.25-9.25A8.27,8.27,0,0,1,34.06,68l-.79-.8a8.13,8.13,0,0,1-1.66-2.35l-9.24,9.24a4.57,4.57,0,0,0-4.77.8c-1.85,1.85-1.38,5.31,1,7.73S24.52,85.48,26.36,83.63Z"}),e("path",{d:"M32.22,53.55c-2.55,2.56-1.39,7.84,2.57,11.8l1.09,1.09c4,4,9.24,5.12,11.79,2.56l1.25-1.25L33.47,52.3Z"}),e("polygon",{points:"78.39 33.24 78.88 31.88 84.58 16.65 69.35 22.34 67.98 22.86 66.96 24.01 39.73 54.92 46.31 61.51 77.21 34.26 78.39 33.24"})])],-1)]))}const _4=M(w4,[["render",b4]]),$4={},k4={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 100 100"};function M4(t,a){return l(),o("svg",k4,a[0]||(a[0]=[e("g",{id:"Livello_2","data-name":"Livello 2"},[e("path",{d:"M19,66.85a12.6,12.6,0,0,0,12.6,12.61H58.48a12.6,12.6,0,0,0,12.6-12.61V65L77.75,71A5.53,5.53,0,0,0,87,66.94V39.84a5.53,5.53,0,0,0-9.25-4.09l-6.67,6.08V39.92a12.6,12.6,0,0,0-12.6-12.6H31.55A12.6,12.6,0,0,0,19,39.92ZM36,42.12,58.47,53.33l-.07.06.07.07L36,64.66Z"})],-1)]))}const Z4=M($4,[["render",M4]]),A4={},S4={width:"100%",height:"100%",viewBox:"0 0 100 100",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","xml:space":"preserve","xmlns:serif":"http://www.serif.com/",style:{"fill-rule":"evenodd","clip-rule":"evenodd","stroke-linejoin":"round","stroke-miterlimit":"2"}};function P4(t,a){return l(),o("svg",S4,a[0]||(a[0]=[E('<path d="M82.533,30.718C82.579,30.737 84.491,28.369 86.339,26.009C88.146,23.701 86.409,20.675 84.711,18.965C81.627,15.86 70.417,7.235 67.838,7.381C67.405,7.405 63.825,12.869 63.899,12.823C63.929,12.803 63.405,5.733 62.847,5.652C55.662,4.605 39.248,7.357 38.89,8.003C38.572,8.578 38.829,15.015 38.46,15.325C38.182,15.56 33.154,10.516 32.846,10.635C27.149,12.83 19.156,21.086 17.21,24.182C17.083,24.384 22.784,28.709 22.678,28.935C22.566,29.175 16.191,27.375 15.972,27.494C13.717,28.72 11.329,41.193 11.351,43.71C11.355,44.066 17.95,44.829 17.813,45.744C17.643,46.881 11.247,45.865 11.196,46.627C10.515,56.771 13.398,85.772 26.753,80.612C29.087,73.002 26.224,71.11 25.905,70.247C13.665,37.127 41.752,11.472 70.618,24.573" fill="currentColor"></path><path d="M64.784,25.522C56.579,21.71 43.883,22.591 35.539,31.137C35.485,31.192 35.479,31.089 35.539,31.137C38.052,33.145 39.962,35.266 40.008,35.244C43.196,33.768 48.766,30.433 60.154,31.379C60.942,31.445 64.892,25.572 64.784,25.522Z" fill="currentColor"></path><g id="Livello_2"><g><path d="M83.045,89.779C79.46,92.788 81.375,93.793 76.811,95.096C72.935,96.181 67.514,97.694 62.42,94.944C58.049,92.585 56.166,88.285 55.323,85.108C54.751,82.903 55.204,80.566 56.562,78.705C57.697,77.13 58.152,75.192 57.831,73.302C57.702,72.474 57.533,71.571 57.296,70.6C57.058,69.63 56.83,68.698 56.602,67.889L57.355,67.785C62.644,67.034 61.749,67.213 66.91,65.873C67.624,65.692 68.377,65.473 69.13,65.264L69.13,65.949C69.314,66.592 69.634,67.191 70.072,67.709C71.757,69.611 74.284,69.002 77.346,70.819C78.764,71.676 79.487,72.094 79.814,72.903C81.202,76.147 74.938,79.933 76.464,83.672C76.743,84.35 77.209,84.942 77.812,85.384C78.827,85.977 80.045,86.165 81.202,85.907C81.283,85.899 83.237,85.496 83.319,85.496C84.602,85.496 83.518,89.355 83.045,89.779Z" style="fill-rule:nonzero;"></path><path d="M84.877,51.618L79.798,54.754C77.429,55.62 74.367,56.166 72.176,56.746C68.291,57.745 63.102,59.729 59.286,60.357C58.544,59.798 55.408,57.892 54.833,57.996C53.682,58.202 52.873,56.771 51.813,57.247C50.494,57.816 49.882,56.346 48.921,57.382C48.448,57.858 44.985,58.105 44.628,58.666C44.292,59.153 42.421,59.267 42.17,59.8C41.373,61.65 40.631,63.358 40.544,65.359C40.463,67.106 40.301,68.287 40.612,70.011C41.172,73.15 41.685,77.782 42.898,80.747C43.592,82.564 33.116,90.472 31.219,87.838C29.473,85.413 31.338,76.432 30.872,71.428C30.872,71.209 30.744,69.754 30.476,67.442C30.377,66.595 30.288,65.539 30.189,64.179C29.923,61.212 29.854,58.231 29.98,55.256C30.129,53.553 30.654,47.17 34.797,41.938C40.903,34.232 51.428,33.576 54.689,33.376C60.959,33.034 69.23,33.846 74.261,36.887C79.292,39.927 84.491,50.496 84.877,51.618Z" style="fill-rule:nonzero;"></path><path d="M87.396,57.577L79.972,60.126C77.514,61.011 75.017,61.696 72.46,62.362C67.39,63.68 62.244,64.713 57.048,65.454L55.016,65.739L53.767,64.227C53.13,63.378 52.345,62.642 51.448,62.048C50.642,61.498 49.717,61.127 48.742,60.964C48.447,60.916 48.149,60.891 47.85,60.888C47.665,60.875 47.48,60.875 47.295,60.888C46.794,60.926 46.301,61.028 45.828,61.192C45.479,61.284 45.159,61.457 44.897,61.696C44.742,61.821 44.605,61.965 44.49,62.124C44.332,62.299 44.196,62.49 44.084,62.695C43.093,64.436 42.805,67.119 42.974,69.697C43.182,72.362 43.721,74.993 44.58,77.535C44.419,78.228 44.197,78.906 43.916,79.562C43.496,80.526 42.953,81.437 42.3,82.273C40.415,78.422 39.053,74.356 38.246,70.172C37.982,68.6 37.896,67.006 37.989,65.416C38.054,63.683 38.443,61.975 39.138,60.374C39.354,59.955 39.606,59.554 39.892,59.175C40.179,58.736 40.511,58.325 40.883,57.948C41.633,57.14 42.551,56.492 43.579,56.045C44.474,55.644 45.42,55.356 46.393,55.189C46.865,55.101 47.341,55.041 47.821,55.008L48.812,55.008C48.983,54.999 49.155,54.999 49.327,55.008C51.291,55.142 53.198,55.698 54.907,56.635C55.926,57.166 56.88,57.804 57.752,58.538C62.091,57.862 66.391,56.973 70.636,55.874C73.035,55.246 75.423,54.59 77.752,53.743L84.789,51.336L87.396,57.577Z" style="fill-rule:nonzero;"></path></g></g>',3)]))}const T4=M(A4,[["render",P4]]),B4={},H4={width:"100%",height:"100%",viewBox:"0 0 100 100",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","xml:space":"preserve","xmlns:serif":"http://www.serif.com/",style:{"fill-rule":"evenodd","clip-rule":"evenodd","stroke-linejoin":"round","stroke-miterlimit":"2"}};function j4(t,a){return l(),o("svg",H4,a[0]||(a[0]=[e("g",{id:"Livello_2"},[e("path",{d:"M20.115,37.792C20.499,38.165 20.674,38.705 20.583,39.232L18.116,53.622C18.1,53.715 18.092,53.809 18.092,53.903C18.092,54.793 18.825,55.526 19.716,55.526C19.98,55.526 20.241,55.462 20.475,55.338L33.388,48.545C33.865,48.286 34.442,48.286 34.919,48.545L47.839,55.338C48.073,55.462 48.334,55.526 48.599,55.526C49.489,55.526 50.222,54.793 50.222,53.903C50.222,53.809 50.214,53.715 50.198,53.622L47.724,39.232C47.633,38.705 47.808,38.165 48.191,37.792L58.661,27.599C58.976,27.293 59.154,26.872 59.154,26.433C59.154,25.63 58.559,24.942 57.765,24.826L43.298,22.728C42.768,22.648 42.309,22.313 42.072,21.832L35.616,8.736C35.342,8.18 34.773,7.826 34.153,7.826C33.533,7.826 32.965,8.18 32.691,8.736L26.234,21.832C25.996,22.31 25.538,22.642 25.009,22.72L10.558,24.826C9.757,24.936 9.155,25.627 9.155,26.435C9.155,26.873 9.332,27.293 9.646,27.599L20.115,37.792Z",style:{"fill-rule":"nonzero"}})],-1),e("g",{id:"Livello_21","serif:id":"Livello_2"},[e("path",{d:"M51.781,74.628C52.164,75.001 52.339,75.541 52.248,76.068L49.782,90.458C49.766,90.551 49.758,90.645 49.758,90.739C49.758,91.629 50.491,92.362 51.381,92.362C51.646,92.362 51.907,92.297 52.141,92.174L65.053,85.38C65.531,85.122 66.108,85.122 66.585,85.38L79.505,92.174C79.739,92.297 80,92.362 80.265,92.362C81.155,92.362 81.888,91.629 81.888,90.739C81.888,90.645 81.88,90.551 81.864,90.458L79.39,76.068C79.299,75.541 79.474,75.001 79.857,74.628L90.326,64.434C90.642,64.129 90.82,63.708 90.82,63.269C90.82,62.466 90.225,61.778 89.43,61.662L74.963,59.564C74.433,59.484 73.975,59.148 73.738,58.668L67.282,45.571C67.008,45.016 66.439,44.662 65.819,44.662C65.199,44.662 64.631,45.016 64.356,45.571L57.9,58.668C57.661,59.145 57.203,59.477 56.675,59.556L42.223,61.662C41.423,61.772 40.82,62.463 40.82,63.271C40.82,63.709 40.998,64.129 41.312,64.434L51.781,74.628Z",style:{"fill-rule":"nonzero"}})],-1)]))}const E4=M(B4,[["render",j4]]),V4={},F4={width:"100%",height:"100%",viewBox:"0 0 100 100",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink","xml:space":"preserve","xmlns:serif":"http://www.serif.com/",style:{"fill-rule":"evenodd","clip-rule":"evenodd","stroke-linejoin":"round","stroke-miterlimit":"2"}};function z4(t,a){return l(),o("svg",F4,a[0]||(a[0]=[e("g",{id:"Livello_2"},[e("path",{d:"M16.976,33.18C17.283,33.478 17.423,33.91 17.35,34.331L15.377,45.844C15.364,45.918 15.357,45.993 15.357,46.068C15.357,46.781 15.944,47.367 16.656,47.367C16.868,47.367 17.077,47.315 17.264,47.216L27.594,41.782C27.976,41.575 28.437,41.575 28.819,41.782L39.155,47.216C39.343,47.315 39.551,47.367 39.763,47.367C40.476,47.367 41.062,46.781 41.062,46.068C41.062,45.993 41.055,45.918 41.042,45.844L39.063,34.331C38.991,33.91 39.131,33.478 39.437,33.18L47.813,25.025C48.065,24.78 48.207,24.443 48.207,24.092C48.207,23.45 47.731,22.899 47.096,22.807L35.522,21.128C35.098,21.064 34.731,20.796 34.542,20.411L29.377,9.934C29.157,9.49 28.703,9.207 28.207,9.207C27.711,9.207 27.256,9.49 27.036,9.934L21.871,20.411C21.68,20.793 21.314,21.059 20.891,21.122L9.33,22.807C8.69,22.895 8.207,23.447 8.207,24.094C8.207,24.444 8.349,24.78 8.601,25.025L16.976,33.18Z",style:{"fill-rule":"nonzero"}})],-1),e("g",{id:"Livello_21","serif:id":"Livello_2"},[e("path",{d:"M60.496,44.581C60.803,44.879 60.943,45.311 60.87,45.733L58.897,57.245C58.884,57.319 58.878,57.394 58.878,57.469C58.878,58.182 59.464,58.768 60.177,58.768C60.389,58.768 60.597,58.717 60.784,58.617L71.114,53.183C71.496,52.976 71.958,52.976 72.34,53.183L82.676,58.617C82.863,58.717 83.072,58.768 83.283,58.768C83.996,58.768 84.582,58.182 84.582,57.469C84.582,57.394 84.576,57.319 84.563,57.245L82.584,45.733C82.511,45.311 82.651,44.879 82.958,44.581L91.333,36.426C91.585,36.181 91.728,35.845 91.728,35.493C91.728,34.851 91.252,34.301 90.616,34.208L79.042,32.529C78.618,32.465 78.252,32.197 78.062,31.812L72.897,21.335C72.678,20.891 72.223,20.608 71.727,20.608C71.231,20.608 70.776,20.891 70.557,21.335L65.392,31.812C65.201,32.195 64.834,32.46 64.412,32.523L52.85,34.208C52.21,34.296 51.728,34.849 51.728,35.495C51.728,35.845 51.87,36.181 52.121,36.426L60.496,44.581Z",style:{"fill-rule":"nonzero"}}),e("path",{d:"M34.209,77.382C34.516,77.68 34.656,78.112 34.583,78.533L32.61,90.046C32.597,90.12 32.591,90.195 32.591,90.27C32.591,90.983 33.177,91.569 33.89,91.569C34.101,91.569 34.31,91.517 34.497,91.418L44.827,85.984C45.209,85.777 45.671,85.777 46.053,85.984L56.389,91.418C56.576,91.517 56.784,91.569 56.996,91.569C57.709,91.569 58.295,90.983 58.295,90.27C58.295,90.195 58.289,90.12 58.276,90.046L56.297,78.533C56.224,78.112 56.364,77.68 56.67,77.382L65.046,69.227C65.298,68.982 65.441,68.645 65.441,68.294C65.441,67.652 64.964,67.101 64.329,67.009L52.755,65.33C52.331,65.266 51.965,64.998 51.775,64.613L46.61,54.136C46.391,53.692 45.936,53.409 45.44,53.409C44.944,53.409 44.489,53.692 44.27,54.136L39.105,64.613C38.914,64.996 38.547,65.261 38.124,65.324L26.563,67.009C25.923,67.097 25.441,67.65 25.441,68.296C25.441,68.646 25.583,68.982 25.834,69.227L34.209,77.382Z",style:{"fill-rule":"nonzero"}})],-1)]))}const O4=M(V4,[["render",z4]]),I4={class:"sticky top-0 z-10 flex h-16 shrink-0 items-center gap-x-6 border-b border-gray-300 bg-white px-4 shadow-xs dark:bg-gray-900 sm:px-6 lg:px-8"},N4={class:"justify-left align-center flex w-full flex-1 pr-2"},D4={class:"flex w-full max-w-lg"},W4={key:0,class:"align-center flex flex-row"},R4={key:0,class:"self-center"},G4={key:0,href:"/subscribe/edit",class:"flex items-center rounded-xl border border-indigo-100 bg-indigo-100 px-3 py-0.5 text-center text-sm font-semibold text-indigo-600 transition duration-150 ease-in-out hover:border-indigo-200 hover:bg-indigo-200"},q4={key:1,class:"flex items-center rounded-xl border border-sky-100 bg-sky-100 px-3 py-0.5 text-center text-sm font-semibold text-sky-600 transition duration-150 ease-in-out hover:border-sky-200 hover:bg-sky-200",href:"/"},K4={key:2,class:"self-center"},U4={href:"/subscribe",class:"flex items-center rounded-xl border border-amber-100 bg-amber-100 px-3 py-0.5 text-center text-sm font-semibold text-amber-600 transition duration-150 ease-in-out hover:border-amber-200 hover:bg-amber-200"},Y4={key:1,class:"flex gap-4 self-center"},J4={key:1,class:"align-center flex flex-row gap-8"},Q4={__name:"TopNav",props:{authenticated:Boolean},emits:"update:sidebar",setup(t,{emit:a}){const d=a;let u=B(!1);const m=()=>{setTimeout(()=>{u.value=!1},200)};return C3(()=>{document.addEventListener("keydown",g=>{(g.key==="k"||g.key==="K")&&(g.metaKey||g.ctrlKey)&&(g.preventDefault(),u.value=!0)})}),(g,r)=>(l(),o("div",I4,[e("button",{type:"button",class:"-m-2.5 p-2.5 text-gray-400 xl:hidden",onClick:r[0]||(r[0]=s=>d("update:sidebar",!0))},[r[3]||(r[3]=e("span",{class:"sr-only"},"Open sidebar",-1)),h(n(q1),{class:"h-5 w-5","aria-hidden":"true"})]),e("div",N4,[e("div",D4,[e("button",{class:"dark:highlight-white/5 hidden w-full items-center rounded-xl py-1.5 pl-2 pr-3 text-sm leading-6 text-slate-400 shadow-xs ring-1 ring-slate-900/10 hover:ring-slate-300 dark:bg-slate-800 dark:hover:bg-slate-700 lg:flex",onClick:r[1]||(r[1]=s=>n1(u)?u.value=!n(u):u=!n(u))},[h(n(J3),{class:"mr-3 h-5 w-5 stroke-2","aria-hidden":"true"}),r[4]||(r[4]=D(" Quick search... ")),r[5]||(r[5]=e("kbd",{class:"ml-auto inline-flex items-center rounded-sm border border-gray-200 px-1 font-sans text-xs text-gray-400"},"⌘K",-1))])])]),t.authenticated?(l(),o("div",W4,[n(y)().props.user.membership.subscribed?(l(),o("div",R4,[n(y)().props.user.membership.individualSubscribed?(l(),o("a",G4,[h(n(o3),{class:"mr-2 inline-block h-6 w-6 fill-indigo-600"}),r[6]||(r[6]=e("span",{class:"inline text-base font-bold"},"Pro",-1))])):n(y)().props.user.membership.teamSubscribed?(l(),o("a",q4,[h(n(o3),{class:"mr-2 inline-block h-6 w-6 fill-sky-600"}),r[7]||(r[7]=e("span",{class:"inline text-base font-bold"},"Class Pro",-1))])):n(y)().props.user.membership.onTrial?(l(),o("div",K4,[e("a",U4,[h(n(o3),{class:"mr-2 inline-block h-6 w-6 fill-amber-600"}),r[8]||(r[8]=e("span",{class:"inline text-base font-bold"},"7 Day Trial",-1))])])):z("",!0)])):(l(),o("div",Y4,[h($3,{link:"/subscribe",size:"xs",color:"indigo",class:"rounded-xl"},{default:x(()=>r[9]||(r[9]=[D(" Join LatinTutorial Pro ")])),_:1})])),h(B0,{class:"ml-4 self-center"})])):(l(),o("div",J4,[h($3,{link:"/register",size:"xs",color:"indigo"},{default:x(()=>r[10]||(r[10]=[D(" Join LatinTutorial ")])),_:1}),r[11]||(r[11]=e("a",{href:"/login",class:"text-xs font-semibold leading-6 text-gray-900"},[D("Log In "),e("span",{"aria-hidden":"true"},"→")],-1))])),(l(),T(V3,{to:"body"},[h(d2,{class:"z-50",open:n(u),"onUpdate:close":r[2]||(r[2]=s=>m())},null,8,["open"])]))]))}},X4={__name:"AchievementIcon",props:{icon:{type:String,required:!0},level:{type:String,default:"basic"},size:{type:String,default:"md"},isEarned:{type:Boolean,default:!1},description:{type:String,default:""},name:{type:String,default:""}},setup(t){const a={Colosseum:o3,FifteenDayStreak:f2,FiveDayStreak:N2,TenDayStreak:v4,Video:Z4,TwentyDayStreak:L4,ThirtyDayStreak:$1,OneYearMember:s4,TwoYearMember:i4,ThreeYearMember:p4,TwoDayStreak:_4,FirstHundredPoints:S2,FirstThousandPoints:F2,FirstFiveThousandPoints:y2,FirstTenThousandPoints:H2,FirstFiftyThousandPoints:m2,FirstHundredThousandPoints:k2,LearnBasicWords:Y2,LearnIntermediateWords:e4,LearnAdvancedWords:G2,TribuneHelmet:T4,DoubleStar:E4,TripleStar:O4};return(d,u)=>(l(),o("div",{class:S(["rounded-full items-center justify-center flex shrink-0",{"bg-orange-300":t.level=="basic"&&t.isEarned,"bg-lime-300":t.level=="intermediate"&&t.isEarned,"bg-purple-300":t.level=="advanced"&&t.isEarned,"bg-gray-300":!t.isEarned,"w-20 h-20":t.size=="lg","w-12 h-12":t.size=="md","w-10 h-10":t.size=="sm"}])},[(l(),T(e3(a[t.icon]),{class:S({"fill-orange-700":t.level=="basic"&&t.isEarned,"fill-lime-700":t.level=="intermediate"&&t.isEarned,"fill-purple-700":t.level=="advanced"&&t.isEarned,"fill-gray-500 text-gray-500":!t.isEarned,"w-16 w-16":t.size=="lg","w-10 h-10":t.size=="md","w-8 w-8":t.size=="sm"})},null,8,["class"]))],2))}};const e7={class:"p-4"},t7={class:"flex items-center"},a7={class:"flex shrink-0 items-center"},r7={class:"ml-3 w-0 flex-1 pt-0.5"},s7={class:"text-base font-semibold text-gray-900"},l7=["innerHTML"],n7={class:"ml-4 flex shrink-0"},o7={__name:"Notification",props:{data:Object,removing:Boolean},emits:["close"],setup(t,{emit:a}){const d=a,u=()=>{d("close")};return(m,g)=>(l(),o("div",null,[e("div",{class:S(["pointer-events-auto w-full max-w-sm overflow-hidden rounded-xl bg-white shadow-lg",{"fade-out":t.removing}])},[e("div",e7,[e("div",t7,[e("div",a7,[t.data.icon=="ArrowUpCircleIcon"?(l(),T(n(M1),{key:0,class:"h-12 w-12 text-blue-500","aria-hidden":"true"})):t.data.icon=="CheckCircleIcon"?(l(),T(n(k1),{key:1,class:"h-12 w-12 text-green-500","aria-hidden":"true"})):(l(),T(X4,{key:2,icon:t.data.icon,level:t.data.level,"is-earned":!0,size:"md","aria-hidden":"true"},null,8,["icon","level"]))]),e("div",r7,[e("p",s7,P(t.data.title),1),e("p",{class:"mt-1 text-sm font-medium text-gray-500",innerHTML:t.data.description},null,8,l7)]),e("div",n7,[e("button",{type:"button",onClick:g[0]||(g[0]=r=>u()),class:"inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden"},[g[1]||(g[1]=e("span",{class:"sr-only"},"Close",-1)),h(n(U1),{class:"h-5 w-5","aria-hidden":"true"})])])])])],2)]))}};const i7={class:"xl:pl-72"},c7={"aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex px-4 py-6 sm:p-6 z-50"},P7={__name:"AppLayout",setup(t){const a=B(!1);let d=B([]);const u=new Map;y().props.authenticated&&window.Echo.private(`user.${y().props.user.id}`).listen("BroadcastAchievement",r=>{m(r.achievement)}).listen("BroadcastAssignmentCompleted",r=>{m(r)}).listen("BroadcastNextLevel",r=>{m(r)});const m=r=>{const s={id:Date.now(),title:r.name||r.title,description:r.description,icon:r.icon,level:r.level?r.level:null,removing:!1};d.value.push(s);const i=setTimeout(()=>{g(s.id)},6e3);u.set(s.id,i)},g=r=>{const s=d.value.find(i=>i.id===r);s&&(s.removing=!0,setTimeout(()=>{const i=d.value.findIndex(b=>b.id===r);i!==-1&&(d.value.splice(i,1),clearTimeout(u.get(r)),u.delete(r))},300))};return v3(()=>{u.forEach(r=>{clearTimeout(r)})}),(r,s)=>(l(),o("div",null,[h(M0,{sidebarOpen:a.value,authenticated:n(y)().props.authenticated,"onUpdate:sidebar":s[0]||(s[0]=i=>a.value=i)},null,8,["sidebarOpen","authenticated"]),e("div",i7,[h(Q4,{"onUpdate:sidebar":s[1]||(s[1]=i=>a.value=i),authenticated:n(y)().props.authenticated},null,8,["authenticated"]),o1(r.$slots,"default")]),(l(),T(V3,{to:"body"},[e("div",c7,[h(i1,{tag:"div",class:"flex w-full flex-col items-end","enter-active-class":"ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-300 absolute","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:x(()=>[(l(!0),o(H,null,O(n(d),i=>(l(),T(o7,{class:"z-50 w-full flex justify-end notification-item pb-4",key:i.id,data:i,removing:i.removing,onClose:()=>g(i.id)},null,8,["data","removing","onClose"]))),128))]),_:1})])]))]))}};export{V1 as L,P7 as _,X4 as a,I1 as b,S7 as d,G3 as r,V5 as t};
