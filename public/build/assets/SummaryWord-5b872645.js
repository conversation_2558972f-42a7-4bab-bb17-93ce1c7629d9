import{l as g,i as $,a1 as B,o as c,d as h,a as e,b as n,w,t as o,q as u,c as b,n as a,u as m,f as O}from"./app-f0078ddb.js";import{_ as k}from"./ButtonItem-718c0517.js";import{r as y}from"./CheckCircleIcon-d86d1232.js";import{r as _}from"./StarIcon-155a2a28.js";/* empty css            */const M={class:"flex w-full flex-row items-center py-6"},z={class:"flex grow flex-col gap-4 truncate"},D={class:"flex grow flex-col gap-2 overflow-hidden"},N={class:"flex flex-col items-start gap-2"},V={class:"text-left text-xl"},A={class:"font-semibold text-gray-900"},R={class:"flex gap-2 text-base font-medium text-gray-600"},j={key:0,class:"text-base font-medium text-gray-600"},q={class:"truncate text-ellipsis text-base font-medium text-gray-600"},E={class:"grid grid-cols-2 gap-4 sm:hidden"},W={class:"text-center"},F={class:"text-2xl font-bold text-purple-600 opacity-75"},G={class:"text-center"},H={class:"text-2xl font-bold text-red-600 opacity-75"},I={class:"flex grid grid-cols-2 gap-4 text-center sm:hidden sm:p-1"},J={class:"ml-4 flex hidden flex-row items-center gap-4 text-center sm:inline-flex"},K={class:"text-center"},P={class:"text-2xl font-bold text-purple-600 opacity-75"},Q={class:"text-center"},T={class:"text-2xl font-bold text-red-600 opacity-75"},te={__name:"SummaryWord",props:{word:Object,isLearned:Boolean,isStarred:Boolean},emits:["update:toggle-learned","update:toggle-starred"],setup(r,{emit:C}){const t=r,d=C,f=()=>{d("update:toggle-learned",{id:t.word.word_id}),axios.post("/words/"+t.word.word_id+"/learn").then(i=>{i.data.status!="success"&&d("update:toggle-learned",{id:t.word.id})})},p=()=>{d("update:toggle-starred",{id:t.word.word_id}),axios.post("/words/"+t.word.word_id+"/star").then(i=>{i.data.status!="success"&&d("update:toggle-starred",{id:t.word.word_id})})},S=g(()=>{switch(!0){case t.word.list==="uncommon":return"border-purple-500 text-purple-500";case t.word.list=="basic":return"border-blue-500 text-blue-500";case t.word.list=="intermediate":return"border-pink-500 text-pink-500";case t.word.list=="advanced":return"border-orange-500 text-orange-500";default:return"border-purple-500 text-purple-500"}});g(()=>{switch(!0){case t.word.list==="uncommon":return"uncommon";case t.word.list=="basic":return"basic";case t.word.list=="intermediate":return"intermediate";case t.word.list=="advanced":return"advanced";default:return"uncommon"}});const v=g(()=>{switch(!0){case t.word.gender===null:return"";case t.word.gender==="m.":return"masculine";case t.word.gender==="f.":return"feminine";case t.word.gender==="n.":return"neuter";case t.word.gender==="m./f.":return"masculine/feminine";case t.word.gender==="m./n.":return"masculine/neuter";case t.word.gender==="m./f./n.":return"masculine/feminine/neuter";case t.word.gender==="f./n.":return"feminine/neuter";default:return t.word.gender}});return(i,s)=>{const L=$("Link"),l=B("tippy");return c(),h("div",M,[e("div",z,[n(L,{href:`/words/w/${r.word.word_id}`,class:"group flex w-full grow flex-col sm:flex-row"},{default:w(()=>[e("div",D,[e("div",N,[e("div",null,[e("h4",V,[e("span",A,o(t.word.latin),1)])]),e("div",R,[e("span",null,o(t.word.pos),1),v.value?(c(),h("span",j,o(v.value),1)):O("",!0),e("span",{class:a(["inline-flex items-center rounded-full border-2 px-2.5 py-0.5 text-xs font-semibold",S.value])},o(r.word.list),3)])]),e("h5",q,o(t.word.definition),1)])]),_:1},8,["href"]),e("div",E,[e("div",W,[e("h5",F,o(r.word.correct),1),s[4]||(s[4]=e("p",{class:"text-sm text-gray-600"},"correct",-1))]),e("div",G,[e("h5",H,o(r.word.total-r.word.correct),1),s[5]||(s[5]=e("p",{class:"text-sm text-gray-600"},"incorrect",-1))])]),e("div",I,[u((c(),b(k,{class:"w-full",color:"white",size:"xs",onClick:s[0]||(s[0]=x=>f())},{default:w(()=>[n(m(y),{class:a(["mx-auto h-5 w-5 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-blue-600",[r.isLearned?"text-blue-600":"text-gray-400"]])},null,8,["class"])]),_:1})),[[l,{content:r.isLearned?"Mark as not learned":"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),u((c(),b(k,{class:"w-full",color:"white",size:"xs",onClick:s[1]||(s[1]=x=>p())},{default:w(()=>[n(m(_),{class:a(["duration-250 mx-auto h-5 w-5 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-green-600",[r.isStarred?"text-green-600":"stroke-current text-gray-400"]])},null,8,["class"])]),_:1})),[[l,{content:r.isStarred?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])]),e("div",J,[e("div",K,[e("h5",P,o(r.word.correct),1),s[6]||(s[6]=e("p",{class:"text-sm text-gray-600"},"correct",-1))]),e("div",Q,[e("h5",T,o(r.word.total-r.word.correct),1),s[7]||(s[7]=e("p",{class:"text-sm text-gray-600"},"incorrect",-1))]),e("div",null,[u(n(m(_),{class:a(["mx-auto h-6 w-6 cursor-pointer stroke-2 transition duration-150 hover:text-green-600",[r.isStarred?"text-green-600":"text-gray-400"]]),onClick:s[2]||(s[2]=x=>p())},null,8,["class"]),[[l,{content:r.isStarred?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])]),e("div",null,[u(n(m(y),{class:a(["mx-auto h-6 w-6 cursor-pointer stroke-2 transition duration-150 hover:text-blue-600",[r.isLearned?"text-blue-600":"text-gray-400"]]),onClick:s[3]||(s[3]=x=>f())},null,8,["class"]),[[l,{content:r.isLearned?"Mark as not learned":"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])])])}}};export{te as default};
