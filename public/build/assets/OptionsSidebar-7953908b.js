import{o as r,d as n,a as s,F as i,h as g,f as p,r as x,W as m,g as y,t as e}from"./app-f0078ddb.js";/* empty css            */const _={key:0,class:"mt-8"},f={class:"mt-2 rounded-lg bg-white shadow-sm"},h={class:"mt-2 flex flex-row items-center"},b=["src"],v={class:"ml-2 flex grow flex-col"},k={class:"text-lg font-bold font-intro text-gray-900"},w={key:0},S={key:1},B={class:"text-xs text-gray-600"},V={__name:"OptionsSidebar",props:{sections:Array,currentList:Array},setup(a){let c=a;const l=()=>{m.get("/practice/grammar",{tab:"readings",sections:c.currentList})};return(d,o)=>(r(),n(i,null,[a.sections?(r(),n("div",_,[o[2]||(o[2]=s("h5",{class:"text-sm font-bold uppercase text-gray-500 mt-8"},"Sections",-1)),s("div",f,[(r(!0),n(i,null,g(a.sections,(t,u)=>(r(),n("div",{key:u,class:"h-20 w-full px-4 py-2"},[s("div",h,[s("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.image_name,class:"h-12 w-12"},null,8,b),s("div",v,[s("h4",k,[y(e(t.work)+" "+e(t.l1)+" "+e(t.book)+".",1),t.start===t.end?(r(),n("span",w,e(t.start),1)):(r(),n("span",S,e(t.start)+"-"+e(t.end),1))]),s("p",B,e(t.word_count)+" nouns and verbs ",1)])])]))),128)),s("div",{class:"mt-2 py-2 px-4 bg-gray-100 group hover:bg-gray-200 rounded-b-lg cursor-pointer transition duration-150 ease-in-out",onClick:o[0]||(o[0]=t=>l())},o[1]||(o[1]=[s("div",{class:"cursor-pointer text-sm text-gray-600 group-hover:text-gray-700 text-center font-semibold transition duration-150 ease-in-out"}," Edit Sections ",-1)]))])])):p("",!0),x(d.$slots,"default")],64))}};export{V as default};
