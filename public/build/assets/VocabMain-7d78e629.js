import{_ as g}from"./WordItem-d0f526f8.js";import{e as i,o as n,d as u,a as f,F as y,h,c as b,n as B,u as c}from"./app-f0078ddb.js";import"./ButtonItem-718c0517.js";import"./CheckCircleIcon-d86d1232.js";import"./StarIcon-155a2a28.js";/* empty css            */const _={class:"grid grid-cols-1 divide-y"},U={__name:"VocabMain",props:{vocab:Object,learned:Array,starred:Array,authenticated:<PERSON><PERSON><PERSON>},setup(d){const a=d;let t=a.learned?i(a.learned):[],s=a.starred?i(a.starred):[];const m=r=>{t.value.some(e=>e==r.id)?t.value=t.value.filter(e=>e!=r.id):t.value.push(r.id)},p=r=>{s.value.some(e=>e==r.id)?s.value=s.value.filter(e=>e!=r.id):s.value.push(r.id)};return(r,e)=>(n(),u("div",null,[f("div",_,[(n(!0),u(y,null,h(a.vocab,(v,o)=>(n(),b(g,{class:B([{"mt-3":o===0},"hover:bg-slate-50 sm:px-4"]),key:o,word:v,"learned-array":c(t),"starred-array":c(s),authenticated:d.authenticated,"onUpdate:toggleLearned":e[0]||(e[0]=l=>m(l)),"onUpdate:toggleStarred":e[1]||(e[1]=l=>p(l))},null,8,["class","word","learned-array","starred-array","authenticated"]))),128))])]))}};export{U as default};
