import{o as l,d as i,a as e,e as g,l as W,p as H,W as S,i as re,c as h,w as f,b as c,j as m,u as r,q as x,bP as he,h as M,t as p,F as V,n as R,k as u,f as v,x as O,g as I,T as ye,R as be,v as k}from"./app-f0078ddb.js";import{_ as ke}from"./AppLayout-33f062bc.js";import{_ as _e}from"./Breadcrumbs-c96e9207.js";import{_ as le}from"./ProgressBar-b7203293.js";import oe from"./ActivityItem-7b8627ed.js";import we from"./GrammarActivityItem-1c811db8.js";import{_ as Le}from"./DropdownWorks-53c01eb4.js";import Ce from"./DropdownBooks-b7db1f80.js";import{_ as D}from"./ButtonItem-718c0517.js";import{p as ie}from"./pluralize-d25a928b.js";import{l as Se}from"./lodash-631955d9.js";import{u as Me}from"./useIntersect-6e15125e.js";import{P as ae}from"./Promotion-3eee0057.js";import{_ as Ve}from"./Footer-0988dcd8.js";import{_ as Ae}from"./MobileSidebar-5e21b4cd.js";import{r as Te}from"./InformationCircleIcon-716f3ffb.js";import{r as Ue}from"./ArrowPathIcon-f74cb8d6.js";import{r as $e}from"./CalendarDaysIcon-19bddb32.js";import{r as je}from"./RectangleGroupIcon-04390470.js";import{r as qe}from"./MagnifyingGlassIcon-a45957e4.js";import{r as ne}from"./XCircleIcon-63af2b2a.js";import{S as He,r as Ie,M as Pe,b as ze,g as Ne}from"./ChevronDownIcon-660c32b0.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./use-resolve-button-type-24d8b5c5.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./datetime-8ddd27a0.js";import"./listbox-f702e976.js";import"./use-text-value-2c18b2b1.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";function Be(d,n){return l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function Ee(d,n){return l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 4.5h14.25M3 9h9.75M3 13.5h9.75m4.5-4.5v12m0 0-3.75-3.75M17.25 21 21 17.25"})])}function Fe(d,n){return l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 4.5h14.25M3 9h9.75M3 13.5h5.25m5.25-.75L17.25 9m0 0L21 12.75M17.25 9v12"})])}function Re(d,n){return l(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}const De={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Ge={class:"px-4 pt-8 sm:px-8"},We={class:"flex flex-row items-center justify-between"},Oe={class:"mt-8"},Ye={class:"sm:hidden"},Ke=["value"],Xe={class:"hidden sm:block"},Ze={class:"border-b border-gray-200"},Je={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Qe=["onClick","aria-current"],et={key:0},tt={class:"mt-1 font-intro text-sm font-medium text-emerald-700"},st={class:"mt-1 text-center font-intro text-sm font-medium text-purple-700"},rt={class:"mt-1 text-center font-intro text-sm font-medium text-indigo-700"},lt={class:"mt-12 grid grid-cols-2 gap-4"},ot={class:"relative mt-2 w-64"},it={class:"pointer-events-none absolute inset-y-0 left-0 flex items-center"},at={class:"absolute inset-y-0 right-0 flex items-center pr-3"},nt={class:"flex w-full items-end justify-end"},dt={class:"py-1"},ct=["onClick"],ut={class:"mt-8 grid w-full grid-cols-1 gap-8"},mt={key:1,class:"px-6 py-14 text-center text-sm sm:px-14"},pt={key:1},vt={class:"mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2"},gt={class:"order-2 mt-4 sm:order-1 sm:mt-0 sm:pr-4"},ft={class:"mt-8"},xt={class:"block text-sm font-medium text-gray-700"},ht={class:"mt-8 grid grid-cols-2 gap-8"},yt={for:"start",class:"block text-sm font-medium text-gray-700"},bt=["innerHTML"],kt=["innerHTML"],_t={class:"mt-1"},wt={for:"last",class:"block text-sm font-medium text-gray-700"},Lt=["innerHTML"],Ct=["innerHTML"],St={class:"mt-1"},Mt={class:"mt-2 text-xs text-gray-600"},Vt=["innerHTML"],At={class:"mx-auto flex items-center"},Tt={class:"order-1 sm:order-2"},Ut={key:0},$t={key:0,class:"mt-2 flex flex-row items-center"},jt=["src"],qt={class:"ml-2 flex grow flex-col"},Ht={class:"font-intro text-lg leading-5 font-bold text-gray-900"},It={key:0},Pt={key:1},zt={class:"text-xs text-gray-600"},Nt={class:"ml-2 w-5 grow-0"},Bt={key:0,class:"mt-4"},Et={key:1,class:"mt-4"},Ft={class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"},Rt={key:0,class:"mx-auto mt-1 mb-1"},Dt={key:1,class:"mx-auto"},Gt={key:1},Wt={key:1,class:"mt-2"},Ot={class:"text-center text-xs font-bold text-gray-500 uppercase"},Yt={"aria-labelledby":"filter activities",class:"mt-8"},Kt={key:0,class:"grid grid-cols-1 divide-y"},Xt={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Zt={class:"relative flex items-center"},Jt={class:"flex h-5 items-center"},Qt={class:"relative flex items-center"},es={class:"flex h-5 items-center"},ts={class:"grid grid-cols-1 divide-y"},ss={class:"ml-4 grid grid-cols-2 gap-3 py-4"},rs={class:"relative flex items-center"},ls={class:"flex h-5 items-center"},os={class:"relative flex items-center"},is={class:"flex h-5 items-center"},as={class:"relative flex items-center"},ns={class:"flex h-5 items-center"},ds={class:"grid grid-cols-1 divide-y"},cs={class:"ml-4 grid grid-cols-2 gap-3 py-4"},us={class:"flex h-5 items-center"},ms=["id","value","name"],ps={class:"ml-3 text-sm"},vs=["for"],gs={key:2,class:"mt-8 bg-white"},fs={class:"mt-2 grid grid-cols-1 gap-2"},xs={key:0,class:"mt-2 block flex w-full justify-center rounded-lg border-2 border-dashed border-gray-300 p-12"},hs={key:0,class:"mt-8"},ys={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},bs={key:1,class:"mt-2"},ks={class:"text-center text-xs font-bold text-gray-500 uppercase"},_s={"aria-labelledby":"filter activities",class:"mt-8"},ws={key:0,class:"grid grid-cols-1 divide-y"},Ls={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Cs={class:"relative flex items-center"},Ss={class:"flex h-5 items-center"},Ms={class:"relative flex items-center"},Vs={class:"flex h-5 items-center"},As={class:"grid grid-cols-1 divide-y"},Ts={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Us={class:"relative flex items-center"},$s={class:"flex h-5 items-center"},js={class:"relative flex items-center"},qs={class:"flex h-5 items-center"},Hs={class:"relative flex items-center"},Is={class:"flex h-5 items-center"},Ps={class:"grid grid-cols-1 divide-y"},zs={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Ns={class:"flex h-5 items-center"},Bs=["id","value","name"],Es={class:"ml-3 text-sm"},Fs=["for"],Rs={key:2,class:"mt-8 rounded-lg bg-white p-4 shadow-sm"},Ds={class:"mt-2 grid grid-cols-1 gap-2"},Gs={key:0,class:"mt-2 block flex w-full justify-center rounded-lg border-2 border-dashed border-gray-300 p-12"},Ws={key:0,class:"mt-8"},Wr={__name:"Index",props:{tags:Array,tab:String,activities:Object,user_activities:Array,sections:Array,sectionItems:Array,works:Array,books:Array,search:String,filters:Array,sort:String,quickLinks:Object},setup(d){const n=d,de=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"#",current:!0}];let B=[{name:"Newest",value:"newest"},{name:"Popularity",value:"popularity"},{name:"Alphabetical",value:"alphabetical"},{name:"My Attempts",value:"attempts"}],P=g(n.tab);const Y=[{name:"Activities",current:n.tab=="activities"||n.tab==null},{name:"Readings",current:n.tab=="readings"}];let _=g(n.search),$=g([]),K=g(n.books),b=g(n.works[0]),z=g(n.books[0]),A=g(""),T=g(""),X=g(),ce=g(),Z=g(!1),o=g(n.filters?n.filters:[]),w=g(!(n.sort&&n.sort.split("_")[1]=="asc")),L=g(!1);const G=g(null);let j=g(n.activities),U=g(j.value.data?j.value.data:[]);const ue=W(()=>j.value.next_page_url!==null);G!==null&&Me(G,()=>{ue.value&&axios.get(j.value.next_page_url).then(a=>{U.value=[...U.value,...a.data.data],j.value=a.data})},{rootMargin:"0px 0px 250px 0px"});const J=()=>{o.value=[]},me=a=>B.find(t=>t.value==a.split("_")[0]);let C=g(n.sort?me(n.sort):B[0]);const E=W(()=>{let a=0;return n.sectionItems.forEach(t=>{a+=t.word_count}),a});n.sectionItems.forEach(a=>{$.value.push(a.book_id+":"+parseInt(a.start)+":"+parseInt(a.end))});const pe=W(()=>E.value<5),Q=a=>{S.get("/practice/grammar",{sections:$.value,tab:a},{replace:!0,preserveScroll:!0})},ee=a=>{let t=a.level.max-a.level.min+1;return(a.xp-a.level.min)/t*100},ve=a=>{$.value.splice(a,1),S.get("/practice/grammar",{tab:n.tab,sections:$.value},{preserveState:!0,replace:!0,preserveScroll:!0})},ge=()=>{$.value.push(z.value.id+":"+parseInt(A.value)+":"+parseInt(T.value)),A.value="",T.value="",S.get("/practice/grammar",{tab:n.tab,sections:$.value},{preserveState:!0,replace:!0,preserveScroll:!0})},te=a=>{let t=a.keyCode?a.keyCode:a.which;(t<48||t>57)&&t!==46&&a.preventDefault()},fe=()=>{Z.value=!0,S.get("/practice/grammar/c/attempt",{sections:$.value})},xe=()=>{_.value="",S.get("/practice/grammar",{},{preserveState:!0,replace:!0,preserveScroll:!0,only:["activities","filters","sort"],onSuccess:()=>{N()}})},N=()=>{j.value=n.activities,U.value=j.value.data};return H(()=>z,()=>{A.value="",T.value=""},{deep:!0}),H(b,a=>{axios.post("/api/practice/vocabulary/get-books",{work:a.id}).then(t=>{K.value=t.data,A.value="",T.value=""})},{deep:!0}),H(_,Se.throttle(()=>{S.get("/practice/grammar",{search:_.value,filters:o.value,sort:C.value.value+"_"+(w.value?"desc":"asc")},{preserveState:!0,replace:!0,preserveScroll:!0,only:["activities"],onSuccess:()=>{N()}})},250)),H(()=>o,()=>{S.get("/practice/grammar",{filters:o.value,search:_.value,sort:C.value.value+"_"+(w.value?"desc":"asc")},{only:["activities"],replace:!0,onSuccess:()=>{N()},preserveState:!0,preserveScroll:!0})},{deep:!0}),H(()=>n.sort,()=>{n.sort&&(C.value=B.find(a=>a.value==n.sort.split("_")[0]),w.value=n.sort.split("_")[1]=="desc")},{deep:!0}),H(()=>C,a=>{S.get("/practice/grammar",{sort:a.value.value+"_"+(w.value?"desc":"asc"),filters:o.value,search:_.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["activities"],onSuccess:()=>{N()}})},{deep:!0}),H(()=>w,a=>{S.get("/practice/grammar",{sort:C.value.value+"_"+(a.value?"desc":"asc"),filters:o.value,search:_.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["activities"],onSuccess:()=>{N()}})},{deep:!0}),(a,t)=>{const se=re("Head"),F=re("Link");return l(),h(ke,null,{default:f(()=>[c(se,null,{default:f(()=>t[25]||(t[25]=[e("title",null,"Practice Grammar",-1)])),_:1}),e("main",De,[e("div",Ge,[e("div",We,[c(_e,{class:"lg:col-span-9 xl:grid-cols-10",pages:de}),c(r(Te),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=s=>m(L)?L.value=!r(L):L=!r(L))})]),t[42]||(t[42]=e("div",{class:"overflow-visible"},[e("div",{class:"mt-8"},[e("h1",{class:"text-5xl font-bold text-gray-900"}," Master the Language "),e("p",{class:"mt-4 text-lg font-normal text-gray-600"}," Practice different aspects of Latin with almost unlimited activities. ")])],-1)),e("div",Oe,[e("div",Ye,[t[26]||(t[26]=e("label",{for:"tabs",class:"sr-only"},"Select a tab",-1)),x(e("select",{id:"tabs",name:"tabs","onUpdate:modelValue":t[1]||(t[1]=s=>m(P)?P.value=s:P=s),onChange:t[2]||(t[2]=s=>Q(r(P).toLowerCase())),class:"block w-full rounded-md border-gray-300 py-2 pr-10 pl-3 text-base focus:border-indigo-500 focus:ring-indigo-500 focus:outline-hidden sm:text-sm"},[(l(),i(V,null,M(Y,s=>e("option",{key:s.name,value:s.name.toLowerCase()},p(s.name),9,Ke)),64))],544),[[he,r(P)]])]),e("div",Xe,[e("div",Ze,[e("nav",Je,[(l(),i(V,null,M(Y,s=>e("div",{key:s.name,onClick:y=>Q(s.name.toLowerCase()),class:R([s.current?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700","cursor-pointer border-b-2 px-1 py-4 text-sm font-bold whitespace-nowrap"]),"aria-current":s.current?"page":void 0},p(s.name),11,Qe)),64))])])]),d.tab=="activities"||d.tab==null?(l(),i("div",et,[r(u)().props.authenticated?(l(),i("div",{key:0,class:R(["mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-2",!d.quickLinks.attempted||!d.quickLinks.unattempted?"md:grid-cols-3 2xl:grid-cols-3":"md:grid-cols-4 2xl:grid-cols-4"])},[d.quickLinks.attempted?(l(),h(F,{key:0,href:`/practice/grammar/a/${d.quickLinks.attempted.id}`,class:"flex h-32 cursor-pointer flex-col items-center justify-center rounded-xl bg-emerald-100 shadow-lg transition duration-150 hover:bg-emerald-200"},{default:f(()=>[c(r(Ue),{class:"h-8 w-8 stroke-2 text-emerald-600"}),t[27]||(t[27]=e("h2",{class:"mt-1 font-intro text-xl font-bold text-emerald-700"}," Review ",-1)),e("h5",tt,p(d.quickLinks.attempted.name),1)]),_:1},8,["href"])):v("",!0),c(F,{href:`/practice/grammar/a/${d.quickLinks.daily.id}`,class:"flex h-32 cursor-pointer flex-col items-center justify-center rounded-xl bg-purple-100 shadow-lg transition duration-150 hover:bg-purple-200"},{default:f(()=>[c(r($e),{class:"h-8 w-8 text-purple-600"}),t[28]||(t[28]=e("h2",{class:"mt-1 font-intro text-xl font-bold text-purple-700"}," Daily Activity ",-1)),e("h5",st,p(d.quickLinks.daily.name),1)]),_:1},8,["href"]),d.quickLinks.unattempted?(l(),h(F,{key:1,href:`/practice/grammar/a/${d.quickLinks.unattempted.id}`,class:"flex h-32 cursor-pointer flex-col items-center justify-center rounded-xl bg-indigo-100 shadow-lg transition duration-150 hover:bg-indigo-200"},{default:f(()=>[c(r(Re),{class:"h-8 w-8 text-indigo-600"}),t[29]||(t[29]=e("h2",{class:"mt-1 font-intro text-xl font-bold text-indigo-700"}," Something New ",-1)),e("h5",rt,p(d.quickLinks.unattempted.name),1)]),_:1},8,["href"])):v("",!0),r(u)().props.authenticated?(l(),h(F,{key:2,href:a.route("practice.activities.index"),class:"flex h-32 cursor-pointer flex-col items-center justify-center rounded-xl bg-sky-100 shadow-lg transition duration-150 hover:bg-sky-200"},{default:f(()=>[c(r(je),{class:"h-8 w-8 text-sky-600"}),t[30]||(t[30]=e("h2",{class:"mt-1 font-intro text-xl font-bold text-sky-700"}," All Activities ",-1)),t[31]||(t[31]=e("h5",{class:"mt-1 text-center font-intro text-sm font-medium text-sky-700"}," Your Attempts ",-1))]),_:1},8,["href"])):v("",!0)],2)):v("",!0),e("div",lt,[e("div",ot,[e("div",it,[c(r(qe),{class:"h-4 w-4 text-gray-400","aria-hidden":"true"})]),e("div",at,[r(_)?(l(),h(r(ne),{key:0,class:"h-5 w-5 transform cursor-pointer stroke-2 text-gray-400 duration-150 hover:text-gray-500","aria-hidden":"true",onClick:t[3]||(t[3]=s=>xe())})):v("",!0)]),x(e("input",{type:"text",name:"activity-search",id:"acivity-search","onUpdate:modelValue":t[4]||(t[4]=s=>m(_)?_.value=s:_=s),class:"peer block w-full border-0 py-1.5 pl-6 text-gray-900 focus:ring-0 sm:text-sm sm:leading-6",placeholder:"Search activities"},null,512),[[O,r(_)]]),t[32]||(t[32]=e("div",{class:"absolute inset-x-0 bottom-0 border-t border-gray-300 transition duration-150 peer-focus:border-t-2 peer-focus:border-blue-500","aria-hidden":"true"},null,-1))]),e("div",nt,[c(r(Ne),{as:"div",class:"relative"},{default:f(()=>[e("div",null,[c(r(He),{class:"group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900"},{default:f(()=>[t[33]||(t[33]=I(" Sort ")),c(r(Ie),{class:"-mr-1 ml-1 h-5 w-5 shrink-0 text-gray-400 group-hover:text-gray-500","aria-hidden":"true"})]),_:1})]),c(ye,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:f(()=>[c(r(Pe),{class:"absolute right-0 z-10 mt-2 w-40 origin-top-left rounded-md bg-white shadow-2xl focus:outline-hidden"},{default:f(()=>[e("div",dt,[(l(!0),i(V,null,M(r(B),s=>(l(),h(r(ze),{key:s},{default:f(({active:y})=>[e("a",{onClick:q=>m(C)?C.value=s:C=s,class:R([s.value==r(C).value?"font-medium text-gray-900":"text-gray-500",y?"bg-gray-100":"","block px-4 py-2 text-sm"])},p(s.name),11,ct)]),_:2},1024))),128))])]),_:1})]),_:1})]),_:1}),e("div",{class:"ml-2 cursor-pointer rounded-full p-1 transition duration-150 hover:bg-gray-100",onClick:t[5]||(t[5]=s=>m(w)?w.value=!r(w):w=!r(w))},[r(w)?(l(),h(r(Ee),{key:0,class:"h-5 w-5 text-gray-500","aria-hidden":"true"})):(l(),h(r(Fe),{key:1,class:"h-5 w-5 text-gray-500","aria-hidden":"true"}))])])]),e("div",ut,[r(U).length>0?(l(!0),i(V,{key:0},M(r(U),s=>(l(),h(we,{key:s.id,activity:s,authenticated:r(u)().props.authenticated},null,8,["activity","authenticated"]))),128)):(l(),i("div",mt,t[34]||(t[34]=[e("p",{class:"mt-4 font-semibold text-gray-900"},"No results found",-1),e("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything to match your request. Please try again. ",-1)])))])])):v("",!0),d.tab=="readings"?(l(),i("div",pt,[e("div",vt,[e("div",gt,[c(Le,{list:d.works,"onUpdate:work":t[6]||(t[6]=s=>m(b)?b.value=s:b=s)},null,8,["list"]),e("div",ft,[e("p",xt," Select the "+p(r(b).l1),1),c(Ce,{items:r(K),"onUpdate:selected":t[7]||(t[7]=s=>m(z)?z.value=s:z=s)},null,8,["items"])]),e("div",ht,[e("div",null,[e("label",yt,[t[35]||(t[35]=I("First ")),r(b).l2?(l(),i("span",{key:0,innerHTML:r(b).l2},null,8,bt)):(l(),i("span",{key:1,innerHTML:r(b).l4},null,8,kt))]),e("div",_t,[x(e("input",{id:"start","onUpdate:modelValue":t[8]||(t[8]=s=>m(A)?A.value=s:A=s),type:"text",name:"start",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 101",onKeypress:te},null,544),[[O,r(A)]])])]),e("div",null,[e("label",wt,[t[36]||(t[36]=I("Last ")),r(b).l2?(l(),i("span",{key:0,innerHTML:r(b).l2},null,8,Lt)):(l(),i("span",{key:1,innerHTML:r(b).l4},null,8,Ct))]),e("div",St,[x(e("input",{id:"last","onUpdate:modelValue":t[9]||(t[9]=s=>m(T)?T.value=s:T=s),type:"text",name:"last",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 157",onKeypress:te},null,544),[[O,r(T)]])])])]),e("p",Mt," Leaving the first or last blank will default to the start or end of this "+p(r(b).l1.toLowerCase())+". ",1),r(X)?(l(),i("p",{key:0,class:"mt-2 text-xs text-red-600",innerHTML:r(X)},null,8,Vt)):v("",!0),c(D,{class:"float-right mt-8 mb-8 flex w-full px-4 text-center",color:"gray",size:"sm",disabled:r(ce),onClick:t[10]||(t[10]=s=>ge())},{default:f(()=>[e("span",At,[t[37]||(t[37]=I("Add to List ")),c(r(Be),{class:"ml-2 inline-block h-5 w-5 self-center"})])]),_:1},8,["disabled"])]),e("div",Tt,[t[41]||(t[41]=e("h3",{class:"text-sm font-medium text-gray-700"},"Current List",-1)),d.sectionItems.length>0?(l(),i("div",Ut,[c(be,{"enter-active-class":"transition ease-out duration-250","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-250","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:f(()=>[(l(!0),i(V,null,M(d.sectionItems,(s,y)=>(l(),i("div",{key:y,class:R(["min-h-20 w-full rounded-lg border border-gray-300 px-4 py-2",y>0?"mt-4":"mt-2"])},[s?(l(),i("div",$t,[e("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.image_name,class:"h-12 w-12"},null,8,jt),e("div",qt,[e("h4",Ht,[I(p(s.work)+" "+p(s.l1)+" "+p(s.book)+".",1),s.start===s.end?(l(),i("span",It,p(s.start),1)):(l(),i("span",Pt,p(s.start)+"-"+p(s.end),1))]),e("p",zt,p(s.word_count)+" nouns and verbs ",1)]),e("div",Nt,[c(r(ne),{class:"h-5 w-5 cursor-pointer stroke-2 text-gray-400 transition duration-250 hover:text-red-600",onClick:q=>ve(y)},null,8,["onClick"])])])):v("",!0)],2))),128))]),_:1}),E.value==0?(l(),i("div",Bt,t[38]||(t[38]=[e("div",{class:"flex h-14 w-full items-center rounded-lg bg-linear-to-r from-amber-600 to-pink-500 px-4 py-2 text-center text-xs font-semibold text-white"}," We can only generate custom questions on sections with verified grammar. Please select a different section. ",-1)]))):E.value<6&&E.value>0?(l(),i("div",Et,[e("div",Ft,p(r(ie)("This",r(U).length))+" "+p(r(ie)("section",r(U).length))+" "+p(r(U).length==1?"does":"do")+" not contain enough words with verified grammar to generate custom questions. ",1)])):(l(),h(D,{key:2,class:"mt-8 w-full",color:"blue",size:"md",disabled:pe.value,onClick:t[11]||(t[11]=s=>fe())},{default:f(()=>[r(Z)?(l(),i("span",Rt,t[39]||(t[39]=[e("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",{class:"ml-2"},"Preparing ...",-1)]))):(l(),i("span",Dt,"Continue"))]),_:1},8,["disabled"]))])):(l(),i("div",Gt,t[40]||(t[40]=[e("div",{class:"relative mt-4 block h-20 w-full rounded-lg border-2 border-dashed border-gray-300 py-5 text-center"},[e("span",{class:"mt-2 block text-sm font-medium text-gray-600"}," No Sections Added ")],-1)])))])])])):v("",!0),e("div",{ref_key:"landmark",ref:G},null,512)])]),c(Ve)]),c(Ae,{class:"lg:hidden",show:r(L),onClose:t[18]||(t[18]=s=>m(L)?L.value=!1:L=!1)},{default:f(()=>[e("div",null,[r(u)().props.authenticated?(l(),h(le,{key:0,class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+r(u)().props.user.level.level,"post-text":r(u)().props.user.xp+" XP",progress:ee(r(u)().props.user)},null,8,["pre-text","post-text","progress"])):v("",!0),r(u)().props.authenticated?(l(),i("div",Wt,[e("p",Ot,p(r(u)().props.user.level.max-r(u)().props.user.xp+1)+" xp to the next level ",1)])):v("",!0),e("section",Yt,[e("div",null,[e("div",{class:"flex text-sm"},[t[43]||(t[43]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:J}," Clear ")])]),r(u)().props.authenticated?(l(),i("div",Kt,[e("div",Xt,[e("div",Zt,[e("div",Jt,[x(e("input",{id:"attempted","onUpdate:modelValue":t[12]||(t[12]=s=>m(o)?o.value=s:o=s),value:"attempted",name:"attempted",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[44]||(t[44]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"attempted",class:"font-semibold text-gray-600"},"practiced ")],-1))]),e("div",Qt,[e("div",es,[x(e("input",{id:"unattempted","onUpdate:modelValue":t[13]||(t[13]=s=>m(o)?o.value=s:o=s),value:"unattempted",name:"unattempted",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[45]||(t[45]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"unattempted",class:"font-semibold text-gray-600"},"not practiced ")],-1))])])])):v("",!0),e("div",ts,[e("div",ss,[e("div",rs,[e("div",ls,[x(e("input",{id:"basic","onUpdate:modelValue":t[14]||(t[14]=s=>m(o)?o.value=s:o=s),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[46]||(t[46]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"basic",class:"font-semibold text-blue-600"},"basic ")],-1))]),e("div",os,[e("div",is,[x(e("input",{id:"intermediate","onUpdate:modelValue":t[15]||(t[15]=s=>m(o)?o.value=s:o=s),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[47]||(t[47]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"intermediate",class:"font-semibold text-rose-600"},"intermediate ")],-1))]),e("div",as,[e("div",ns,[x(e("input",{id:"advanced","onUpdate:modelValue":t[16]||(t[16]=s=>m(o)?o.value=s:o=s),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[48]||(t[48]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"advanced",class:"font-semibold text-orange-600"},"advanced ")],-1))])])]),e("div",ds,[e("div",cs,[(l(!0),i(V,null,M(d.tags,(s,y)=>(l(),i("div",{class:"relative flex items-center",key:y},[e("div",us,[x(e("input",{id:`tag-${y}`,"onUpdate:modelValue":t[17]||(t[17]=q=>m(o)?o.value=q:o=q),value:s.name,name:s.name,type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,ms),[[k,r(o)]])]),e("div",ps,[e("label",{for:`tag-${y}`,class:"font-semibold text-gray-600"},p(s.name),9,vs)])]))),128))])])])]),r(u)().props.authenticated?(l(),i("div",gs,[t[51]||(t[51]=e("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Recent Grammar Activities ",-1)),e("div",fs,[(l(!0),i(V,null,M(d.user_activities,s=>(l(),h(oe,{key:s.id,activity:s},null,8,["activity"]))),128)),d.user_activities.length==0?(l(),i("div",xs,t[49]||(t[49]=[e("h5",{class:"text-sm font-semibold text-gray-900"}," No Activities Yet ",-1)]))):v("",!0)]),c(D,{class:"mt-4 w-full",color:"gray",size:"md",link:a.route("practice.activities.index")},{default:f(()=>t[50]||(t[50]=[I("View All Activities")])),_:1},8,["link"])])):v("",!0)]),r(u)().props.authenticated?v("",!0):(l(),i("div",hs,[c(ae)]))]),_:1},8,["show"]),e("aside",ys,[e("div",null,[r(u)().props.authenticated?(l(),h(le,{key:0,class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+r(u)().props.user.level.level,"post-text":r(u)().props.user.xp+" XP",progress:ee(r(u)().props.user)},null,8,["pre-text","post-text","progress"])):v("",!0),r(u)().props.authenticated?(l(),i("div",bs,[e("p",ks,p(r(u)().props.user.level.max-r(u)().props.user.xp+1)+" xp to the next level ",1)])):v("",!0),e("section",_s,[e("div",null,[e("div",{class:"flex text-sm"},[t[52]||(t[52]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:J}," Clear ")])]),r(u)().props.authenticated?(l(),i("div",ws,[e("div",Ls,[e("div",Cs,[e("div",Ss,[x(e("input",{id:"attempted","onUpdate:modelValue":t[19]||(t[19]=s=>m(o)?o.value=s:o=s),value:"attempted",name:"attempted",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[53]||(t[53]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"attempted",class:"font-semibold text-gray-600"},"practiced ")],-1))]),e("div",Ms,[e("div",Vs,[x(e("input",{id:"unattempted","onUpdate:modelValue":t[20]||(t[20]=s=>m(o)?o.value=s:o=s),value:"unattempted",name:"unattempted",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[54]||(t[54]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"unattempted",class:"font-semibold text-gray-600"},"not practiced ")],-1))])])])):v("",!0),e("div",As,[e("div",Ts,[e("div",Us,[e("div",$s,[x(e("input",{id:"basic","onUpdate:modelValue":t[21]||(t[21]=s=>m(o)?o.value=s:o=s),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[55]||(t[55]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"basic",class:"font-semibold text-blue-600"},"basic ")],-1))]),e("div",js,[e("div",qs,[x(e("input",{id:"intermediate","onUpdate:modelValue":t[22]||(t[22]=s=>m(o)?o.value=s:o=s),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[56]||(t[56]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"intermediate",class:"font-semibold text-rose-600"},"intermediate ")],-1))]),e("div",Hs,[e("div",Is,[x(e("input",{id:"advanced","onUpdate:modelValue":t[23]||(t[23]=s=>m(o)?o.value=s:o=s),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,r(o)]])]),t[57]||(t[57]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"advanced",class:"font-semibold text-orange-600"},"advanced ")],-1))])])]),e("div",Ps,[e("div",zs,[(l(!0),i(V,null,M(d.tags,(s,y)=>(l(),i("div",{class:"relative flex items-center",key:y},[e("div",Ns,[x(e("input",{id:`tag-${y}`,"onUpdate:modelValue":t[24]||(t[24]=q=>m(o)?o.value=q:o=q),value:s.name,name:s.name,type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,Bs),[[k,r(o)]])]),e("div",Es,[e("label",{for:`tag-${y}`,class:"font-semibold text-gray-600"},p(s.name),9,Fs)])]))),128))])])])]),r(u)().props.authenticated?(l(),i("div",Rs,[t[59]||(t[59]=e("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Recent Grammar Activities ",-1)),e("div",Ds,[(l(!0),i(V,null,M(d.user_activities,s=>(l(),h(oe,{key:s.id,activity:s},null,8,["activity"]))),128)),d.user_activities.length==0?(l(),i("div",Gs,t[58]||(t[58]=[e("h5",{class:"text-sm font-semibold text-gray-900"}," No Activities Yet ",-1)]))):v("",!0)])])):v("",!0),r(u)().props.authenticated?(l(),h(D,{key:3,class:"mt-8 w-full",color:"gray",size:"md",link:a.route("practice.activities.index")},{default:f(()=>t[60]||(t[60]=[I("View All Activities")])),_:1},8,["link"])):v("",!0)]),r(u)().props.authenticated?v("",!0):(l(),i("div",Ws,[c(ae)]))])]),_:1})}}};export{Wr as default};
