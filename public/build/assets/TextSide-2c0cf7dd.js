import{k as g,l as C,i as h,o as a,d as i,a as t,b as n,w as y,f as r,c as p,T,F as B,h as q,n as x,g as b,t as w}from"./app-f0078ddb.js";import{_ as m}from"./ToggleItem-94c3ab1e.js";import{P as V}from"./Promotion-3eee0057.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";import"./_plugin-vue_export-helper-c27b6911.js";const L={key:0},D={key:0,class:"mb-8"},N={class:"mt-1 space-y-1",role:"group","aria-labelledby":"desktop-teams-headline"},P={class:"grid grid-cols-1 gap-2"},U={key:3,class:"mt-4"},$={key:0,class:"mt-4"},z={class:"grid grid-cols-2 gap-2"},A={class:"mt-4 -ml-4 text-center"},E={key:1},F={class:"grid grid-cols-1 gap-2 sm:grid-cols-3 lg:grid-cols-1"},I={class:"mt-4"},X={__name:"TextSide",props:{selectingText:{type:Boolean,required:!0},splitSections:{type:Boolean,required:!0},displaySyntax:{type:Boolean,required:!0},caseColor:{type:Boolean,required:!0},hasProse:{type:Boolean,required:!0},verifiedSyntax:{type:Boolean,required:!0},section:{type:Object,required:!0},subscribed:{type:Boolean,required:!0}},emits:["update:caseColor","update:displaySyntax","update:selectingText","update:splitSections"],setup(s,{emit:f}){const d=f,v=g().props.user?g().props.user.case_colors:null,k=C(()=>[{id:1,name:"Nominative",color:"blue-600"},{id:5,name:"Ablative",color:"purple-600"},{id:2,name:"Genitive",color:"emerald-600"},{id:6,name:"Vocative",color:"fuchsia-600"},{id:3,name:"Dative",color:"orange-600"},{id:7,name:"Locative",color:"yellow-600"},{id:4,name:"Accusative",color:"pink-600"},{id:8,name:"Verbs",color:"gray-900"}].map(l=>{const e=v.find(u=>u.case_id===l.id);return{...l,color:e?e.color:l.color}})),c=(l,e)=>{switch(l){case"color":d("update:caseColor",e),localStorage.setItem("case-color",e);break;case"syntax":d("update:displaySyntax",e),localStorage.setItem("display-syntax",e);break;case"select":d("update:selectingText",e),localStorage.setItem("selecting-text",e);break;case"split":d("update:splitSections",e),localStorage.setItem("split-sections",e);break}};return(l,e)=>{const u=h("Link");return s.subscribed?(a(),i("div",L,[l.$page.props.user.id===0?(a(),i("div",D,[e[5]||(e[5]=t("h3",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Developer Links ",-1)),t("div",null,[t("div",N,[n(u,{href:s.section.line_start+"-"+s.section.line_end+"/syntax",class:"group flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900"},{default:y(()=>e[4]||(e[4]=[t("span",{class:"mr-4 h-2.5 w-2.5 rounded-full bg-blue-500","aria-hidden":"true"},null,-1),t("span",{class:"truncate"}," Verify Syntax ",-1)])),_:1},8,["href"])])])])):r("",!0),e[8]||(e[8]=t("h3",{id:"reading-toolkit",class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Reading Toolkit ",-1)),t("div",P,[n(m,{class:"mt-4",label:"Selecting Text",enabled:s.selectingText,description:"Toggle vocabulary and text selection.","onUpdate:toggle":e[0]||(e[0]=o=>c("select",o))},null,8,["enabled"]),s.hasProse?(a(),p(m,{key:0,class:"mt-4",label:"Split Sections",enabled:s.splitSections,description:"Split sections by line.","onUpdate:toggle":e[1]||(e[1]=o=>c("split",o))},null,8,["enabled"])):r("",!0),s.verifiedSyntax?(a(),p(m,{key:1,class:"mt-4",label:"Display Syntax",enabled:s.displaySyntax,description:"Display syntax in vocablary card.","onUpdate:toggle":e[2]||(e[2]=o=>c("syntax",o))},null,8,["enabled"])):r("",!0),s.verifiedSyntax?(a(),p(m,{key:2,class:"mt-4",label:"Case Colorize",enabled:s.caseColor,"text-color":"iris",description:"Show noun case by color.","onUpdate:toggle":e[3]||(e[3]=o=>c("color",o))},null,8,["enabled"])):r("",!0),s.verifiedSyntax?r("",!0):(a(),i("div",U,e[6]||(e[6]=[t("p",{class:"text-sm text-gray-600"}," Syntax has not yet been verified for this section. Please check back later. ",-1)])))]),n(T,{"enter-active-class":"transition ease-in-out duration-300","enter-from-class":"opacity-0 transform scale-y-0 -translate-y-1/2","enter-to-class":"opacity-100 transform scale-y-100 translate-y-0","leave-active-class":"transition ease-in-out duration-300","leave-from-class":"opacity-100 transform scale-y-100 translate-y-0","leave-to-class":"opacity-0 transform scale-y-0 -translate-y-1/2"},{default:y(()=>[s.caseColor&&s.verifiedSyntax?(a(),i("div",$,[t("div",z,[(a(!0),i(B,null,q(k.value,(o,S)=>(a(),i("div",{key:S,class:x(["ml-4 flex items-center text-sm font-semibold",`text-${o.color}`])},[t("span",{class:x(["mr-2 h-5 w-5 rounded-full",`bg-${o.color}`])},null,2),b(" "+w(o.name),1)],2))),128))]),t("div",A,[n(u,{class:"text-sm font-medium text-blue-600 hover:text-blue-800",href:"/user/profile#case-colors"},{default:y(()=>e[7]||(e[7]=[b(" Customize Your Case Colors ")])),_:1})])])):r("",!0)]),_:1})])):(a(),i("div",E,[e[10]||(e[10]=t("h3",{id:"reading-toolkit",class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Reading Toolkit ",-1)),t("div",F,[e[9]||(e[9]=t("div",{class:"mt-4"},[t("p",{class:"text-sm font-medium text-gray-600"}," Log in and access tools to help you read this section. ")],-1)),t("section",I,[n(V)])])]))}}};export{X as default};
