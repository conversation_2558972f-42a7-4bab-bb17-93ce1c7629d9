import{e as p,l as $,p as h,W as U,i as N,o as f,c as L,w as g,b as i,a as e,g as _,u as l,T as ee,d as w,h as I,j as a,n as te,t as se,F as z,k as F,f as B,q as n,x as re,v as u}from"./app-f0078ddb.js";import{t as le,_ as oe}from"./AppLayout-33f062bc.js";import{_ as ae}from"./Breadcrumbs-c96e9207.js";import{_ as ie}from"./WordItem-d0f526f8.js";import{u as ne}from"./useIntersect-6e15125e.js";import{_ as G}from"./DropdownGeneral-ce7a4558.js";import de from"./MobileFilters-8eff67cb.js";import{P as ue}from"./Promotion-3eee0057.js";import{_ as O}from"./ButtonItem-718c0517.js";import{_ as me}from"./Footer-0988dcd8.js";import{S as ce,r as ve,M as pe,b as fe,g as ge}from"./ChevronDownIcon-660c32b0.js";import{r as xe}from"./FunnelIcon-14ad2bdb.js";import{r as be}from"./MagnifyingGlassIcon-a45957e4.js";import{r as ye}from"./XCircleIcon-63af2b2a.js";import{r as P}from"./CheckCircleIcon-d86d1232.js";import{r as T}from"./StarIcon-155a2a28.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./use-resolve-button-type-24d8b5c5.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./use-text-value-2c18b2b1.js";const he={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},_e={class:"p-8"},we={class:"mt-8 flex w-full flex-row","aria-labelledby":"Description"},ke={class:"grow"},Se={class:"flex flex-col sm:flex-row"},Ve={class:"mt-4 sm:mt-0"},Ue={"aria-labelledby":"filter-heading",class:"mt-8 block border-t border-gray-200 py-6 lg:hidden"},Ce={class:"flex items-center justify-between"},je={class:"py-1"},Le=["onClick"],Ae={class:"mt-8"},Me={class:"grid grid-cols-1 divide-y"},We={key:1,class:"px-6 py-14 text-center text-sm sm:px-14"},Fe={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Ne={key:0,class:"mb-8"},Ie={"aria-labelledby":"Search"},ze={class:"w-full"},Be={class:"relative mt-3"},Ge={class:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"},Oe={class:"absolute inset-y-0 right-0 flex items-center pr-3"},Pe={"aria-labelledby":"sort videos",class:"mt-8"},Te={"aria-labelledby":"filter videos",class:"mt-8"},qe={"aria-labelledby":"filter videos",class:"mt-8"},De={class:"grid grid-cols-1 divide-y"},He={key:0,class:"ml-4 grid grid-cols-2 gap-3 py-4"},Re={class:"relative flex items-center"},Ee={class:"flex h-5 items-center"},Ye={class:"ml-3 text-sm"},Je={for:"learned",class:"font-medium text-gray-700"},Ke={class:"relative flex items-center"},Qe={class:"flex h-5 items-center"},Xe={class:"ml-3 text-sm"},Ze={for:"unlearned",class:"font-medium text-gray-700"},$e={class:"relative flex items-center"},et={class:"flex h-5 items-center"},tt={class:"ml-3 text-sm"},st={for:"starred",class:"font-medium text-gray-700"},rt={class:"relative flex items-center"},lt={class:"flex h-5 items-center"},ot={class:"ml-3 text-sm"},at={for:"unstarred",class:"font-medium text-gray-700"},it={key:1},nt={class:"mt-4"},dt={class:"text-sm font-medium text-gray-600"},ut={class:"grid grid-cols-1 divide-y"},mt={class:"ml-4 grid grid-cols-2 gap-3 py-4"},ct={class:"relative flex items-center"},vt={class:"flex h-5 items-center"},pt={class:"relative flex items-center"},ft={class:"flex h-5 items-center"},gt={class:"relative flex items-center"},xt={class:"flex h-5 items-center"},bt={class:"relative flex items-center"},yt={class:"flex h-5 items-center"},ht={class:"relative flex items-center"},_t={class:"flex h-5 items-center"},wt={class:"relative flex items-center"},kt={class:"flex h-5 items-center"},St={class:"grid grid-cols-1 divide-y"},Vt={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Ut={class:"relative flex items-center"},Ct={class:"flex h-5 items-center"},jt={class:"relative flex items-center"},Lt={class:"flex h-5 items-center"},At={class:"relative flex items-center"},Mt={class:"flex h-5 items-center"},Wt={class:"relative flex items-center"},Ft={class:"flex h-5 items-center"},Nt={class:"relative flex items-center"},It={class:"flex h-5 items-center"},js={__name:"Index",props:{words:Object,filters:Array,learned:Array,starred:Array,search:String,sort:String,group:String,groups:Array},setup(q){const d=q,A=p(null);let x=p(d.words),b=p(x.value.data);const D=$(()=>x.value.next_page_url!==null),M=p(!1);A!==null&&ne(A,()=>{D.value&&axios.get(x.value.next_page_url).then(o=>{b.value=[...b.value,...o.data.data],x.value=o.data})},{rootMargin:"0px 0px 250px 0px"});const C=()=>{x.value=d.words,b.value=x.value.data},H=()=>{m.value="",U.get("/words",{},{preserveState:!0,replace:!0,preserveScroll:!0,only:["words","filters","sort"],onSuccess:()=>{C()}})},R=[{name:"Words",href:"/words",current:!0}];let m=p(d.search),k=p(d.learned),S=p(d.starred),y=[{name:"Relevance",value:"relevance"},{name:"Alphabetical",value:"alphabetical"}],V=d.groups;V.unshift({name:"All Groups",value:0});const E=o=>y.find(t=>t.value==o),Y=o=>V.find(t=>t.value==Number(o));let c=p(d.sort?E(d.sort):y[0]),v=p(d.group?Y(d.group):V[0]);h(()=>c,o=>{U.get("/words",{sort:o.value.value,filters:r.value,search:m.value,group:v.value.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["words"],onSuccess:()=>{C()}})},{deep:!0}),h(m,le(()=>{U.get("/words",{search:m.value,filters:r.value,sort:c.value.value,group:v.value.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["words"],onSuccess:()=>{C()}})},250)),h(()=>d.words,o=>{x.value=o,b.value=x.value.data},{deep:!0});const J=o=>{k.value.some(t=>t==o.id)?k.value=k.value.filter(t=>t!=o.id):k.value.push(o.id)},K=o=>{S.value.some(t=>t==o.id)?S.value=S.value.filter(t=>t!=o.id):S.value.push(o.id)};let r=p(d.filters);const Q=()=>{r.value=[]};return h(()=>r,()=>{U.get("/words",{filters:r.value,search:m.value,sort:c.value.value,group:v.value.value},{only:["words"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),h(()=>d.sort,()=>{d.sort&&(c.value=y.find(o=>o.value==d.sort))},{deep:!0}),h(()=>v,o=>{U.get("/words",{sort:c.value.value,filters:r.value,search:m.value,group:o.value.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["words"],onSuccess:()=>{C()}})},{deep:!0}),(o,t)=>{const X=N("Head"),Z=N("Link");return f(),L(oe,null,{default:g(()=>[i(X,null,{default:g(()=>t[26]||(t[26]=[e("title",null,"Words",-1)])),_:1}),e("main",he,[e("div",_e,[i(ae,{class:"lg:col-span-9 xl:grid-cols-10",pages:R}),e("section",we,[e("div",ke,[e("div",Se,[t[28]||(t[28]=e("h1",{class:"grow text-4xl font-bold text-gray-900"},"Words",-1)),e("div",Ve,[i(O,{link:"/words/custom",color:"indigoOutline",size:"xs",class:"cursor-pointer"},{default:g(()=>t[27]||(t[27]=[_("Make Your Own List")])),_:1})])]),t[29]||(t[29]=e("div",null,[e("p",{class:"mt-4 text-base font-normal text-gray-600"}," Search through the list of Latin words on this site, or manage your own lists. ")],-1))])]),e("section",Ue,[t[32]||(t[32]=e("h2",{id:"filter-heading",class:"sr-only"},"Word filters",-1)),e("div",Ce,[i(l(ge),{as:"div",class:"relative inline-block text-left"},{default:g(()=>[e("div",null,[i(l(ce),{class:"group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900"},{default:g(()=>[t[30]||(t[30]=_(" Sort ")),i(l(ve),{class:"-mr-1 ml-1 h-5 w-5 shrink-0 text-gray-400 group-hover:text-gray-500","aria-hidden":"true"})]),_:1})]),i(ee,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:g(()=>[i(l(pe),{class:"absolute left-0 z-10 mt-2 w-40 origin-top-left rounded-md bg-white shadow-2xl focus:outline-hidden"},{default:g(()=>[e("div",je,[(f(!0),w(z,null,I(l(y),s=>(f(),L(l(fe),{key:s},{default:g(({active:W})=>[e("a",{onClick:j=>a(c)?c.value=s:c=s,class:te([W?"bg-gray-100":"","block cursor-pointer px-4 py-2 text-sm font-medium text-gray-900"])},se(s.name),11,Le)]),_:2},1024))),128))])]),_:1})]),_:1})]),_:1}),i(O,{color:"white",size:"xs",onClick:t[0]||(t[0]=s=>M.value=!0)},{default:g(()=>[i(l(xe),{class:"mr-1 inline h-4 w-4 stroke-2"}),t[31]||(t[31]=_(" Filters "))]),_:1})])]),i(de,{show:M.value,array:l(r),"current-group":l(v),"group-list":l(V),"onUpdate:array":t[1]||(t[1]=s=>a(r)?r.value=s:r=s),onClose:t[2]||(t[2]=s=>M.value=!1),"onUpdate:query":t[3]||(t[3]=s=>a(m)?m.value=s:m=s),"onUpdate:group":t[4]||(t[4]=s=>a(v)?v.value=s:v=s)},null,8,["show","array","current-group","group-list"]),e("section",Ae,[e("div",Me,[l(b).length>0?(f(!0),w(z,{key:0},I(l(b),(s,W)=>(f(),L(ie,{class:"hover:bg-slate-50 lg:px-4",key:W,word:s,authenticated:l(F)().props.authenticated,"onUpdate:toggleLearned":t[5]||(t[5]=j=>J(j)),"onUpdate:toggleStarred":t[6]||(t[6]=j=>K(j)),"learned-array":l(k),"starred-array":l(S)},null,8,["word","authenticated","learned-array","starred-array"]))),128)):(f(),w("div",We,t[33]||(t[33]=[e("p",{class:"mt-4 font-semibold text-gray-900"},"No results found",-1),e("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ",-1)])))]),e("div",{ref_key:"landmark",ref:A},null,512)])]),i(me)]),e("aside",Fe,[l(F)().props.authenticated?B("",!0):(f(),w("section",Ne,[i(ue)])),e("section",Ie,[e("div",ze,[t[34]||(t[34]=e("label",{for:"search",class:"sr-only"},"Search",-1)),t[35]||(t[35]=e("div",{class:"flex text-sm"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Search")],-1)),e("div",Be,[e("div",Ge,[i(l(be),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),e("div",Oe,[l(m)?(f(),L(l(ye),{key:0,class:"h-5 w-5 transform cursor-pointer stroke-2 text-gray-400 duration-150 hover:text-gray-500","aria-hidden":"true",onClick:t[7]||(t[7]=s=>H())})):B("",!0)]),n(e("input",{id:"search_videos","onUpdate:modelValue":t[8]||(t[8]=s=>a(m)?m.value=s:m=s),name:"search_videos",class:"block w-full rounded-lg border border-transparent bg-white py-2 pr-3 pl-10 text-sm leading-5 placeholder-gray-500 shadow-sm focus:border-blue-500 focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:outline-hidden",placeholder:"Search all words",autocomplete:"off"},null,512),[[re,l(m)]])])])]),e("section",Pe,[i(G,{modelValue:l(c),"onUpdate:modelValue":t[9]||(t[9]=s=>a(c)?c.value=s:c=s),current:l(c)?l(c):l(y)[0].value.value,title:"Sort",list:l(y)},null,8,["modelValue","current","list"])]),e("section",Te,[i(G,{modelValue:l(v),"onUpdate:modelValue":t[10]||(t[10]=s=>a(v)?v.value=s:v=s),current:l(v),title:"Groups",list:l(V)},null,8,["modelValue","current","list"])]),e("section",qe,[e("div",null,[e("div",{class:"flex text-sm"},[t[36]||(t[36]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:Q}," Clear ")])]),e("div",De,[l(F)().props.authenticated?(f(),w("div",He,[e("div",Re,[e("div",Ee,[n(e("input",{id:"learned","onUpdate:modelValue":t[11]||(t[11]=s=>a(r)?r.value=s:r=s),value:"learned",name:"learned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),e("div",Ye,[e("label",Je,[i(l(P),{class:"h-6 w-6 stroke-2 text-blue-500","aria-hidden":"true"})])])]),e("div",Ke,[e("div",Qe,[n(e("input",{id:"unlearned","onUpdate:modelValue":t[12]||(t[12]=s=>a(r)?r.value=s:r=s),value:"unlearned",name:"unlearned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),e("div",Xe,[e("label",Ze,[i(l(P),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])]),e("div",$e,[e("div",et,[n(e("input",{id:"starred","onUpdate:modelValue":t[13]||(t[13]=s=>a(r)?r.value=s:r=s),value:"starred",name:"starred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),e("div",tt,[e("label",st,[i(l(T),{class:"h-6 w-6 stroke-2 text-green-600","aria-hidden":"true"})])])]),e("div",rt,[e("div",lt,[n(e("input",{id:"unstarred","onUpdate:modelValue":t[14]||(t[14]=s=>a(r)?r.value=s:r=s),value:"unstarred",name:"unstarred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),e("div",ot,[e("label",at,[i(l(T),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])])])):(f(),w("div",it,[e("div",nt,[e("p",dt,[t[38]||(t[38]=_(" Please ")),i(Z,{class:"text-blue-600 hover:text-blue-700 hover:underline",href:"/login"},{default:g(()=>t[37]||(t[37]=[_("login or make an account ")])),_:1}),t[39]||(t[39]=_(" to access more tools with vocabulary. "))])])]))]),e("div",ut,[e("div",mt,[e("div",ct,[e("div",vt,[n(e("input",{id:"basic","onUpdate:modelValue":t[15]||(t[15]=s=>a(r)?r.value=s:r=s),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[40]||(t[40]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"basic",class:"font-semibold text-emerald-600"},"basic ")],-1))]),e("div",pt,[e("div",ft,[n(e("input",{id:"intermediate","onUpdate:modelValue":t[16]||(t[16]=s=>a(r)?r.value=s:r=s),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[41]||(t[41]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"intermediate",class:"font-semibold text-blue-600"},"intermediate ")],-1))]),e("div",gt,[e("div",xt,[n(e("input",{id:"advanced","onUpdate:modelValue":t[17]||(t[17]=s=>a(r)?r.value=s:r=s),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[42]||(t[42]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"advanced",class:"font-semibold text-orange-600"},"advanced ")],-1))]),e("div",bt,[e("div",yt,[n(e("input",{id:"uncommon","onUpdate:modelValue":t[18]||(t[18]=s=>a(r)?r.value=s:r=s),value:"uncommon",name:"uncommon",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[43]||(t[43]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"uncommon",class:"font-semibold text-violet-600"},"uncommon ")],-1))]),e("div",ht,[e("div",_t,[n(e("input",{id:"core","onUpdate:modelValue":t[19]||(t[19]=s=>a(r)?r.value=s:r=s),value:"core",name:"core",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[44]||(t[44]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"core",class:"font-semibold text-gray-600"},"core ")],-1))]),e("div",wt,[e("div",kt,[n(e("input",{id:"noncore","onUpdate:modelValue":t[20]||(t[20]=s=>a(r)?r.value=s:r=s),value:"noncore",name:"noncore",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[45]||(t[45]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"noncore",class:"font-semibold text-gray-600"},"non-core ")],-1))])])]),e("div",St,[e("div",Vt,[e("div",Ut,[e("div",Ct,[n(e("input",{id:"nouns","onUpdate:modelValue":t[21]||(t[21]=s=>a(r)?r.value=s:r=s),value:"nouns",name:"nouns",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[46]||(t[46]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"nouns",class:"font-semibold text-gray-600"},"nouns ")],-1))]),e("div",jt,[e("div",Lt,[n(e("input",{id:"verbs","onUpdate:modelValue":t[22]||(t[22]=s=>a(r)?r.value=s:r=s),value:"verbs",name:"verbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[47]||(t[47]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"verbs",class:"font-semibold text-gray-600"},"verbs ")],-1))]),e("div",At,[e("div",Mt,[n(e("input",{id:"adjectives","onUpdate:modelValue":t[23]||(t[23]=s=>a(r)?r.value=s:r=s),value:"adjectives",name:"adjectives",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[48]||(t[48]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adjectives",class:"font-semibold text-gray-600"},"adjectives ")],-1))]),e("div",Wt,[e("div",Ft,[n(e("input",{id:"adverbs","onUpdate:modelValue":t[24]||(t[24]=s=>a(r)?r.value=s:r=s),value:"adverbs",name:"adverbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[49]||(t[49]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adverbs",class:"font-semibold text-gray-600"},"adverbs ")],-1))]),e("div",Nt,[e("div",It,[n(e("input",{id:"other","onUpdate:modelValue":t[25]||(t[25]=s=>a(r)?r.value=s:r=s),value:"other",name:"other",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[u,l(r)]])]),t[50]||(t[50]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"other",class:"font-semibold text-gray-600"},"other ")],-1))])])])])])])]),_:1})}}};export{js as default};
