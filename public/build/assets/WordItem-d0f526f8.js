import{_ as y}from"./ButtonItem-718c0517.js";import{r as C}from"./CheckCircleIcon-d86d1232.js";import{r as A}from"./StarIcon-155a2a28.js";import{l as m,i as S,a1 as z,o as s,d as g,a as t,b as n,w as v,t as a,f as h,n as l,q as f,c as $,u as x}from"./app-f0078ddb.js";const D={class:"flex w-full flex-row items-center py-4 sm:h-24"},M={class:"flex grow flex-col gap-4 truncate"},N={class:"flex grow flex-col gap-2 overflow-hidden"},V={class:"items-left flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4"},R={class:"text-left text-xl"},j={class:"font-semibold text-gray-900"},q={class:"flex gap-2 text-base font-medium text-gray-600"},E={key:0,class:"text-base font-medium text-gray-600"},I={class:"truncate text-base font-medium text-gray-600"},W={key:0,class:"flex grid grid-cols-2 gap-4 p-1 text-center sm:hidden"},F={key:0,class:"ml-4 flex hidden flex-row gap-4 text-center sm:inline-flex lg:flex-col lg:gap-2 2xl:flex-row 2xl:gap-4"},P={__name:"WordItem",props:{word:Object,learnedArray:Array,starredArray:Array,authenticated:Boolean},emits:["update:toggle-learned","update:toggle-starred"],setup(d,{emit:B}){const e=d,i=B,k=()=>{i("update:toggle-learned",{id:e.word.id,core:e.word.core}),axios.post("/words/"+e.word.id+"/learn").then(r=>{r.data.status!="success"&&i("update:toggle-learned",{id:e.word.id,core:e.word.core})})},_=()=>{i("update:toggle-starred",{id:e.word.id,core:e.word.core}),axios.post("/words/"+e.word.id+"/star").then(r=>{r.data.status!="success"&&i("update:toggle-starred",{id:e.word.id,core:e.word.core})})},w=m(()=>e.learnedArray.some(r=>r==e.word.id)),c=m(()=>e.starredArray.some(r=>r==e.word.id)),O=m(()=>{switch(!0){case e.word.list=="uncommon":return"border-purple-500 text-purple-500";case e.word.list=="basic":return"border-emerald-500 text-emerald-500";case e.word.list=="intermediate":return"border-blue-500 text-blue-500";case e.word.list=="advanced":return"border-orange-500 text-orange-500";default:return"border-purple-500 text-purple-500"}}),b=m(()=>{switch(!0){case e.word.gender===null:return"";case e.word.gender==="m.":return"masculine";case e.word.gender==="f.":return"feminine";case e.word.gender==="n.":return"neuter";case e.word.gender==="m./f.":return"masculine/feminine";case e.word.gender==="m./n.":return"masculine/neuter";case e.word.gender==="m./f./n.":return"masculine/feminine/neuter";case e.word.gender==="f./n.":return"feminine/neuter";default:return e.word.gender}});return(r,o)=>{const L=S("Link"),u=z("tippy");return s(),g("div",D,[t("div",M,[n(L,{href:`/words/w/${e.word.id}`,class:"group w-full grow"},{default:v(()=>[t("div",N,[t("div",V,[t("div",null,[t("h4",R,[t("span",j,a(e.word.latin),1)])]),t("div",q,[t("span",null,a(e.word.pos),1),b.value?(s(),g("span",E,a(b.value),1)):h("",!0),t("span",{class:l(["inline-flex items-center rounded-full border-2 px-2.5 py-0.5 text-xs font-semibold",O.value])},a(d.word.list),3)])]),t("div",null,[t("h5",I,a(e.word.definition),1)])])]),_:1},8,["href"]),d.authenticated?(s(),g("div",W,[f((s(),$(y,{class:"w-full",color:"white",size:"xs",onClick:o[0]||(o[0]=p=>k())},{default:v(()=>[n(x(C),{class:l(["mx-auto h-5 w-5 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-blue-600",[w.value?"text-blue-600":"text-gray-400"]])},null,8,["class"])]),_:1})),[[u,{content:"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),f((s(),$(y,{class:"w-full",color:"white",size:"xs",onClick:o[1]||(o[1]=p=>_())},{default:v(()=>[n(x(A),{class:l(["duration-250 mx-auto h-5 w-5 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-green-600",[c.value?"text-green-600":"stroke-current text-gray-400"]])},null,8,["class"])]),_:1})),[[u,{content:c.value?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])):h("",!0)]),d.authenticated?(s(),g("div",F,[t("div",null,[f(n(x(C),{class:l(["mx-auto h-6 w-6 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-blue-600",[w.value?"text-blue-600":"text-gray-400"]]),onClick:o[2]||(o[2]=p=>k())},null,8,["class"]),[[u,{content:w.value?"Mark as not learned":"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])]),t("div",null,[f(n(x(A),{class:l(["mx-auto h-6 w-6 cursor-pointer stroke-current stroke-2 transition duration-150 hover:text-green-600",[c.value?"text-green-600":"stroke-current text-gray-400"]]),onClick:o[3]||(o[3]=p=>_())},null,8,["class"]),[[u,{content:c.value?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])])):h("",!0)])}}};export{P as _};
