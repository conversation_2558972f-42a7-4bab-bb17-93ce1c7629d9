import{_ as h}from"./RenderToken-2b0a5644.js";import{l as g,o as l,d as m,h as u,F as x,c as d,n as f}from"./app-f0078ddb.js";import"./removePunctuation-702d8a66.js";/* empty css            */const b={__name:"TextMain",props:{verses:Array,tokens:Array,highlight:String,caseColor:<PERSON>olean,selectingText:<PERSON>olean,hasProse:<PERSON>olean,showLineNumbers:<PERSON>olean,splitSections:Boolean},emits:["update:highlighted"],setup(e,{emit:i}){const r=e,a=g(()=>{var s=[[]];let o=0;return r.verses.forEach((t,n)=>{t.prose==1&&r.verses.length>1?s[o].push(t):(n==0?s[0]=t:s.push(t),o++)}),s}),c=i;return(s,o)=>(l(!0),m(x,null,u(a.value,t=>(l(),d(h,{key:t.id,verse:t,tokens:e.tokens,highlighted:e.highlight,"case-color":e.caseColor,"selecting-text":e.selectingText,"show-line-numbers":e.showLineNumbers,"split-sections":e.splitSections,"has-prose":e.hasProse,"onUpdate:highlighted":o[0]||(o[0]=n=>c("update:highlighted",n)),class:f(["text-gray-900 font-intro break-normal text-lg font-medium text-gray-900 sm:text-2xl lg:text-2xl mb-2",[e.selectingText?"cursor-text select-text":"cursor-pointer select-none"]])},null,8,["verse","tokens","highlighted","case-color","selecting-text","show-line-numbers","split-sections","has-prose","class"]))),128))}};export{b as default};
