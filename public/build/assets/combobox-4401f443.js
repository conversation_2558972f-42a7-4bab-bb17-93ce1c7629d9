import{l as x,u as J,U as be,p as j,_ as ne,$ as ge,B as ue,H as K,e as P,V as xe,a0 as B,I as de,A as le,L as Y,F as Se,J as Z,K as ce,z as _,Z as Oe}from"./app-f0078ddb.js";import{d as Ie,e as ye}from"./form-cb36670c.js";import{o as te}from"./disposables-4ddc41dd.js";import{u as W,o as O,E as Re,A as H,T as oe,i as X,N as G}from"./render-c34c346a.js";import{w as Ce,n as we}from"./use-outside-click-484df218.js";import{s as Ee}from"./use-resolve-button-type-24d8b5c5.js";import{c as A,f as ie,u as Te}from"./calculate-active-index-51ff911b.js";import{i as Me}from"./use-tree-walker-100527b8.js";import{f as ze,u as Ae,o as F}from"./keyboard-982fc047.js";import{t as Pe,i as Q,l as De}from"./open-closed-7f51e238.js";import{t as ae}from"./dialog-86f7bd91.js";import{i as Fe,O as $e}from"./focus-management-8406d052.js";function N(o,d,l){let n=l.initialDeps??[],e;function a(){var i,t,f,c;let r;l.key&&((i=l.debug)!=null&&i.call(l))&&(r=Date.now());const h=o();if(!(h.length!==n.length||h.some((E,z)=>n[z]!==E)))return e;n=h;let M;if(l.key&&((t=l.debug)!=null&&t.call(l))&&(M=Date.now()),e=d(...h),l.key&&((f=l.debug)!=null&&f.call(l))){const E=Math.round((Date.now()-r)*100)/100,z=Math.round((Date.now()-M)*100)/100,V=z/16,D=(m,y)=>{for(m=String(m);m.length<y;)m=" "+m;return m};console.info(`%c⏱ ${D(z,5)} /${D(E,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*V,120))}deg 100% 31%);`,l==null?void 0:l.key)}return(c=l==null?void 0:l.onChange)==null||c.call(l,e),e}return a.updateDeps=i=>{n=i},a}function ee(o,d){if(o===void 0)throw new Error(`Unexpected undefined${d?`: ${d}`:""}`);return o}const Ve=(o,d)=>Math.abs(o-d)<1,ke=(o,d,l)=>{let n;return function(...e){o.clearTimeout(n),n=o.setTimeout(()=>d.apply(this,e),l)}},Be=o=>o,_e=o=>{const d=Math.max(o.startIndex-o.overscan,0),l=Math.min(o.endIndex+o.overscan,o.count-1),n=[];for(let e=d;e<=l;e++)n.push(e);return n},je=(o,d)=>{const l=o.scrollElement;if(!l)return;const n=o.targetWindow;if(!n)return;const e=i=>{const{width:t,height:f}=i;d({width:Math.round(t),height:Math.round(f)})};if(e(l.getBoundingClientRect()),!n.ResizeObserver)return()=>{};const a=new n.ResizeObserver(i=>{const t=()=>{const f=i[0];if(f!=null&&f.borderBoxSize){const c=f.borderBoxSize[0];if(c){e({width:c.inlineSize,height:c.blockSize});return}}e(l.getBoundingClientRect())};o.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()});return a.observe(l,{box:"border-box"}),()=>{a.unobserve(l)}},se={passive:!0},re=typeof window>"u"?!0:"onscrollend"in window,Le=(o,d)=>{const l=o.scrollElement;if(!l)return;const n=o.targetWindow;if(!n)return;let e=0;const a=o.options.useScrollendEvent&&re?()=>{}:ke(n,()=>{d(e,!1)},o.options.isScrollingResetDelay),i=r=>()=>{const{horizontal:h,isRtl:I}=o.options;e=h?l.scrollLeft*(I&&-1||1):l.scrollTop,a(),d(e,r)},t=i(!0),f=i(!1);f(),l.addEventListener("scroll",t,se);const c=o.options.useScrollendEvent&&re;return c&&l.addEventListener("scrollend",f,se),()=>{l.removeEventListener("scroll",t),c&&l.removeEventListener("scrollend",f)}},Ne=(o,d,l)=>{if(d!=null&&d.borderBoxSize){const n=d.borderBoxSize[0];if(n)return Math.round(n[l.options.horizontal?"inlineSize":"blockSize"])}return Math.round(o.getBoundingClientRect()[l.options.horizontal?"width":"height"])},We=(o,{adjustments:d=0,behavior:l},n)=>{var e,a;const i=o+d;(a=(e=n.scrollElement)==null?void 0:e.scrollTo)==null||a.call(e,{[n.options.horizontal?"left":"top"]:i,behavior:l})};class Ke{constructor(d){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let l=null;const n=()=>l||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:l=new this.targetWindow.ResizeObserver(e=>{e.forEach(a=>{const i=()=>{this._measureElement(a.target,a)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(i):i()})}));return{disconnect:()=>{var e;(e=n())==null||e.disconnect(),l=null},observe:e=>{var a;return(a=n())==null?void 0:a.observe(e,{box:"border-box"})},unobserve:e=>{var a;return(a=n())==null?void 0:a.unobserve(e)}}})(),this.range=null,this.setOptions=l=>{Object.entries(l).forEach(([n,e])=>{typeof e>"u"&&delete l[n]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Be,rangeExtractor:_e,onChange:()=>{},measureElement:Ne,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...l}},this.notify=l=>{var n,e;(e=(n=this.options).onChange)==null||e.call(n,this,l)},this.maybeNotify=N(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),l=>{this.notify(l)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(l=>l()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var l;const n=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==n){if(this.cleanup(),!n){this.maybeNotify();return}this.scrollElement=n,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((l=this.scrollElement)==null?void 0:l.window)??null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,a)=>{this.scrollAdjustments=0,this.scrollDirection=a?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=a,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(l,n)=>{const e=new Map,a=new Map;for(let i=n-1;i>=0;i--){const t=l[i];if(e.has(t.lane))continue;const f=a.get(t.lane);if(f==null||t.end>f.end?a.set(t.lane,t):t.end<f.end&&e.set(t.lane,!0),e.size===this.options.lanes)break}return a.size===this.options.lanes?Array.from(a.values()).sort((i,t)=>i.end===t.end?i.index-t.index:i.end-t.end)[0]:void 0},this.getMeasurementOptions=N(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(l,n,e,a,i)=>(this.pendingMeasuredCacheIndexes=[],{count:l,paddingStart:n,scrollMargin:e,getItemKey:a,enabled:i}),{key:!1}),this.getMeasurements=N(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:l,paddingStart:n,scrollMargin:e,getItemKey:a,enabled:i},t)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(r=>{this.itemSizeCache.set(r.key,r.size)}));const f=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const c=this.measurementsCache.slice(0,f);for(let r=f;r<l;r++){const h=a(r),I=this.options.lanes===1?c[r-1]:this.getFurthestMeasurement(c,r),M=I?I.end+this.options.gap:n+e,E=t.get(h),z=typeof E=="number"?E:this.options.estimateSize(r),V=M+z,D=I?I.lane:r%this.options.lanes;c[r]={index:r,start:M,size:z,end:V,key:h,lane:D}}return this.measurementsCache=c,c},{key:!1,debug:()=>this.options.debug}),this.calculateRange=N(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(l,n,e,a)=>this.range=l.length>0&&n>0?Ue({measurements:l,outerSize:n,scrollOffset:e,lanes:a}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=N(()=>{let l=null,n=null;const e=this.calculateRange();return e&&(l=e.startIndex,n=e.endIndex),this.maybeNotify.updateDeps([this.isScrolling,l,n]),[this.options.rangeExtractor,this.options.overscan,this.options.count,l,n]},(l,n,e,a,i)=>a===null||i===null?[]:l({startIndex:a,endIndex:i,overscan:n,count:e}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=l=>{const n=this.options.indexAttribute,e=l.getAttribute(n);return e?parseInt(e,10):(console.warn(`Missing attribute name '${n}={index}' on measured element.`),-1)},this._measureElement=(l,n)=>{const e=this.indexFromElement(l),a=this.measurementsCache[e];if(!a)return;const i=a.key,t=this.elementsCache.get(i);t!==l&&(t&&this.observer.unobserve(t),this.observer.observe(l),this.elementsCache.set(i,l)),l.isConnected&&this.resizeItem(e,this.options.measureElement(l,n,this))},this.resizeItem=(l,n)=>{const e=this.measurementsCache[l];if(!e)return;const a=this.itemSizeCache.get(e.key)??e.size,i=n-a;i!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(e,i,this):e.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(e.index),this.itemSizeCache=new Map(this.itemSizeCache.set(e.key,n)),this.notify(!1))},this.measureElement=l=>{if(!l){this.elementsCache.forEach((n,e)=>{n.isConnected||(this.observer.unobserve(n),this.elementsCache.delete(e))});return}this._measureElement(l,void 0)},this.getVirtualItems=N(()=>[this.getVirtualIndexes(),this.getMeasurements()],(l,n)=>{const e=[];for(let a=0,i=l.length;a<i;a++){const t=l[a],f=n[t];e.push(f)}return e},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=l=>{const n=this.getMeasurements();if(n.length!==0)return ee(n[ve(0,n.length-1,e=>ee(n[e]).start,l)])},this.getOffsetForAlignment=(l,n,e=0)=>{const a=this.getSize(),i=this.getScrollOffset();n==="auto"&&(n=l>=i+a?"end":"start"),n==="center"?l+=(e-a)/2:n==="end"&&(l-=a);const t=this.options.horizontal?"scrollWidth":"scrollHeight",c=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[t]:this.scrollElement[t]:0)-a;return Math.max(Math.min(c,l),0)},this.getOffsetForIndex=(l,n="auto")=>{l=Math.max(0,Math.min(l,this.options.count-1));const e=this.measurementsCache[l];if(!e)return;const a=this.getSize(),i=this.getScrollOffset();if(n==="auto")if(e.end>=i+a-this.options.scrollPaddingEnd)n="end";else if(e.start<=i+this.options.scrollPaddingStart)n="start";else return[i,n];const t=n==="end"?e.end+this.options.scrollPaddingEnd:e.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(t,n,e.size),n]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(l,{align:n="start",behavior:e}={})=>{this.cancelScrollToIndex(),e==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(l,n),{adjustments:void 0,behavior:e})},this.scrollToIndex=(l,{align:n="auto",behavior:e}={})=>{l=Math.max(0,Math.min(l,this.options.count-1)),this.cancelScrollToIndex(),e==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const a=this.getOffsetForIndex(l,n);if(!a)return;const[i,t]=a;this._scrollToOffset(i,{adjustments:void 0,behavior:e}),e!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(l))){const[c]=ee(this.getOffsetForIndex(l,t));Ve(c,this.getScrollOffset())||this.scrollToIndex(l,{align:t,behavior:e})}else this.scrollToIndex(l,{align:t,behavior:e})}))},this.scrollBy=(l,{behavior:n}={})=>{this.cancelScrollToIndex(),n==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+l,{adjustments:void 0,behavior:n})},this.getTotalSize=()=>{var l;const n=this.getMeasurements();let e;if(n.length===0)e=this.options.paddingStart;else if(this.options.lanes===1)e=((l=n[n.length-1])==null?void 0:l.end)??0;else{const a=Array(this.options.lanes).fill(null);let i=n.length-1;for(;i>0&&a.some(t=>t===null);){const t=n[i];a[t.lane]===null&&(a[t.lane]=t.end),i--}e=Math.max(...a.filter(t=>t!==null))}return Math.max(e-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(l,{adjustments:n,behavior:e})=>{this.options.scrollToFn(l,{behavior:e,adjustments:n},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(d)}}const ve=(o,d,l,n)=>{for(;o<=d;){const e=(o+d)/2|0,a=l(e);if(a<n)o=e+1;else if(a>n)d=e-1;else return e}return o>0?o-1:0};function Ue({measurements:o,outerSize:d,scrollOffset:l,lanes:n}){const e=o.length-1;let i=ve(0,e,f=>o[f].start,l),t=i;if(n===1)for(;t<e&&o[t].end<l+d;)t++;else if(n>1){const f=Array(n).fill(0);for(;t<e&&f.some(r=>r<l+d);){const r=o[t];f[r.lane]=r.end,t++}const c=Array(n).fill(l+d);for(;i>0&&c.some(r=>r>=l);){const r=o[i];c[r.lane]=r.start,i--}i=Math.max(0,i-i%n),t=Math.min(e,t+(n-1-t%n))}return{startIndex:i,endIndex:t}}function qe(o){const d=new Ke(J(o)),l=be(d),n=d._didMount();return j(()=>J(o).getScrollElement(),e=>{e&&d._willUpdate()},{immediate:!0}),j(()=>J(o),e=>{d.setOptions({...e,onChange:(a,i)=>{var t;ne(l),(t=e.onChange)==null||t.call(e,a,i)}}),d._willUpdate(),ne(l)},{immediate:!0}),ge(n),l}function He(o){return qe(x(()=>({observeElementRect:je,observeElementOffset:Le,scrollToFn:We,...J(o)})))}function Je(){let o=te();return ue(()=>o.dispose()),o}function Ye(){let o=Je();return d=>{o.dispose(),o.nextFrame(d)}}var pe=(o=>(o[o.Left=0]="Left",o[o.Right=2]="Right",o))(pe||{});function Ze(o,d){return o===d}var Ge=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Ge||{}),Qe=(o=>(o[o.Single=0]="Single",o[o.Multi=1]="Multi",o))(Qe||{}),Xe=(o=>(o[o.Pointer=0]="Pointer",o[o.Focus=1]="Focus",o[o.Other=2]="Other",o))(Xe||{});let fe=Symbol("ComboboxContext");function U(o){let d=ce(fe,null);if(d===null){let l=new Error(`<${o} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,U),l}return d}let he=Symbol("VirtualContext"),et=K({name:"VirtualProvider",setup(o,{slots:d}){let l=U("VirtualProvider"),n=x(()=>{let t=O(l.optionsRef);if(!t)return{start:0,end:0};let f=window.getComputedStyle(t);return{start:parseFloat(f.paddingBlockStart||f.paddingTop),end:parseFloat(f.paddingBlockEnd||f.paddingBottom)}}),e=He(x(()=>({scrollPaddingStart:n.value.start,scrollPaddingEnd:n.value.end,count:l.virtual.value.options.length,estimateSize(){return 40},getScrollElement(){return O(l.optionsRef)},overscan:12}))),a=x(()=>{var t;return(t=l.virtual.value)==null?void 0:t.options}),i=P(0);return j([a],()=>{i.value+=1}),de(he,l.virtual.value?e:null),()=>[Y("div",{style:{position:"relative",width:"100%",height:`${e.value.getTotalSize()}px`},ref:t=>{if(t){if(typeof process<"u"&&{}.JEST_WORKER_ID!==void 0||l.activationTrigger.value===0)return;l.activeOptionIndex.value!==null&&l.virtual.value.options.length>l.activeOptionIndex.value&&e.value.scrollToIndex(l.activeOptionIndex.value)}}},e.value.getVirtualItems().map(t=>Oe(d.default({option:l.virtual.value.options[t.index],open:l.comboboxState.value===0})[0],{key:`${i.value}-${t.index}`,"data-index":t.index,"aria-setsize":l.virtual.value.options.length,"aria-posinset":t.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${t.start}px)`,overflowAnchor:"none"}})))]}}),pt=K({name:"Combobox",emits:{"update:modelValue":o=>!0},props:{as:{type:[Object,String],default:"template"},disabled:{type:[Boolean],default:!1},by:{type:[String,Function],nullable:!0,default:null},modelValue:{type:[Object,String,Number,Boolean],default:void 0},defaultValue:{type:[Object,String,Number,Boolean],default:void 0},form:{type:String,optional:!0},name:{type:String,optional:!0},nullable:{type:Boolean,default:!1},multiple:{type:[Boolean],default:!1},immediate:{type:[Boolean],default:!1},virtual:{type:Object,default:null}},inheritAttrs:!1,setup(o,{slots:d,attrs:l,emit:n}){let e=P(1),a=P(null),i=P(null),t=P(null),f=P(null),c=P({static:!1,hold:!1}),r=P([]),h=P(null),I=P(2),M=P(!1);function E(s=v=>v){let v=h.value!==null?r.value[h.value]:null,b=s(r.value.slice()),g=b.length>0&&b[0].dataRef.order.value!==null?b.sort((T,k)=>T.dataRef.order.value-k.dataRef.order.value):$e(b,T=>O(T.dataRef.domRef)),w=v?g.indexOf(v):null;return w===-1&&(w=null),{options:g,activeOptionIndex:w}}let z=x(()=>o.multiple?1:0),V=x(()=>o.nullable),[D,m]=Ie(x(()=>o.modelValue),s=>n("update:modelValue",s),x(()=>o.defaultValue)),y=x(()=>D.value===void 0?W(z.value,{1:[],0:void 0}):D.value),$=null,p=null;function R(s){return W(z.value,{0(){return m==null?void 0:m(s)},1:()=>{let v=B(u.value.value).slice(),b=B(s),g=v.findIndex(w=>u.compare(b,B(w)));return g===-1?v.push(b):v.splice(g,1),m==null?void 0:m(v)}})}let C=x(()=>{});j([C],([s],[v])=>{if(u.virtual.value&&s&&v&&h.value!==null){let b=s.indexOf(v[h.value]);b!==-1?h.value=b:h.value=null}});let u={comboboxState:e,value:y,mode:z,compare(s,v){if(typeof o.by=="string"){let b=o.by;return(s==null?void 0:s[b])===(v==null?void 0:v[b])}return o.by===null?Ze(s,v):o.by(s,v)},calculateIndex(s){return u.virtual.value?o.by===null?u.virtual.value.options.indexOf(s):u.virtual.value.options.findIndex(v=>u.compare(v,s)):r.value.findIndex(v=>u.compare(v.dataRef.value,s))},defaultValue:x(()=>o.defaultValue),nullable:V,immediate:x(()=>!1),virtual:x(()=>null),inputRef:i,labelRef:a,buttonRef:t,optionsRef:f,disabled:x(()=>o.disabled),options:r,change(s){m(s)},activeOptionIndex:x(()=>{if(M.value&&h.value===null&&(u.virtual.value?u.virtual.value.options.length>0:r.value.length>0)){if(u.virtual.value){let v=u.virtual.value.options.findIndex(b=>{var g;return!((g=u.virtual.value)!=null&&g.disabled(b))});if(v!==-1)return v}let s=r.value.findIndex(v=>!v.dataRef.disabled);if(s!==-1)return s}return h.value}),activationTrigger:I,optionsPropsRef:c,closeCombobox(){M.value=!1,!o.disabled&&e.value!==1&&(e.value=1,h.value=null)},openCombobox(){if(M.value=!0,!o.disabled&&e.value!==0){if(u.value.value){let s=u.calculateIndex(u.value.value);s!==-1&&(h.value=s)}e.value=0}},setActivationTrigger(s){I.value=s},goToOption(s,v,b){M.value=!1,$!==null&&cancelAnimationFrame($),$=requestAnimationFrame(()=>{if(o.disabled||f.value&&!c.value.static&&e.value===1)return;if(u.virtual.value){h.value=s===A.Specific?v:ie({focus:s},{resolveItems:()=>u.virtual.value.options,resolveActiveIndex:()=>{var T,k;return(k=(T=u.activeOptionIndex.value)!=null?T:u.virtual.value.options.findIndex(L=>{var q;return!((q=u.virtual.value)!=null&&q.disabled(L))}))!=null?k:null},resolveDisabled:T=>u.virtual.value.disabled(T),resolveId(){throw new Error("Function not implemented.")}}),I.value=b??2;return}let g=E();if(g.activeOptionIndex===null){let T=g.options.findIndex(k=>!k.dataRef.disabled);T!==-1&&(g.activeOptionIndex=T)}let w=s===A.Specific?v:ie({focus:s},{resolveItems:()=>g.options,resolveActiveIndex:()=>g.activeOptionIndex,resolveId:T=>T.id,resolveDisabled:T=>T.dataRef.disabled});h.value=w,I.value=b??2,r.value=g.options})},selectOption(s){let v=r.value.find(g=>g.id===s);if(!v)return;let{dataRef:b}=v;R(b.value)},selectActiveOption(){if(u.activeOptionIndex.value!==null){if(u.virtual.value)R(u.virtual.value.options[u.activeOptionIndex.value]);else{let{dataRef:s}=r.value[u.activeOptionIndex.value];R(s.value)}u.goToOption(A.Specific,u.activeOptionIndex.value)}},registerOption(s,v){let b=xe({id:s,dataRef:v});if(u.virtual.value){r.value.push(b);return}p&&cancelAnimationFrame(p);let g=E(w=>(w.push(b),w));h.value===null&&u.isSelected(v.value.value)&&(g.activeOptionIndex=g.options.indexOf(b)),r.value=g.options,h.value=g.activeOptionIndex,I.value=2,g.options.some(w=>!O(w.dataRef.domRef))&&(p=requestAnimationFrame(()=>{let w=E();r.value=w.options,h.value=w.activeOptionIndex}))},unregisterOption(s,v){if($!==null&&cancelAnimationFrame($),v&&(M.value=!0),u.virtual.value){r.value=r.value.filter(g=>g.id!==s);return}let b=E(g=>{let w=g.findIndex(T=>T.id===s);return w!==-1&&g.splice(w,1),g});r.value=b.options,h.value=b.activeOptionIndex,I.value=2},isSelected(s){return W(z.value,{0:()=>u.compare(B(u.value.value),B(s)),1:()=>B(u.value.value).some(v=>u.compare(B(v),B(s)))})},isActive(s){return h.value===u.calculateIndex(s)}};Ce([i,t,f],()=>u.closeCombobox(),x(()=>e.value===0)),de(fe,u),Pe(x(()=>W(e.value,{0:Q.Open,1:Q.Closed})));let S=x(()=>{var s;return(s=O(i))==null?void 0:s.closest("form")});return le(()=>{j([S],()=>{if(!S.value||o.defaultValue===void 0)return;function s(){u.change(o.defaultValue)}return S.value.addEventListener("reset",s),()=>{var v;(v=S.value)==null||v.removeEventListener("reset",s)}},{immediate:!0})}),()=>{var s,v,b;let{name:g,disabled:w,form:T,...k}=o,L={open:e.value===0,disabled:w,activeIndex:u.activeOptionIndex.value,activeOption:u.activeOptionIndex.value===null?null:u.virtual.value?u.virtual.value.options[(s=u.activeOptionIndex.value)!=null?s:0]:(b=(v=u.options.value[u.activeOptionIndex.value])==null?void 0:v.dataRef.value)!=null?b:null,value:y.value};return Y(Se,[...g!=null&&y.value!=null?ye({[g]:y.value}).map(([q,me])=>Y(ze,Re({features:Ae.Hidden,key:q,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:T,disabled:w,name:q,value:me}))):[],H({theirProps:{...l,...oe(k,["by","defaultValue","immediate","modelValue","multiple","nullable","onUpdate:modelValue","virtual"])},ourProps:{},slot:L,slots:d,attrs:l,name:"Combobox"})])}}}),ft=K({name:"ComboboxButton",props:{as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(o,{attrs:d,slots:l,expose:n}){var e;let a=(e=o.id)!=null?e:`headlessui-combobox-button-${X()}`,i=U("ComboboxButton");n({el:i.buttonRef,$el:i.buttonRef});function t(r){i.disabled.value||(i.comboboxState.value===0?i.closeCombobox():(r.preventDefault(),i.openCombobox()),_(()=>{var h;return(h=O(i.inputRef))==null?void 0:h.focus({preventScroll:!0})}))}function f(r){switch(r.key){case F.ArrowDown:r.preventDefault(),r.stopPropagation(),i.comboboxState.value===1&&i.openCombobox(),_(()=>{var h;return(h=i.inputRef.value)==null?void 0:h.focus({preventScroll:!0})});return;case F.ArrowUp:r.preventDefault(),r.stopPropagation(),i.comboboxState.value===1&&(i.openCombobox(),_(()=>{i.value.value||i.goToOption(A.Last)})),_(()=>{var h;return(h=i.inputRef.value)==null?void 0:h.focus({preventScroll:!0})});return;case F.Escape:if(i.comboboxState.value!==0)return;r.preventDefault(),i.optionsRef.value&&!i.optionsPropsRef.value.static&&r.stopPropagation(),i.closeCombobox(),_(()=>{var h;return(h=i.inputRef.value)==null?void 0:h.focus({preventScroll:!0})});return}}let c=Ee(x(()=>({as:o.as,type:d.type})),i.buttonRef);return()=>{var r,h;let I={open:i.comboboxState.value===0,disabled:i.disabled.value,value:i.value.value},{...M}=o,E={ref:i.buttonRef,id:a,type:c.value,tabindex:"-1","aria-haspopup":"listbox","aria-controls":(r=O(i.optionsRef))==null?void 0:r.id,"aria-expanded":i.comboboxState.value===0,"aria-labelledby":i.labelRef.value?[(h=O(i.labelRef))==null?void 0:h.id,a].join(" "):void 0,disabled:i.disabled.value===!0?!0:void 0,onKeydown:f,onClick:t};return H({ourProps:E,theirProps:M,slot:I,attrs:d,slots:l,name:"ComboboxButton"})}}}),ht=K({name:"ComboboxInput",props:{as:{type:[Object,String],default:"input"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},displayValue:{type:Function},defaultValue:{type:String,default:void 0},id:{type:String,default:null}},emits:{change:o=>!0},setup(o,{emit:d,attrs:l,slots:n,expose:e}){var a;let i=(a=o.id)!=null?a:`headlessui-combobox-input-${X()}`,t=U("ComboboxInput"),f=x(()=>Fe(O(t.inputRef))),c={value:!1};e({el:t.inputRef,$el:t.inputRef});function r(){t.change(null);let p=O(t.optionsRef);p&&(p.scrollTop=0),t.goToOption(A.Nothing)}let h=x(()=>{var p;let R=t.value.value;return O(t.inputRef)?typeof o.displayValue<"u"&&R!==void 0?(p=o.displayValue(R))!=null?p:"":typeof R=="string"?R:"":""});le(()=>{j([h,t.comboboxState,f],([p,R],[C,u])=>{if(c.value)return;let S=O(t.inputRef);S&&((u===0&&R===1||p!==C)&&(S.value=p),requestAnimationFrame(()=>{var s;if(c.value||!S||((s=f.value)==null?void 0:s.activeElement)!==S)return;let{selectionStart:v,selectionEnd:b}=S;Math.abs((b??0)-(v??0))===0&&v===0&&S.setSelectionRange(S.value.length,S.value.length)}))},{immediate:!0}),j([t.comboboxState],([p],[R])=>{if(p===0&&R===1){if(c.value)return;let C=O(t.inputRef);if(!C)return;let u=C.value,{selectionStart:S,selectionEnd:s,selectionDirection:v}=C;C.value="",C.value=u,v!==null?C.setSelectionRange(S,s,v):C.setSelectionRange(S,s)}})});let I=P(!1);function M(){I.value=!0}function E(){te().nextFrame(()=>{I.value=!1})}let z=Ye();function V(p){switch(c.value=!0,z(()=>{c.value=!1}),p.key){case F.Enter:if(c.value=!1,t.comboboxState.value!==0||I.value)return;if(p.preventDefault(),p.stopPropagation(),t.activeOptionIndex.value===null){t.closeCombobox();return}t.selectActiveOption(),t.mode.value===0&&t.closeCombobox();break;case F.ArrowDown:return c.value=!1,p.preventDefault(),p.stopPropagation(),W(t.comboboxState.value,{0:()=>t.goToOption(A.Next),1:()=>t.openCombobox()});case F.ArrowUp:return c.value=!1,p.preventDefault(),p.stopPropagation(),W(t.comboboxState.value,{0:()=>t.goToOption(A.Previous),1:()=>{t.openCombobox(),_(()=>{t.value.value||t.goToOption(A.Last)})}});case F.Home:if(p.shiftKey)break;return c.value=!1,p.preventDefault(),p.stopPropagation(),t.goToOption(A.First);case F.PageUp:return c.value=!1,p.preventDefault(),p.stopPropagation(),t.goToOption(A.First);case F.End:if(p.shiftKey)break;return c.value=!1,p.preventDefault(),p.stopPropagation(),t.goToOption(A.Last);case F.PageDown:return c.value=!1,p.preventDefault(),p.stopPropagation(),t.goToOption(A.Last);case F.Escape:if(c.value=!1,t.comboboxState.value!==0)return;p.preventDefault(),t.optionsRef.value&&!t.optionsPropsRef.value.static&&p.stopPropagation(),t.nullable.value&&t.mode.value===0&&t.value.value===null&&r(),t.closeCombobox();break;case F.Tab:if(c.value=!1,t.comboboxState.value!==0)return;t.mode.value===0&&t.activationTrigger.value!==1&&t.selectActiveOption(),t.closeCombobox();break}}function D(p){d("change",p),t.nullable.value&&t.mode.value===0&&p.target.value===""&&r(),t.openCombobox()}function m(p){var R,C,u;let S=(R=p.relatedTarget)!=null?R:ae.find(s=>s!==p.currentTarget);if(c.value=!1,!((C=O(t.optionsRef))!=null&&C.contains(S))&&!((u=O(t.buttonRef))!=null&&u.contains(S))&&t.comboboxState.value===0)return p.preventDefault(),t.mode.value===0&&(t.nullable.value&&t.value.value===null?r():t.activationTrigger.value!==1&&t.selectActiveOption()),t.closeCombobox()}function y(p){var R,C,u;let S=(R=p.relatedTarget)!=null?R:ae.find(s=>s!==p.currentTarget);(C=O(t.buttonRef))!=null&&C.contains(S)||(u=O(t.optionsRef))!=null&&u.contains(S)||t.disabled.value||t.immediate.value&&t.comboboxState.value!==0&&(t.openCombobox(),te().nextFrame(()=>{t.setActivationTrigger(1)}))}let $=x(()=>{var p,R,C,u;return(u=(C=(R=o.defaultValue)!=null?R:t.defaultValue.value!==void 0?(p=o.displayValue)==null?void 0:p.call(o,t.defaultValue.value):null)!=null?C:t.defaultValue.value)!=null?u:""});return()=>{var p,R,C,u,S,s,v;let b={open:t.comboboxState.value===0},{displayValue:g,onChange:w,...T}=o,k={"aria-controls":(p=t.optionsRef.value)==null?void 0:p.id,"aria-expanded":t.comboboxState.value===0,"aria-activedescendant":t.activeOptionIndex.value===null?void 0:t.virtual.value?(R=t.options.value.find(L=>!t.virtual.value.disabled(L.dataRef.value)&&t.compare(L.dataRef.value,t.virtual.value.options[t.activeOptionIndex.value])))==null?void 0:R.id:(C=t.options.value[t.activeOptionIndex.value])==null?void 0:C.id,"aria-labelledby":(s=(u=O(t.labelRef))==null?void 0:u.id)!=null?s:(S=O(t.buttonRef))==null?void 0:S.id,"aria-autocomplete":"list",id:i,onCompositionstart:M,onCompositionend:E,onKeydown:V,onInput:D,onFocus:y,onBlur:m,role:"combobox",type:(v=l.type)!=null?v:"text",tabIndex:0,ref:t.inputRef,defaultValue:$.value,disabled:t.disabled.value===!0?!0:void 0};return H({ourProps:k,theirProps:T,slot:b,attrs:l,slots:n,features:G.RenderStrategy|G.Static,name:"ComboboxInput"})}}}),mt=K({name:"ComboboxOptions",props:{as:{type:[Object,String],default:"ul"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},hold:{type:[Boolean],default:!1}},setup(o,{attrs:d,slots:l,expose:n}){let e=U("ComboboxOptions"),a=`headlessui-combobox-options-${X()}`;n({el:e.optionsRef,$el:e.optionsRef}),Z(()=>{e.optionsPropsRef.value.static=o.static}),Z(()=>{e.optionsPropsRef.value.hold=o.hold});let i=De(),t=x(()=>i!==null?(i.value&Q.Open)===Q.Open:e.comboboxState.value===0);Me({container:x(()=>O(e.optionsRef)),enabled:x(()=>e.comboboxState.value===0),accept(c){return c.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:c.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(c){c.setAttribute("role","none")}});function f(c){c.preventDefault()}return()=>{var c,r,h;let I={open:e.comboboxState.value===0},M={"aria-labelledby":(h=(c=O(e.labelRef))==null?void 0:c.id)!=null?h:(r=O(e.buttonRef))==null?void 0:r.id,id:a,ref:e.optionsRef,role:"listbox","aria-multiselectable":e.mode.value===1?!0:void 0,onMousedown:f},E=oe(o,["hold"]);return H({ourProps:M,theirProps:E,slot:I,attrs:d,slots:e.virtual.value&&e.comboboxState.value===0?{...l,default:()=>[Y(et,{},l.default)]}:l,features:G.RenderStrategy|G.Static,visible:t.value,name:"ComboboxOptions"})}}}),bt=K({name:"ComboboxOption",props:{as:{type:[Object,String],default:"li"},value:{type:[Object,String,Number,Boolean]},disabled:{type:Boolean,default:!1},order:{type:[Number],default:null}},setup(o,{slots:d,attrs:l,expose:n}){let e=U("ComboboxOption"),a=`headlessui-combobox-option-${X()}`,i=P(null),t=x(()=>o.disabled);n({el:i,$el:i});let f=x(()=>{var m;return e.virtual.value?e.activeOptionIndex.value===e.calculateIndex(o.value):e.activeOptionIndex.value===null?!1:((m=e.options.value[e.activeOptionIndex.value])==null?void 0:m.id)===a}),c=x(()=>e.isSelected(o.value)),r=ce(he,null),h=x(()=>({disabled:o.disabled,value:o.value,domRef:i,order:x(()=>o.order)}));le(()=>e.registerOption(a,h)),ue(()=>e.unregisterOption(a,f.value)),Z(()=>{let m=O(i);m&&(r==null||r.value.measureElement(m))}),Z(()=>{e.comboboxState.value===0&&f.value&&(e.virtual.value||e.activationTrigger.value!==0&&_(()=>{var m,y;return(y=(m=O(i))==null?void 0:m.scrollIntoView)==null?void 0:y.call(m,{block:"nearest"})}))});function I(m){m.preventDefault(),m.button===pe.Left&&(t.value||(e.selectOption(a),we()||requestAnimationFrame(()=>{var y;return(y=O(e.inputRef))==null?void 0:y.focus({preventScroll:!0})}),e.mode.value===0&&e.closeCombobox()))}function M(){var m;if(o.disabled||(m=e.virtual.value)!=null&&m.disabled(o.value))return e.goToOption(A.Nothing);let y=e.calculateIndex(o.value);e.goToOption(A.Specific,y)}let E=Te();function z(m){E.update(m)}function V(m){var y;if(!E.wasMoved(m)||o.disabled||(y=e.virtual.value)!=null&&y.disabled(o.value)||f.value)return;let $=e.calculateIndex(o.value);e.goToOption(A.Specific,$,0)}function D(m){var y;E.wasMoved(m)&&(o.disabled||(y=e.virtual.value)!=null&&y.disabled(o.value)||f.value&&(e.optionsPropsRef.value.hold||e.goToOption(A.Nothing)))}return()=>{let{disabled:m}=o,y={active:f.value,selected:c.value,disabled:m},$={id:a,ref:i,role:"option",tabIndex:m===!0?void 0:-1,"aria-disabled":m===!0?!0:void 0,"aria-selected":c.value,disabled:void 0,onMousedown:I,onFocus:M,onPointerenter:z,onMouseenter:z,onPointermove:V,onMousemove:V,onPointerleave:D,onMouseleave:D},p=oe(o,["order","value"]);return H({ourProps:$,theirProps:p,slot:y,attrs:l,slots:d,name:"ComboboxOption"})}}});export{ht as i,pt as l,ft as n,bt as r,mt as u};
