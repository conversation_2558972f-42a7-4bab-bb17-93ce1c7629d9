import{N as oe,S as g,i as ue,u as $,o as Q,A as X,T as se}from"./render-c34c346a.js";import{s as de,t as fe,i as p,l as ve}from"./open-closed-7f51e238.js";import{c as pe}from"./env-c107754a.js";import{o as Y}from"./disposables-4ddc41dd.js";import{H as Z,e as b,L as _,l as F,A as S,B as k,J as B,p as me,I as x,n as he,K as P}from"./app-f0078ddb.js";function ce(e){let t={called:!1};return(...r)=>{if(!t.called)return t.called=!0,e(...r)}}function O(e,...t){e&&t.length>0&&e.classList.add(...t)}function w(e,...t){e&&t.length>0&&e.classList.remove(...t)}var D=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(D||{});function ge(e,t){let r=Y();if(!e)return r.dispose;let{transitionDuration:i,transitionDelay:o}=getComputedStyle(e),[n,l]=[i,o].map(a=>{let[u=0]=a.split(",").filter(Boolean).map(s=>s.includes("ms")?parseFloat(s):parseFloat(s)*1e3).sort((s,d)=>d-s);return u});return n!==0?r.setTimeout(()=>t("finished"),n+l):t("finished"),r.add(()=>t("cancelled")),r.dispose}function W(e,t,r,i,o,n){let l=Y(),a=n!==void 0?ce(n):()=>{};return w(e,...o),O(e,...t,...r),l.nextFrame(()=>{w(e,...r),O(e,...i),l.add(ge(e,u=>(w(e,...i,...t),O(e,...o),a(u))))}),l.add(()=>w(e,...t,...r,...i,...o)),l.add(()=>a("cancelled")),l.dispose}function c(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let U=Symbol("TransitionContext");var be=(e=>(e.Visible="visible",e.Hidden="hidden",e))(be||{});function ye(){return P(U,null)!==null}function Se(){let e=P(U,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function Ee(){let e=P(j,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let j=Symbol("NestingContext");function C(e){return"children"in e?C(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function q(e){let t=b([]),r=b(!1);S(()=>r.value=!0),k(()=>r.value=!1);function i(n,l=g.Hidden){let a=t.value.findIndex(({id:u})=>u===n);a!==-1&&($(l,{[g.Unmount](){t.value.splice(a,1)},[g.Hidden](){t.value[a].state="hidden"}}),!C(t)&&r.value&&(e==null||e()))}function o(n){let l=t.value.find(({id:a})=>a===n);return l?l.state!=="visible"&&(l.state="visible"):t.value.push({id:n,state:"visible"}),()=>i(n,g.Unmount)}return{children:t,register:o,unregister:i}}let G=oe.RenderStrategy,Le=Z({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:i,expose:o}){let n=b(0);function l(){n.value|=p.Opening,t("beforeEnter")}function a(){n.value&=~p.Opening,t("afterEnter")}function u(){n.value|=p.Closing,t("beforeLeave")}function s(){n.value&=~p.Closing,t("afterLeave")}if(!ye()&&de())return()=>_(we,{...e,onBeforeEnter:l,onAfterEnter:a,onBeforeLeave:u,onAfterLeave:s},i);let d=b(null),E=F(()=>e.unmount?g.Unmount:g.Hidden);o({el:d,$el:d});let{show:m,appear:M}=Se(),{register:N,unregister:A}=Ee(),f=b(m.value?"visible":"hidden"),R={value:!0},y=ue(),L={value:!1},I=q(()=>{!L.value&&f.value!=="hidden"&&(f.value="hidden",A(y),s())});S(()=>{let v=N(y);k(v)}),B(()=>{if(E.value===g.Hidden&&y){if(m.value&&f.value!=="visible"){f.value="visible";return}$(f.value,{hidden:()=>A(y),visible:()=>N(y)})}});let V=c(e.enter),z=c(e.enterFrom),ee=c(e.enterTo),J=c(e.entered),te=c(e.leave),ne=c(e.leaveFrom),le=c(e.leaveTo);S(()=>{B(()=>{if(f.value==="visible"){let v=Q(d);if(v instanceof Comment&&v.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function re(v){let H=R.value&&!M.value,h=Q(d);!h||!(h instanceof HTMLElement)||H||(L.value=!0,m.value&&l(),m.value||u(),v(m.value?W(h,V,z,ee,J,T=>{L.value=!1,T===D.Finished&&a()}):W(h,te,ne,le,J,T=>{L.value=!1,T===D.Finished&&(C(I)||(f.value="hidden",A(y),s()))})))}return S(()=>{me([m],(v,H,h)=>{re(h),R.value=!1},{immediate:!0})}),x(j,I),fe(F(()=>$(f.value,{visible:p.Open,hidden:p.Closed})|n.value)),()=>{let{appear:v,show:H,enter:h,enterFrom:T,enterTo:Fe,entered:Be,leave:Ce,leaveFrom:Ae,leaveTo:He,...K}=e,ae={ref:d},ie={...K,...M.value&&m.value&&pe.isServer?{class:he([r.class,K.class,...V,...z])}:{}};return X({theirProps:ie,ourProps:ae,slot:{},slots:i,attrs:r,features:G,visible:f.value==="visible",name:"TransitionChild"})}}}),Te=Le,we=Z({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:i}){let o=ve(),n=F(()=>e.show===null&&o!==null?(o.value&p.Open)===p.Open:e.show);B(()=>{if(![!0,!1].includes(n.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let l=b(n.value?"visible":"hidden"),a=q(()=>{l.value="hidden"}),u=b(!0),s={show:n,appear:F(()=>e.appear||!u.value)};return S(()=>{B(()=>{u.value=!1,n.value?l.value="visible":C(a)||(l.value="hidden")})}),x(j,a),x(U,s),()=>{let d=se(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),E={unmount:e.unmount};return X({ourProps:{...E,as:"template"},theirProps:{},slot:{},slots:{...i,default:()=>[_(Te,{onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave"),...r,...E,...d},i.default)]},attrs:{},features:G,visible:l.value==="visible",name:"Transition"})}}});export{we as S,Le as h};
