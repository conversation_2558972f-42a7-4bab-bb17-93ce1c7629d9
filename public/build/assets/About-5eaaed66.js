import{e as _,i as k,o as i,d as n,b as a,u as o,w as m,F as d,m as L,a as e,j as h,h as u,t as c,n as x,g}from"./app-f0078ddb.js";import{_ as M,L as v}from"./AppLayout-33f062bc.js";import{_ as T}from"./Breadcrumbs-c96e9207.js";import{_ as V}from"./Footer-0988dcd8.js";import{_ as S}from"./MobileSidebar-5e21b4cd.js";import{r as N}from"./InformationCircleIcon-716f3ffb.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";const A={class:"lg:pr-96 2xl:pr-[32rem]"},C={class:"px-4 py-8 sm:px-8"},Z={class:"flex flex-row items-center justify-between"},$={class:"mx-auto mt-12"},z={class:"mx-auto max-w-2xl lg:mx-0 lg:max-w-none"},O={class:"mt-6 flex flex-col gap-x-8 gap-y-20"},j={class:"lg:flex lg:flex-auto"},U={class:"w-64 space-y-8 xl:w-80"},W={class:"text-base leading-7 text-gray-600"},q={class:"text-5xl font-semibold tracking-tight text-gray-900"},B={class:"my-8 sm:mx-auto sm:w-full sm:max-w-md"},F={class:"mt-8 flex-1 text-left"},P={class:"mt-2"},H={class:"hidden bg-slate-50 p-8 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},I={class:"mb-8 sm:mx-auto sm:w-full sm:max-w-md"},Q={class:"mt-8 flex-1 text-left"},D={class:"mt-2"},Ne={__name:"About",props:{stats:Object},setup(b){const l=b,y=[{name:"About",href:"#",current:!0}],f=[{name:"About",href:route("about"),current:!0},{name:"Contact",href:route("contact"),current:!1},{name:"Privacy Policy",href:"/privacy-policy",current:!1},{name:"Terms of Service",href:"/terms-of-service",current:!1},{name:"Frequently Asked Questions",href:"/faq",current:!1}],w=[{label:"Minutes of Videos about Latin",value:Number(l.stats.video_minutes).toLocaleString()},{label:"Video Views",value:Number(l.stats.video_views).toLocaleString()},{label:"Vocabulary Words Practiced",value:Number(l.stats.words_practiced).toLocaleString()},{label:"Grammar Questions Attempted",value:Number(l.stats.langauge_practice).toLocaleString()}];let r=_(!1);return(E,t)=>{const p=k("Link");return i(),n(d,null,[a(o(L),{title:"About"}),a(M,null,{default:m(()=>[e("main",A,[e("div",C,[e("div",Z,[a(T,{class:"lg:col-span-9 xl:grid-cols-10",pages:y}),a(o(N),{class:"ml-2 mt-1 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=s=>h(r)?r.value=!o(r):r=!o(r))})]),t[4]||(t[4]=e("div",{class:"relative -z-10"},[e("svg",{class:"absolute inset-x-0 top-0 -z-10 h-[64rem] w-full stroke-gray-200 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]","aria-hidden":"true"},[e("defs",null,[e("pattern",{id:"1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84",width:"200",height:"200",x:"50%",y:"-1",patternUnits:"userSpaceOnUse"},[e("path",{d:"M.5 200V.5H200",fill:"none"})])]),e("svg",{x:"50%",y:"-1",class:"overflow-visible fill-gray-50"},[e("path",{d:"M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z","stroke-width":"0"})]),e("rect",{width:"100%",height:"100%","stroke-width":"0",fill:"url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)"})])],-1)),e("div",$,[e("div",z,[t[3]||(t[3]=e("h2",{class:"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"}," Our mission ",-1)),e("div",O,[t[2]||(t[2]=e("div",{class:"lg:w-full lg:max-w-2xl lg:flex-auto"},[e("p",{class:"text-xl leading-8 text-gray-600"}," The goal of LatinTutorial is to help students and teachers alike learn Latin more quickly and easily. We provide clear and concise explanations of Latin grammar, and provide free exercises and resources to help students learn Latin. We also provide teachers with the tools to complement the work they are doing in the classroom. "),e("div",{class:"mt-10 max-w-xl text-base leading-7 text-gray-700"},[e("p",null," The best way to learn Latin is to combine video tutorials with language and vocabulary practice. LatinTutorial provides both video tutorials covering Latin grammar, and a variety of exercises to practice what you learn. "),e("p",{class:"mt-10"}," Our content continues to grow each week. We are always adding new videos, exercises, and other resources to help you learn Latin more effectively. ")])],-1)),e("div",j,[e("dl",U,[(i(),n(d,null,u(w,s=>e("div",{key:s.label,class:"flex flex-col-reverse gap-y-4"},[e("dt",W,c(s.label),1),e("dd",q,c(s.value),1)])),64))])])])])]),t[5]||(t[5]=e("div",{class:"relative isolate -z-10 mt-32 sm:mt-48"},[e("div",{class:"absolute inset-x-0 top-1/2 -z-10 flex -translate-y-1/2 justify-center overflow-hidden [mask-image:radial-gradient(50%_45%_at_50%_55%,white,transparent)]"},[e("svg",{class:"h-[40rem] w-[80rem] flex-none stroke-gray-200","aria-hidden":"true"},[e("defs",null,[e("pattern",{id:"e9033f3e-f665-41a6-84ef-756f6778e6fe",width:"200",height:"200",x:"50%",y:"50%",patternUnits:"userSpaceOnUse",patternTransform:"translate(-100 0)"},[e("path",{d:"M.5 200V.5H200",fill:"none"})])]),e("svg",{x:"50%",y:"50%",class:"overflow-visible fill-gray-50"},[e("path",{d:"M-300 0h201v201h-201Z M300 200h201v201h-201Z","stroke-width":"0"})]),e("rect",{width:"100%",height:"100%","stroke-width":"0",fill:"url(#e9033f3e-f665-41a6-84ef-756f6778e6fe)"})])])],-1))]),a(V,{class:"mb-8"})]),a(S,{class:"lg:hidden",show:o(r),onClose:t[1]||(t[1]=s=>h(r)?r.value=!1:r=!1)},{default:m(()=>[e("div",B,[a(v,{class:"mx-auto w-full"})]),t[7]||(t[7]=e("p",{class:"mt-6 text-base font-medium leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. ",-1)),e("div",F,[t[6]||(t[6]=e("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),e("div",P,[(i(),n(d,null,u(f,s=>a(p,{key:s.name,href:s.href,disabled:s.current,class:x(["block py-1 text-sm",s.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:m(()=>[g(c(s.name),1)]),_:2},1032,["href","disabled","class"])),64))])])]),_:1},8,["show"]),e("aside",H,[e("div",I,[a(v,{class:"mx-auto w-full"})]),t[9]||(t[9]=e("p",{class:"mt-6 text-base font-medium leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. ",-1)),e("div",Q,[t[8]||(t[8]=e("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),e("div",D,[(i(),n(d,null,u(f,s=>a(p,{key:s.name,href:s.href,disabled:s.current,class:x(["block py-1 text-sm",s.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:m(()=>[g(c(s.name),1)]),_:2},1032,["href","disabled","class"])),64))])])])]),_:1})],64)}}};export{Ne as default};
