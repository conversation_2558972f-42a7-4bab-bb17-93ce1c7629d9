import{N as Kt,O as qt}from"./app-f0078ddb.js";var z={exports:{}};(function(Bt,mt){(function(E,P){P(mt)})(Kt,function(E){function P(e,r){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),s.push.apply(s,o)}return s}function B(e){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?P(Object(s),!0).forEach(function(o){gt(e,o,s[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):P(Object(s)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(s,o))})}return e}function l(e){"@babel/helpers - typeof";return l=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},l(e)}function gt(e,r,s){return r=pt(r),r in e?Object.defineProperty(e,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[r]=s,e}function yt(e,r){if(typeof e!="object"||e===null)return e;var s=e[Symbol.toPrimitive];if(s!==void 0){var o=s.call(e,r||"default");if(typeof o!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function pt(e){var r=yt(e,"string");return typeof r=="symbol"?r:String(r)}function Tt(e,r,s){var o,a="";if(e=typeof e=="number"?String(e):e,e.length>r)return e;for(o=0;o<r;o=o+1)a+=String(s);return(a+e).slice(-a.length)}function w(){this.reset()}w.prototype.toString=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["hours","minutes","seconds"],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:":",s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:2;e=e||["hours","minutes","seconds"],r=r||":",s=s||2;var o=[],a;for(a=0;a<e.length;a=a+1)this[e[a]]!==void 0&&(e[a]==="secondTenths"?o.push(this[e[a]]):o.push(Tt(this[e[a]],s,"0")));return o.join(r)},w.prototype.reset=function(){this.secondTenths=0,this.seconds=0,this.minutes=0,this.hours=0,this.days=0};function m(){this.events={}}m.prototype.on=function(e,r){var s=this;return Array.isArray(this.events[e])||(this.events[e]=[]),this.events[e].push(r),function(){return s.removeListener(e,r)}},m.prototype.removeListener=function(e,r){if(Array.isArray(this.events[e])){var s=this.events[e].indexOf(r);s>-1&&this.events[e].splice(s,1)}},m.prototype.removeAllListeners=function(e){e?Array.isArray(this.events[e])&&(this.events[e]=[]):this.events={}},m.prototype.emit=function(e){for(var r=this,s=arguments.length,o=new Array(s>1?s-1:0),a=1;a<s;a++)o[a-1]=arguments[a];Array.isArray(this.events[e])&&this.events[e].forEach(function(p){return p.apply(r,o)})};var A=10,D=60,I=60,N=24,_=0,j=1,C=2,U=3,L=4,v="secondTenths",g="seconds",M="minutes",R="hours",y="days",Q=[v,g,M,R,y],h={secondTenths:100,seconds:1e3,minutes:6e4,hours:36e5,days:864e5},St={secondTenths:A,seconds:D,minutes:I,hours:N};function F(e,r){return(e%r+r)%r}function Z(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=new w,s=new w,o,a=new m,p=!1,H=!1,T,x,Y,k={},G,c,d,K,S,J,u={detail:{timer:this}};nt(e);function Ot(t,n){var i=St[t];s[t]=n,t===y?r[t]=Math.abs(n):n>=0?r[t]=F(n,i):r[t]=F(i-F(n,i),i)}function bt(t){return O(t,y)}function Et(t){return O(t,R)}function Pt(t){return O(t,M)}function wt(t){return O(t,g)}function At(t){return O(t,v)}function O(t,n){var i=s[n];return Ot(n,b(t,h[n])),s[n]!==i}function W(){X(),_t()}function X(){clearInterval(o),o=void 0,p=!1,H=!1}function $(t){ct()?(S=tt(),c=q(G.target)):nt(t),Dt()}function Dt(){var t=h[T];rt(V(Date.now()))||(o=setInterval(It,t),p=!0,H=!1)}function tt(){return V(Date.now())-s.secondTenths*h[v]*x}function It(){var t=V(Date.now()),n=et();Nt(n),Y(u.detail.timer),rt(t)&&(ot(),f("targetAchieved",u))}function et(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:V(Date.now()),n=x>0?t-S:S-t,i={};return i[v]=At(n),i[g]=wt(n),i[M]=Pt(n),i[R]=Et(n),i[y]=bt(n),i}function V(t){return Math.floor(t/h[T])*h[T]}function Nt(t){t[v]&&f("secondTenthsUpdated",u),t[g]&&f("secondsUpdated",u),t[M]&&f("minutesUpdated",u),t[R]&&f("hoursUpdated",u),t[y]&&f("daysUpdated",u)}function rt(t){return c instanceof Array&&t>=J}function _t(){r.reset(),s.reset()}function nt(t){t=t||{},T=jt(t.precision),Y=typeof t.callback=="function"?t.callback:function(){},K=t.countdown===!0,x=K===!0?-1:1,l(t.startValues)==="object"?Ut(t.startValues):d=null,S=tt(),et(),l(t.target)==="object"?c=q(t.target):K?(t.target={seconds:0},c=q(t.target)):c=null,k={precision:T,callback:Y,countdown:l(t)==="object"&&t.countdown===!0,target:c,startValues:d},G=t}function jt(t){if(t=typeof t=="string"?t:g,!Ct(t))throw new Error("Error in precision parameter: ".concat(t," is not a valid value"));return t}function Ct(t){return Q.indexOf(t)>=0}function st(t){var n;if(l(t)==="object")if(t instanceof Array){if(t.length!==5)throw new Error("Array size not valid");n=t}else{for(var i in t)if(Q.indexOf(i)<0)throw new Error("Error in startValues or target parameter: ".concat(i," is not a valid input value"));n=[t.secondTenths||0,t.seconds||0,t.minutes||0,t.hours||0,t.days||0]}n=n.map(function(Gt){return parseInt(Gt,10)});var dt=n[_],lt=n[j]+b(dt,A),vt=n[C]+b(lt,D),ht=n[U]+b(vt,I),Yt=n[L]+b(ht,N);return n[_]=dt%A,n[j]=lt%D,n[C]=vt%I,n[U]=ht%N,n[L]=Yt,n}function b(t,n){var i=t/n;return i<0?Math.ceil(i):Math.floor(i)}function q(t){if(t){c=st(t);var n=it(c);return J=S+n.secondTenths*h[v]*x,c}}function Ut(t){d=st(t),r.secondTenths=d[_],r.seconds=d[j],r.minutes=d[C],r.hours=d[U],r.days=d[L],s=it(d,s)}function it(t,n){var i=n||{};return i.days=t[L],i.hours=i.days*N+t[U],i.minutes=i.hours*I+t[C],i.seconds=i.minutes*D+t[j],i.secondTenths=i.seconds*A+t[[_]],i}function ot(){W(),f("stopped",u)}function Lt(){W(),$(G),f("reset",u)}function Mt(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};t=B(B({},e),t),!ft()&&($(t),f("started",u))}function Rt(){X(),H=!0,f("paused",u)}function at(t,n){a.on(t,n)}function ut(t,n){a.removeListener(t,n)}function Ht(t){a.removeAllListeners(t)}function f(t,n){a.emit(t,n)}function ft(){return p}function ct(){return H}function xt(){return r}function Vt(){return s}function Ft(){return k}typeof this<"u"&&(this.start=Mt,this.pause=Rt,this.stop=ot,this.reset=Lt,this.isRunning=ft,this.isPaused=ct,this.getTimeValues=xt,this.getTotalTimeValues=Vt,this.getConfig=Ft,this.addEventListener=at,this.on=at,this.removeEventListener=ut,this.removeAllEventListeners=Ht,this.off=ut)}E.Timer=Z,E.default=Z,Object.defineProperty(E,"__esModule",{value:!0})})})(z,z.exports);var zt=z.exports;const Zt=qt(zt);export{Zt as T};
