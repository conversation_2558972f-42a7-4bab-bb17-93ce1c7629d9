import{D as v}from"./datetime-8ddd27a0.js";import{_ as E}from"./TruncateVerse-a9219b3d.js";import{D as L,l as O,e as m,o as r,d as n,a as t,t as l,F as _,h as y,b as p,n as w,u as b,E as i}from"./app-f0078ddb.js";import{r as k}from"./ArrowPathIcon-f74cb8d6.js";import"./removePunctuation-702d8a66.js";/* empty css            */const B={class:"flex flex-col items-center justify-center space-y-4 bg-gray-950 text-white"},C={class:"mx-auto mt-16 max-h-screen max-w-2xl px-6 lg:max-w-7xl lg:px-8"},M={class:""},Q={class:"col-span-4 mt-10 grid grid-cols-1 gap-4 sm:mt-16 lg:grid-cols-9 lg:grid-rows-3"},S={class:"flex p-px lg:col-span-4"},I={class:"w-full overflow-hidden rounded-lg bg-gray-900 ring-1 ring-white/15 max-lg:rounded-t-[2rem] lg:rounded-tl-[2rem]"},P={class:"p-10"},U={class:"ml-2 mt-4 text-2xl"},z={class:"ml-2 mt-4 text-2xl italic"},F={class:"flex p-px lg:col-span-2"},G={class:"w-full overflow-hidden rounded-lg bg-gray-900 ring-1 ring-white/15 lg:rounded-tr-[2rem]"},H={class:"p-10"},N={class:"-pb-[32px] mt-2 max-w-lg text-[80px] text-gray-400"},V={class:"col-span-3 pl-4"},W={class:"mt-4 flex flex-row items-center gap-4"},J={class:"w-32 text-xl font-semibold"},K={class:"w-full grow rounded-2xl px-4"},R={class:"flex p-px lg:col-span-3"},X={class:"w-full overflow-hidden rounded-lg bg-gray-900 ring-1 ring-white/15 lg:rounded-bl-[2rem]"},Y={class:"p-10"},Z={class:"flex items-center justify-between"},tt={class:"mt-2 text-xl font-medium tracking-tight text-white"},st={class:"mt-4 text-xl italic"},et={class:"flex p-px lg:col-span-3"},ot={class:"w-full overflow-hidden rounded-lg bg-gray-900 ring-1 ring-white/15 max-lg:rounded-b-[2rem] lg:rounded-br-[2rem]"},lt={class:"p-10"},at={class:"flex items-center justify-between"},rt={class:"mt-2 text-xl font-medium tracking-tight text-white"},nt={class:"italic"},it={class:"mt-2 line-clamp-4 max-w-lg text-sm/6 text-gray-400"},dt={class:"col-span-3 flex flex-col pl-4"},ct={class:"grid grid-cols-2 gap-3"},ut={class:"flex flex-row items-center gap-2 text-base"},mt={class:"w-32 truncate font-semibold"},xt={class:"grow"},yt={__name:"Classroom",props:{quote:{type:Object,required:!0},teams:{type:Array,required:!0},topUsers:{type:Array,required:!0},translation:{type:Object,required:!0},date:{type:Object,required:!0}},setup(a){L(3e4);const x=a,q=v.now(),D=()=>{const s=v.fromISO("2025-05-05").diff(q,"days").days;return Math.round(Math.abs(s))},T=O(()=>Math.max(...x.teams.map(g))),g=o=>o.total_count,d=(o,s)=>{let e=g(o);return e>0?`${s/e*100}%`:"0%"},$=o=>{let s=g(o);return s>0?`${s/T.value*100}%`:"0%"},c=m(x.quote),h=m(!1),j=async()=>{h.value=!0;try{const o=await axios.post("/classroom-dashboard/refresh-quote");setTimeout(()=>{c.value=o.data.quote,h.value=!1},150)}catch(o){console.error("Error refreshing quote:",o)}},u=m(x.translation),f=m(!1),A=async()=>{f.value=!0;try{const o=await axios.post("/classroom-dashboard/refresh-grammar");setTimeout(()=>{u.value=o.data,f.value=!1},150)}catch(o){console.error("Error refreshing quote:",o)}};return(o,s)=>(r(),n("div",B,[t("div",C,[t("div",M,[t("div",Q,[t("div",S,[t("div",I,[t("div",P,[s[0]||(s[0]=t("h1",{class:"text-4xl font-bold"},"Salvēte, omnēs!",-1)),s[1]||(s[1]=t("p",{class:"mt-8 text-2xl"},"Hodiē est ...",-1)),t("p",U,l(a.date.roman_date),1),t("p",z,l(a.date.roman_date_english),1)])])]),t("div",F,[t("div",G,[t("div",H,[s[2]||(s[2]=t("h3",{class:"text-sm/4 font-semibold text-gray-400"}," May 5, 2024 ",-1)),s[3]||(s[3]=t("p",{class:"mt-2 text-lg font-medium tracking-tight text-white"}," Days to the AP Latin Exam ",-1)),t("p",N,l(D()),1)])])]),t("div",V,[s[4]||(s[4]=t("h2",{class:"mb-4 text-2xl font-semibold"},"Infinitas",-1)),(r(!0),n(_,null,y(a.teams,e=>(r(),n("div",W,[t("h3",J,l(e.name),1),t("div",K,[t("div",{class:"flex h-4 w-full overflow-hidden rounded-full bg-gray-200",style:i(`width: ${$(e)}`)},[t("div",{class:"h-full bg-orange-300",style:i(`width: ${d(e,e.intervals[1])}`)},null,4),t("div",{class:"h-full bg-lime-300",style:i(`width: ${d(e,e.intervals[7])}`)},null,4),t("div",{class:"h-full bg-sky-300",style:i(`width: ${d(e,e.intervals[16])}`)},null,4),t("div",{class:"h-full bg-indigo-300",style:i(`width: ${d(e,e.intervals[30])}`)},null,4)],4)])]))),256))]),t("div",R,[t("div",X,[t("div",Y,[t("div",Z,[s[5]||(s[5]=t("h3",{class:"text-sm/4 font-semibold text-gray-400"}," Quote of the Day ",-1)),p(b(k),{class:w(["h-5 w-5 cursor-pointer text-gray-400 transition-colors duration-150 hover:text-gray-300",{"animate-spin":f.value}]),onClick:A},null,8,["class"])]),t("p",tt,[p(E,{token:u.value.token,verse:u.value.verse,isDark:!0},null,8,["token","verse"])]),t("p",st,l(u.value.translation),1),s[6]||(s[6]=t("p",{class:"mt-2 max-w-lg text-sm/6 text-gray-400"},null,-1))])])]),t("div",et,[t("div",ot,[t("div",lt,[t("div",at,[s[7]||(s[7]=t("h3",{class:"text-sm/4 font-semibold text-gray-400"}," Quote of the Day ",-1)),p(b(k),{class:w(["h-5 w-5 cursor-pointer text-gray-400 transition-colors duration-150 hover:text-gray-300",{"animate-spin":h.value}]),onClick:j},null,8,["class"])]),t("p",rt,l(c.value.phrase),1),t("p",nt,l(c.value.translation),1),t("p",it,l(c.value.notes),1)])])]),t("div",dt,[s[8]||(s[8]=t("h2",{class:"mb-4 text-2xl font-semibold"},"Top Hexameter Users",-1)),t("div",ct,[(r(!0),n(_,null,y(a.topUsers,e=>(r(),n("div",ut,[t("span",mt,l(e.name),1),t("span",xt,l(e.rating),1)]))),256))])])])])])]))}};export{yt as default};
