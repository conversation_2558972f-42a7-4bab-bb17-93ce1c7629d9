import{e as y,p as x,o as r,c as h,w as o,b as n,g as w,t as m,u as e,a as s,d,h as _,n as f,f as k,F as V,T as N,j}from"./app-f0078ddb.js";import{r as A}from"./ChevronUpDownIcon-aff937c8.js";import{r as B}from"./CheckIcon-4bbdc2ab.js";import{E as F,j as C,A as D,F as E,I as T}from"./listbox-f702e976.js";const z={class:"relative mt-2"},I={key:0,class:"flex items-center"},S={key:1,class:"flex items-center"},G={class:"ml-3 block truncate"},L={class:"pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2"},O={class:"flex items-center"},J={__name:"DropdownGeneral",props:{list:Array,title:String,current:Object},emits:["update:item"],setup(i,{emit:b}){const u=i;let t=y("");t.value=u.list.find(a=>a.value==u.current.value);const v=b;return x(()=>t,a=>{v("update:item",a)},{deep:!0}),x(()=>u.list,a=>{t.value=a[0]},{deep:!0}),(a,c)=>(r(),h(e(T),{modelValue:e(t),"onUpdate:modelValue":c[0]||(c[0]=l=>j(t)?t.value=l:t=l),as:"div"},{default:o(()=>[n(e(F),{class:"block text-sm font-bold text-gray-500 uppercase"},{default:o(()=>[w(m(i.title),1)]),_:1}),s("div",z,[n(e(C),{class:"relative w-full cursor-default rounded-lg border border-gray-300 bg-white py-2 pr-10 pl-3 text-left text-gray-900 shadow-xs focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 focus:outline-hidden sm:text-sm"},{default:o(()=>[i.list.length==0?(r(),d("span",I,c[1]||(c[1]=[s("span",{class:"ml-3 block truncate"},"No Data Available",-1)]))):(r(),d("span",S,[s("span",G,m(e(t).name),1)])),s("span",L,[n(e(A),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),n(N,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:o(()=>[n(e(D),{class:"absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg focus:outline-hidden sm:text-sm"},{default:o(()=>[(r(!0),d(V,null,_(i.list,l=>(r(),h(e(E),{key:l.id,as:"template",value:l},{default:o(({active:p,selected:g})=>[s("li",{class:f([p?"bg-indigo-600 text-white":"text-gray-900","relative cursor-default py-2 pr-9 pl-3 select-none"])},[s("div",O,[s("span",{class:f([g?"font-semibold":"font-normal","ml-3 block truncate"])},m(l.name),3)]),g?(r(),d("span",{key:0,class:f([p?"text-white":"text-indigo-600","absolute inset-y-0 right-0 flex items-center pr-4"])},[n(e(B),{class:"h-5 w-5","aria-hidden":"true"})],2)):k("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]))}};export{J as _};
