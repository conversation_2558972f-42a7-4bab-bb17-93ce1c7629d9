import{_ as v}from"./AppLayout-33f062bc.js";import{_ as g}from"./Breadcrumbs-c96e9207.js";import V from"./TokenSyntax-803d58d4.js";import{_ as C}from"./ButtonItem-718c0517.js";import{l as L,e as B,A as S,i as $,o as s,c as k,w as p,b as d,a as e,t as r,d as i,h as u,F as f,u as A}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";/* empty css            */const N={class:"p-8"},F={class:"grid grid-cols-3 gap-4"},H={class:"col-span-3 mt-6 space-y-4"},j={class:"overflow-hidden"},z={class:"px-4 py-5 sm:p-6"},D={class:"flex flex-col"},E={class:"text-3xl font-semibold text-gray-800"},I={class:"mt-2 text-lg text-gray-600"},M={class:"overflow-hidden"},O={class:"px-4 py-5 sm:p-6"},R={class:"group mb-2 flex w-full rounded-md pr-4"},T={class:"flex flex-row flex-wrap rounded-lg border border-white py-px"},q={class:"cursor-text select-text break-normal text-2xl font-normal leading-relaxed text-gray-700"},G={key:0},J={key:1},So={__name:"VerifySyntax",props:{section:Object,tokens:Array,alternates:Array},setup(t){const o=t,_=L(()=>o.section.chapter?o.section.book.work.name+" "+o.section.book.work.l1+" "+o.section.book.book+" "+o.section.book.work.l2+" "+o.section.chapter:o.section.book.work.name+" "+o.section.book.work.l1+" "+o.section.book.book+" Lines "+o.section.line_start+"-"+o.section.line_end),b=[{name:"Read",href:"/read",current:!1},{name:o.section.book.work.name,href:"/read/"+o.section.book.work.author.url+"/"+o.section.book.work.url,current:!1},{name:o.section.book.work.l1+" "+o.section.book.book+": "+o.section.book.subtitle,href:"/read/"+o.section.book.work.author.url+"/"+o.section.book.work.url+"/"+o.section.book.work.l1.toLowerCase()+"-"+o.section.book.book,current:!1},{name:o.section.book.work.l4+"s "+o.section.line_start+"-"+o.section.line_end,href:"/read/"+o.section.book.work.author.url+"/"+o.section.book.work.url+"/"+o.section.book.work.l1.toLowerCase()+"-"+o.section.book.book+"/"+o.section.line_start+"-"+o.section.line_end,current:!1},{name:"Verify Syntax",href:"#",current:!0}];let a=B(!1);S(()=>{o.section.syntax_verified==1&&(a=!0)});const h=n=>o.tokens.filter(m=>m.verse_id==n),w=()=>{axios.post("/api/verify-section-syntax",{id:o.section.id}).then(n=>{n.data.success&&(a=!0,setTimeout(()=>{location.href="/read/"+o.section.book.work.author.url+"/"+o.section.book.work.url+"/"+o.section.book.work.l1.toLowerCase()+"-"+o.section.book.book},1e3))})};return(n,m)=>{const x=$("Head");return s(),k(v,null,{default:p(()=>[d(x,{title:_.value},null,8,["title"]),e("div",N,[d(g,{class:"lg:col-span-9 xl:grid-cols-10",pages:b}),e("div",F,[e("main",H,[e("div",j,[e("div",z,[e("div",D,[e("h1",E,r(t.section.book.work.name)+" "+r(t.section.book.book)+"."+r(t.section.line_start)+"-"+r(t.section.line_end),1),e("h3",I,r(t.section.description),1)])])]),e("div",null,[e("div",M,[e("div",O,[(s(!0),i(f,null,u(t.section.verses,c=>(s(),i("div",{key:c.id,class:"mb-4"},[e("div",R,[e("div",T,[e("div",q,r(c.text),1)])]),(s(!0),i(f,null,u(h(c.id),l=>(s(),k(V,{key:l.id,token:l,alternates:t.alternates.filter(y=>y.token_id===l.id),class:"mb-6"},null,8,["token","alternates"]))),128))]))),128)),e("div",null,[d(C,{color:"primary",size:"lg",class:"mt-8 w-full",onClick:w},{default:p(()=>[A(a)?(s(),i("span",G," Verified ")):(s(),i("span",J," Verify Section "))]),_:1})])])])])])])])]),_:1})}}};export{So as default};
