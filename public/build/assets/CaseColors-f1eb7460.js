import{o as n,d as c,a as l,F as _,h as C,n as h,C as $,e as S,b as y,w as m,W as p,g as b,q as E,u as x,bZ as z,t as T,aw as V,z as v}from"./app-f0078ddb.js";import{S as A}from"./SectionTitle-05f6d081.js";import{_ as L}from"./ButtonItem-718c0517.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"w-80 rounded-full p-2"},D={class:"grid grid-cols-8 gap-2"},F=["onClick"],R={__name:"ColorPicker",props:{tailwindColors:Array,caseName:String,updateColor:Function,selectedColor:String},setup(s){return(f,d)=>(n(),c("div",B,[l("div",D,[d[0]||(d[0]=l("div",{class:"hidden text-amber-600 text-blue-600 text-cyan-600 text-emerald-600 text-fuchsia-600 text-gray-200 text-gray-300 text-gray-400 text-gray-500 text-gray-600 text-gray-700 text-gray-800 text-gray-900 text-green-600 text-indigo-600 text-lime-600 text-orange-600 text-pink-600 text-purple-600 text-rose-600 text-sky-600 text-teal-600 text-violet-600 text-yellow-600"},null,-1)),(n(!0),c(_,null,C(s.tailwindColors,r=>(n(),c("button",{key:r,class:h([r,"h-8 w-8 cursor-pointer rounded-full border border-white",s.selectedColor===r?"ring-2 ring-blue-600":"ring-white"]),onClick:a=>s.updateColor(s.caseName,r)},null,10,F))),128))])]))}};const W={class:"md:grid md:grid-cols-3 md:gap-6"},j={class:"mt-5 px-4 md:col-span-2 md:mt-0"},q={class:"grid grid-cols-2 gap-4 lg:grid-cols-4"},G={class:"ml-2 font-semibold capitalize"},J={__name:"CaseColors",props:{caseColors:Object},setup(s){const f=s,d=["bg-pink-600","bg-rose-600","bg-orange-600","bg-amber-600","bg-yellow-600","bg-lime-600","bg-green-600","bg-emerald-600","bg-teal-600","bg-cyan-600","bg-sky-600","bg-blue-600","bg-indigo-600","bg-violet-600","bg-purple-600","bg-fuchsia-600","bg-gray-200","bg-gray-300","bg-gray-400","bg-gray-500","bg-gray-600","bg-gray-700","bg-gray-800","bg-gray-900"],r=[{id:1,name:"nom",fullName:"Nominative",color:"blue-600"},{id:2,name:"gen",fullName:"Genitive",color:"emerald-600"},{id:3,name:"dat",fullName:"Dative",color:"orange-600"},{id:4,name:"acc",fullName:"Accusative",color:"pink-600"},{id:5,name:"abl",fullName:"Ablative",color:"purple-600"},{id:6,name:"voc",fullName:"Vocative",color:"fuchsia-600"},{id:7,name:"loc",fullName:"Locative",color:"yellow-600"},{id:8,name:"ver",fullName:"Verbs",color:"gray-900"}],a=$(r.reduce((o,e)=>{const t=f.caseColors.find(g=>g.case_id===e.id);return o[e.name]=t?`bg-${t.color}`:`bg-${e.color}`,o},{})),k=()=>{r.forEach(o=>{a[o.name]=`bg-${o.color}`}),p.post("/api/case-colors/reset",{},{preserveScroll:!0})},u=S([]),w=(o,e)=>{const t=r.find(i=>i.name===o),g=e.startsWith("bg-")?e.slice(3):e;a[o]=e,g===t.color?p.post(`/api/case-colors/color/${t.id}/reset`,{},{preserveScroll:!0,onSuccess:()=>{v(()=>{u.value.forEach(i=>i._tippy.hide())})}}):p.post(`/api/case-colors/color/${t.id}`,{color:g},{preserveScroll:!0,onSuccess:()=>{v(()=>{u.value.forEach(i=>i._tippy.hide())})}})},N=o=>{if(typeof document<"u"){const e=document.createElement("div"),t=a[o];return V(R,{tailwindColors:d,caseName:o,updateColor:w,selectedColor:t}).mount(e),e}return"Loading..."};return(o,e)=>(n(),c("div",W,[y(A,null,{title:m(()=>e[1]||(e[1]=[b(" Case Colors ")])),description:m(()=>e[2]||(e[2]=[b(" Customize your reading by selecting case colors from our standard palette. ")])),_:1}),l("div",j,[l("div",q,[(n(),c(_,null,C(r,t=>l("div",{key:t.id,class:"flex items-center gap-2"},[l("div",null,[E(l("button",{ref_for:!0,ref_key:"tooltipRefs",ref:u,class:h([x(a)[t.name],"h-10 w-10 cursor-pointer rounded-full border border-white"])},null,2),[[x(z),{content:()=>N(t.name),allowHTML:!0,interactive:!0,trigger:"click",theme:"rounded",placement:"bottom"}]])]),l("label",G,T(t.fullName),1)])),64))]),y(L,{class:"float-right mt-4 mr-2",color:"white",size:"sm",onClick:e[0]||(e[0]=t=>k())},{default:m(()=>e[3]||(e[3]=[b("Reset")])),_:1})])]))}};export{J as default};
