import{e as u,l as it,p as W,i as nt,o as r,c as w,w as y,b as n,a as e,j as L,u as o,q as P,bP as dt,d as l,h as _,t as i,F as k,n as A,g as C,x as Y,f as c,k as d,R as ct,W as z}from"./app-f0078ddb.js";import{_ as ut}from"./AppLayout-33f062bc.js";import{_ as pt}from"./Breadcrumbs-c96e9207.js";import{_ as mt}from"./DropdownWorks-53c01eb4.js";import{_ as H}from"./ButtonItem-718c0517.js";import{_ as K}from"./ProgressBar-b7203293.js";import O from"./ActivityItem-7b8627ed.js";import ft from"./DropdownBooks-b7db1f80.js";import{P as X}from"./Promotion-3eee0057.js";import{u as gt}from"./useIntersect-6e15125e.js";import{_ as xt}from"./Footer-0988dcd8.js";import{_ as bt}from"./MobileSidebar-5e21b4cd.js";import{r as vt}from"./InformationCircleIcon-716f3ffb.js";import{r as ht}from"./PlusCircleIcon-e71ff64a.js";import{r as J}from"./StarIcon-155a2a28.js";import{r as Q}from"./CheckCircleIcon-d86d1232.js";import{r as S}from"./PlusIcon-fbc40698.js";import{r as Z}from"./XCircleIcon-63af2b2a.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./listbox-f702e976.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./datetime-8ddd27a0.js";const yt={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},_t={class:"p-8"},kt={class:"flex flex-row items-center justify-between"},wt={class:"overflow-visible"},Lt={class:"grid grid-cols-1 gap-4 sm:grid-cols-2"},Ct={class:"order-2 sm:order-1"},Vt={class:"sm:hidden"},$t=["value"],At={class:"hidden sm:block"},Tt={class:"border-b border-gray-200"},Mt={class:"-mb-px flex space-x-8","aria-label":"Tabs"},zt=["onClick","aria-current"],Ht={key:0},jt={class:"mt-8 sm:pr-4"},It={class:"mt-8"},Pt={class:"block text-sm font-medium text-gray-700"},St={class:"mt-8 grid grid-cols-2 gap-8"},Bt={for:"start",class:"block text-sm font-medium text-gray-700"},Nt=["innerHTML"],Et=["innerHTML"],Ut={class:"mt-1"},Ft={for:"last",class:"block text-sm font-medium text-gray-700"},Gt=["innerHTML"],Rt=["innerHTML"],qt={class:"mt-1"},Dt={class:"mt-2 text-xs text-gray-600"},Wt=["innerHTML"],Yt={class:"mx-auto flex items-center"},Kt={key:1},Ot={class:"mt-8"},Xt=["onClick"],Jt={class:"ml-4 flex grow flex-col"},Qt={class:"font-intro text-lg font-bold capitalize leading-5 text-gray-900"},Zt={class:"mt-0.5 line-clamp-2 flex items-center font-sans text-sm font-medium leading-5 text-gray-500"},te={class:"rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-100"},ee=["onClick"],se={class:"ml-4 flex grow flex-col"},oe={class:"font-intro text-lg font-bold capitalize leading-5 text-gray-900"},re={class:"mt-0.5 line-clamp-2 flex items-center font-sans text-sm font-medium leading-5 text-gray-500"},le={class:"rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-100"},ae=["onClick"],ie={class:"ml-4 flex grow flex-col"},ne={class:"font-intro text-lg font-bold capitalize leading-5 text-gray-900"},de={class:"mt-0.5 line-clamp-2 flex items-center font-sans text-sm font-medium leading-5 text-gray-500"},ce={class:"rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-100"},ue={class:"order-1 mb-4 mt-4 sm:order-2 sm:mb-0 sm:pl-4"},pe={key:0,class:"mt-4"},me={key:0,class:"my-2 flex flex-row items-center"},fe=["src"],ge={class:"ml-2 flex grow flex-col"},xe={class:"font-intro text-lg font-bold leading-5 text-gray-900"},be={key:0},ve={key:1},he={class:"mt-1 text-xs text-gray-600"},ye={class:"ml-2 w-5 grow-0"},_e={key:1,class:"my-2 flex flex-row items-center"},ke={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},we={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},Le={class:"ml-2 flex grow flex-col"},Ce={class:"font-intro text-lg font-bold text-gray-900"},Ve={class:"text-xs text-gray-600"},$e={class:"ml-2 w-5 grow-0"},Ae={key:0,class:"mx-auto mb-1 mt-1"},Te={key:1,class:"mx-auto"},Me={key:1},ze={key:1,class:"mt-2"},He={class:"text-center text-xs font-bold uppercase text-gray-500"},je={key:2,class:"mt-8 rounded-lg bg-white p-4 shadow-sm"},Ie={class:"mt-2 grid grid-cols-1 gap-2"},Pe={key:0,class:"mt-2 block flex w-full justify-center rounded-lg border-2 border-dashed border-gray-300 p-12"},Se={key:0,class:"mt-8"},Be={class:"hidden bg-slate-50 p-8 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Ne={key:1,class:"mt-2"},Ee={class:"text-center text-xs font-bold uppercase text-gray-500"},Ue={key:2,class:"mt-8 rounded-lg bg-white p-4 shadow-sm"},Fe={class:"mt-2 grid grid-cols-1 gap-2"},Ge={key:0,class:"mt-2 block flex w-full justify-center rounded-lg border-2 border-dashed border-gray-300 p-12"},Re={key:0},Es={__name:"Index",props:{works:Array,books:Array,sections:Array,items:Array,activities:Array,tab:String,frequencyList:Array,groups:Object,personalVocabLists:Array},setup(p){const m=p,tt=[{name:"Practice",href:"/practice",current:!1},{name:"Vocabulary",href:"#",current:!0}];let g=u([]),B=u(m.books),f=u(m.works[0]),V=u(m.books[0]),b=u(""),v=u(""),N=u(),et=u(),E=u(!1),$=u(m.tab),x=u(!1);const j=u(null);let T=u(m.groups),I=u(T.value.data);const st=it(()=>T.value.next_page_url!==null),ot=()=>{st.value&&axios.get(T.value.next_page_url).then(a=>{I.value=[...I.value,...a.data.data],T.value=a.data})};j!==null&&m.tab=="lists"&&gt(j,ot,{rootMargin:"0px 0px 250px 0px"}),m.items.forEach(a=>{a.vocabList?g.value.push(a.vocabList.id):g.value.push(a.sectionList.book_id+":"+parseInt(a.sectionList.start)+":"+parseInt(a.sectionList.end))});const U=a=>{z.get("/practice/vocabulary",{sections:g.value,tab:a},{replace:!0,preserveScroll:!0})},F=[{name:"Lists",current:m.tab=="lists"||m.tab==null},{name:"Works",current:m.tab=="works"}],G=a=>{let s=a.level.max-a.level.min+1;return(a.xp-a.level.min)/s*100},R=a=>{g.value.splice(a,1),z.get("/practice/vocabulary",{sections:g.value,tab:m.tab},{preserveState:!0,replace:!0,preserveScroll:!0})},rt=a=>{switch(!0){case a=="basic":return"Basic";case a=="intermediate":return"Inter";case a=="advanced":return"Adv";default:return"Basic"}},M=(a=null)=>{a?g.value.push(a):g.value.push(V.value.id+":"+parseInt(b.value)+":"+parseInt(v.value)),b.value="",v.value="",z.get("/practice/vocabulary",{sections:g.value,tab:m.tab},{preserveState:!0,replace:!0,preserveScroll:!0})},q=a=>{let s=a.keyCode?a.keyCode:a.which;(s<48||s>57)&&s!==46&&a.preventDefault()},lt=()=>{E.value=!0,z.get("/practice/vocabulary/attempt",{sections:g.value})};return W(()=>V,()=>{b.value="",v.value=""},{deep:!0}),W(f,a=>{axios.post("/api/practice/vocabulary/get-books",{work:a.id}).then(s=>{B.value=s.data,b.value="",v.value=""})},{deep:!0}),(a,s)=>{const D=nt("Head");return r(),w(ut,null,{default:y(()=>[n(D,null,{default:y(()=>s[10]||(s[10]=[e("title",null,"Practice Vocabulary",-1)])),_:1}),e("main",yt,[e("div",_t,[e("div",kt,[n(pt,{class:"lg:col-span-9 xl:grid-cols-10",pages:tt}),n(o(vt),{class:"ml-2 mt-1 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:s[0]||(s[0]=t=>L(x)?x.value=!o(x):x=!o(x))})]),e("div",wt,[s[20]||(s[20]=e("div",{class:"mt-8 pb-8"},[e("h1",{class:"text-4xl font-bold text-gray-900"}," Customize Your Vocabulary Practice "),e("p",{class:"mt-4 text-base font-normal text-gray-600"}," Practice by selecting words from a text or list. ")],-1)),e("div",Lt,[e("div",Ct,[e("div",Vt,[s[11]||(s[11]=e("label",{for:"tabs",class:"sr-only"},"Select a tab",-1)),P(e("select",{id:"tabs",name:"tabs","onUpdate:modelValue":s[1]||(s[1]=t=>L($)?$.value=t:$=t),onChange:s[2]||(s[2]=t=>U(o($).toLowerCase())),class:"block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm"},[(r(),l(k,null,_(F,t=>e("option",{key:t.name,value:t.name.toLowerCase()},i(t.name),9,$t)),64))],544),[[dt,o($)]])]),e("div",At,[e("div",Tt,[e("nav",Mt,[(r(),l(k,null,_(F,t=>e("div",{key:t.name,onClick:h=>U(t.name.toLowerCase()),class:A([t.current?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700","cursor-pointer whitespace-nowrap border-b-2 px-1 py-4 text-sm font-bold"]),"aria-current":t.current?"page":void 0},i(t.name),11,zt)),64))])])]),p.tab=="works"?(r(),l("div",Ht,[e("div",jt,[n(mt,{list:p.works,"onUpdate:work":s[3]||(s[3]=t=>L(f)?f.value=t:f=t)},null,8,["list"]),e("div",It,[e("p",Pt," Select the "+i(o(f).l1),1),n(ft,{items:o(B),"onUpdate:selected":s[4]||(s[4]=t=>L(V)?V.value=t:V=t)},null,8,["items"])]),e("div",St,[e("div",null,[e("label",Bt,[s[12]||(s[12]=C("First ")),o(f).l2?(r(),l("span",{key:0,innerHTML:o(f).l2},null,8,Nt)):(r(),l("span",{key:1,innerHTML:o(f).l4},null,8,Et))]),e("div",Ut,[P(e("input",{id:"start","onUpdate:modelValue":s[5]||(s[5]=t=>L(b)?b.value=t:b=t),type:"text",name:"start",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 101",onKeypress:q},null,544),[[Y,o(b)]])])]),e("div",null,[e("label",Ft,[s[13]||(s[13]=C("Last ")),o(f).l2?(r(),l("span",{key:0,innerHTML:o(f).l2},null,8,Gt)):(r(),l("span",{key:1,innerHTML:o(f).l4},null,8,Rt))]),e("div",qt,[P(e("input",{id:"last","onUpdate:modelValue":s[6]||(s[6]=t=>L(v)?v.value=t:v=t),type:"text",name:"last",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 157",onKeypress:q},null,544),[[Y,o(v)]])])])]),e("p",Dt," Leaving the first or last blank will default to the start or end of this "+i(o(f).l1.toLowerCase())+". ",1),o(N)?(r(),l("p",{key:0,class:"mt-2 text-xs text-red-600",innerHTML:o(N)},null,8,Wt)):c("",!0),n(H,{class:"float-right mb-8 mt-8 flex w-full px-4 text-center",color:"gray",size:"sm",disabled:o(et),onClick:s[7]||(s[7]=t=>M())},{default:y(()=>[e("span",Yt,[s[14]||(s[14]=C("Add to List ")),n(o(ht),{class:"ml-2 inline-block h-5 w-5 self-center"})])]),_:1},8,["disabled"])])])):c("",!0),p.tab=="lists"?(r(),l("div",Kt,[e("div",Ot,[o(d)().props.authenticated?(r(!0),l(k,{key:0},_(p.personalVocabLists,t=>(r(),l("div",null,[t.count>0?(r(),l("div",{class:"duration-250 group mb-4 flex cursor-pointer flex-row items-center rounded-lg bg-white px-2 py-4 transition ease-in-out hover:bg-gray-50",key:t.id,onClick:h=>M(t.id)},[e("div",{class:A(["flex h-12 w-12 items-center justify-center rounded-full p-2",t.id=="star"?"bg-green-100 text-green-600":"bg-blue-100 text-blue-600"])},[t.id=="star"?(r(),w(o(J),{key:0,class:"h-10 w-10 stroke-2 text-green-700"})):(r(),w(o(Q),{key:1,class:"h-10 w-10 stroke-2 text-blue-700"}))],2),e("div",Jt,[e("h3",Qt,i(t.name),1),e("p",Zt,i(t.count)+" words ",1)]),e("div",te,[n(o(S),{class:"h-6 w-6 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])],8,Xt)):c("",!0)]))),256)):c("",!0),(r(!0),l(k,null,_(p.frequencyList,t=>(r(),l("div",{class:"duration-250 group mb-4 flex cursor-pointer flex-row items-center rounded-lg bg-white px-2 py-4 transition ease-in-out hover:bg-gray-50",key:t.list,onClick:h=>M(t.abbreviation)},[e("div",{class:A(["text-large flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro font-bold",t.abbreviation=="basic"?"bg-emerald-300 text-emerald-700":t.abbreviation=="intermediate"?"bg-blue-300 text-blue-700":"bg-orange-300 text-orange-700"])},i(rt(t.abbreviation)),3),e("div",se,[e("h3",oe,i(t.list),1),e("p",re,i(t.count)+" words ",1)]),e("div",le,[n(o(S),{class:"h-6 w-6 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])],8,ee))),128)),(r(!0),l(k,null,_(o(I),t=>(r(),l("div",{class:"duration-250 group mb-4 flex cursor-pointer flex-row items-center rounded-lg bg-white px-2 py-4 transition ease-in-out hover:bg-gray-50",key:t.id,onClick:h=>M("group-"+t.id)},[s[15]||(s[15]=e("div",{class:"flex h-12 w-12 items-center justify-center rounded-full bg-slate-300 p-2 font-intro text-sm font-bold text-slate-700"}," Group ",-1)),e("div",ie,[e("h3",ne,i(t.name),1),e("p",de,i(t.count)+" words ",1)]),e("div",ce,[n(o(S),{class:"h-6 w-6 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])],8,ae))),128))]),e("div",{ref_key:"landmark",ref:j},null,512)])):c("",!0)]),e("div",ue,[s[19]||(s[19]=e("h3",{class:"text-sm font-bold text-gray-700"},"Current List",-1)),p.items.length>0?(r(),l("div",pe,[n(ct,{"enter-active-class":"transition ease-out duration-250","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-250","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:y(()=>[(r(!0),l(k,null,_(p.items,(t,h)=>(r(),l("div",{key:h,class:A(["min-h-20 w-full rounded-xl border border-gray-300 px-4 py-2 shadow-md",h>0?"mt-4":"mt-2"])},[t.sectionList?(r(),l("div",me,[e("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.sectionList.image_name,class:"h-12 w-12"},null,8,fe),e("div",ge,[e("h4",xe,[C(i(t.sectionList.work)+" "+i(t.sectionList.l1)+" "+i(t.sectionList.book)+".",1),t.sectionList.start===t.sectionList.end?(r(),l("span",be,i(t.sectionList.start),1)):(r(),l("span",ve,i(t.sectionList.start)+"-"+i(t.sectionList.end),1))]),e("p",he,i(t.word_count)+" words | "+i(t.core_count)+" core, "+i(t.word_count-t.core_count)+" not core ",1)]),e("div",ye,[n(o(Z),{class:"duration-250 h-6 w-6 cursor-pointer stroke-2 text-gray-400 transition hover:text-red-600",onClick:at=>R(h)},null,8,["onClick"])])])):c("",!0),t.vocabList?(r(),l("div",_e,[s[16]||(s[16]=e("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),t.vocabList.id=="star"?(r(),l("div",ke,[n(o(J),{class:"h-8 w-8 stroke-2"})])):t.vocabList.id=="learn"?(r(),l("div",we,[n(o(Q),{class:"h-8 w-8 stroke-2"})])):(r(),l("div",{key:2,class:A(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${t.vocabList.icon_color}-300 text-${t.vocabList.icon_color}-700`])},i(t.vocabList.abbreviation),3)),e("div",Le,[e("h4",Ce,i(t.vocabList.name),1),e("p",Ve,i(t.word_count)+" words | "+i(t.core_count)+" core, "+i(t.word_count-t.core_count)+" not core ",1)]),e("div",$e,[n(o(Z),{class:"duration-250 h-6 w-6 cursor-pointer stroke-2 text-gray-400 transition hover:text-red-600",onClick:at=>R(h)},null,8,["onClick"])])])):c("",!0)],2))),128))]),_:1}),n(H,{class:"mt-8 w-full",color:"blue",size:"md",onClick:s[8]||(s[8]=t=>lt())},{default:y(()=>[o(E)?(r(),l("span",Ae,s[17]||(s[17]=[e("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",{class:"ml-2"},"Preparing ...",-1)]))):(r(),l("span",Te,"Continue"))]),_:1})])):(r(),l("div",Me,s[18]||(s[18]=[e("div",{class:"relative mt-4 block h-20 w-full rounded-lg border-2 border-dashed border-gray-300 py-5 text-center"},[e("span",{class:"mt-2 block text-sm font-medium text-gray-600"}," No Sections Added ")],-1)])))])])])]),n(xt)]),n(bt,{class:"lg:hidden",show:o(x),onClose:s[9]||(s[9]=t=>L(x)?x.value=!1:x=!1)},{default:y(()=>[e("div",null,[o(d)().props.authenticated?(r(),w(K,{key:0,class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+o(d)().props.user.level.level,"post-text":o(d)().props.user.xp+" XP",progress:G(o(d)().props.user)},null,8,["pre-text","post-text","progress"])):c("",!0),o(d)().props.authenticated?(r(),l("div",ze,[e("p",He,i(o(d)().props.user.level.max-o(d)().props.user.xp+1)+" xp to the next level ",1)])):c("",!0),o(d)().props.authenticated?(r(),l("div",je,[s[23]||(s[23]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"}," Recent Vocabulary Activities ",-1)),e("div",Ie,[(r(!0),l(k,null,_(p.activities,t=>(r(),w(O,{key:t.id,activity:t},null,8,["activity"]))),128)),p.activities.length==0?(r(),l("div",Pe,s[21]||(s[21]=[e("h5",{class:"text-sm font-semibold text-gray-900"}," No Activities Yet ",-1)]))):c("",!0)]),n(H,{class:"mt-8 w-full",color:"gray",size:"md",link:a.route("practice.activities.index")},{default:y(()=>s[22]||(s[22]=[C("View All Activities")])),_:1},8,["link"])])):c("",!0)]),o(d)().props.authenticated?c("",!0):(r(),l("div",Se,[n(X)]))]),_:1},8,["show"]),e("aside",Be,[e("div",null,[o(d)().props.authenticated?(r(),w(K,{key:0,class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+o(d)().props.user.level.level,"post-text":o(d)().props.user.xp+" XP",progress:G(o(d)().props.user)},null,8,["pre-text","post-text","progress"])):c("",!0),o(d)().props.authenticated?(r(),l("div",Ne,[e("p",Ee,i(o(d)().props.user.level.max-o(d)().props.user.xp+1)+" xp to the next level ",1)])):c("",!0),o(d)().props.authenticated?(r(),l("div",Ue,[s[26]||(s[26]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"}," Recent Vocabulary Activities ",-1)),e("div",Fe,[(r(!0),l(k,null,_(p.activities,t=>(r(),w(O,{key:t.id,activity:t},null,8,["activity"]))),128)),p.activities.length==0?(r(),l("div",Ge,s[24]||(s[24]=[e("h5",{class:"text-sm font-semibold text-gray-900"}," No Activities Yet ",-1)]))):c("",!0)]),n(H,{class:"mt-8 w-full",color:"gray",size:"md",link:a.route("practice.activities.index")},{default:y(()=>s[25]||(s[25]=[C("View All Activities")])),_:1},8,["link"])])):c("",!0)]),o(d)().props.authenticated?c("",!0):(r(),l("div",Re,[n(X)]))])]),_:1})}}};export{Es as default};
