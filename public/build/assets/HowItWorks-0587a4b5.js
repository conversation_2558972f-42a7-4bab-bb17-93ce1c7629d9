import{_ as a}from"./AppLayout-33f062bc.js";import{_ as n}from"./Breadcrumbs-c96e9207.js";import{i as l,o as m,c as d,w as i,b as s,a as e}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";/* empty css            */const c={class:"relative isolate bg-white pb-16"},p={class:"p-8"},u={class:"flex flex-row items-center justify-between"},J={__name:"HowItWorks",setup(x){const r=[{name:"Infinitas",href:"/infinitas",current:!1},{name:"How It Works",href:"/infinitas/how-it-works",current:!0}];return(f,t)=>{const o=l("Head");return m(),d(a,null,{default:i(()=>[s(o,null,{default:i(()=>t[0]||(t[0]=[e("title",null,"Infinitas | How It Works",-1)])),_:1}),e("div",c,[t[3]||(t[3]=e("div",{class:"absolute inset-x-0 -top-3 -z-10 mt-32 transform-gpu overflow-hidden px-36 blur-3xl sm:mt-56","aria-hidden":"true"},[e("div",{class:"mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-indigo-400 to-sky-200 opacity-30",style:{"clip-path":`polygon(
              74.1% 44.1%,
              100% 61.6%,
              97.5% 26.9%,
              85.5% 0.1%,
              80.7% 2%,
              72.5% 32.5%,
              60.2% 62.4%,
              52.4% 68.1%,
              47.5% 58.3%,
              45.2% 34.5%,
              27.5% 76.7%,
              0.1% 64.9%,
              17.9% 100%,
              27.6% 76.8%,
              76.1% 97.7%,
              74.1% 44.1%
            )`}})],-1)),e("div",p,[e("div",u,[s(n,{class:"lg:col-span-9 xl:grid-cols-10",pages:r})]),t[1]||(t[1]=e("div",{class:"py-24 sm:py-32"},[e("div",{class:"mx-auto"},[e("div",{class:"mx-auto max-w-2xl lg:mx-0"},[e("p",{class:"text-base/7 font-semibold text-indigo-600"}," How it works "),e("h2",{class:"mt-2 text-5xl font-semibold tracking-tight text-gray-900 sm:text-7xl"}," Infinitas "),e("p",{class:"mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"}," Infinitas provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")])])],-1)),t[2]||(t[2]=e("div",{class:"mt-16 grid grid-cols-1 gap-8 lg:grid-cols-5"},[e("div",{class:"max-w-2xl min-w-0 flex-auto lg:col-span-3 lg:max-w-none lg:pr-0"},[e("header",{class:"mb-9 space-y-1"},[e("p",{class:"font-display text-sm font-semibold text-sky-600"}," Introduction "),e("h1",{class:"font-display text-3xl tracking-tight text-slate-900 dark:text-white"}," Personalized Vocabulary Practice ")]),e("article",null,[e("h3",{class:"mb-2 text-xl"},"Initial Learning"),e("p",{class:"mb-8 text-base"}," You begin with a manageable set of approximately 20 Latin words. "),e("h3",{class:"mb-2 text-xl"},"Mastery Criteria"),e("p",{class:"mb-8 text-base"}," A word is considered learned when you’ve correctly recalled it five more times than you’ve missed it. Once this criterion is met, the word is “banked” for future review, and a new word is introduced into your practice set. ")]),e("article",null,[e("h3",{class:"mb-2 text-xl"},"Spaced Repetition Scheduling"),e("p",{class:"mb-8 text-base"}," Infinitas employs a tiered system to determine when you’ll review each word, leveraging the spacing effect to optimize long-term retention. The spacing effect is a phenomenon where information is more easily recalled when study sessions are spaced out over time, rather than crammed in a short period. "),e("ul",{class:"mb-8 list-inside list-disc space-y-4"},[e("li",null," Tier 1: Newly learned words enter at this level. You’ll review these words the next day. "),e("li",null," Tier 2: If recalled correctly, the word moves here, and you’ll review it one week later. "),e("li",null," Tier 3: A subsequent correct recall advances the word to this tier, scheduling it for review 16 days later. "),e("li",null," Mastered: A final correct recall places the word in the mastered list, where it will appear once every 30 days for reinforcement. ")]),e("p",{class:"mb-8 text-base"}," If you miss a word at any tier, it returns to Tier 1, ensuring focused attention on words that need more practice. ")])]),e("aside",{class:"order-1 -mt-4 lg:order-2 lg:col-span-2",id:"sticky"},[e("div",{class:"sticky top-20 rounded-xl bg-white p-2 lg:p-4"},"blah")])],-1))])])]),_:1})}}};export{J as default};
