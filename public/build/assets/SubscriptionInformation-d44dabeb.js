import{S as g}from"./SectionTitle-05f6d081.js";import{_ as T}from"./ButtonItem-718c0517.js";import{D as n}from"./datetime-8ddd27a0.js";import{e as u,o as s,d as t,b as y,w as c,a as r,n as S,u as i,g as a,t as d,f as h}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css            */const v={class:"md:grid md:grid-cols-3 md:gap-6"},x={class:"mt-5 md:col-span-2 md:mt-0"},L={key:0,class:"text-lg font-bold text-gray-900"},w={key:0},_={class:"capitalize"},E={key:1,class:"capitalize"},Y={key:1,class:"text-lg font-bold text-white"},D={key:2,class:"text-lg font-bold text-white"},I={key:0},M={key:1},N={key:2},A={class:"font-bold"},C={key:3},z={key:4},V={class:"mt-5"},B={key:0},F={key:1},O={key:2},P={key:3},H={__name:"SubscriptionInformation",props:{user:Object,trialEndsAt:String},setup(e){const l=e;var p=u(l.user.membership.onTrial),m=u(l.user.membership.subscribed),f=u(l.user.membership.individualSubscribed),k=u(l.user.membership.teamSubscribed);return(b,o)=>(s(),t("div",v,[y(g,null,{title:c(()=>o[0]||(o[0]=[a(" Subscription Status ")])),description:c(()=>o[1]||(o[1]=[a(" Gain full access to all the content on LatinTutorial and support this site. ")])),_:1}),r("div",x,[r("div",{class:S(["rounded-xl px-4 py-5 sm:p-6",i(m)?"bg-slate-100":i(p)?"bg-linear-to-r from-sky-500 to-cyan-400":"bg-linear-to-r from-pink-500 to-rose-500"])},[i(m)?(s(),t("h3",L,[o[4]||(o[4]=a(" You are subscribed to ")),i(f)?(s(),t("span",w,[o[2]||(o[2]=a(" the ")),r("span",_,d(e.user.membership.plan),1),o[3]||(o[3]=a(" Plan."))])):h("",!0),i(k)?(s(),t("span",E," Class Pro.")):h("",!0)])):i(p)?(s(),t("h3",Y," You are on your 7 day free trial. ")):(s(),t("h3",D," You are not subscribed to a plan on LatinTutorial. ")),r("div",{class:S(["mt-3 max-w-xl text-sm font-medium",i(m)?"text-gray-600":"text-white"])},[e.user.membership.ends_at?(s(),t("p",I," Your subscription will end on "+d(i(n).fromISO(e.user.membership.subscription_ends_at).toLocaleString(i(n).DATETIME_FULL))+". ",1)):h("",!0),i(m)&&!e.user.membership.ends_at&&i(f)?(s(),t("p",M," Member since "+d(i(n).fromISO(e.user.membership.created_at).toLocaleString(i(n).DATETIME_FULL))+". ",1)):e.user.membership.onTrial?(s(),t("p",N,[o[5]||(o[5]=a(" Your trial will end on ")),r("span",A,d(i(n).fromSQL(e.trialEndsAt).toLocaleString(i(n).DATETIME_FULL)),1),o[6]||(o[6]=a(". Please subscribe to get full access to this site. "))])):e.user.membership.teamSubscribed?(s(),t("p",C," Your classroom subscription is active while you are a member of a subscribed classroom. ")):(s(),t("p",z," Your access to the content on LatinTutorial is limited until you subscribe. "))],2),r("div",V,[r("div",null,[y(T,{size:"sm",type:"button",link:e.user.membership.individualSubscribed?b.route("edit-subscription"):e.user.membership.teamSubscribed?b.route("classes"):b.route("subscribe"),color:e.user.membership.subscribed?"black":"white"},{default:c(()=>[e.user.membership.individualSubscribed?(s(),t("span",B," Manage Subscription ")):e.user.membership.teamSubscribed?(s(),t("span",F," Manage Classroom ")):e.user.membership.onTrial?(s(),t("span",O," Subscribe Now ")):(s(),t("span",P," Subscribe Now "))]),_:1},8,["link","color"])])])],2)])]))}};export{H as default};
