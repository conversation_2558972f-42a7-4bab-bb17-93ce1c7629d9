import{C as i,B as u,L as m,d as y,a as f,b as x,P as h,c as b}from"./chart-183f17ad.js";import{e as l,p as g,A,o as C,d as v,a as k}from"./app-f0078ddb.js";/* empty css            */const D={__name:"ExperienceChart",props:{labels:{type:Array,required:!0},data:{type:Array,required:!0},data2:{type:Array,required:!1}},setup(d){const a=d,s=l(null);let t;const o=Math.max(...a.data2)<=10?10:void 0,c=Math.max(...a.data)<=10?10:void 0,e={labels:a.labels,datasets:[{label:"experience",data:a.data,backgroundColor:"#34d399",borderColor:"#34d399",borderWidth:1,pointRadius:3},{label:"activities",type:"bar",data:a.data2,backgroundColor:"#7dd3fc"}]},r=l({plugins:{title:{display:!1}},responsive:!0,maintainAspectRatio:!1,scales:{x:{display:!1},y1:{type:"linear",position:"left",ticks:{beginAtZero:!0},min:0,max:c,grace:"5% 5%"},y2:{type:"linear",position:"right",ticks:{beginAtZero:!0},min:0,max:o,grace:"5% 5%"}}});e.datasets[0].yAxisID="y1",e.datasets[1].yAxisID="y2";const n=()=>{t&&t.destroy(),t=new i(s.value,{type:"line",data:e,options:r.value})};return g(()=>[a.labels,a.data],p=>{e.labels=a.labels,e.datasets[0].data=a.data,a.data2&&(e.datasets[1].data=a.data2),r.value.scales.y1.max=Math.max(...a.data)<=10?10:void 0,r.value.scales.y2.max=Math.max(...a.data2)<=10?10:void 0,n()},{deep:!0}),A(()=>{n()}),i.register(u,m,y,f,x,h,b),(p,_)=>(C(),v("div",null,[k("canvas",{ref_key:"chartRef",ref:s,id:"my-chart-id",height:"160"},null,512)]))}};export{D as default};
