import m from"./ApiTokenManager-b953f1b0.js";import{_ as s}from"./AppLayout-33f062bc.js";import{o as p,c as e,w as r,a as i,b as a}from"./app-f0078ddb.js";import"./ActionMessage-0a1272b7.js";import"./DialogModal-b2ffd0f0.js";import"./SectionTitle-05f6d081.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./Checkbox-85576c2c.js";import"./FormSection-45b6b921.js";import"./InputError-7edb5cf8.js";import"./InputLabel-3b7f7747.js";import"./PrimaryButton-f16ada05.js";import"./SectionBorder-b49c1148.js";import"./TextInput-940981ae.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */const l={class:"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8"},Y={__name:"Index",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(t){return(n,o)=>(p(),e(s,{title:"API Tokens"},{header:r(()=>o[0]||(o[0]=[i("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," API Tokens ",-1)])),default:r(()=>[i("div",null,[i("div",l,[a(m,{tokens:t.tokens,"available-permissions":t.availablePermissions,"default-permissions":t.defaultPermissions},null,8,["tokens","available-permissions","default-permissions"])])])]),_:1}))}};export{Y as default};
