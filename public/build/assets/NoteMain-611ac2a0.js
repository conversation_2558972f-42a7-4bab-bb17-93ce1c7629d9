import{_ as m}from"./RenderToken-2b0a5644.js";import h from"./NoteItem-3ad59666.js";import{l as c,o as i,d as u,h as d,F as x,c as f,w as p,b as k,n as v}from"./app-f0078ddb.js";import"./removePunctuation-702d8a66.js";import"./lodash-631955d9.js";/* empty css            */const w={__name:"NoteMain",props:{verses:Array,tokens:Array,highlight:String,caseColor:Boolean,selectingText:<PERSON>olean,hasProse:<PERSON>olean,sectionId:Number,verseName:String},emits:["update:highlighted","update:is-editing"],setup(t,{emit:l}){const o=t,g=c(()=>{var n=[];return o.verses.forEach((e,s)=>{e.prose==1&&o.verses.length>1?n.push(e):s==0?n[0]=e:n.push(e)}),n}),a=l;return(n,e)=>(i(!0),u(x,null,d(g.value,s=>(i(),f(m,{key:s.id,verse:s,tokens:t.tokens,highlighted:t.highlight,"case-color":t.caseColor,"selecting-text":t.selectingText,"show-all-line-numbers":!0,"has-prose":t.hasProse,"onUpdate:highlighted":e[1]||(e[1]=r=>a("update:highlighted",r)),class:v(["mt-2 text-gray-900 font-intro break-normal text-lg font-medium leading-normal text-gray-900 sm:text-2xl lg:text-2xl",[t.selectingText?"cursor-text select-text":"cursor-pointer select-none"]])},{default:p(()=>[k(h,{note:s.notes.length>0?s.notes[0].note:null,verse:s.id,"verse-name":t.verseName,onIsEditing:e[0]||(e[0]=r=>a("update:is-editing",r))},null,8,["note","verse","verse-name"])]),_:2},1032,["verse","tokens","highlighted","case-color","selecting-text","has-prose","class"]))),128))}};export{w as default};
