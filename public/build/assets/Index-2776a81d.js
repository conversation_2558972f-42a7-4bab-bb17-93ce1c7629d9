import{e as f,l as T,p as y,i as z,o as n,c as $,w as S,b as p,a as e,u as l,j as v,d,h as C,F as A,k as u,t as x,q as b,v as h,g as I,f as E,W as L}from"./app-f0078ddb.js";import{_ as G}from"./AppLayout-33f062bc.js";import{_ as H}from"./Breadcrumbs-c96e9207.js";import W from"./ActivityItem-7fcf9e15.js";import{u as X}from"./useIntersect-6e15125e.js";import{_ as F}from"./DropdownGeneral-ce7a4558.js";import{_ as N}from"./ProgressBar-b7203293.js";import{_ as q}from"./Footer-0988dcd8.js";import{_ as R}from"./MobileSidebar-5e21b4cd.js";import{r as J}from"./InformationCircleIcon-716f3ffb.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./datetime-8ddd27a0.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";const K={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},Q={class:"py-8 px-4 sm:px-8"},Y={class:"flex flex-row justify-between items-center"},Z={class:"mt-8"},ee={class:"grid grid-cols-1 divide-y"},te={"aria-labelledby":"progress"},se={class:"mt-2"},le={class:"text-xs font-bold text-gray-500 text-center uppercase"},re={"aria-labelledby":"sort activities",class:"pt-8"},ae={"aria-labelledby":"filter activities"},oe={class:"pt-8"},ie={class:"flex text-sm"},ne={class:"flex-1 text-right"},de={class:"grid grid-cols-1 divide-y"},ce={class:"ml-4 grid grid-cols-2 gap-3 py-4"},ue={class:"relative flex items-center"},me={class:"flex h-5 items-center"},pe={class:"relative flex items-center"},ve={class:"flex h-5 items-center"},xe={"aria-labelledby":"userStats",class:"mt-8"},ge={class:"grid grid-cols-1 gap-px bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2 rounded-lg overflow-hidden"},fe={class:"text-sm font-medium leading-6 text-gray-500"},_e={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},ye={key:0},be={key:0,class:"ml-1 text-sm font-medium text-gray-500"},he={key:1},we={class:"hidden lg:inline-block bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},ke={"aria-labelledby":"progress"},Ve={class:"mt-2"},Se={class:"text-xs font-bold text-gray-500 text-center uppercase"},Ce={"aria-labelledby":"sort activities",class:"pt-8"},Ae={"aria-labelledby":"filter activities"},Le={class:"pt-8"},je={class:"flex text-sm"},Me={class:"flex-1 text-right"},Ue={class:"grid grid-cols-1 divide-y"},$e={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Ie={class:"relative flex items-center"},Ee={class:"flex h-5 items-center"},Fe={class:"relative flex items-center"},Ne={class:"flex h-5 items-center"},Pe={"aria-labelledby":"userStats",class:"mt-8"},Be={class:"grid grid-cols-1 gap-px bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2 shadow-sm rounded-lg overflow-hidden"},Oe={class:"text-sm font-medium leading-6 text-gray-500"},De={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},Te={key:0},ze={key:0,class:"ml-1 text-sm font-medium text-gray-500"},Ge={key:1},Et={__name:"Index",props:{activities:Object,sort:String,stats:Object,filters:Array},setup(P){let B=[{name:"Practice",href:"/practice",current:!1},{name:"Activities",href:"#",current:!0}];const o=P,w=f(null);let c=f(o.activities),m=f(!1),_=f(c.value.data);const O=T(()=>c.value.next_page_url!==null);w!==null&&X(w,()=>{O.value&&axios.get(c.value.next_page_url).then(r=>{_.value=[..._.value,...r.data.data],c.value=r.data})},{rootMargin:"0px 0px 250px 0px"});const j=()=>{L.get("/practice/activities",{sort:i.value.value},{only:["activities","stats","sort","filters"],onSuccess:()=>{k(),o.sort&&(i.value=g.find(r=>r.value==o.sort))},preserveScroll:!0})};let a=f(o.filters),g=[{name:"Newest",value:"newest"},{name:"Oldest",value:"oldest"}];const D=r=>g.find(t=>t.value==r);let i=f(o.sort?D(o.sort):g[0]);const M=r=>{let t=r.level.max-r.level.min+1;return(r.xp-r.level.min)/t*100};y(()=>o.sort,()=>{o.sort&&(i.value=g.find(r=>r.value==o.sort))},{deep:!0}),y(()=>o.activities,r=>{c.value=r,_.value=c.value.data},{deep:!0}),y(()=>i,r=>{L.get("/practice/activities",{sort:r.value.value,filters:a.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["activities","stats","filters","sort"],onSuccess:()=>{k()}})},{deep:!0});const k=()=>{c.value=o.activities,_.value=c.value.data,V.value=[{name:"Activities",value:c.value.total},{name:"Accuracy",value:`${Math.round(o.stats.average_accuracy*100)}%`}]};y(()=>a,()=>{L.get("/practice/activities",{filters:a.value,sort:i.value.value},{only:["activities","stats","filters","sort"],replace:!0,onSuccess:()=>{k()},preserveState:!0,preserveScroll:!0})},{deep:!0}),y(()=>o.filters,()=>{o.sort&&(i.value=g.find(r=>r.value==o.sort))});let V=f([{name:"Activities",value:c.value.total},{name:"Accuracy",value:`${Math.round(o.stats.average_accuracy*100)}%`}]);return(r,t)=>{const U=z("Head");return n(),$(G,null,{default:S(()=>[p(U,null,{default:S(()=>t[10]||(t[10]=[e("title",null,"Practice Activities",-1)])),_:1}),e("main",K,[e("div",Q,[e("div",Y,[p(H,{class:"lg:col-span-9 xl:grid-cols-10",pages:l(B)},null,8,["pages"]),p(l(J),{class:"lg:hidden w-6 h-6 mt-1 ml-2 transition duration-150 ease-in-out cursor-pointer stroke-2 stroke-current hover:text-slate-500 text-slate-400",onClick:t[0]||(t[0]=s=>v(m)?m.value=!l(m):m=!l(m))})]),t[11]||(t[11]=e("section",{class:"mt-8 w-full","aria-labelledby":"Description"},[e("h1",{class:"text-4xl text-gray-900 font-bold"},"Activities"),e("p",{class:"text-base font-normal text-gray-600 mt-4"}," View all of your activities on LatinTutorial. ")],-1)),e("section",Z,[e("div",ee,[(n(!0),d(A,null,C(l(_),s=>(n(),$(W,{key:s.id,activity:s},null,8,["activity"]))),128))]),e("div",{ref_key:"landmark",ref:w},null,512)])]),p(q)]),p(R,{class:"lg:hidden",show:l(m),onClose:t[5]||(t[5]=s=>v(m)?m.value=!1:m=!1)},{default:S(()=>[e("section",te,[p(N,{class:"mt-8",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+l(u)().props.user.level.level,"post-text":l(u)().props.user.xp+" XP",progress:M(l(u)().props.user)},null,8,["pre-text","post-text","progress"]),e("div",se,[e("p",le,x(l(u)().props.user.level.max-l(u)().props.user.xp+1)+" xp to the next level ",1)])]),e("section",re,[p(F,{modelValue:l(i),"onUpdate:modelValue":t[1]||(t[1]=s=>v(i)?i.value=s:i=s),title:"Sort",list:l(g),current:l(i)},null,8,["modelValue","list","current"])]),e("section",ae,[e("div",oe,[e("div",ie,[t[12]||(t[12]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold uppercase text-gray-500"},"Filters")],-1)),e("div",ne,[e("button",{class:"font-bold uppercase text-blue-500 opacity-100",onClick:t[2]||(t[2]=s=>j())}," Clear ")])]),e("div",de,[e("div",ce,[e("div",ue,[e("div",me,[b(e("input",{id:"vocabulary","onUpdate:modelValue":t[3]||(t[3]=s=>v(a)?a.value=s:a=s),name:"vocabulary",type:"checkbox",value:"vocabulary",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[h,l(a)]])]),t[13]||(t[13]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"vocabulary",class:"font-medium text-gray-700"},"Vocabulary ")],-1))]),e("div",pe,[e("div",ve,[b(e("input",{id:"grammar","onUpdate:modelValue":t[4]||(t[4]=s=>v(a)?a.value=s:a=s),name:"grammar",type:"checkbox",value:"grammar",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[h,l(a)]])]),t[14]||(t[14]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"grammar",class:"font-medium text-gray-700"},"Grammar ")],-1))])])])])]),e("section",xe,[e("div",null,[t[15]||(t[15]=e("div",{class:"flex-1 text-left mb-2"},[e("h4",{class:"font-bold uppercase text-gray-500 text-sm"},"Stats")],-1)),e("dl",ge,[(n(!0),d(A,null,C(l(V),s=>(n(),d("div",{key:s.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[e("dt",fe,x(s.name),1),e("dd",_e,[s.value?(n(),d("span",ye,[I(x(s.value)+" ",1),s.unit?(n(),d("span",be,x(s.unit),1)):E("",!0)])):(n(),d("span",he,"–"))])]))),128))])])])]),_:1},8,["show"]),e("aside",we,[e("section",ke,[p(N,{class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+l(u)().props.user.level.level,"post-text":l(u)().props.user.xp+" XP",progress:M(l(u)().props.user)},null,8,["pre-text","post-text","progress"]),e("div",Ve,[e("p",Se,x(l(u)().props.user.level.max-l(u)().props.user.xp+1)+" xp to the next level ",1)])]),e("section",Ce,[p(F,{modelValue:l(i),"onUpdate:modelValue":t[6]||(t[6]=s=>v(i)?i.value=s:i=s),title:"Sort",list:l(g),current:l(i)},null,8,["modelValue","list","current"])]),e("section",Ae,[e("div",Le,[e("div",je,[t[16]||(t[16]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold uppercase text-gray-500"},"Filters")],-1)),e("div",Me,[e("button",{class:"font-bold uppercase text-blue-500 opacity-100",onClick:t[7]||(t[7]=s=>j())}," Clear ")])]),e("div",Ue,[e("div",$e,[e("div",Ie,[e("div",Ee,[b(e("input",{id:"vocabulary","onUpdate:modelValue":t[8]||(t[8]=s=>v(a)?a.value=s:a=s),name:"vocabulary",type:"checkbox",value:"vocabulary",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[h,l(a)]])]),t[17]||(t[17]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"vocabulary",class:"font-medium text-gray-700"},"Vocabulary ")],-1))]),e("div",Fe,[e("div",Ne,[b(e("input",{id:"grammar","onUpdate:modelValue":t[9]||(t[9]=s=>v(a)?a.value=s:a=s),name:"grammar",type:"checkbox",value:"grammar",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[h,l(a)]])]),t[18]||(t[18]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"grammar",class:"font-medium text-gray-700"},"Grammar ")],-1))])])])])]),e("section",Pe,[e("div",null,[t[19]||(t[19]=e("div",{class:"flex-1 text-left mb-2"},[e("h4",{class:"font-bold uppercase text-gray-500 text-sm"},"Stats")],-1)),e("dl",Be,[(n(!0),d(A,null,C(l(V),s=>(n(),d("div",{key:s.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[e("dt",Oe,x(s.name),1),e("dd",De,[s.value?(n(),d("span",Te,[I(x(s.value)+" ",1),s.unit?(n(),d("span",ze,x(s.unit),1)):E("",!0)])):(n(),d("span",Ge,"–"))])]))),128))])])])])]),_:1})}}};export{Et as default};
