import{D as I,S as N,f as C,g as b,h as w}from"./SortableEvent-286d5fef.js";const d=Symbol("onDragStart"),l=Symbol("onDragOverContainer"),c=Symbol("onDragOver"),h=Symbol("onDragStop");function E({dragEvent:n}){const t=n.source.textContent.trim()||n.source.id||"sortable element";if(n.over){const e=n.over.textContent.trim()||n.over.id||"sortable element";return n.source.compareDocumentPosition(n.over)&Node.DOCUMENT_POSITION_FOLLOWING?`Placed ${t} after ${e}`:`Placed ${t} before ${e}`}else return`Placed ${t} into a different container`}const D={"sortable:sorted":E};class P extends I{constructor(t=[],e={}){super(t,{...e,announcements:{...D,...e.announcements||{}}}),this.startIndex=null,this.startContainer=null,this[d]=this[d].bind(this),this[l]=this[l].bind(this),this[c]=this[c].bind(this),this[h]=this[h].bind(this),this.on("drag:start",this[d]).on("drag:over:container",this[l]).on("drag:over",this[c]).on("drag:stop",this[h])}destroy(){super.destroy(),this.off("drag:start",this[d]).off("drag:over:container",this[l]).off("drag:over",this[c]).off("drag:stop",this[h])}index(t){return this.getSortableElementsForContainer(t.parentNode).indexOf(t)}getSortableElementsForContainer(t){return[...t.querySelectorAll(this.options.draggable)].filter(r=>r!==this.originalSource&&r!==this.mirror&&r.parentNode===t)}[d](t){this.startContainer=t.source.parentNode,this.startIndex=this.index(t.source);const e=new N({dragEvent:t,startIndex:this.startIndex,startContainer:this.startContainer});this.trigger(e),e.canceled()&&t.cancel()}[l](t){if(t.canceled())return;const{source:e,over:r,overContainer:o}=t,i=this.index(e),s=new C({dragEvent:t,currentIndex:i,source:e,over:r});if(this.trigger(s),s.canceled())return;const g=this.getSortableElementsForContainer(o),a=m({source:e,over:r,overContainer:o,children:g});if(!a)return;const{oldContainer:u,newContainer:f}=a,S=this.index(t.source),p=new b({dragEvent:t,oldIndex:i,newIndex:S,oldContainer:u,newContainer:f});this.trigger(p)}[c](t){if(t.over===t.originalSource||t.over===t.source)return;const{source:e,over:r,overContainer:o}=t,i=this.index(e),s=new C({dragEvent:t,currentIndex:i,source:e,over:r});if(this.trigger(s),s.canceled())return;const g=this.getDraggableElementsForContainer(o),a=m({source:e,over:r,overContainer:o,children:g});if(!a)return;const{oldContainer:u,newContainer:f}=a,S=this.index(e),p=new b({dragEvent:t,oldIndex:i,newIndex:S,oldContainer:u,newContainer:f});this.trigger(p)}[h](t){const e=new w({dragEvent:t,oldIndex:this.startIndex,newIndex:this.index(t.source),oldContainer:this.startContainer,newContainer:t.source.parentNode});this.trigger(e),this.startIndex=null,this.startContainer=null}}function x(n){return Array.prototype.indexOf.call(n.parentNode.children,n)}function m({source:n,over:t,overContainer:e,children:r}){const o=!r.length,i=n.parentNode!==e,s=t&&n.parentNode===t.parentNode;return o?O(n,e):s?v(n,t):i?y(n,t,e):null}function O(n,t){const e=n.parentNode;return t.appendChild(n),{oldContainer:e,newContainer:t}}function v(n,t){const e=x(n),r=x(t);return e<r?n.parentNode.insertBefore(n,t.nextElementSibling):n.parentNode.insertBefore(n,t),{oldContainer:n.parentNode,newContainer:n.parentNode}}function y(n,t,e){const r=n.parentNode;return t?t.parentNode.insertBefore(n,t):e.appendChild(n),{oldContainer:r,newContainer:n.parentNode}}export{P as S};
