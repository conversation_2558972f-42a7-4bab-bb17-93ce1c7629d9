import{D as g}from"./datetime-8ddd27a0.js";import{r as v}from"./CheckIcon-6a201aa1.js";import{r as C}from"./ArrowPathIcon-f74cb8d6.js";import{e as L,p as b,o as s,d as a,a as t,n as f,t as i,b as p,u as m,F as N}from"./app-f0078ddb.js";const D={class:"flex flex-row"},F={class:"grow"},S={class:"ml-4"},j={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-emerald-300"},A={key:1,class:"flex h-12 w-12 grow-0 items-center justify-center rounded-full bg-amber-300"},B={key:0},I={class:"grow"},O={class:"font-bold text-emerald-600"},T={key:1},V={class:"grow"},z={key:0,class:"font-bold text-amber-600"},E={key:1,class:"font-bold text-amber-600"},H={__name:"AssignmentModule",props:{assignment:Object,wordsSeen:Number,wordsCorrect:Number,time:Number,accuracy:Number},emits:["completed"],setup(e,{emit:y}){const o=e,h=y,c=L(o.assignment);return b(()=>o.assignment,n=>{c.value=n},{deep:!0}),b([()=>o.time,()=>o.wordsCorrect,()=>o.accuracy],([n,r,x])=>{const{time:d,correct:l,accuracy:u}=o.assignment.targets,_=d==null||n>=d,w=l==null||r>=l,k=u==null||x>=u;_&&w&&k&&c.value.completed!=1&&axios.post("/api/mark-assignment-completed",{assignmentId:c.value.assignment_completion_id,completed:!0}).then(()=>{h("completed")})}),(n,r)=>(s(),a(N,null,[r[0]||(r[0]=t("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Assignment",-1)),t("div",{class:f(["mt-2 w-full items-center rounded-lg border-2 bg-amber-100 p-4 text-sm font-medium text-gray-800",e.assignment.completed===1?"border-emerald-300 bg-emerald-100":"border-amber-300 bg-amber-100"])},[t("div",D,[t("p",F,[t("span",{class:f(["line-clamp-2 font-medium",e.assignment.completed===1?"text-emerald-600":"text-amber-600"])},i(e.assignment.description),3)]),t("div",S,[e.assignment.completed===1?(s(),a("div",j,[p(m(v),{class:"h-8 w-8 stroke-2 text-emerald-600"})])):(s(),a("div",A,[p(m(C),{class:"h-8 w-8 text-amber-600"})]))])]),e.assignment.completed===1?(s(),a("div",B,[t("p",I,[t("span",O," Completed on "+i(m(g).fromISO(e.assignment.completed_at).toFormat("LLLL d, yyyy 'at' h:mm a")),1)])])):(s(),a("div",T,[t("p",V,[e.assignment.due_at?(s(),a("span",z," Due on "+i(m(g).fromISO(e.assignment.due_at).toFormat("LLLL d, yyyy 'at' h:mm a")),1)):(s(),a("span",E," No due date."))])]))],2)],64))}};export{H as _};
