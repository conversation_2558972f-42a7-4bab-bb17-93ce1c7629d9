import{p as i}from"./pluralize-d25a928b.js";import{D as n}from"./datetime-8ddd27a0.js";import _ from"./AssignmentItemOwner-5f5b8c19.js";import f from"./AssignmentItemStudent-2daa0e28.js";import{_ as v}from"./ButtonItem-718c0517.js";import{l as h,u,o as e,d as s,a as m,t as d,F as o,h as p,c as r,f as c,b as x,w as I}from"./app-f0078ddb.js";const S={key:0,class:"pb-6"},w={key:0,class:"mt-6"},D={class:"mb-2 text-sm font-extrabold uppercase text-gray-400"},A={key:1,class:"mt-6"},T={class:"mb-2 text-sm font-extrabold uppercase text-gray-400"},B={key:2,class:"mt-6"},V={class:"mb-2 text-sm font-extrabold uppercase text-gray-400"},F={key:3,class:"mt-6"},N={class:"mb-2 text-sm font-extrabold uppercase text-gray-400"},$={class:"mt-8 text-center"},z={key:0},C={key:1},K={__name:"Assignments",props:{assignments:Object},setup(y){const l=y.assignments.assignments.filter(a=>n.fromISO(a.due_at).startOf("day").toISO()>=n.now().startOf("day").toISO()),g=h(()=>l.slice(0,2).filter(a=>n.fromISO(a.due_at).startOf("day").toISO()===n.now().startOf("day").toISO())),O=h(()=>l.slice(0,2).filter(a=>n.fromISO(a.due_at).startOf("day").toISO()===n.now().plus({days:1}).startOf("day").toISO())),k=h(()=>l.slice(0,2).filter(a=>n.fromISO(a.due_at).startOf("day").toISO()===n.now().plus({days:2}).startOf("day").toISO())),b=h(()=>l.slice(0,2).filter(a=>n.fromISO(a.due_at).startOf("day").toISO()>n.now().plus({days:2}).toISO()));return(a,E)=>u(l).length>0?(e(),s("div",S,[g.value.length>0?(e(),s("div",w,[m("h5",D,d(u(i)("assignment",g.value.count))+" Due Today ",1),(e(!0),s(o,null,p(g.value,t=>(e(),s(o,{key:t.id},[t.is_student?(e(),r(f,{key:0,assignment:t,dashboard:!0},null,8,["assignment"])):(e(),r(_,{key:1,assignment:t,dashboard:!0,team:t.team,students:t.students},null,8,["assignment","team","students"]))],64))),128))])):c("",!0),O.value.length>0?(e(),s("div",A,[m("h5",T,d(u(i)("assignment",O.value.count))+" Due Tomorrow ",1),(e(!0),s(o,null,p(O.value,t=>(e(),s(o,{key:t.id},[t.is_student?(e(),r(f,{key:0,assignment:t,dashboard:!0},null,8,["assignment"])):(e(),r(_,{key:1,assignment:t,dashboard:!0,team:t.team,students:t.students},null,8,["assignment","team","students"]))],64))),128))])):c("",!0),k.value.length>0?(e(),s("div",B,[m("h5",V,d(u(i)("assignment",k.value.count))+" Due "+d(u(n).now().plus({days:2}).toFormat("cccc")),1),(e(!0),s(o,null,p(k.value,t=>(e(),s(o,{key:t.id},[t.is_student?(e(),r(f,{key:0,assignment:t,dashboard:!0},null,8,["assignment"])):(e(),r(_,{key:1,assignment:t,dashboard:!0,team:t.team,students:t.students},null,8,["assignment","team","students"]))],64))),128))])):c("",!0),b.value.length>0?(e(),s("div",F,[m("h5",N," Upcoming "+d(u(i)("assignment",b.value.count)),1),(e(!0),s(o,null,p(b.value,t=>(e(),s(o,{key:t.id},[t.is_student?(e(),r(f,{key:0,assignment:t,dashboard:!0},null,8,["assignment"])):(e(),r(_,{key:1,assignment:t,dashboard:!0,team:t.team,students:t.students},null,8,["assignment","team","students"]))],64))),128))])):c("",!0),m("div",$,[x(v,{color:"white",size:"sm",link:a.route("assignments.index"),class:"px-6 text-sm font-semibold text-gray-500 transition duration-300 hover:text-gray-600"},{default:I(()=>[y.assignments.count>2?(e(),s("span",z,d(y.assignments.count-2)+" more "+d(u(i)("assignment",y.assignments.count-2)),1)):(e(),s("span",C,"View all assignments"))]),_:1},8,["link"])])])):c("",!0)}};export{K as _};
