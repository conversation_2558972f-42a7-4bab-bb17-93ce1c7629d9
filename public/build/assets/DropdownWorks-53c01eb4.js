import{e as _,p as k,o as r,c as x,w as o,b as n,g as y,u as e,a as t,d as i,t as c,h as v,n as m,f as V,F as B,T as j,j as A}from"./app-f0078ddb.js";import{E as D,j as N,A as F,F as C,I as E}from"./listbox-f702e976.js";import{r as H}from"./ChevronUpDownIcon-aff937c8.js";import{r as I}from"./CheckIcon-4bbdc2ab.js";const M={class:"relative mt-2"},T={key:0,class:"flex items-center"},W={key:1,class:"flex items-center"},z=["src"],G={class:"ml-3 block truncate"},S={class:"pointer-events-none absolute inset-y-0 right-0 ml-3 flex items-center pr-2"},q={class:"flex items-center"},L=["src"],J={__name:"DropdownWorks",props:{list:Array,current:Object},emits:["update:work"],setup(d,{emit:w}){const f=d;let s=_(f.current||f.list[0]);const b=w,p=u=>u?"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+u:"https://images.unsplash.com/photo-1633783156075-a01661455344?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=3264&q=80";return k(()=>s.value,()=>{b("update:work",s.value)}),(u,l)=>(r(),x(e(E),{modelValue:e(s),"onUpdate:modelValue":l[0]||(l[0]=a=>A(s)?s.value=a:s=a),as:"div"},{default:o(()=>[n(e(D),{class:"block text-sm font-medium text-gray-700"},{default:o(()=>l[1]||(l[1]=[y(" Select the Work ")])),_:1}),t("div",M,[n(e(N),{class:"relative w-full cursor-default rounded-lg border border-gray-300 bg-white py-2 pr-10 pl-3 text-left shadow-xs focus:border-indigo-500 focus:ring-1 focus:ring-indigo-500 focus:outline-hidden sm:text-sm"},{default:o(()=>[d.list.length==0?(r(),i("span",T,l[2]||(l[2]=[t("span",{class:"h-6 w-6 shrink-0 rounded-full bg-indigo-600"},null,-1),t("span",{class:"ml-3 block truncate"},"No Data Available",-1)]))):(r(),i("span",W,[t("img",{src:p(e(s).image_name),alt:"",class:"h-6 w-6 shrink-0 rounded-full"},null,8,z),t("span",G,c(e(s).name)+" | "+c(e(s).author),1)])),t("span",S,[n(e(H),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),n(j,{"leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:o(()=>[n(e(F),{class:"absolute z-10 mt-1 max-h-56 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg focus:outline-hidden sm:text-sm"},{default:o(()=>[(r(!0),i(B,null,v(d.list,a=>(r(),x(e(C),{key:a.id,as:"template",value:a},{default:o(({active:h,selected:g})=>[t("li",{class:m([h?"bg-indigo-600 text-white":"text-gray-900","relative cursor-default py-2 pr-9 pl-3 select-none"])},[t("div",q,[t("img",{src:p(a.image_name),alt:"",class:"h-6 w-6 shrink-0 rounded-full"},null,8,L),t("span",{class:m([g?"font-semibold":"font-normal","ml-3 block truncate"])},c(a.name)+" | "+c(a.author),3)]),g?(r(),i("span",{key:0,class:m([h?"text-white":"text-indigo-600","absolute inset-y-0 right-0 flex items-center pr-4"])},[n(e(I),{class:"h-5 w-5","aria-hidden":"true"})],2)):V("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]))}};export{J as _};
