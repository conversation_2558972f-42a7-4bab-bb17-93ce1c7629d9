import{e as u,p as V,A as D,B as H,o as p,d as m,a as i,F as B,h as E,u as g,b as _,w as y,R,n as x,j,t as F,g as b}from"./app-f0078ddb.js";import{_ as k}from"./ButtonItem-718c0517.js";import{r as G}from"./replaceMacra-3b9666ed.js";import{_ as h}from"./lodash-631955d9.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css            */const O={class:"flex-1"},q=["innerHTML"],J={class:"relative mt-12 grid w-full touch-manipulation grid-cols-1 justify-items-center px-16 select-none"},Q={class:"h-24 w-full"},W={class:"align-end mx-auto flex"},X=["id","onClick","onFocus"],Y={class:"mx-auto self-center"},Z={class:"h-24 w-full"},ee=["onClick"],te={class:"grid grid-cols-1 gap-4 sm:grid-cols-3 sm:gap-8"},se={__name:"Type",props:{options:String,stem:String,difficulty:Number},emits:["submit"],setup(C,{emit:N}){let d=["A","C","D","E","F","G","H","I","L","M","N","O","P","R","S","T","U","V"];const f=C;let a=u([]),o=u([]);u(!1),u(!1);let s=u(0),c=u(!1);const S=N,L=e=>{const t=[];for(let l=0;l<e.length;l++)t.push({id:l,data:G(e[l]),used:!1});return t};let v=u("");switch(f.difficulty){case 1:d=h.shuffle(d),v.value=d.join("").substring(0,1);break;case 2:d=h.shuffle(d),v.value=d.join("").substring(0,3);break}v.value=v.value+f.options,a.value=L(h.shuffle(v.value.split(""))),o.value=new Array(f.options.length),V(()=>f.options,()=>{a.value=[],a.value=L(h.shuffle(f.options.split(""))),o=new Array(a.length),s.value=0,$nextTick(()=>{document.addEventListener("keydown",w)})}),D(()=>{document.addEventListener("keydown",w)}),H(()=>{document.removeEventListener("keydown",w)});const $=(e,t)=>{s.value=t,e.target.select(),c.value=!0},w=e=>{e.ctrlKey||e.metaKey||e.altKey||(e.preventDefault(),c.value||(e.which<=90&&e.which>=48&&I(e.key),e.which==8&&z(),e.which==13&&U()),e.which==37&&A(s.value),e.which==39&&T(s.value))},T=e=>{e<o.value.length-1&&(s.value=e+1)},A=e=>{e>0&&(s.value=e-1),e==0&&(s.value=0)},M=()=>{a.value=h.shuffle(a.value)},I=(e,t)=>{if(a.value.findIndex(n=>n.used==!1&&n.data==e.toUpperCase())>=0){if(o.value[s]){let n=a.value.findIndex(r=>r.used==!0&&r.data==o[s].toUpperCase());a.value[n].used=!1}if(o.value.splice(s.value,1,e),t)var l=t;else var l=a.value.findIndex(r=>r.used==!1&&r.data==e.toUpperCase());a.value[l].used=!0,T(s.value)}},z=()=>{if(o.value[s.value]||s.value!=0&&s.value--,o.value[s.value]){var e=o.value[s.value],t=a.value.slice().reverse().findIndex(l=>l.used==!0&&l.data.toUpperCase()==e.toUpperCase());t=a.value.length-t-1,a.value[t].used=!1,o.value.splice(s.value,1,"")}},K=()=>{o.value=new Array(o.value.length),a.value.forEach(e=>{e.used=!1}),s.value=0},U=()=>{document.removeEventListener("keydown",w);let e=o.value.map(t=>t).join("").toUpperCase();S("submit",{answer:e})};return(e,t)=>(p(),m("div",O,[i("h1",{class:"mt-4 mb-4 text-center text-2xl font-semibold text-gray-900",innerHTML:C.stem},null,8,q),t[6]||(t[6]=i("h3",{class:"text-center text-lg font-medium text-gray-600"}," Use the letters below to spell the word. ",-1)),i("div",J,[i("div",Q,[i("div",W,[(p(!0),m(B,null,E(g(o),(l,n)=>(p(),m("div",{key:n,class:x(["mx-1 inline-block",{"rounded-lg ring-2 ring-teal-500 ring-offset-4":g(s)==n}])},[i("div",{id:"input"+n,ref_for:!0,ref:"input",type:"text",maxlength:"1",class:x(["items-auto flex h-12 border-t-0 border-r-0 border-b border-l-0 border-gray-700 p-1 text-center uppercase transition duration-150 ease-in-out focus:border-teal-500 focus:ring-white focus:outline-hidden",[g(o).length<9?"w-10 text-3xl":"w-8 text-2xl"]]),onClick:r=>j(s)?s.value=n:s=n,onFocus:r=>$(r,n),onBlur:t[0]||(t[0]=r=>j(c)?c.value=!1:c=!1)},[i("p",Y,F(g(o)[n]),1)],42,X)],2))),128))])]),t[2]||(t[2]=i("div",{class:"my-8 w-full border border-gray-400"},null,-1)),i("div",Z,[_(R,{name:"letter-list",tag:"p",class:"grid auto-cols-max grid-flow-col justify-center"},{default:y(()=>[(p(!0),m(B,null,E(g(a),(l,n)=>(p(),m("span",{key:l.id,class:x(["mx-2 inline-block cursor-pointer text-4xl font-bold text-indigo-700 uppercase",[l.used?"opacity-50":"opacity-100"]]),onClick:r=>I(l.data,n)},F(l.data),11,ee))),128))]),_:1})])]),i("div",te,[_(k,{size:"lg",class:"w-full",color:"indigo",onClick:M},{default:y(()=>t[3]||(t[3]=[b("Shuffle")])),_:1}),_(k,{size:"lg",class:"w-full",color:"white",onClick:K},{default:y(()=>t[4]||(t[4]=[b("Clear")])),_:1}),_(k,{size:"lg",class:"w-full",color:"pink",onClick:t[1]||(t[1]=l=>U())},{default:y(()=>t[5]||(t[5]=[b("Submit")])),_:1})])]))}},ue=P(se,[["__scopeId","data-v-20f815e0"]]);export{ue as default};
