import{_ as N,a as R,r as F}from"./AppLayout-33f062bc.js";import{_ as h}from"./ButtonItem-718c0517.js";import{_ as T}from"./ProgressBar-b7203293.js";import{l as y,e as U,i as A,o as l,c as p,w as o,k as u,b as i,a as t,d,t as a,u as c,F as w,h as _,n as V,G as D,g as f,f as v,E as $,j as Z}from"./app-f0078ddb.js";import{_ as M}from"./Assignments-a7efae90.js";import Y from"./ActivityItem-7b8627ed.js";import K from"./ExperienceChart-ef2b8bd4.js";import{D as S}from"./datetime-8ddd27a0.js";import{_ as X}from"./Footer-0988dcd8.js";import{I as L}from"./InfinitasIcon-1a3ae135.js";/* empty css                                                              */import{r as C}from"./RectangleGroupIcon-04390470.js";import{r as q}from"./CalendarDaysIcon-19bddb32.js";import{r as J}from"./ArrowPathIcon-f74cb8d6.js";import{r as Q}from"./PlayCircleIcon-8bd12a30.js";import{r as tt}from"./BookOpenIcon-1746d343.js";import{r as et}from"./QueueListIcon-824b634b.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */import"./pluralize-d25a928b.js";import"./AssignmentItemOwner-5f5b8c19.js";import"./ClassroomModal-05d10768.js";import"./UtilityDropdown-2d786282.js";import"./CopyToClipboard-21badf5d.js";import"./clipboard-a66b13b3.js";import"./AssignModule-74fae4e9.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";import"./Combobox-f427c07d.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./AssignReading-df4942fb.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./DropdownBooks-b7db1f80.js";import"./RectangleGroupIcon-c6b3f31f.js";import"./AssignmentItemStudent-2daa0e28.js";import"./CheckBadgeIcon-e7792a17.js";import"./chart-183f17ad.js";const st={class:"flex flex-col lg:flex-row"},lt={class:"order-2 flex w-full pb-8 lg:order-1 lg:pr-96 2xl:pr-[32rem]"},ot={class:"flex w-full flex-col gap-8 px-8 pb-8"},it={key:0},rt={class:"mt-8 mb-8 font-intro text-3xl font-bold text-gray-900"},nt={class:"ml-6 flex-auto"},at={class:"text-lg font-medium text-gray-700"},dt={class:"text-base font-medium text-gray-500"},ct={"aria-labelledby":"infinite-vocab",class:"mt-16"},mt={class:"flex flex-col items-center gap-4 sm:flex-row sm:gap-8"},ut={class:"flex grow flex-col gap-2"},ft={key:0},xt={key:1},gt={key:0},pt={key:1,"aria-labelledby":"where-you-left-off"},ht={class:"mt-14 mb-14 flex flex-col justify-end gap-8 sm:flex-row sm:justify-start lg:mt-8 lg:mb-0 lg:pl-0"},vt={class:"mt-2 font-intro font-sans text-4xl font-bold sm:mt-8"},bt={class:"mt-4 text-base font-medium text-slate-600"},yt={key:0},wt={key:1,class:"text-gray-400"},_t={class:"mt-8 flex flex-col gap-4 sm:flex-row"},kt={class:"order-first ml-auto w-full flex-none space-y-8 sm:order-last sm:w-44 xl:order-none"},Vt={class:"relative hidden sm:inline-block"},$t=["src"],St={class:"relative sm:hidden"},zt=["src"],jt={"aria-labelledby":"infinite-vocab",class:"mt-16"},Ht={class:"pb-16"},At={class:"flex flex-col items-center gap-4 sm:flex-row sm:gap-8"},Dt={class:"grid grid-cols-2 gap-4"},Mt={key:0,class:"flex flex-col gap-4"},Lt={class:"w-full rounded-2xl px-4"},Ct={class:"flex h-4 w-full overflow-hidden rounded-full bg-gray-200"},It={class:"mt-2 grid grid-cols-2"},Et={class:"text-2xl text-orange-600"},Pt={class:"text-2xl text-lime-600"},Ot={class:"mt-4 text-2xl text-sky-600"},Wt={class:"mt-4 text-2xl text-indigo-600"},Gt={class:"float-right mt-8 flex w-full flex-col gap-4 sm:w-auto sm:flex-row"},Bt={"aria-labelledby":"activities-and-experience",class:"mt-16"},Nt={class:"grid h-full w-full grid-cols-1 items-stretch gap-8 md:grid-cols-2 lg:grid-cols-1 2xl:grid-cols-2"},Rt={class:"flex items-end"},Ft={key:0,class:"mt-4 w-full rounded-2xl bg-slate-100 p-3"},Tt={key:1,class:"mt-4 flex h-[216px] items-center justify-center rounded-xl bg-slate-100 hover:bg-slate-200"},Ut={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-amber-500"},Zt={class:"relative mt-4 flex h-[216px] w-full flex-col rounded-2xl bg-slate-800 p-3"},Yt={class:"grow"},Kt={key:1,class:"absolute inset-0 flex w-full items-center p-4 opacity-100 transition duration-250 ease-in-out"},Xt={class:"mt-2 flex flex-row justify-evenly px-4 text-xs font-semibold"},qt=["onClick"],Jt={"aria-labelledby":"personal-recommendations",class:"mt-16"},Qt={class:"mt-8 grid grid-cols-1 gap-8"},te={class:"mt-1 px-4 text-center font-intro text-sm leading-tight font-medium text-purple-700"},ee={class:"mt-1 text-center font-intro text-sm leading-tight font-medium text-indigo-700"},se={"aria-labelledby":"personal-recommendations",class:"mt-16"},le={class:"mt-8 flex flex-col gap-8 md:flex-row lg:flex-col 2xl:flex-row"},oe={class:"flex grow items-center justify-between gap-4"},ie={class:"mx-auto mt-6 px-8 py-2 text-center font-intro text-2xl font-bold text-gray-900"},re={"aria-labelledby":"personal-recommendations",class:"mt-16"},ne={class:"mt-8 grid grid-cols-1 gap-16 md:gap-8 2xl:grid-cols-2"},ae={class:"aspect-h-3 aspect-w-4"},de={class:"relative"},ce=["src"],me={class:"flex flex-1 flex-col justify-between bg-white font-intro"},ue={class:"mt-2 mb-1 text-lg font-bold text-gray-900 sm:mt-4 sm:text-xl lg:text-xl 2xl:mt-2"},fe={class:"line-clamp-3 text-sm leading-5 text-gray-600 sm:text-base lg:font-medium"},xe={"aria-labelledby":"personal-recommendations",class:"mt-16"},ge={class:"mt-8 flex flex-col gap-4 md:flex-row lg:flex-col 2xl:flex-row"},pe=["src"],he={class:"mt-2 flex w-full flex-1 flex-col md:mt-0 md:w-1/2 lg:mt-2 lg:w-full 2xl:mt-0"},ve={class:"font-intro text-lg leading-tight font-bold text-gray-900 transition duration-150 ease-in-out hover:text-gray-700 sm:text-xl lg:text-xl"},be={class:"mt-1 flex"},ye={key:0,class:"text-xs font-bold text-green-500 uppercase sm:text-sm"},we={key:1,class:"text-xs font-bold text-indigo-500 uppercase sm:text-sm"},_e={key:2,class:"text-xs font-bold text-indigo-500 uppercase sm:text-sm"},ke={class:"mt-2 font-intro text-sm leading-5 font-medium text-gray-600 sm:text-base 2xl:text-sm"},Ve={class:"mt-12 grid grid-cols-1 gap-8"},$e={class:"grid grid-cols-1 gap-4 2xl:grid-cols-2"},Se={class:"line-clamp-3 text-center text-2xl font-bold text-white md:text-3xl"},ze={class:"text-center text-2xl font-bold text-gray-900 md:text-3xl"},je={class:"order-1 bg-white p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:order-2 lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 lg:bg-slate-50 2xl:w-[32rem]"},He={"aria-labelledby":"user-information"},Ae={class:"flex flex-col items-center sm:flex-row sm:gap-8 lg:flex-col lg:gap-0"},De=["src","alt"],Me={class:"grow"},Le={class:"text-center font-intro text-4xl font-bold sm:text-left lg:text-center"},Ce={class:"grid hidden grid-cols-1 md:inline-block lg:hidden"},Ie={class:"mt-12 grid w-full grid-cols-1 gap-4 sm:mx-auto sm:w-2/3 sm:w-full sm:grid-cols-2 md:hidden lg:inline-block lg:grid-cols-1 2xl:grid-cols-2"},Ee={class:"mt-12 border-t border-gray-300 pt-4"},Pe={class:"flex flex-row space-x-4 overflow-x-auto py-4"},Oe={class:"mt-2"},We={class:"text-center text-xs font-bold text-gray-500 uppercase"},Ge={class:"mt-8 text-center text-sm font-semibold text-gray-500"},Be={key:0,"aria-labelledby":"pinnedWorks",class:"mt-12 space-y-4 border-t border-b border-gray-300 pt-8 pb-8 lg:border-b-0 lg:pb-0"},Ne={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1"},Re={class:"group -ml-2 w-full rounded-lg py-4 pl-2 hover:bg-slate-100 lg:hover:bg-white"},Fe=["src"],Te={class:"ml-4 flex grow flex-col"},Ue={class:"flex flex-row"},Ze={class:"inline-block font-intro text-lg leading-5 font-bold dark:text-white"},Ye={class:"mt-1 line-clamp-2 font-intro text-sm font-semibold text-gray-500 dark:text-gray-300"},Ke="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='152' height='152' viewBox='0 0 152 152'%3E%3Cg fill-rule='evenodd'%3E%3Cg id='temple' fill='%23000000' fill-opacity='0.05'%3E%3Cpath d='M152 150v2H0v-2h28v-8H8v-20H0v-2h8V80h42v20h20v42H30v8h90v-8H80v-42h20V80h42v40h8V30h-8v40h-42V50H80V8h40V0h2v8h20v20h8V0h2v150zm-2 0v-28h-8v20h-20v8h28zM82 30v18h18V30H82zm20 18h20v20h18V30h-20V10H82v18h20v20zm0 2v18h18V50h-18zm20-22h18V10h-18v18zm-54 92v-18H50v18h18zm-20-18H28V82H10v38h20v20h38v-18H48v-20zm0-2V82H30v18h18zm-20 22H10v18h18v-18zm54 0v18h38v-20h20V82h-18v20h-20v20H82zm18-20H82v18h18v-18zm2-2h18V82h-18v18zm20 40v-18h18v18h-18zM30 0h-2v8H8v20H0v2h8v40h42V50h20V8H30V0zm20 48h18V30H50v18zm18-20H48v20H28v20H10V30h20V10h38v18zM30 50h18v18H30V50zm-2-40H10v18h18V10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E",sl={__name:"Index",props:{achievements:Array,activities:Array,assignments:Object,recentSection:Object,video:Object,experienceData:Object,tag:Object,grammarActivity:Object,activitiesCompleted:Object,randomWorks:Array,pinnedWorks:Array,latestVideo:Object,reviewActivity:Object,dailyActivity:Object,wordGroup:Object,user_stats:Array},setup(s){const n=s,I=x=>{let e=x.level.max-x.level.min+1;return(x.xp-x.level.min)/e*100},E=y(()=>n.recentSection.chapter?`/read/${n.recentSection.book.work.author.url}/${n.recentSection.book.work.url}/${n.recentSection.book.url}/${n.recentSection.book.work.l2.toLowerCase()}-${n.recentSection.chapter}/${n.recentSection.line_start}-${n.recentSection.line_end}`:`/read/${n.recentSection.book.work.author.url}/${n.recentSection.book.work.url}/${n.recentSection.book.url}/${n.recentSection.line_start}-${n.recentSection.line_end}`),P=y(()=>n.recentSection.chapter?n.recentSection.book_id+":"+n.recentSection.chapter+":"+n.recentSection.chapter:n.recentSection.book_id+":"+n.recentSection.line_start+":"+n.recentSection.line_end),O=y(()=>!n.recentSection&&u().props.user.xp==0&&n.activities.length==0);y(()=>{const x=S.fromISO(u().props.user.membership.created_at),g=S.local().diff(x,["years","months","days"]);return g.years>=1?Math.floor(g.years):g.months>=1?Math.floor(g.months):Math.floor(g.days)}),y(()=>{const x=S.fromISO(u().props.user.membership.created_at),g=S.local().diff(x,["years","months","days"]);return g.years>=1?"years":g.months>=1?"months":"days"});const W=[{name:"Dashboard",description:"Your launching pad for all things on LatinTutorial",url:"/dashboard",icon:F,color:"bg-indigo-500"},{name:"Videos",description:"Find videos on grammar, vocabulary, and more",url:"/watch",icon:Q,color:"bg-pink-500"},{name:"Read",description:"Put your skills of reading Latin to use with different works",url:"/read",icon:tt,color:"bg-sky-500"},{name:"Practice",description:"Keep your Latin skills sharp with vocabulary and grammar exercises",url:"/practice",icon:C,color:"bg-amber-500"},{name:"Words",description:"Search through the list of Latin words on this site",url:"/words",icon:et,color:"bg-teal-500"}];let b=U(n.experienceData.length-1);const G=x=>S.fromFormat(x,"yyyy-MM-dd").toFormat("LLL"),z=x=>{const e=n.user_stats.find(g=>g.interval===x);return e?e.total:0},j=x=>{const e=n.user_stats.find(g=>g.interval===x);return e?`${e.total/B.value*100}%`:"0%"},B=y(()=>{if(n.user_stats)return n.user_stats.reduce((x,e)=>x+e.total,0)});return(x,e)=>{const g=A("Head"),m=A("Link");return l(),p(N,null,{default:o(()=>[i(g,{title:"Dashboard"}),t("div",st,[t("main",lt,[t("div",ot,[e[39]||(e[39]=t("div",{class:"relative -z-10"},[t("svg",{class:"absolute inset-x-0 top-0 -z-10 h-[64rem] w-full stroke-gray-200 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]","aria-hidden":"true"},[t("defs",null,[t("pattern",{id:"1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84",width:"200",height:"200",x:"50%",y:"-1",patternUnits:"userSpaceOnUse"},[t("path",{d:"M.5 200V.5H200",fill:"none"})])]),t("svg",{x:"50%",y:"-1",class:"overflow-visible fill-gray-50"},[t("path",{d:"M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z","stroke-width":"0"})]),t("rect",{width:"100%",height:"100%","stroke-width":"0",fill:"url(#1f932ae7-37de-4c0a-a8b0-a6e3b4d44b84)"})])],-1)),O.value?(l(),d("section",it,[t("h2",rt," Welcome to LatinTutorial, "+a(c(u)().props.user.name)+"! ",1),e[4]||(e[4]=t("h4",{class:"mt-8 mb-4 font-sans text-sm font-bold text-gray-600 uppercase"}," Let's get started ",-1)),(l(),d(w,null,_(W,r=>t("div",{key:r.id},[i(m,{class:"flex cursor-pointer flex-row rounded-xl p-3 select-none hover:bg-slate-100",href:r.url},{default:o(()=>[t("div",{class:V(["flex h-12 w-12 flex-none items-center justify-center rounded-lg",r.color])},[(l(),p(D(r.icon),{class:"h-8 w-8 text-white","aria-hidden":"true"}))],2),t("div",nt,[t("p",at,a(r.name),1),t("p",dt,a(r.description)+". ",1)])]),_:2},1032,["href"])])),64)),t("section",ct,[t("div",mt,[t("div",null,[i(L,{class:"inline w-32"})]),t("div",ut,[e[1]||(e[1]=t("h1",{class:"inline text-center text-4xl font-bold text-gray-900 sm:text-left"}," Infinitas ",-1)),e[2]||(e[2]=t("h5",{class:"-mt-1 text-sm font-bold text-slate-500 uppercase"}," Grow your vocabulary ",-1)),e[3]||(e[3]=t("p",{class:"mt-2 inline text-center text-base font-medium text-slate-600 sm:text-left"}," Infinitas provides you with an endless stream of vocabulary words, tailored to you and your needs. ",-1)),i(h,{color:"indigo",size:"sm",class:"mt-4 w-full self-end sm:w-56",link:"/infinitas"},{default:o(()=>e[0]||(e[0]=[f(" Infinite Practice ")])),_:1})])])]),s.assignments?(l(),d("section",ft,[i(M,{assignments:s.assignments},null,8,["assignments"])])):v("",!0)])):(l(),d("div",xt,[s.assignments?(l(),d("section",gt,[i(M,{assignments:s.assignments},null,8,["assignments"])])):v("",!0),s.recentSection?(l(),d("section",pt,[t("div",ht,[t("div",null,[t("h1",vt,a(s.recentSection.name),1),e[7]||(e[7]=t("h5",{class:"mt-1 text-sm font-bold text-slate-500 uppercase"}," Where you left off ",-1)),t("p",bt,[s.recentSection.description?(l(),d("span",yt,a(s.recentSection.description),1)):(l(),d("span",wt,a(s.recentSection.verse),1))]),t("div",_t,[i(h,{color:"indigo",size:"sm",class:"w-full px-6 sm:w-48",link:E.value},{default:o(()=>e[5]||(e[5]=[f(" Continue Reading ")])),_:1},8,["link"]),i(h,{color:"white",size:"sm",class:"w-full px-6 sm:w-48",link:`/practice/vocabulary/attempt?sections[]=${P.value}`},{default:o(()=>e[6]||(e[6]=[f(" Practice Vocabulary ")])),_:1},8,["link"])])]),t("div",kt,[t("div",Vt,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/rectangle/${s.recentSection.book.work.image_name}`,alt:"",class:"w-full rounded-xl bg-gray-900/5 shadow-lg sm:aspect-2/3 sm:object-cover"},null,8,$t),e[8]||(e[8]=t("div",{class:"pointer-events-none absolute inset-0 rounded-xl ring-1 ring-gray-900/10 ring-inset"},null,-1))]),t("div",St,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/${s.recentSection.book.work.image_name}`,alt:"",class:"aspect-h-1 aspect-w-1 w-full rounded-xl bg-gray-900/5 shadow-lg sm:object-cover"},null,8,zt),e[9]||(e[9]=t("div",{class:"pointer-events-none absolute inset-0 rounded-xl ring-1 ring-gray-900/10 ring-inset"},null,-1))])])])])):v("",!0),t("section",jt,[t("div",Ht,[t("div",At,[t("div",null,[i(L,{class:"inline w-32"})]),t("div",Dt,[e[14]||(e[14]=t("div",{class:"flex grow flex-col gap-2"},[t("h1",{class:"inline text-center text-4xl font-bold text-gray-900 sm:text-left"}," Infinitas "),t("h5",{class:"-mt-1 text-center text-sm font-bold text-slate-500 uppercase sm:text-left"}," Grow your vocabulary "),t("p",{class:"mt-2 inline text-center text-base font-medium text-slate-600 sm:text-left"}," Infinitas provides you with an endless stream of vocabulary words, tailored to you and your needs. ")],-1)),t("div",null,[s.user_stats?(l(),d("div",Mt,[t("div",Lt,[t("div",Ct,[t("div",{class:"h-full bg-orange-300",style:$(`width: ${j(1)}`)},null,4),t("div",{class:"h-full bg-lime-300",style:$(`width: ${j(7)}`)},null,4),t("div",{class:"h-full bg-sky-300",style:$(`width: ${j(16)}`)},null,4),t("div",{class:"h-full bg-indigo-300",style:$(`width: ${j(30)}`)},null,4)]),t("div",It,[t("div",null,[t("h2",Et,a(z(1))+" words ",1),e[10]||(e[10]=t("p",{class:"text-lg text-gray-600"},"level 1",-1))]),t("div",null,[t("h2",Pt,a(z(7))+" words ",1),e[11]||(e[11]=t("p",{class:"text-lg text-gray-600"},"level 2",-1))]),t("div",null,[t("h2",Ot,a(z(16))+" words ",1),e[12]||(e[12]=t("p",{class:"text-lg text-gray-600"},"level 3",-1))]),t("div",null,[t("h2",Wt,a(z(30))+" words ",1),e[13]||(e[13]=t("p",{class:"text-lg text-gray-600"},"mastered",-1))])])])])):v("",!0)])])]),t("div",Gt,[i(h,{color:"white",size:"sm",class:"w-full self-end sm:w-48",link:"/infinitas"},{default:o(()=>e[15]||(e[15]=[f(" Learn More ")])),_:1}),i(h,{color:"indigo",size:"sm",class:"w-full self-end sm:w-48",link:"/infinitas/attempt"},{default:o(()=>e[16]||(e[16]=[f(" Infinite Practice ")])),_:1})])])]),t("section",Bt,[t("div",Nt,[t("div",null,[t("div",Rt,[e[18]||(e[18]=t("h5",{class:"text-sm font-bold text-slate-500 uppercase"}," Activities ",-1)),s.activities.length>0?(l(),p(m,{key:0,href:"/practice/activities",class:"duration-150ome ml-auto text-xs font-semibold text-gray-500 transition hover:text-gray-600 hover:underline"},{default:o(()=>e[17]||(e[17]=[f(" View all ")])),_:1})):v("",!0)]),s.activities.length>0?(l(),d("div",Ft,[t("div",null,[(l(!0),d(w,null,_(s.activities,r=>(l(),p(Y,{activity:r,key:r.id},null,8,["activity"]))),128))])])):(l(),d("div",Tt,[i(m,{class:"flex cursor-pointer flex-col rounded-xl p-3 select-none",href:"/practice"},{default:o(()=>[t("div",Ut,[(l(),p(D(c(C)),{class:"h-8 w-8 text-white","aria-hidden":"true"}))]),e[19]||(e[19]=t("div",{class:"mt-2 text-center"},[t("p",{class:"text-lg font-semibold text-gray-700"}," Practice "),t("p",{class:"text-base font-medium text-gray-500"}," Keep your Latin skills sharp with vocabulary and grammar exercises. ")],-1))]),_:1})]))]),t("div",null,[t("div",null,[e[21]||(e[21]=t("h5",{class:"text-sm font-bold text-slate-500 uppercase"}," Experience ",-1)),t("div",Zt,[t("div",Yt,[c(u)().props.user.xp>0?(l(),p(K,{key:0,labels:s.experienceData[c(b)].dates,data:s.experienceData[c(b)].points},null,8,["labels","data"])):(l(),d("div",Kt,e[20]||(e[20]=[t("div",{class:"mx-auto rounded-md bg-slate-500 px-4 py-2 text-center text-sm font-medium text-white opacity-75 backdrop-blur-sm backdrop-filter"}," Earn XP by completing activities ",-1)])))]),t("div",Xt,[(l(!0),d(w,null,_(s.experienceData,(r,k)=>(l(),d("div",{class:V(["w-10 cursor-pointer rounded-full px-2 py-1 text-center transition duration-150",k==c(b)?"bg-emerald-300 text-slate-800":"text-slate-500 hover:bg-slate-300 hover:text-slate-800"]),onClick:H=>Z(b)?b.value=k:b=k},a(G(r.dates[0])),11,qt))),256))])])])])])])])),t("section",Jt,[e[26]||(e[26]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Practice the Language ",-1)),t("div",Qt,[t("div",{class:V(["grid grid-cols-1 gap-8 sm:grid-cols-3",s.grammarActivity.attempted?"2xl:grid-cols-4":"2xl:grid-cols-3"])},[i(m,{href:`/practice/grammar/a/${s.dailyActivity.id}`,class:V(["flex inline-flex h-32 cursor-pointer flex-col items-center justify-center rounded-2xl bg-purple-100 transition duration-150 hover:bg-purple-200",s.grammarActivity.attempted?"sm:col-span-2 2xl:col-span-1":"sm:col-span-1"])},{default:o(()=>[i(c(q),{class:"h-8 w-8 text-purple-600"}),e[22]||(e[22]=t("h2",{class:"mt-1 px-4 text-center font-intro text-xl font-bold text-purple-700"}," Daily Activity ",-1)),t("h5",te,a(s.dailyActivity.name),1)]),_:1},8,["href","class"]),n.grammarActivity.attempted?(l(),p(m,{key:0,href:`/practice/grammar/a/${s.grammarActivity.attempted.id}`,class:"flex h-32 cursor-pointer flex-col items-center justify-center rounded-2xl bg-indigo-100 transition duration-150 hover:bg-indigo-200 sm:inline-flex"},{default:o(()=>[i(c(J),{class:"h-8 w-8 stroke-2 text-indigo-600"}),e[23]||(e[23]=t("h2",{class:"mt-1 text-center font-intro text-xl font-bold text-indigo-700"}," Try Again ",-1)),t("h5",ee,a(s.grammarActivity.attempted.name),1)]),_:1},8,["href"])):v("",!0),i(m,{href:"/practice/grammar/a/ob8yHAhfm",class:"group relative flex h-32 cursor-pointer flex-col items-center justify-center rounded-2xl bg-teal-100 transition duration-150 hover:bg-teal-200"},{default:o(()=>e[24]||(e[24]=[t("div",{class:"absolute top-0 z-10 mt-3 rounded-full bg-teal-200 px-3 py-0.5 text-xs font-bold text-teal-600 shadow-inner transition duration-150 group-hover:bg-teal-300"}," review ",-1),t("h2",{class:"mt-3 font-intro text-2xl font-bold text-teal-700"}," Noun Review ",-1)])),_:1}),i(m,{href:"/practice/grammar/a/RlClgvvLB",class:V(["group relative flex h-32 cursor-pointer flex-col items-center justify-center rounded-2xl bg-sky-100 transition duration-150 hover:bg-sky-200",s.grammarActivity.attempted?"sm:col-span-2 2xl:col-span-1":"sm:col-span-1"])},{default:o(()=>e[25]||(e[25]=[t("div",{class:"absolute top-0 z-10 mt-3 rounded-full bg-sky-200 px-3 py-0.5 text-xs font-bold text-sky-600 shadow-inner transition duration-150 group-hover:bg-sky-300"}," review ",-1),t("h2",{class:"mt-3 font-intro text-2xl font-bold text-sky-700"}," Verb Review ",-1)])),_:1},8,["class"])],2)])]),t("section",se,[e[32]||(e[32]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Essential Vocabulary ",-1)),t("div",le,[t("div",oe,[i(m,{href:"/practice/vocabulary/attempt?sections[]=basic",class:"flex h-24 w-24 items-center justify-center rounded-full bg-emerald-300 font-intro text-3xl font-bold text-emerald-700 transition duration-150 hover:bg-emerald-200 hover:text-emerald-600 sm:h-36 sm:w-36 2xl:h-24 2xl:w-24"},{default:o(()=>e[27]||(e[27]=[f(" Basic ")])),_:1}),i(m,{href:"/practice/vocabulary/attempt?sections[]=intermediate",class:"flex h-24 w-24 items-center justify-center rounded-full bg-blue-300 font-intro text-3xl font-bold text-blue-700 transition duration-150 hover:bg-blue-200 hover:text-blue-600 sm:h-36 sm:w-36 2xl:h-24 2xl:w-24"},{default:o(()=>e[28]||(e[28]=[f(" Inter ")])),_:1}),i(m,{href:"/practice/vocabulary/attempt?sections[]=advanced",class:"flex h-24 w-24 items-center justify-center rounded-full bg-orange-300 font-intro text-3xl font-bold text-orange-700 transition duration-150 hover:bg-orange-200 hover:text-orange-600 sm:h-36 sm:w-36 2xl:h-24 2xl:w-24"},{default:o(()=>e[29]||(e[29]=[f(" Adv ")])),_:1})]),e[31]||(e[31]=t("div",{class:"w-1/8 self-center text-center font-sans text-2xl font-bold text-gray-600 uppercase"}," or ",-1)),i(m,{href:`/words?group=${s.wordGroup.id}`,class:"group relative flex min-h-[128px] w-full cursor-pointer items-center justify-center rounded-2xl bg-slate-100 p-2 transition duration-150 hover:bg-slate-200 md:w-96 lg:w-full 2xl:w-96"},{default:o(()=>[e[30]||(e[30]=t("div",{class:"absolute top-0 z-10 mx-auto mt-3 rounded-full bg-slate-200 px-3 py-0.5 text-xs font-bold text-gray-600 shadow-inner transition duration-150 group-hover:bg-slate-300"}," Word Group ",-1)),t("div",ie,a(s.wordGroup.name),1)]),_:1},8,["href"])])]),t("section",re,[e[34]||(e[34]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Readings",-1)),t("div",ne,[(l(!0),d(w,null,_(s.randomWorks,r=>(l(),p(m,{href:`/read/${r.author.url}/${r.url}`,class:"group relative flex-col"},{default:o(()=>[t("div",ae,[t("div",de,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/${r.image_name}`,alt:"",class:"w-full rounded-2xl"},null,8,ce),e[33]||(e[33]=t("div",{class:"absolute inset-0 flex w-full items-end p-4 opacity-0 transition duration-250 ease-in-out group-hover:opacity-100"},[t("div",{class:"mx-auto rounded-md bg-white px-4 py-2 text-center text-sm font-medium text-gray-900 opacity-75 backdrop-blur-sm backdrop-filter"}," Explore this Work ")],-1))])]),t("div",me,[t("h3",ue,a(r.name),1),t("p",fe,a(r.description),1)])]),_:2},1032,["href"]))),256))])]),t("section",xe,[e[38]||(e[38]=t("h5",{class:"text-sm font-bold text-gray-500 uppercase"},"Videos",-1)),t("div",ge,[i(m,{href:`/watch/${s.latestVideo.url_slug}`,class:"w-full rounded-2xl md:w-1/2 lg:w-full 2xl:w-1/2"},{default:o(()=>[t("img",{class:"w-full rounded-2xl bg-gray-50 shadow-xs transition duration-150 ease-in-out hover:shadow-lg",src:s.latestVideo.vimeo_thumbnail?s.latestVideo.vimeo_thumbnail:`https://i.ytimg.com/vi/${s.latestVideo.youtube}/mqdefault.jpg`,alt:""},null,8,pe)]),_:1},8,["href"]),t("div",he,[i(m,{href:`/watch/${s.latestVideo.url_slug}`},{default:o(()=>[t("h2",ve,a(s.latestVideo.title),1)]),_:1},8,["href"]),t("div",be,[s.latestVideo.youtube_id?s.latestVideo.youtube_id&&new Date(s.latestVideo.youtube_published_at)>new Date?(l(),d("h5",we," Early Access ")):(l(),d("h5",_e," newest video ")):(l(),d("h5",ye," LatinTutorial Exclusive ")),e[35]||(e[35]=t("div",{class:"ml-4 inline-flex"},null,-1))]),t("p",ke,a(s.latestVideo.description),1)])]),t("div",Ve,[t("div",$e,[i(m,{href:`/watch/${s.video.url_slug}`,class:"relative flex h-48 w-full flex-col items-center justify-center overflow-hidden rounded-2xl bg-slate-100 bg-slate-900 bg-repeat px-4 pt-8 pb-4 font-intro"},{default:o(()=>[e[36]||(e[36]=t("div",{class:"absolute top-0 z-10 mt-4 rounded-full bg-slate-600 px-4 py-1 text-sm font-bold text-white shadow-inner"}," watch now ",-1)),t("div",null,[t("h3",Se,a(s.video.title),1)])]),_:1},8,["href"]),i(m,{href:`/watch?topics[]=${s.tag.id}`,class:"relative flex h-48 w-full flex-col items-center justify-center overflow-hidden rounded-2xl bg-slate-100 bg-repeat p-4 font-intro",style:$(`background-image: url(${Ke})`)},{default:o(()=>[e[37]||(e[37]=t("div",{class:"absolute top-0 z-10 mt-4 rounded-full bg-slate-200 px-4 py-1 text-sm font-bold text-gray-600 shadow-inner"}," video topic ",-1)),t("h3",ze,a(s.tag.name),1)]),_:1},8,["href","style"])])])]),i(X)])]),t("aside",je,[t("section",He,[t("div",Ae,[t("img",{class:"mx-auto h-32 w-32 rounded-full object-cover lg:mb-4",src:c(u)().props.user.profile_photo_url,alt:c(u)().props.user.name},null,8,De),t("div",Me,[e[40]||(e[40]=t("h5",{class:"text-center font-intro text-xl font-semibold text-gray-500 sm:text-left sm:text-2xl lg:text-center lg:text-xl"}," Salve, ",-1)),t("h1",Le,a(c(u)().props.user.name),1)]),t("div",Ce,[i(h,{link:"/user/profile",size:"sm",color:"indigo",class:"mb-4 w-full"},{default:o(()=>e[41]||(e[41]=[f(" View Profile ")])),_:1}),c(u)().props.user.membership.individualSubscribed?(l(),p(h,{key:0,link:"/subscribe/edit",size:"sm",color:"gray",class:"w-full"},{default:o(()=>e[42]||(e[42]=[f(" Manage Subscription ")])),_:1})):c(u)().props.user.membership.teamSubscribed?(l(),p(h,{key:1,link:"/classes",size:"sm",color:"black",class:"w-full"},{default:o(()=>e[43]||(e[43]=[f(" Manage Your Classes ")])),_:1})):(l(),p(h,{key:2,link:"/subscribe/",size:"sm",color:"black",class:"w-full"},{default:o(()=>e[44]||(e[44]=[f("Go Pro")])),_:1}))])]),t("div",Ie,[i(h,{link:"/user/profile",size:"sm",color:"indigo",class:"w-full lg:col-start-1 lg:mb-4"},{default:o(()=>e[45]||(e[45]=[f(" View Profile ")])),_:1}),c(u)().props.user.membership.individualSubscribed?(l(),p(h,{key:0,link:"/subscribe/edit",size:"sm",color:"gray",class:"w-full"},{default:o(()=>e[46]||(e[46]=[f(" Manage Subscription ")])),_:1})):c(u)().props.user.membership.teamSubscribed?(l(),p(h,{key:1,link:"/",size:"sm",color:"black",class:"w-full"},{default:o(()=>e[47]||(e[47]=[f(" Manage Your Classroom ")])),_:1})):(l(),p(h,{key:2,link:"/subscribe/",size:"sm",color:"black",class:"w-full"},{default:o(()=>e[48]||(e[48]=[f("Go Pro")])),_:1}))]),t("div",Ee,[t("div",Pe,[(l(!0),d(w,null,_(s.achievements,(r,k)=>(l(),p(R,{size:"sm",icon:r.icon,description:r.description,name:r.name,level:r.level,key:k,"is-earned":c(u)().props.user.achievements.filter(H=>r.id===H.id).length>0},null,8,["icon","description","name","level","is-earned"]))),128))]),i(T,{class:"mt-2",height:"h-3",size:"lg","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+c(u)().props.user.level.level,"post-text":c(u)().props.user.xp+" XP",progress:I(c(u)().props.user)},null,8,["pre-text","post-text","progress"]),t("div",Oe,[t("p",We,a(c(u)().props.user.level.max-c(u)().props.user.xp+1)+" xp to the next level ",1)])]),t("div",Ge,[i(m,{class:"transition duration-150 ease-in-out hover:underline",href:"/achievements"},{default:o(()=>e[49]||(e[49]=[f("View your achievements")])),_:1})])]),s.pinnedWorks.length>0?(l(),d("section",Be,[e[50]||(e[50]=t("h4",{class:"text-sm font-bold text-gray-500 uppercase"}," Pinned Works ",-1)),t("div",Ne,[(l(!0),d(w,null,_(s.pinnedWorks,r=>(l(),d("div",Re,[i(m,{class:"flex flex-row",href:`/read/${r.author.url}/${r.url}`},{default:o(()=>[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${r.image_name}`,class:"h-12 w-12 flex-none self-center rounded-full"},null,8,Fe),t("div",Te,[t("div",Ue,[t("h3",Ze,a(r.name),1)]),t("p",Ye,a(r.author.name),1)])]),_:2},1032,["href"])]))),256))])])):v("",!0),e[51]||(e[51]=t("section",{"aria-labelledby":"assignments"},null,-1))])])]),_:1})}}};export{sl as default};
