import{l as b,e as x,p as C,o,d as r,a as e,F as m,h as f,f as l,t as n,b as p,u as d,j as N,r as W,W as v,g as h,n as y}from"./app-f0078ddb.js";import{_ as $}from"./DropdownGeneral-ce7a4558.js";import{I as B}from"./InfinitasIcon-1a3ae135.js";import{r as F}from"./StarIcon-155a2a28.js";import{r as I}from"./CheckCircleIcon-d86d1232.js";/* empty css            */import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./keyboard-982fc047.js";import"./open-closed-7f51e238.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"grid grid-cols-1 gap-8"},O={class:"mt-2 rounded-lg bg-white shadow-sm"},P={key:0,class:"my-2 flex flex-row items-center"},R=["src"],T={class:"ml-2 flex grow flex-col"},U={class:"font-intro text-lg font-bold text-gray-900"},z={key:0},D={key:1},J={class:"text-xs text-gray-600"},X={key:1,class:"my-2 flex flex-row items-center"},q={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},G={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},H={key:2,class:"flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 p-2 font-intro text-sm font-bold text-indigo-700"},K={class:"ml-2 flex grow flex-col"},Q={class:"font-intro text-lg font-bold text-gray-900"},Y={class:"text-xs text-gray-600"},Z={key:0,class:"mt-4"},tt={class:"mr-2 mt-2 inline-block rounded-full bg-slate-200 px-2 py-1 text-xs font-bold text-slate-500"},et={key:0,class:"content-left text-left"},st=["src"],ot={class:"ml-2 text-base font-medium text-gray-900"},rt={key:1,class:"mt-2 text-left text-sm font-medium text-gray-500"},nt={key:2,class:"content-left mt-2 text-left text-sm font-bold text-amber-600"},at={"aria-labelledby":"userStats"},it={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 sm:grid-cols-2 lg:grid-cols-2"},lt={class:"text-sm font-medium leading-6 text-gray-500"},ct={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},ut={key:0},dt={key:0,class:"ml-1 text-sm font-medium text-gray-500"},mt={key:1},ft={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},pt=["href"],Ft={__name:"ReviewSidebar",props:{sections:Array,currentList:Array,description:String,stats:{type:Object,required:!1},totalWords:{type:Number,required:!1},sort:String},emits:["update:sort"],setup(i,{emit:_}){let s=i;const w=_;function L(){s.currentList[0]=="infinitas"?v.get("/infinitas/attempt"):v.get("/practice/vocabulary/attempt",{sections:s.currentList})}const k=b(()=>{if(s.stats)switch(!0){case!s.stats.time:return null;case Math.round(s.stats.time/10)<15:return"Just a few seconds spent";case Math.round(s.stats.time/10)<30:return"Less than 30 seconds spent";case Math.round(s.stats.time/10)<60:return"Less than a minute spent";case Math.round(s.stats.time/10)<90:return"About a minute spent";case Math.round(s.stats.time/10)<150:return"About 2 minutes spent";case Math.round(s.stats.time/10)<210:return"About 3 minutes spent";case Math.round(s.stats.time/10)<270:return"About 4 minutes spent";case Math.round(s.stats.time/10)<330:return"About 5 minutes spent";default:return"About "+Math.round(s.stats.time/600)+" minutes spent"}return null}),A=b(()=>s.stats.attempts==0?0:s.stats.attempts<10?s.stats.correct:Math.round(10*s.stats.correct/s.stats.attempts)<1?1:Math.round(10*s.stats.correct/s.stats.attempts)),S=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}];let M=x([{name:"Correct",value:s.stats.correct,unit:"out of "+s.stats.attempts},{name:"Best Streak",value:s.stats.streak,unit:"in a row"},{name:"XP Earned",value:s.stats.xp_earned},{name:"Words Seen",value:s.totalWords}]),g=[{name:"Correct",value:"correct"},{name:"Incorrect",value:"incorrect"},{name:"Attempts",value:"attempts"},{name:"Alphabetical",value:"alphabetical"}];const V=u=>g.find(a=>a.value==u);let c=x(s.sort?V(s.sort):g[0]);return C(()=>c,u=>{w("update:sort",u.value.value)},{deep:!0}),(u,a)=>(o(),r("div",null,[e("div",E,[e("section",null,[a[5]||(a[5]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Sections",-1)),e("div",O,[(o(!0),r(m,null,f(i.sections,(t,j)=>(o(),r("div",{key:j,class:"h-20 w-full px-4 py-2"},[t.sectionList?(o(),r("div",P,[e("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+t.sectionList.image_name,class:"h-12 w-12"},null,8,R),e("div",T,[e("h4",U,[h(n(t.sectionList.work)+" "+n(t.sectionList.l1)+" "+n(t.sectionList.book)+".",1),t.sectionList.start===t.sectionList.end?(o(),r("span",z,n(t.sectionList.start),1)):(o(),r("span",D,n(t.sectionList.start)+"-"+n(t.sectionList.end),1))]),e("p",J,n(t.sectionList.word_count)+" words | "+n(t.sectionList.core_count)+" core, "+n(t.sectionList.word_count-t.sectionList.core_count)+" not core ",1)])])):l("",!0),t.vocabList?(o(),r("div",X,[a[2]||(a[2]=e("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),t.vocabList.id=="star"?(o(),r("div",q,[p(d(F),{class:"h-8 w-8 stroke-2"})])):t.vocabList.id=="learn"?(o(),r("div",G,[p(d(I),{class:"h-8 w-8 stroke-2"})])):t.vocabList.id=="infinitas"?(o(),r("div",H,[p(B,{class:"inline w-8"})])):(o(),r("div",{key:3,class:y(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${t.vocabList.icon_color}-300 text-${t.vocabList.icon_color}-700`])},n(t.vocabList.abbreviation),3)),e("div",K,[e("h4",Q,n(t.vocabList.name),1),e("p",Y,n(t.vocabList.word_count)+" words | "+n(t.vocabList.core_count)+" core, "+n(t.vocabList.core_count=="∞"?"∞":t.vocabList.word_count-t.vocabList.core_count)+" not core ",1)])])):l("",!0)]))),128)),e("div",{class:"group mt-2 cursor-pointer rounded-b-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:a[0]||(a[0]=t=>L())},a[3]||(a[3]=[e("div",{class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"}," Try Again ",-1)]))]),i.stats.filters?(o(),r("div",Z,[a[4]||(a[4]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Filters",-1)),(o(!0),r(m,null,f(i.stats.filters.split(","),t=>(o(),r("div",tt,n(t),1))),256))])):l("",!0)]),e("section",null,[i.stats?(o(),r("p",et,[e("img",{src:i.stats.user_avatar,class:"inline h-6 w-6 rounded-full"},null,8,st),e("span",ot,n(i.stats.user),1)])):l("",!0),i.description?(o(),r("p",rt,n(i.description),1)):l("",!0),i.stats?(o(),r("p",nt,n(k.value),1)):l("",!0)]),e("section",null,[p($,{modelValue:d(c),"onUpdate:modelValue":a[1]||(a[1]=t=>N(c)?c.value=t:c=t),list:d(g),current:d(c),title:"Sort"},null,8,["modelValue","list","current"])]),e("section",at,[e("div",null,[e("dl",it,[(o(!0),r(m,null,f(d(M),t=>(o(),r("div",{key:t.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[e("dt",lt,n(t.name),1),e("dd",ct,[t.value?(o(),r("span",ut,[h(n(t.value)+" ",1),t.unit?(o(),r("span",dt,n(t.unit),1)):l("",!0)])):(o(),r("span",mt,"–"))])]))),128))])])]),e("section",null,[a[6]||(a[6]=e("h5",{class:"text-sm font-bold uppercase text-gray-500"},"Performance",-1)),e("div",ft,[(o(),r(m,null,f(S,t=>e("div",{class:y(["h-4 w-full rounded-sm shadow-sm",A.value>=t.value?t.color:"bg-gray-300"]),key:t.value},null,2)),64))])]),e("section",null,[e("a",{href:u.route("practice.activities.index")},a[7]||(a[7]=[e("button",{class:"duration-250 mt-8 w-full items-center rounded-lg border border-transparent bg-white px-3 py-2 text-center text-sm font-bold leading-4 text-gray-700 shadow-sm transition hover:bg-gray-100 hover:text-gray-800 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"}," View All Activities ",-1)]),8,pt)]),W(u.$slots,"default")])]))}};export{Ft as default};
