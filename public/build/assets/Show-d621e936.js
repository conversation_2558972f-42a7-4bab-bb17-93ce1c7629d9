import{_ as $}from"./AppLayout-33f062bc.js";import{_ as I}from"./Breadcrumbs-c96e9207.js";import L from"./SummaryItem-8026db9b.js";import b from"./ReviewSidebar-3c306e61.js";import{e as d,l as w,i as j,o as n,c as x,w as u,b as l,a as e,u as r,j as k,d as p,f as N,t as A,F as B,h as O,n as R,W as D}from"./app-f0078ddb.js";import{D as F}from"./datetime-8ddd27a0.js";import{p as V}from"./pluralize-d25a928b.js";import{_ as z}from"./Footer-0988dcd8.js";import{_ as E}from"./MobileSidebar-5e21b4cd.js";import{r as H}from"./InformationCircleIcon-716f3ffb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./TruncateVerse-a9219b3d.js";import"./removePunctuation-702d8a66.js";import"./XCircleIcon-63af2b2a.js";import"./ArrowLongRightIcon-d06130d7.js";import"./DropdownGeneral-ce7a4558.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./VideoItemSmall-c82820c8.js";/* empty css            */const P={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},U={class:"px-4 py-8 sm:px-8"},W={class:"flex flex-row items-center justify-between"},q={key:0,class:"mt-4 mb-8 text-sm font-normal text-rose-600"},G={key:1},Q={id:"desktop-teams-headline",class:"text-sm font-bold tracking-wider text-gray-500 uppercase"},T={class:"grid grid-cols-1 divide-y"},J={key:2,class:"relative mt-4 block h-20 w-full rounded-lg border-2 border-dashed border-gray-300 py-5 text-center"},K={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},qt={__name:"Show",props:{stats:Object,list:Object,sections:{type:Array,required:!1},sort:String,activity:Object,videos:Array},setup(s){const o=s;let _=[{name:"Practice",href:"/practice",current:!1},{name:"Activities",href:"/practice/activities",current:!1},{name:"Review",href:"#",current:!0}],C=d(!1),f=d(!0),i=d(!1),c=d(o.list);o.stats.attempts==o.list.length&&(C.value=!0,f.value=!1);const v=w(()=>o.stats.completed_at?F.fromISO(o.stats.completed_at).toLocal().toFormat("'completed at' h:mm a 'on' ccc LLL dd yyyy"):"N/A"),g=w(()=>{var m=[];return o.sections&&o.sections.forEach(t=>{m.push(t.book_id+":"+parseInt(t.start)+":"+parseInt(t.end))}),m}),y=m=>{D.get("/practice/grammar/attempt/"+o.stats.id,{sort:m},{preserveState:!0,replace:!0,preserveScroll:!0,only:["list"],onSuccess:()=>{c.value=o.list}})};return(m,t)=>{const S=j("Head");return n(),x($,null,{default:u(()=>[l(S,null,{default:u(()=>t[4]||(t[4]=[e("title",null,"Grammar Review",-1)])),_:1}),e("main",P,[e("div",U,[e("div",W,[l(I,{class:"lg:col-span-9 xl:grid-cols-10",pages:r(_)},null,8,["pages"]),l(r(H),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=a=>k(i)?i.value=!r(i):i=!r(i))})]),t[6]||(t[6]=e("div",{class:"overflow-visible"},[e("div",{class:"mt-8 pb-8"},[e("h1",{class:"text-4xl font-bold text-gray-900"},"Review")])],-1)),e("div",null,[e("div",null,[r(f)?(n(),p("p",q," Some of your attempts don't seem to have been processed yet. Please check back later for a more accurate summary. ")):N("",!0),r(c).length>0?(n(),p("div",G,[e("h3",Q,A(r(V)("Item",r(c).length)),1),e("div",null,[e("div",T,[(n(!0),p(B,null,O(r(c),(a,h)=>(n(),x(L,{key:h,item:a,class:R({"mt-5":h===0})},null,8,["item","class"]))),128))])])])):(n(),p("div",J,t[5]||(t[5]=[e("span",{class:"mt-2 block text-sm font-medium text-gray-600"}," No Questions Completed ",-1)])))])])]),l(z)]),l(E,{class:"lg:hidden",show:r(i),onClose:t[2]||(t[2]=a=>k(i)?i.value=!1:i=!1)},{default:u(()=>[l(b,{"is-finished":!0,sections:s.sections,activity:s.activity,videos:s.videos,"current-list":g.value,description:v.value,stats:s.stats,"total-words":s.list.length,sort:s.sort,"onUpdate:sort":t[1]||(t[1]=a=>y(a))},null,8,["sections","activity","videos","current-list","description","stats","total-words","sort"])]),_:1},8,["show"]),e("aside",K,[l(b,{"is-finished":!0,sections:s.sections,activity:s.activity,videos:s.videos,"current-list":g.value,description:v.value,stats:s.stats,"total-words":s.list.length,sort:s.sort,"onUpdate:sort":t[3]||(t[3]=a=>y(a))},null,8,["sections","activity","videos","current-list","description","stats","total-words","sort"])])]),_:1})}}};export{qt as default};
