import{_ as h}from"./AppLayout-33f062bc.js";import{_ as g}from"./Breadcrumbs-c96e9207.js";import _ from"./Summary-27ab5eca.js";import w from"./ReviewSidebar-5ebca430.js";import{e as c,p as y,l as n,i as x,o as L,c as A,w as p,b as o,a as r,u as i,j as k,W as $}from"./app-f0078ddb.js";import{D as C}from"./datetime-8ddd27a0.js";import{_ as S}from"./Footer-0988dcd8.js";import{r as j}from"./InformationCircleIcon-716f3ffb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./SummaryWord-5b872645.js";import"./StarIcon-155a2a28.js";/* empty css            */import"./pluralize-d25a928b.js";import"./DropdownGeneral-ce7a4558.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./InfinitasIcon-1a3ae135.js";const R={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},W={class:"px-4 py-8 sm:px-8"},B={class:"flex flex-row justify-between items-center"},I={class:"overflow-visible"},N={class:"bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},St={__name:"Show",props:{stats:Object,list:Object,learned:Array,learnedWords:Array,starred:Array,sections:Array},setup(s){const e=s;let d=[{name:"Practice",href:"/practice",current:!1},{name:"Vocabulary",href:"/practice/vocabulary",current:!1},{name:"Review",href:"#",current:!0}],m=c([{name:"Correct",value:"correct"},{name:"Incorrect",value:"incorrect"},{name:"Attempts",value:"attempts"},{name:"Alphabetical",value:"alphabetical"}][0]),a=c(!1);y(()=>m,()=>{$.get(e.list.path,{sort:m.value.value},{preserveState:!0,replace:!0,preserveScroll:!0})},{deep:!0});let u={correct:e.stats.correct,incorrect:e.stats.attempts-e.stats.correct,attempts:e.stats.attempts};const f=n(()=>e.stats.completed_at?C.fromISO(e.stats.completed_at).toLocal().toFormat("'completed at' h:mm a 'on' ccc LLL dd yyyy"):"N/A"),v=n(()=>{var l=[];return e.sections.forEach(t=>{t.sectionList&&l.push(t.sectionList.book_id+":"+t.sectionList.start+":"+t.sectionList.end),t.vocabList&&l.push(t.vocabList.id)}),l});return(l,t)=>{const b=x("Head");return L(),A(h,null,{default:p(()=>[o(b,null,{default:p(()=>t[1]||(t[1]=[r("title",null,"Practice Review",-1)])),_:1}),r("main",R,[r("div",W,[r("div",B,[o(g,{class:"lg:col-span-9 xl:grid-cols-10",pages:i(d)},null,8,["pages"]),o(i(j),{class:"lg:hidden w-6 h-6 mt-1 ml-2 transition duration-150 ease-in-out cursor-pointer stroke-2 stroke-current hover:text-slate-500 text-slate-400",onClick:t[0]||(t[0]=V=>k(a)?a.value=!i(a):a=!i(a))})]),r("div",I,[t[2]||(t[2]=r("div",{class:"mt-8 pb-8"},[r("h1",{class:"text-4xl text-gray-900 font-bold"},"Review")],-1)),o(_,{stats:i(u),summary:s.list,"learned-words":s.learnedWords,learned:s.learned,starred:s.starred},null,8,["stats","summary","learned-words","learned","starred"])])]),o(S)]),r("aside",N,[o(w,{"is-finished":!0,sections:s.sections,description:f.value,"current-list":v.value,stats:s.stats},null,8,["sections","description","current-list","stats"])])]),_:1})}}};export{St as default};
