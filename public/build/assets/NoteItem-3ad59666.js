import{C as V,e as u,p as f,A as B,B as E,o as c,d,a as s,q as T,x as q,u as a,b as y,w as _,T as x,t as g,f as h}from"./app-f0078ddb.js";import{l as j}from"./lodash-631955d9.js";/* empty css            */const A={class:"mt-2 font-sans flex flex-col"},C={class:"flex justify-between gap-4 items-center"},I={class:"mt-2 h-6 flex justify-between mr-2"},$={class:"grow"},D={key:0},H={class:"text-xs text-red-600 font-medium mb-2"},M={key:0,class:"text-left w-16"},U={class:"text-xs text-gray-500 mr-2"},G={__name:"NoteItem",props:{note:{type:String,required:!1},verse:{type:Number,required:!0},verseName:{type:String,required:!0}},emits:["is-editing"],setup(b,{emit:w}){const m=b,o=V({verse:m.verse,note:m.note});let t=u("");const S=w,N=()=>{t.value="Saving...",o.post("/api/submit-note",{preserveScroll:!0,preserveState:!0,onSuccess:()=>{t.value="Saved",setTimeout(()=>t.value="",2e3)},onError:()=>{t.value="Error",setTimeout(()=>t.value="",2e3)}})},v=j.debounce(N,1500);f(()=>o.note,(i,e)=>{const n=(i||"").trim(),k=(e||"").trim();n!==k&&v()},{deep:!0});const l=u(!1);f(()=>l.value,i=>{S("is-editing",i)});const r=u(null);B(()=>{p()}),E(()=>{v.cancel()});const p=()=>{r.value&&(r.value.style.height="auto",r.value.style.height=`${r.value.scrollHeight}px`)};return(i,e)=>(c(),d("div",A,[s("form",null,[s("div",C,[T(s("textarea",{"onUpdate:modelValue":e[0]||(e[0]=n=>a(o).note=n),id:"note",name:"note",rows:"2",placeholder:"Add a note...",class:"block w-full border-0 py-1.5 resize-none text-gray-900 placeholder:text-gray-400 font-intro font-medium border border-gray-300 focus-none border-l-4 focus:border-indigo-300 focus:ring-none focus:outline-hidden focus:ring-transparent transition duration-150 ease-in-out",onInput:p,onFocus:e[1]||(e[1]=n=>l.value=!0),onBlur:e[2]||(e[2]=n=>l.value=!1),ref_key:"textarea",ref:r},null,544),[[q,a(o).note]])]),s("div",I,[s("div",$,[y(x,{"enter-active-class":"transition ease-in-out duration-250","enter-from-class":"opacity-0 ","enter-to-class":"opacity-100 ","leave-active-class":"transition ease-in-out duration-250","leave-from-class":"opacity-100 ","leave-to-class":"opacity-0 "},{default:_(()=>[a(o).errors.note?(c(),d("div",D,[s("p",H,g(a(o).errors.note),1)])):h("",!0)]),_:1})]),y(x,{"enter-active-class":"transition ease-in-out duration-250","enter-from-class":"opacity-0 ","enter-to-class":"opacity-100 ","leave-active-class":"transition ease-in-out duration-250","leave-from-class":"opacity-100 ","leave-to-class":"opacity-0 "},{default:_(()=>[a(t)?(c(),d("div",M,[s("p",U,g(a(t)),1)])):h("",!0)]),_:1})])])]))}};export{G as default};
