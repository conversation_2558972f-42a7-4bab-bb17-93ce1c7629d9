import{o as f,d as g,a,H as I,e as v,I as F,l as L,A as N,B as Y,J as G,K as A,i as $,b as u,u as m,w as h,F as k,m as U,j as q,h as D,t as _,c as B,n as O,g as M}from"./app-f0078ddb.js";import{_ as J,L as j}from"./AppLayout-33f062bc.js";import{_ as X}from"./Breadcrumbs-c96e9207.js";import{_ as Z}from"./Footer-0988dcd8.js";import{r as ee}from"./InformationCircleIcon-716f3ffb.js";import{i as E,u as H,o as b,A as C,N as W}from"./render-c34c346a.js";import{s as te}from"./use-resolve-button-type-24d8b5c5.js";import{t as se,i as T,l as ae}from"./open-closed-7f51e238.js";import{o as x}from"./keyboard-982fc047.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";function oe(s,i){return f(),g("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M18 12H6"})])}function ne(s,i){return f(),g("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v12m6-6H6"})])}var re=(s=>(s[s.Open=0]="Open",s[s.Closed=1]="Closed",s))(re||{});let K=Symbol("DisclosureContext");function P(s){let i=A(K,null);if(i===null){let d=new Error(`<${s} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(d,P),d}return i}let R=Symbol("DisclosurePanelContext");function le(){return A(R,null)}let ie=I({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(s,{slots:i,attrs:d}){let l=v(s.defaultOpen?0:1),e=v(null),p=v(null),t={buttonId:v(`headlessui-disclosure-button-${E()}`),panelId:v(`headlessui-disclosure-panel-${E()}`),disclosureState:l,panel:e,button:p,toggleDisclosure(){l.value=H(l.value,{0:1,1:0})},closeDisclosure(){l.value!==1&&(l.value=1)},close(n){t.closeDisclosure();let c=(()=>n?n instanceof HTMLElement?n:n.value instanceof HTMLElement?b(n):b(t.button):b(t.button))();c==null||c.focus()}};return F(K,t),se(L(()=>H(l.value,{0:T.Open,1:T.Closed}))),()=>{let{defaultOpen:n,...c}=s,o={open:l.value===0,close:t.close};return C({theirProps:c,ourProps:{},slot:o,slots:i,attrs:d,name:"Disclosure"})}}}),ue=I({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(s,{attrs:i,slots:d,expose:l}){let e=P("DisclosureButton"),p=le(),t=L(()=>p===null?!1:p.value===e.panelId.value);N(()=>{t.value||s.id!==null&&(e.buttonId.value=s.id)}),Y(()=>{t.value||(e.buttonId.value=null)});let n=v(null);l({el:n,$el:n}),t.value||G(()=>{e.button.value=n.value});let c=te(L(()=>({as:s.as,type:i.type})),n);function o(){var r;s.disabled||(t.value?(e.toggleDisclosure(),(r=b(e.button))==null||r.focus()):e.toggleDisclosure())}function y(r){var w;if(!s.disabled)if(t.value)switch(r.key){case x.Space:case x.Enter:r.preventDefault(),r.stopPropagation(),e.toggleDisclosure(),(w=b(e.button))==null||w.focus();break}else switch(r.key){case x.Space:case x.Enter:r.preventDefault(),r.stopPropagation(),e.toggleDisclosure();break}}function S(r){switch(r.key){case x.Space:r.preventDefault();break}}return()=>{var r;let w={open:e.disclosureState.value===0},{id:V,...z}=s,Q=t.value?{ref:n,type:c.value,onClick:o,onKeydown:y}:{id:(r=e.buttonId.value)!=null?r:V,ref:n,type:c.value,"aria-expanded":e.disclosureState.value===0,"aria-controls":e.disclosureState.value===0||b(e.panel)?e.panelId.value:void 0,disabled:s.disabled?!0:void 0,onClick:o,onKeydown:y,onKeyup:S};return C({ourProps:Q,theirProps:z,slot:w,attrs:i,slots:d,name:"DisclosureButton"})}}}),ce=I({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(s,{attrs:i,slots:d,expose:l}){let e=P("DisclosurePanel");N(()=>{s.id!==null&&(e.panelId.value=s.id)}),Y(()=>{e.panelId.value=null}),l({el:e.panel,$el:e.panel}),F(R,e.panelId);let p=ae(),t=L(()=>p!==null?(p.value&T.Open)===T.Open:e.disclosureState.value===0);return()=>{var n;let c={open:e.disclosureState.value===0,close:e.close},{id:o,...y}=s,S={id:(n=e.panelId.value)!=null?n:o,ref:e.panel};return C({ourProps:S,theirProps:y,slot:c,attrs:i,slots:d,features:W.RenderStrategy|W.Static,visible:t.value,name:"DisclosurePanel"})}}});const de={class:"lg:pr-96 2xl:pr-[32rem]"},me={class:"px-4 py-8 sm:px-8"},pe={class:"flex flex-row items-center justify-between"},fe={class:"mx-auto mt-12"},he={class:"mx-auto max-w-7xl px-6"},ve={class:"mx-auto max-w-4xl divide-y divide-gray-900/10"},ye={class:"mt-10 space-y-6 divide-y divide-gray-900/10"},ge={class:"text-base/7 font-semibold"},be={class:"ml-6 flex h-7 items-center"},xe={class:"text-base/7 text-gray-600"},we={class:"my-8 sm:mx-auto sm:w-full sm:max-w-md"},ke={class:"mt-8 flex-1 text-left"},_e={class:"mt-2"},Le={class:"hidden bg-slate-50 p-8 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Te={class:"mb-8 sm:mx-auto sm:w-full sm:max-w-md"},Se={class:"mt-8 flex-1 text-left"},De={class:"mt-2"},it={__name:"Faq",setup(s){const i=[{question:"Can I use LatinTutorial's content for my class?",answer:"Yes! In fact, much of LatinTutorial was created as a supplement to the classroom experience. Teachers can purchase classroom subscriptions, and any student enrolled in a subscribed class can use the site for free."},{question:"Why doesn't LatinTutorial show long marks over Latin words?",answer:"Long marks are a modern convention that can be helpful for pronunciation, but they are not part of the original Latin language and many Latin texts don't have long marks. In addition, textbooks and dictionaries disagree on when to show long marks, and there is inconsistency and variability for some words. To that end, LatinTutorial takes the easy way out by focusing on the original language and does not use long marks."},{question:"Wait, are they long marks, macrons, or macra?",answer:"Yes, they are."},{question:"The Romans didn't use spaces or punctuation. Why does LatinTutorial?",answer:"LatinTutorial isn't that cruel. The Romans didn't use spaces or punctuation, but they also didn't use computers, the internet, or smartphones. We're not trying to be Romans; we're trying to learn Latin."},{question:"How can I support LatinTutorial?",answer:"You can support LatinTutorial by subscribing to the site, purchasing a classroom subscription, or making a donation. You can also support LatinTutorial by sharing the site with your friends, classmates, and teachers."},{question:"I've found a mistake, a bug, or something that just doesn't look right. What should I do?",answer:"You can also support LatinTutorial by reporting these issues! Please use the Get In Touch link at the bottom of each page to report any issues you find. We appreciate your help in making LatinTutorial better for everyone."},{question:"How can I contact LatinTutorial?",answer:"You can contact LatinTutorial via the Contact link to the right or, if you have an account and are logged in, by clicking on the Get In Touch link at the bottom of each page. We are always happy to hear from you and will try to respond as soon as possible (but sometimes life or our Latin classes take priority)!"}],d=[{name:"Frequently Asked Questions",href:"#",current:!0}],l=[{name:"About",href:route("about"),current:!1},{name:"Contact",href:route("contact"),current:!1},{name:"Privacy Policy",href:"/privacy-policy",current:!1},{name:"Terms of Service",href:"/terms-of-service",current:!1},{name:"Frequently Asked Questions",href:"/faq",current:!0}];let e=v(!1);return(p,t)=>{const n=$("Link"),c=$("MobileSidebar");return f(),g(k,null,[u(m(U),{title:"Privacy Policy"}),u(J,null,{default:h(()=>[a("main",de,[a("div",me,[a("div",pe,[u(X,{class:"lg:col-span-9 xl:grid-cols-10",pages:d}),u(m(ee),{class:"ml-2 mt-1 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:t[0]||(t[0]=o=>q(e)?e.value=!m(e):e=!m(e))})]),a("div",fe,[a("div",he,[a("div",ve,[t[2]||(t[2]=a("h2",{class:"text-4xl font-semibold tracking-tight text-gray-900 sm:text-5xl"}," Frequently asked questions ",-1)),a("dl",ye,[(f(),g(k,null,D(i,o=>u(m(ie),{as:"div",key:o.question,class:"pt-6"},{default:h(({open:y})=>[a("dt",null,[u(m(ue),{class:"flex w-full items-start justify-between text-left text-gray-900"},{default:h(()=>[a("span",ge,_(o.question),1),a("span",be,[y?(f(),B(m(oe),{key:1,class:"size-6","aria-hidden":"true"})):(f(),B(m(ne),{key:0,class:"size-6","aria-hidden":"true"}))])]),_:2},1024)]),u(m(ce),{as:"dd",class:"mt-2 pr-12"},{default:h(()=>[a("p",xe,_(o.answer),1)]),_:2},1024)]),_:2},1024)),64))])])])])]),u(Z,{class:"mb-8"})]),u(c,{class:"lg:hidden",show:m(e),onClose:t[1]||(t[1]=o=>q(e)?e.value=!1:e=!1)},{default:h(()=>[a("div",we,[u(j,{class:"mx-auto w-full"})]),t[4]||(t[4]=a("p",{class:"mt-6 text-base font-medium leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. ",-1)),a("div",ke,[t[3]||(t[3]=a("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),a("div",_e,[(f(),g(k,null,D(l,o=>u(n,{key:o.name,href:o.href,disabled:o.current,class:O(["block py-1 text-sm",o.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:h(()=>[M(_(o.name),1)]),_:2},1032,["href","disabled","class"])),64))])])]),_:1},8,["show"]),a("aside",Le,[a("div",Te,[u(j,{class:"mx-auto w-full"})]),t[6]||(t[6]=a("p",{class:"mt-6 text-base font-medium leading-normal text-gray-600"}," LatinTutorial strives to make learning all aspect of Latin clear and concise. ",-1)),a("div",Se,[t[5]||(t[5]=a("h4",{class:"text-sm font-bold uppercase text-gray-500"},"Learn More",-1)),a("div",De,[(f(),g(k,null,D(l,o=>u(n,{key:o.name,href:o.href,disabled:o.current,class:O(["block py-1 text-sm",o.current?"font-bold text-gray-900":"font-medium text-gray-600 hover:text-gray-900 hover:underline"])},{default:h(()=>[M(_(o.name),1)]),_:2},1032,["href","disabled","class"])),64))])])])]),_:1})],64)}}};export{it as default};
