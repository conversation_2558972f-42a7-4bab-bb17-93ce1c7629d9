import{_ as N}from"./AppLayout-33f062bc.js";import{_ as R}from"./Breadcrumbs-c96e9207.js";import{p as h}from"./pluralize-d25a928b.js";import{e as x,l as W,i as H,a1 as I,o as a,c as b,w as _,b as c,a as t,t as r,j as y,u as e,k as C,q as U,d,n as V,f as w,h as S,P as k,F as $,W as z}from"./app-f0078ddb.js";import{D as L}from"./datetime-8ddd27a0.js";import{P as F}from"./Promotion-3eee0057.js";import M from"./MobileSidebar-abd0789e.js";import{_ as T}from"./Footer-0988dcd8.js";import{r as q}from"./InformationCircleIcon-716f3ffb.js";import{r as G}from"./StarIcon-155a2a28.js";import{r as J}from"./ChevronRightIcon-a926c707.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */import"./ChevronRightIcon-0e7ec64c.js";const K={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Q={class:"px-4 py-8 sm:px-8"},X={class:"flex flex-row items-center justify-between"},Y={class:"mt-4 w-full py-6"},Z={"aria-labelledby":"Description"},tt=["src"],et={class:"w-full py-6"},ot={class:"flex flex-col font-intro"},st={class:"flex flex-row"},rt={class:"grow"},nt={class:"font-intro text-4xl font-bold text-gray-900"},it={class:"mt-2 font-intro text-xl font-bold text-gray-900"},lt={class:"mt-2 font-intro text-base font-medium text-gray-600"},at={class:"grid grid-cols-1 gap-2 border-b border-gray-300 pb-4 font-semibold text-gray-600 sm:mt-4 sm:grid-cols-3"},ct={class:"text-center sm:text-left"},dt={class:"text-center sm:text-center"},ut={class:"text-center sm:text-right"},mt={class:"mt-8"},ft={class:"divide-7 flex flex-col"},pt={class:"flex grow flex-col font-intro"},ht=["textContent"],_t={class:"mt-2 line-clamp-2 flex items-center text-base leading-5 font-semibold text-gray-500"},xt={class:"ml-8"},gt={class:"ml-8"},wt={class:"mt-2 font-medium text-gray-700"},kt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},vt={key:0,class:"mb-8"},bt={class:"flex flex-col justify-between border-b border-gray-300 pb-8"},yt={class:"text-sm font-bold text-gray-500 uppercase"},Ct={key:0},St={key:1},$t={class:"py-4 transition duration-150 ease-in-out"},Lt={class:"flex grow flex-col font-intro"},jt=["textContent"],Dt={class:"mt-1 text-sm font-medium text-gray-400"},Pt={class:"mt-2 flex items-center text-base leading-5 font-semibold text-gray-600"},Et={key:0,class:"line-clamp-2"},Ot={key:1,class:"line-clamp-2 text-gray-400"},At={class:"self-center rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-200"},Bt={key:0,class:"mt-2 text-sm font-bold text-indigo-500"},Nt={"aria-labelledby":"Works"},Rt={class:"mt-4 flex flex-col items-center font-intro"},Wt=["src"],Ht={class:"flex grow flex-col"},It=["textContent"],Ut=["textContent"],Vt=["textContent"],je={__name:"Index",props:{work:Object,books:Array,collections:Array,recentSection:Object,firstSection:Object,works:Array},setup(n){const i=n,j=[{name:"Read",href:"/read",current:!1},{name:i.work.name,href:"#",current:!0}];let u=x(!1),m=x(i.books);m.value=m.value.sort((o,l)=>o.book-l.book);let g=x(0);m.value.forEach(o=>{g.value+=o.token_count});let p=x(i.work.is_pinned);const D=o=>o.book==0?"Preface":i.work.l1+" "+o.book+" - "+o.subtitle,v=W(()=>{let o=0;return m.value.forEach(l=>{o+=l.verse_count}),o}),P=o=>o.sections.length>1?i.work.url+"/"+o.url:i.work.url+"/"+o.url+"/"+o.sections[0].line_start+"-"+o.sections[0].line_end,E=o=>o.chapter?route("read.section-chapter",{author:i.work.author.url,work:i.work.url,book:i.work.l1.toLowerCase()+"-"+o.book,chapter:i.work.l2.toLowerCase()+"-"+o.chapter,line_start:o.line_start,line_end:o.line_end}):route("read.section",{author:i.work.author.url,work:i.work.url,book:i.work.l1.toLowerCase()+"-"+o.book,line_start:o.line_start,line_end:o.line_end});let f=i.recentSection?i.recentSection:i.firstSection;const O=()=>{p.value=!p.value,z.post("/api/toggle-work-pinned",{work_id:i.work.id},{preserveState:!0,preserveScroll:!0,onError:()=>{p.value=!p.value}})};return(o,l)=>{const A=H("Head"),B=I("tippy");return a(),b(N,null,{default:_(()=>[c(A,null,{default:_(()=>[t("title",null,r(n.work.name),1)]),_:1}),t("main",K,[t("div",Q,[t("div",X,[c(R,{class:"lg:col-span-9 xl:grid-cols-10",pages:j}),c(e(q),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:l[0]||(l[0]=s=>y(u)?u.value=!e(u):u=!e(u))})]),t("div",Y,[t("section",Z,[t("img",{class:"overflow-hidden rounded-2xl bg-white",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/${n.work.image_name}`,alt:""},null,8,tt)]),t("section",et,[t("div",ot,[t("div",st,[t("div",rt,[t("h1",nt,r(n.work.name),1)]),e(C)().props.authenticated?U((a(),d("div",{key:0,onClick:l[1]||(l[1]=s=>O()),class:"group flex h-8 w-8 cursor-pointer items-center justify-center rounded-full px-1 transition duration-150 ease-in-out hover:bg-green-100"},[c(e(G),{class:V(["h-6 w-6 stroke-current transition duration-150 ease-in-out group-hover:text-green-600",e(p)?"fill-current text-green-600":"fill-none text-slate-600"])},null,8,["class"])])),[[B,{content:e(p)?"Unpin this work":"Pin this work",placement:"bottom",trigger:"mouseenter",hideOnClick:!1}]]):w("",!0)]),t("h1",it,r(n.work.author.name),1),t("p",lt,r(n.work.description),1)])]),t("section",at,[t("div",ct,r(`${e(m).filter(s=>s.book>0).length} ${e(h)(n.work.l1,e(m).filter(s=>s.book>0)).toLowerCase()}`),1),t("div",dt,r(v.value.toLocaleString()+" "+e(h)(n.work.l4,v.value).toLocaleLowerCase()),1),t("div",ut,r(`${e(g).toLocaleString()} ${e(h)("word",e(g)).toLowerCase()}`),1)]),t("section",mt,[t("div",ft,[(a(!0),d($,null,S(e(m),s=>(a(),d("div",{key:s.id,class:"transition duration-150 ease-in-out"},[c(e(k),{href:P(s),class:"group flex cursor-pointer flex-row items-center px-2 py-4 transition duration-250 ease-in-out hover:bg-slate-50 sm:px-4"},{default:_(()=>[t("div",pt,[t("h3",{class:"line-clamp-1 text-lg leading-5 font-bold text-gray-900",textContent:r(D(s))},null,8,ht),t("p",_t,[t("span",null,r(s.sections.length.toLocaleString())+" "+r(e(h)("section",s.token_count)),1),t("span",xt,r(s.verse_count.toLocaleString())+" "+r(e(h)(n.work.l4.toLowerCase(),s.verse_count)),1),t("span",gt,r(s.token_count.toLocaleString())+" "+r(e(h)("word",s.token_count)),1)]),t("p",wt,r(s.description),1)])]),_:2},1032,["href"])]))),128))])])])]),c(T)]),c(M,{class:"lg:hidden",show:e(u),"recent-section":n.recentSection,work:n.work,collections:n.collections,works:n.works,"section-hero":e(f),onClose:l[2]||(l[2]=s=>y(u)?u.value=!1:u=!1)},null,8,["show","recent-section","work","collections","works","section-hero"]),t("aside",kt,[e(C)().props.authenticated?w("",!0):(a(),d("section",vt,[c(F)])),t("div",bt,[t("h5",yt,[n.recentSection?(a(),d("span",Ct,"Most Recent Section")):(a(),d("span",St,"Start Reading"))]),t("div",$t,[c(e(k),{class:"group flex cursor-pointer flex-row items-center py-4 transition duration-250 ease-in-out",href:E(e(f))},{default:_(()=>[t("div",Lt,[t("h3",{class:"line-clamp-1 text-lg leading-5 font-bold text-gray-900",textContent:r(e(f).name)},null,8,jt),t("span",Dt,r(e(f).token_count)+" words",1),t("p",Pt,[e(f).description?(a(),d("span",Et,r(e(f).description),1)):(a(),d("span",Ot,r(e(f).verse),1))])]),t("div",At,[c(e(J),{class:"h-8 w-8 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])]),_:1},8,["href"])]),n.recentSection?(a(),d("p",Bt," Last visited on "+r(e(L).fromISO(n.recentSection.last_accessed).toLocaleString(e(L).DATE_MED)),1)):w("",!0)]),t("section",Nt,[l[3]||(l[3]=t("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Check out other works ",-1)),t("div",Rt,[(a(!0),d($,null,S(n.works,s=>(a(),b(e(k),{key:s.id,href:`/read/${s.author.url}/${s.url}`,class:"group flex w-full cursor-pointer flex-row items-center rounded-lg px-2 py-4 transition duration-150 ease-in-out hover:bg-slate-100"},{default:_(()=>[t("img",{class:"mr-2 h-16 w-16 rounded-full",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${s.image}`,alt:""},null,8,Wt),t("div",Ht,[t("h4",{class:"font-intro text-lg leading-5 font-bold text-gray-900",textContent:r(s.name)},null,8,It),t("p",{class:"text-xs font-semibold text-gray-500",textContent:r(s.author.name)},null,8,Ut),t("p",{class:"mt-1 line-clamp-2 text-xs font-medium text-gray-700",textContent:r(s.description)},null,8,Vt)])]),_:2},1032,["href"]))),128))])])])]),_:1})}}};export{je as default};
