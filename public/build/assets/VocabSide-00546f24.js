import{e as y,p as h,i as _,o as c,d as m,b as n,w as v,a as e,g as u,q as l,v as d,u as i,j as r,f as k,W as w}from"./app-f0078ddb.js";import{_ as V}from"./ButtonItem-718c0517.js";import{_ as U}from"./CopyToClipboard-21badf5d.js";import{r as f}from"./CheckCircleIcon-d86d1232.js";import{r as g}from"./StarIcon-155a2a28.js";/* empty css            */import"./clipboard-a66b13b3.js";const C={key:0,class:"mt-4"},j={key:1},S={class:"text-sm text-gray-600 font-medium"},L={"aria-labelledby":"filter videos"},B={class:"pt-8"},N={class:"grid grid-cols-1 divide-y"},A={key:0,class:"ml-4 grid grid-cols-2 gap-3 py-4"},F={class:"relative flex items-center"},T={class:"flex h-5 items-center"},W={class:"ml-3 text-sm"},q={for:"learned",class:"font-medium text-gray-700"},z={class:"relative flex items-center"},D={class:"flex h-5 items-center"},E={class:"ml-3 text-sm"},G={for:"unlearned",class:"font-medium text-gray-700"},M={class:"relative flex items-center"},P={class:"flex h-5 items-center"},R={class:"ml-3 text-sm"},Y={for:"starred",class:"font-medium text-gray-700"},H={class:"relative flex items-center"},I={class:"flex h-5 items-center"},J={class:"ml-3 text-sm"},K={for:"unstarred",class:"font-medium text-gray-700"},O={class:"grid grid-cols-1 divide-y"},Q={class:"ml-4 grid grid-cols-2 gap-3 py-4"},X={class:"relative flex items-center"},Z={class:"flex h-5 items-center"},$={class:"relative flex items-center"},ee={class:"flex h-5 items-center"},se={class:"relative flex items-center"},te={class:"flex h-5 items-center"},oe={class:"relative flex items-center"},ie={class:"flex h-5 items-center"},le={class:"relative flex items-center"},de={class:"flex h-5 items-center"},re={class:"relative flex items-center"},ne={class:"flex h-5 items-center"},ae={class:"grid grid-cols-1 divide-y"},ce={class:"ml-4 grid grid-cols-2 gap-3 py-4"},me={class:"relative flex items-center"},ue={class:"flex h-5 items-center"},ve={class:"relative flex items-center"},xe={class:"flex h-5 items-center"},fe={class:"relative flex items-center"},ge={class:"flex h-5 items-center"},be={class:"relative flex items-center"},pe={class:"flex h-5 items-center"},ye={class:"relative flex items-center"},he={class:"flex h-5 items-center"},Le={__name:"VocabSide",props:{currentUrl:String,filters:Array,clipboardList:String,subscribed:Boolean},setup(a){const x=a;let s=y(x.filters);const b=()=>{s.value=[]};return h(()=>s,()=>{w.get(x.currentUrl,{tab:"vocab",filters:s.value},{only:["vocab","tab","filters"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),(_e,t)=>{const p=_("Link");return c(),m("div",null,[a.subscribed?(c(),m("div",C,[n(U,{data:a.clipboardList,message:"Copied",class:"w-full"},{default:v(()=>[n(V,{size:"sm",color:"lightGray",class:"w-full"},{default:v(()=>t[15]||(t[15]=[u("Copy This List to Your Clipboard ")])),_:1})]),_:1},8,["data"])])):(c(),m("div",j,[e("p",S,[t[17]||(t[17]=u(" Please ")),n(p,{class:"hover:underline text-blue-600 hover:text-blue-700",href:"/login"},{default:v(()=>t[16]||(t[16]=[u("login or make an account ")])),_:1}),t[18]||(t[18]=u(" to work further with this vocabulary list. "))])])),e("section",L,[e("div",B,[e("div",{class:"flex text-sm"},[t[19]||(t[19]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold uppercase text-gray-500"},"Filters")],-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold uppercase text-blue-500 opacity-100",onClick:b}," Clear ")])]),e("div",N,[a.subscribed?(c(),m("div",A,[e("div",F,[e("div",T,[l(e("input",{id:"learned","onUpdate:modelValue":t[0]||(t[0]=o=>r(s)?s.value=o:s=o),value:"learned",name:"learned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),e("div",W,[e("label",q,[n(i(f),{class:"h-6 w-6 text-blue-500 stroke-2","aria-hidden":"true"})])])]),e("div",z,[e("div",D,[l(e("input",{id:"unlearned","onUpdate:modelValue":t[1]||(t[1]=o=>r(s)?s.value=o:s=o),value:"unlearned",name:"unlearned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),e("div",E,[e("label",G,[n(i(f),{class:"text-gray-500 h-6 w-6 stroke-2","aria-hidden":"true"})])])]),e("div",M,[e("div",P,[l(e("input",{id:"starred","onUpdate:modelValue":t[2]||(t[2]=o=>r(s)?s.value=o:s=o),value:"starred",name:"starred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),e("div",R,[e("label",Y,[n(i(g),{class:"h-6 w-6 text-green-600 stroke-2","aria-hidden":"true"})])])]),e("div",H,[e("div",I,[l(e("input",{id:"unstarred","onUpdate:modelValue":t[3]||(t[3]=o=>r(s)?s.value=o:s=o),value:"unstarred",name:"unstarred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),e("div",J,[e("label",K,[n(i(g),{class:"text-gray-500 h-6 w-6 stroke-2","aria-hidden":"true"})])])])])):k("",!0)]),e("div",O,[e("div",Q,[e("div",X,[e("div",Z,[l(e("input",{id:"basic","onUpdate:modelValue":t[4]||(t[4]=o=>r(s)?s.value=o:s=o),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[20]||(t[20]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"basic",class:"font-semibold text-blue-600"},"basic ")],-1))]),e("div",$,[e("div",ee,[l(e("input",{id:"intermediate","onUpdate:modelValue":t[5]||(t[5]=o=>r(s)?s.value=o:s=o),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[21]||(t[21]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"intermediate",class:"font-semibold text-rose-600"},"intermediate ")],-1))]),e("div",se,[e("div",te,[l(e("input",{id:"advanced","onUpdate:modelValue":t[6]||(t[6]=o=>r(s)?s.value=o:s=o),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[22]||(t[22]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"advanced",class:"font-semibold text-orange-600"},"advanced ")],-1))]),e("div",oe,[e("div",ie,[l(e("input",{id:"uncommon","onUpdate:modelValue":t[7]||(t[7]=o=>r(s)?s.value=o:s=o),value:"uncommon",name:"uncommon",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[23]||(t[23]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"uncommon",class:"font-semibold text-violet-600"},"uncommon ")],-1))]),e("div",le,[e("div",de,[l(e("input",{id:"core","onUpdate:modelValue":t[8]||(t[8]=o=>r(s)?s.value=o:s=o),value:"core",name:"core",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[24]||(t[24]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"core",class:"font-semibold text-gray-600"},"core ")],-1))]),e("div",re,[e("div",ne,[l(e("input",{id:"noncore","onUpdate:modelValue":t[9]||(t[9]=o=>r(s)?s.value=o:s=o),value:"noncore",name:"noncore",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[25]||(t[25]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"noncore",class:"font-semibold text-gray-600"},"non-core ")],-1))])])]),e("div",ae,[e("div",ce,[e("div",me,[e("div",ue,[l(e("input",{id:"nouns","onUpdate:modelValue":t[10]||(t[10]=o=>r(s)?s.value=o:s=o),value:"nouns",name:"nouns",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[26]||(t[26]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"nouns",class:"font-semibold text-gray-600"},"nouns ")],-1))]),e("div",ve,[e("div",xe,[l(e("input",{id:"verbs","onUpdate:modelValue":t[11]||(t[11]=o=>r(s)?s.value=o:s=o),value:"verbs",name:"verbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[27]||(t[27]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"verbs",class:"font-semibold text-gray-600"},"verbs ")],-1))]),e("div",fe,[e("div",ge,[l(e("input",{id:"adjectives","onUpdate:modelValue":t[12]||(t[12]=o=>r(s)?s.value=o:s=o),value:"adjectives",name:"adjectives",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[28]||(t[28]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adjectives",class:"font-semibold text-gray-600"},"adjectives ")],-1))]),e("div",be,[e("div",pe,[l(e("input",{id:"adverbs","onUpdate:modelValue":t[13]||(t[13]=o=>r(s)?s.value=o:s=o),value:"adverbs",name:"adverbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[29]||(t[29]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adverbs",class:"font-semibold text-gray-600"},"adverbs ")],-1))]),e("div",ye,[e("div",he,[l(e("input",{id:"other","onUpdate:modelValue":t[14]||(t[14]=o=>r(s)?s.value=o:s=o),value:"other",name:"other",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(s)]])]),t[30]||(t[30]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"other",class:"font-semibold text-gray-600"},"other ")],-1))])])])])])])}}};export{Le as default};
