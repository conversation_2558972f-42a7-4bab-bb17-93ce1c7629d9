import{d as p}from"./vuedraggable.umd-aab17b5c.js";import{_ as b}from"./ButtonItem-718c0517.js";import A from"./NextButtonSmall-9e6ffefc.js";import{_ as V}from"./_plugin-vue_export-helper-c27b6911.js";import{r as $}from"./CheckCircleIcon-d86d1232.js";import{r as L}from"./XCircleIcon-63af2b2a.js";import{e as v,A as B,o as d,d as u,a as r,r as y,b as n,w as m,u as c,F as T,n as w,g as _}from"./app-f0078ddb.js";/* empty css            */const W={class:"flex-1"},j={class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900"},q={class:"text-center text-lg font-medium text-gray-600"},M={class:"relative mt-12 grid w-full grid-cols-1 justify-items-center sm:px-16"},S=["onClick"],H=["innerHTML"],N=["onClick"],z=["innerHTML"],D={class:"mt-8 text-center"},K={key:0,class:"flex justify-center text-blue-500"},F={key:1,class:"flex justify-center text-red-600"},I={key:1,class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8 xl:px-10"},U={__name:"DragWithWords",props:{options:Array,questionKey:String,stem:String,instructions:String,isAnswer:Boolean,isCorrect:Boolean,disabled:Boolean},emits:["submit","next-question"],setup(a,{emit:k}){const o=a,h=k,i=v([]),l=v([]);B(()=>{i.value=o.options.map((t,e)=>({id:e,data:t}))});function C(){h("submit",{answer:l.value.map(t=>t.data),key:o.questionKey,isArray:!1})}function x(t,e){e?(l.value=l.value.filter(s=>s.id!==t.id),i.value.push(t)):(i.value=i.value.filter(s=>s.id!==t.id),l.value.push(t))}function g(t){return o.isAnswer?o.questionKey.includes(t)?"bg-teal-100 border-teal-400":"bg-red-100 border-red-400":"bg-gray-100 hover:bg-gray-200 border-gray-500 cursor-move"}return(t,e)=>(d(),u("div",W,[r("h1",j,[y(t.$slots,"stem",{},void 0,!0)]),r("h3",q,[y(t.$slots,"instructions",{},void 0,!0)]),r("div",M,[n(c(p),{modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=s=>l.value=s),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"flex h-24 w-full items-center justify-center space-x-1 rounded-2xl border-2 border-dashed border-gray-400 py-2","ghost-class":"ghost",animation:200,disabled:o.disabled||o.isAnswer},{item:m(({element:s})=>[r("div",{class:w(["inline-flex items-center rounded-2xl border-2 px-4 py-1 text-2xl font-medium text-gray-800 shadow-sm select-none",g(s.data)]),onClick:f=>x(s,!0)},[r("span",{innerHTML:s.data},null,8,H)],10,S)]),_:1},8,["modelValue","disabled"]),e[4]||(e[4]=r("div",{class:"my-8 w-full border border-gray-400"},null,-1)),n(c(p),{modelValue:i.value,"onUpdate:modelValue":e[1]||(e[1]=s=>i.value=s),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"flex h-24 w-full items-start justify-center space-x-1","ghost-class":"ghost",animation:200,disabled:o.disabled||o.isAnswer},{item:m(({element:s})=>[r("div",{class:w(["inline-flex items-center rounded-2xl border-2 px-4 py-1 text-2xl font-medium text-gray-800 shadow-sm select-none",g(s.data)]),onClick:f=>x(s,!1)},[r("span",{innerHTML:s.data},null,8,z)],10,N)]),_:1},8,["modelValue","disabled"])]),r("div",D,[a.isAnswer?(d(),u(T,{key:0},[a.isCorrect?(d(),u("div",K,[n(c($),{class:"mr-2 h-10 w-10"}),e[5]||(e[5]=r("p",{class:"text-2xl font-semibold"},"Correct",-1))])):(d(),u("div",F,[n(c(L),{class:"mr-2 h-10 w-10"}),e[6]||(e[6]=r("p",{class:"text-2xl font-semibold"},"Incorrect",-1))])),n(A,{"is-answer":a.isAnswer,"is-correct":a.isCorrect,disabled:a.disabled,onNextQuestion:e[2]||(e[2]=s=>t.$emit("next-question"))},null,8,["is-answer","is-correct","disabled"])],64)):(d(),u("div",I,[n(b,{size:"lg",class:"w-full",color:"white",onClick:e[3]||(e[3]=()=>{l.value=[],i.value=o.options.map((s,f)=>({id:f,data:s}))})},{default:m(()=>e[7]||(e[7]=[_("Clear")])),_:1}),n(b,{size:"lg",class:"w-full",color:"pink",onClick:C},{default:m(()=>e[8]||(e[8]=[_("Submit")])),_:1})]))])]))}},Y=V(U,[["__scopeId","data-v-38271215"]]);export{Y as default};
