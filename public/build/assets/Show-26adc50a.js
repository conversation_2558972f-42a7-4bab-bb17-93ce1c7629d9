import{_ as O}from"./AppLayout-33f062bc.js";import{_ as M,r as N}from"./Breadcrumbs-c96e9207.js";import{u as D}from"./useIntersect-6e15125e.js";import{p as I}from"./pluralize-d25a928b.js";import{D as w}from"./datetime-8ddd27a0.js";import"./clipboard-a66b13b3.js";import{e as p,l as V,i as C,o as a,c as B,w as c,b as s,a as t,t as i,u as l,d,h as E,F as H,g as S,f as _,n as R}from"./app-f0078ddb.js";import"./index-b0adb136.js";import{_ as T}from"./Footer-0988dcd8.js";import{r as b}from"./ChevronRightIcon-a926c707.js";import{r as z}from"./ChatBubbleBottomCenterTextIcon-4924c50e.js";import{r as A}from"./PencilSquareIcon-048eb348.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";/* empty css            */const F={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},P={class:"p-8"},U={class:"mt-4 w-full py-6"},G={class:"flex flex-row font-intro"},q=["src"],J={class:"grow ml-4 self-center"},K={class:"text-4xl font-bold text-gray-900"},Q={class:"mt-1 text-2xl font-bold text-gray-600"},W={class:"mt-4 font-intro font-medium text-lg text-gray-900"},X={class:"mt-4 grid grid-cols-3 text-gray-600 border-b border-gray-300 pb-4 font-semibold"},Y={class:"text-left"},Z={class:"text-center"},tt={class:"text-right"},et={class:"mt-8"},ot={class:"grid grid-cols-1 gap-4"},rt={class:"divide-y"},st={class:"flex grow flex-col font-intro"},lt=["textContent"],nt={class:"mt-2 line-clamp-2 flex items-center text-base font-semibold leading-5 text-gray-500"},it={class:"self-center group-hover:bg-sky-100 transition duration-150 ease-in-out rounded-full p-1"},at={class:"bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},ct={class:"grid grid-cols-2 gap-4 text-gray-500 text-sm font-medium"},dt={key:0,class:"flex"},ut={class:"mt-8 flex flex-col justify-between border-b border-gray-300 pb-8"},mt={class:"text-sm font-bold uppercase text-gray-500"},ft={key:0},ht={key:1},xt={class:"hover:bg-slate-100 mt-2 rounded-xl transition duration-150 ease-in-out px-2 py-4"},gt={class:"flex grow flex-col font-intro"},pt=["textContent"],_t={class:"mt-2 line-clamp-2 flex items-center text-base font-semibold leading-5 text-gray-600"},bt={class:"self-center group-hover:bg-sky-100 transition duration-150 ease-in-out rounded-full p-1"},vt={key:0,class:"mt-2 text-sm font-bold text-sky-600"},kt={class:"mt-4 grid grid-cols-2 gap-8"},ce={__name:"Show",props:{collection:Object,author:Object,sections:Object,assignments:Object,nextCollection:String,previousCollection:String,recentSection:Object},setup(e){const o=e,L=[{name:"Read",href:"/read",current:!1},{name:o.collection.work.name,href:"/read/"+o.author.url+"/"+o.collection.work.url,current:!1},{name:"Collections",href:"/read/"+o.author.url+"/"+o.collection.work.url+"/collections",current:!1},{name:o.collection.name,href:"#",current:!0}],v=r=>r.chapter?o.collection.work.l2+" "+r.chapter:"Lines "+r.line_start+"-"+r.line_end,k=r=>route("read.collection.show",{author:o.author.url,work:o.collection.work.url,collection:r}),y=r=>r.chapter?route("read.section-chapter",{author:o.author.url,work:o.book.work.url,book:o.book.work.l1.toLowerCase()+"-"+o.book.book,chapter:o.book.work.l2.toLowerCase()+"-"+r.chapter,line_start:r.line_start,line_end:r.line_end}):route("read.section",{author:o.author.url,work:o.collection.work.url,book:o.collection.work.l1.toLowerCase()+"-"+r.book.book,line_start:r.line_start,line_end:r.line_end});let h=o.recentSection?o.recentSection:o.sections[0];const x=p(null);let m=p(o.sections),g=p(m.value.data);const j=V(()=>m.value.next_page_url!==null);return x!==null&&D(x,()=>{j.value&&axios.get(m.value.next_page_url).then(r=>{g.value=[...g.value,...r.data.data],m.value=r.data})},{rootMargin:"0px 0px 250px 0px"}),(r,n)=>{const $=C("Head"),u=C("Link");return a(),B(O,null,{default:c(()=>[s($,null,{default:c(()=>[t("title",null,`
        `+i(e.collection.name)+`
      `,1)]),_:1}),t("main",F,[t("div",P,[s(M,{class:"lg:col-span-9 xl:grid-cols-10",pages:L}),t("div",U,[t("div",G,[t("img",{class:"mx-auto h-24 w-24 rounded-full shadow-md",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+e.collection.image,alt:""},null,8,q),t("div",J,[t("h1",K,i(e.collection.name),1),t("h3",Q,i(e.collection.reference),1)])]),t("div",W,i(e.collection.description),1)]),t("div",X,[t("div",Y,i(e.sections.total.toLocaleString())+" sections ",1),t("div",Z,i(e.collection.verses_count.toLocaleString()+" "+l(I)(e.collection.work.l4,e.collection.verses_count).toLocaleLowerCase()),1),t("div",tt,i(e.collection.token_count.toLocaleString()+" words"),1)]),t("div",et,[t("div",ot,[n[0]||(n[0]=t("div",{class:"flex flex-row items-end"},[t("div",{class:"grow"},[t("h5",{class:"text-sm font-bold uppercase text-gray-500"}," All Sections ")])],-1)),t("div",rt,[(a(!0),d(H,null,E(l(g),f=>(a(),d("div",{key:f.id,class:"py-3 hover:bg-gray-50 transition duration-150 ease-in-out px-2"},[s(u,{href:y(f),class:"duration-250 flex flex-row items-center rounded-lg py-4 transition ease-in-out group cursor-pointer"},{default:c(()=>[t("div",st,[t("h3",{class:"text-lg font-bold leading-5 text-gray-900",textContent:i(v(f))},null,8,lt),t("p",nt,i(f.description),1)]),t("div",it,[s(l(b),{class:"h-8 w-8 text-gray-400 stroke-2 group-hover:text-sky-700 transition duration-150 ease-in-out"})])]),_:2},1032,["href"])]))),128))]),t("div",{ref_key:"landmark",ref:x},null,512)])])]),s(T)]),t("aside",at,[t("div",ct,[e.previousCollection?(a(),d("div",dt,[s(u,{class:"flex items-center hover:text-gray-700 transition ease-in-out duration-150",href:k(e.previousCollection)},{default:c(()=>[s(l(N),{class:"mr-1 inline h-5 w-5 shrink-0","aria-hidden":"true"}),n[1]||(n[1]=S(" Previous "))]),_:1},8,["href"])])):_("",!0),e.nextCollection?(a(),d("div",{key:1,class:R(["flex justify-end",{"col-span-2":!e.previousCollection}])},[s(u,{class:"flex items-center hover:text-gray-700 transition ease-in-out duration-150",href:k(e.nextCollection)},{default:c(()=>[n[2]||(n[2]=S(" Next ")),s(l(b),{class:"ml-1 inline h-5 w-5 shrink-0","aria-hidden":"true"})]),_:1},8,["href"])],2)):_("",!0)]),t("div",ut,[t("h5",mt,[e.recentSection?(a(),d("span",ft,"Most Recent Section")):(a(),d("span",ht,"Start Reading"))]),t("div",xt,[s(u,{class:"duration-250 flex flex-row items-center py-4 transition ease-in-out group cursor-pointer",href:y(l(h))},{default:c(()=>[t("div",gt,[t("h3",{class:"text-lg font-bold leading-5 text-gray-900 line-clamp-1",textContent:i(v(l(h)))},null,8,pt),t("p",_t,i(l(h).description),1)]),t("div",bt,[s(l(b),{class:"h-8 w-8 text-gray-400 stroke-2 group-hover:text-sky-700 transition duration-150 ease-in-out"})])]),_:1},8,["href"])]),e.recentSection?(a(),d("p",vt," Last visited on "+i(l(w).fromISO(e.recentSection.last_accessed).toLocaleString(l(w).DATE_MED)),1)):_("",!0)]),n[5]||(n[5]=t("h5",{class:"text-sm font-bold uppercase text-gray-500 mt-8"},"Practice",-1)),t("div",kt,[s(u,{href:`/practice/vocabulary/attempt?sections[]=${e.collection.link.join("&sections[]=")}`,class:"rounded-xl shadow-lg bg-orange-100 hover:bg-orange-200 transition duration-150 h-28 flex items-center justify-center flex-col space-y-2"},{default:c(()=>[s(l(z),{class:"w-8 h-8 text-orange-600"}),n[3]||(n[3]=t("h3",{class:"text-xl font-semibold text-orange-700"},"Vocabulary",-1))]),_:1},8,["href"]),s(u,{href:`/practice/grammar/c/attempt?sections[]=${e.collection.link.join("&sections[]=")}`,class:"rounded-xl shadow-lg bg-purple-100 hover:bg-purple-200 transition duration-150 h-28 flex items-center justify-center flex-col space-y-2"},{default:c(()=>[s(l(A),{class:"w-8 h-8 text-purple-600"}),n[4]||(n[4]=t("h3",{class:"text-xl font-semibold text-purple-700"},"Grammar",-1))]),_:1},8,["href"])])])]),_:1})}}};export{ce as default};
