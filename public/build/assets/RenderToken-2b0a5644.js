import{r as u}from"./removePunctuation-702d8a66.js";import{k as B,o as t,d as l,F as x,h as f,n as h,a as y,f as b,t as P,r as D}from"./app-f0078ddb.js";const I={key:0},q={key:0},j=["onClick","innerHTML"],F={key:0},V={key:1,class:"leading-relaxed"},Z=["onClick","innerHTML"],O={key:0},R=["onClick","innerHTML"],_={key:0},G={key:0,class:"mr-4 hidden self-center text-lg text-gray-500 sm:block"},Q={__name:"RenderToken",props:{verse:Object,highlighted:String,tokens:Array,caseColor:Boolean,selectingText:Boolean,showLineNumbers:Boolean,showAllLineNumbers:<PERSON>olean,hasProse:<PERSON>olean,splitSections:<PERSON><PERSON><PERSON>,isEditing:<PERSON><PERSON><PERSON>},emits:["update:highlighted"],setup(i,{emit:H}){const p=i,T=H,m=s=>{var n=[];if(s.length>1)s.forEach(e=>{var a=e.text.split(" ");a.forEach((c,r)=>{n.push({verseId:e.id,token:c,index:r})})});else return s.text.split(" ");return n},S=B().props.user?B().props.user.case_colors:null,w=(s,n,e)=>{var a=u(s);p.selectingText||T("update:highlighted",{token:a,index:n,verse:e})},o=s=>{const n=/^[^\w\s]+/,e=s.match(n);return e?e[0]:""},g=s=>{let n="",e=!1;for(let a=0;a<s.length;a++)e&&s[a].match(/[^a-zA-Z]/)?n+=s[a]:s[a].match(/[a-zA-Z]/)&&(e=!0);return n},C=(s,n,e)=>!p.selectingText&&p.highlighted==u(s)+"["+e+"]["+n+"]",$=()=>{T("update:highlighted",{token:"",index:"",verse:""})},z=s=>{var n=s.toString().slice(-1);if(p.showLineNumbers&&(n==0||n==5)||p.showAllLineNumbers)return!0},L=(s,n,e)=>{if(!p.caseColor)return`<span class="text-gray-900">${s}</span>`;var a=p.tokens.find(v=>v.token===u(s)&&v.verse_id===e&&v.line_order===n);if(!a)return s.trim()==="..."?'<span class="text-gray-500">...</span>':`<span class="text-gray-500">${s}</span>`;const c=(v,M)=>{const A=S.find(N=>N.case_id===v);return A?`text-${A.color}`:`text-${M}`};var r,d;switch(!0){case a.grammar.includes("+ quĕ"):d=g(s),s=u(s).replace(/(que$)/,""),r='<span class="text-gray-500">que</span>'+d;break;case a.grammar.includes("+ vĕ"):d=g(s),s=u(s).replace(/(ve$)/,""),r='<span class="text-gray-500">ve</span>'+d;break;case a.grammar.includes("+ nĕ"):d=g(s),s=u(s).replace(/(ne$)/,""),r='<span class="text-gray-500">ne</span>'+d;break;case a.grammar.includes("+ cum"):d=g(s),s=u(s).replace(/(cum$)/,""),r='<span class="text-gray-500">cum</span>'+d;break;default:r="";break}switch(!0){case a.grammar.includes("nominative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(1,"blue-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("genitive"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(2,"emerald-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("dative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(3,"orange-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("accusative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(4,"pink-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("ablative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(5,"purple-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("vocative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(6,"fuchsia-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case a.grammar.includes("locative"):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(7,"yellow-600")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";case(a.grammar.includes("indicative")||a.grammar.includes("subjunctive")||a.grammar.includes("imperative")||a.grammar.includes("infinitive")):return'<span class="text-gray-500">'+o(s)+'</span><span class="'+c(8,"gray-900")+'">'+u(s)+"</span>"+r+'<span class="text-gray-500">'+g(s)+"</span>";default:return'<span class="text-gray-500">'+s+"</span>"+r}},E=s=>{const e=window.getSelection().toString().replace(/\u00A0/g," ");s.clipboardData.setData("text/plain",e),s.preventDefault()};return(s,n)=>(t(),l("div",{onCopy:E},[i.hasProse&&i.verse.length>1?(t(),l("div",I,[i.splitSections?(t(),l("span",q,[(t(!0),l(x,null,f(i.verse,(e,a)=>(t(),l("p",{key:e.id,class:h([{"mt-4":a>0},"leading-relaxed"])},[(t(!0),l(x,null,f(m(e),(c,r)=>(t(),l("span",{key:r,class:"inline-block"},[y("span",{class:h(["flex-1",{"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100 transition duration-250":C(c,r,e.id)}]),onClick:d=>w(c,r,e.id),innerHTML:L(c,r,e.id)},null,10,j),r!==m(e).length-1?(t(),l("span",F," ")):b("",!0)]))),128))],2))),128))])):(t(),l("p",V,[(t(!0),l(x,null,f(m(i.verse),(e,a)=>(t(),l("span",{key:a,class:"inline-block"},[y("span",{class:h({"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100 transition duration-250":C(e.token,e.index,e.verseId)}),onClick:c=>w(e.token,e.index,e.verseId,e.id),innerHTML:L(e.token,e.index,e.verseId)},null,10,Z),a!==m(i.verse).length-1?(t(),l("span",O," ")):b("",!0)]))),128))]))])):(t(),l("div",{key:1,class:h(["group flex w-full rounded-md pr-4 leading-relaxed",{"mt-4":i.verse.stanza_start==1}])},[y("span",{class:h(["rounded-lg border-2 border-white py-px",{"ml-12":i.verse.indent==1}])},[(t(!0),l(x,null,f(m(i.verse),(e,a)=>(t(),l("span",{key:a,class:"inline-block"},[y("span",{class:h({"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100 transition duration-250":C(e,a,i.verse.id)}),onClick:c=>w(e,a,i.verse.id),innerHTML:L(e,a,i.verse.id)},null,10,R),a!==m(i.verse).length-1?(t(),l("span",_," ")):b("",!0)]))),128))],2),y("div",{class:"grow",onClick:$}),y("div",{class:"group flex select-none",onClick:$},[z(i.verse.l4)?(t(),l("span",G,P(i.verse.l4),1)):b("",!0)])],2)),D(s.$slots,"default")],32))}};export{Q as _};
