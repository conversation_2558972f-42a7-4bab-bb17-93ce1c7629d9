import{o as m,d as x,a as e,e as p,p as h,i as L,c as _,w as a,b as u,t as w,j as d,u as l,F as $,h as F,q as g,v as f,g as k,W as C}from"./app-f0078ddb.js";import{_ as M}from"./AppLayout-33f062bc.js";import{_ as N}from"./Breadcrumbs-c96e9207.js";import O from"./AssignmentItemStudent-2daa0e28.js";import{u as U}from"./useInfiniteScroll-1e8e8e17.js";import{_ as D}from"./ClassroomModal-05d10768.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./datetime-8ddd27a0.js";import"./InfinitasIcon-1a3ae135.js";import"./CheckBadgeIcon-e7792a17.js";import"./useIntersect-6e15125e.js";function j(i,n){return m(),x("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})])}const H={class:"relative isolate bg-white px-6 pb-16 lg:px-8"},W={class:"py-2 md:py-8"},I={class:"flex flex-row items-center justify-between"},T={class:"py-6"},Y={class:"sm:flex sm:items-center sm:justify-between"},q={class:"sm:flex sm:space-x-5"},z={class:"shrink-0"},E=["src","alt"],P={class:"mt-4 flex flex-col sm:mt-0 sm:pt-1 sm:text-left"},R={class:"mt-2 flex w-full items-center"},G={class:"inline-block text-4xl font-bold text-gray-900 sm:text-5xl"},J={class:"grow self-end"},K={class:"ml-4 inline-block cursor-pointer text-sm font-medium text-gray-500 transition duration-150 ease-in-out hover:text-teal-600"},Q={class:"mt-2 flex gap-2"},X={class:"block inline text-3xl font-medium text-gray-600"},Z={class:"mx-2 mt-16 grid grid-cols-1 gap-8 lg:grid-cols-5"},ee={class:"order-2 -m-2 flex rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:order-1 lg:col-span-3 lg:-m-4 lg:rounded-2xl lg:p-4"},te={key:0,class:"w-full rounded-lg bg-white px-6 py-4 lg:rounded-xl"},se={key:1,class:"flex w-full flex-col items-center rounded-xl bg-white px-6 py-14 py-4 text-center text-sm sm:px-14"},oe={class:"order-1 -mt-4 lg:order-2 lg:col-span-2",id:"sticky"},le={class:"sticky top-20 rounded-xl bg-white p-2 lg:p-4"},re={class:"grid grid-cols-1 gap-3 px-4 py-4"},ie={class:"relative flex items-center"},ne={class:"flex h-5 items-center"},ae={class:"relative flex items-center"},de={class:"flex h-5 items-center"},me={class:"relative flex items-center"},ue={class:"flex h-5 items-center"},ce={class:"relative flex items-center"},pe={class:"flex h-5 items-center"},ge={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100"},et={__name:"Student",props:{team:Object,assignments:Object,classes:Array,errors:Object,filters:Array},setup(i){const n=i,S=[{name:"Classes",href:"/classes",current:!1},{name:n.team.name,href:"#",current:!0}],b=p(null);let v=p(n.assignments||{data:[],next_page_url:null});const{items:c}=U(v.value,b);let s=p(n.filters||[]);const V=()=>{s.value=[]};h(()=>s,()=>{C.get(`/classes/${n.team.slug}`,{filters:s.value},{only:["assignments, filters"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),h(()=>n.assignments,y=>{v.value=y,c.value=v.value.data},{deep:!0});let r=p(!1);const A=()=>{C.post("/api/leave-class",{team:n.team.slug},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{r.value=!1}})};return(y,t)=>{const B=L("Head");return m(),_(M,null,{default:a(()=>[u(B,null,{default:a(()=>t[7]||(t[7]=[e("title",null,"Classes",-1)])),_:1}),e("div",H,[t[16]||(t[16]=e("div",{class:"absolute inset-x-0 -top-3 -z-10 mt-32 transform-gpu overflow-hidden px-36 blur-3xl sm:mt-56","aria-hidden":"true"},[e("div",{class:"mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-indigo-400 to-sky-200 opacity-30",style:{"clip-path":`polygon(
              74.1% 44.1%,
              100% 61.6%,
              97.5% 26.9%,
              85.5% 0.1%,
              80.7% 2%,
              72.5% 32.5%,
              60.2% 62.4%,
              52.4% 68.1%,
              47.5% 58.3%,
              45.2% 34.5%,
              27.5% 76.7%,
              0.1% 64.9%,
              17.9% 100%,
              27.6% 76.8%,
              76.1% 97.7%,
              74.1% 44.1%
            )`}})],-1)),e("div",W,[e("div",I,[u(N,{class:"lg:col-span-9 xl:grid-cols-10",pages:S})])]),e("section",null,[e("div",T,[e("div",Y,[e("div",q,[e("div",z,[e("img",{class:"mx-auto h-32 w-32 rounded-full",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/${i.team.photo_url}`,alt:i.team.name},null,8,E)]),e("div",P,[e("div",R,[e("h5",G,w(i.team.name),1),e("div",J,[e("span",K,[u(l(j),{class:"h-6 w-6",onClick:t[0]||(t[0]=o=>d(r)?r.value=!0:r=!0)})])])]),e("div",Q,[e("div",X,w(i.team.owner),1)])])])])])]),e("section",null,[e("div",Z,[e("div",ee,[l(c)&&l(c).length>0?(m(),x("div",te,[t[8]||(t[8]=e("div",{class:"text-xs font-semibold uppercase leading-6 text-gray-500"}," Your Assignments ",-1)),(m(!0),x($,null,F(l(c),o=>(m(),_(O,{assignment:o,team:i.team,"due-date":!!o.due_at},null,8,["assignment","team","due-date"]))),256))])):(m(),x("div",se,t[9]||(t[9]=[e("div",null,[e("p",{class:"mt-4 font-semibold text-gray-900"},"No results found"),e("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ")],-1)]))),e("div",{ref_key:"landmark",ref:b},null,512)]),e("div",oe,[e("div",le,[e("div",{class:"flex flex-row items-center"},[t[10]||(t[10]=e("h3",{class:"text-2xl font-bold text-gray-900"},"Assignments",-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold uppercase text-blue-500 opacity-100 hover:text-blue-600",onClick:V}," Clear ")])]),t[15]||(t[15]=e("p",{class:"mt-2 text-sm text-gray-600"}," Filter your assignments to better manage what you need to and have done. ",-1)),e("div",re,[e("div",ie,[e("div",ne,[g(e("input",{id:"to-do","onUpdate:modelValue":t[1]||(t[1]=o=>d(s)?s.value=o:s=o),value:"to-do",name:"to-do",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,l(s)]])]),t[11]||(t[11]=e("div",{class:"ml-3 text-base"},[e("label",{for:"to-do",class:"cursor-pointer font-semibold text-gray-900"},"To do")],-1))]),e("div",ae,[e("div",de,[g(e("input",{id:"in-progress","onUpdate:modelValue":t[2]||(t[2]=o=>d(s)?s.value=o:s=o),value:"in-progress",name:"in-progress",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,l(s)]])]),t[12]||(t[12]=e("div",{class:"ml-3 text-base"},[e("label",{for:"in-progress",class:"cursor-pointer font-semibold text-gray-900"},"In progress")],-1))]),e("div",me,[e("div",ue,[g(e("input",{id:"completed","onUpdate:modelValue":t[3]||(t[3]=o=>d(s)?s.value=o:s=o),value:"completed",name:"completed",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,l(s)]])]),t[13]||(t[13]=e("div",{class:"ml-3 text-base"},[e("label",{for:"completed",class:"cursor-pointer font-semibold text-gray-900"},"Completed")],-1))]),e("div",ce,[e("div",pe,[g(e("input",{id:"late","onUpdate:modelValue":t[4]||(t[4]=o=>d(s)?s.value=o:s=o),value:"late",name:"late",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,l(s)]])]),t[14]||(t[14]=e("div",{class:"ml-3 text-base"},[e("label",{for:"late",class:"cursor-pointer font-semibold text-gray-900"},"Done Late")],-1))])])])])])])]),u(D,{open:l(r),onCloseModal:t[6]||(t[6]=o=>d(r)?r.value=!1:r=!1)},{title:a(()=>t[17]||(t[17]=[k(" Leave this Class ")])),icon:a(()=>[e("div",ge,[u(l(j),{class:"h-6 w-6 text-red-600","aria-hidden":"true"})])]),main:a(()=>t[18]||(t[18]=[k(" Are you sure you wish to leave this classroom? You will no longer have access to the assignments and content. ")])),actionButton:a(()=>[e("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:text-sm",onClick:t[5]||(t[5]=o=>A())}," Leave ")]),_:1},8,["open"])]),_:1})}}};export{et as default};
