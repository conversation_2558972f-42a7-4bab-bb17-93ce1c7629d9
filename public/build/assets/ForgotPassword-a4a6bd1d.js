import{C as c,o as l,c as g,w as r,b as o,u as a,m as f,a as s,d as x,t as m,f as p,s as w,q as y,x as b,g as n,n as v}from"./app-f0078ddb.js";import{_ as h}from"./InputError-7edb5cf8.js";import{_}from"./ButtonItem-718c0517.js";import{A as k}from"./AuthLayout-f6b59921.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"flex min-h-screen flex-1 flex-col justify-center py-10 sm:px-6 lg:px-8"},C={class:"mt-10 sm:mx-auto sm:w-full sm:max-w-[480px]"},V={class:"bg-white px-6 py-12 shadow-sm sm:rounded-lg sm:px-12"},B={key:0,class:"mb-4 font-medium text-sm text-green-600 font-sans"},E={class:"mt-2"},F={class:"flex items-center justify-end mt-4"},A={__name:"ForgotPassword",props:{status:String},setup(i){const e=c({email:""}),d=()=>{e.post(route("password.email"))};return(L,t)=>(l(),g(k,null,{default:r(()=>[o(a(f),{title:"Forgot Password"}),s("div",N,[t[5]||(t[5]=s("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[s("img",{class:"mx-auto h-10 w-auto",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/img/latintutorial-colosseum.svg",alt:"LatinTutorial"}),s("h2",{class:"mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"}," Forgot your password? ")],-1)),s("div",C,[s("div",V,[t[3]||(t[3]=s("div",{class:"mb-4 text-sm text-gray-600 font-sans"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),i.status?(l(),x("div",B,m(i.status),1)):p("",!0),s("form",{onSubmit:w(d,["prevent"]),class:"font=sans"},[s("div",null,[t[1]||(t[1]=s("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email address",-1)),s("div",E,[y(s("input",{id:"email",name:"email",type:"email","onUpdate:modelValue":t[0]||(t[0]=u=>a(e).email=u),required:"",autofocus:"",autocomplete:"username",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,512),[[b,a(e).email]])]),o(h,{class:"mt-2",message:a(e).errors.email},null,8,["message"])]),s("div",F,[o(_,{class:v(["w-full",{"opacity-25":a(e).processing}]),size:"sm",color:"indigo",disabled:a(e).processing},{default:r(()=>t[2]||(t[2]=[n(" Email Password Reset Link ")])),_:1},8,["class","disabled"])])],32)]),t[4]||(t[4]=s("p",{class:"mt-10 text-center text-sm text-gray-500"},[n(" Not a member? "+m(" ")+" "),s("a",{href:"/register",class:"font-semibold leading-6 text-indigo-600 hover:text-indigo-500"},"Create an account and start a 7 day free trial")],-1))])])]),_:1}))}};export{A as default};
