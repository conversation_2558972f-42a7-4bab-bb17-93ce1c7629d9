import{P as k}from"./Promotion-3eee0057.js";import{i as w,o as a,c as p,w as u,u as l,a as t,b as d,k as v,d as c,f as x,t as i,h as C,F as S}from"./app-f0078ddb.js";import{p as L}from"./pluralize-d25a928b.js";import{D as h}from"./datetime-8ddd27a0.js";import{r as H}from"./XMarkIcon-9bc7c0bd.js";import{r as j}from"./ChevronRightIcon-0e7ec64c.js";import{h as _,S as $}from"./transition-a0923044.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css            */import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";const B={class:"fixed inset-0 z-40 flex"},D={class:"relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl"},N={class:"flex items-center justify-end px-4"},O={class:"mt-4 px-4"},z={key:0,class:"mb-8"},A={class:"flex flex-col justify-between border-b border-gray-300 pb-8"},E={class:"mx-2 text-sm font-bold text-gray-500 uppercase"},M={key:0},V={key:1},F={class:"mt-2 py-4 transition duration-150 ease-in-out"},P={class:"flex grow flex-col font-intro"},R=["textContent"],T={class:"mt-1 text-sm font-medium text-gray-400"},I={class:"mt-2 flex items-center text-base leading-5 font-semibold text-gray-600"},U={key:0,class:"line-clamp-2"},W={key:1,class:"line-clamp-2 text-gray-400"},q={class:"self-center rounded-full p-1 transition duration-150 ease-in-out group-hover:bg-slate-200"},G={key:0,class:"mx-2 mt-2 text-sm font-bold text-indigo-500"},J={"aria-labelledby":"Works"},K={class:"mt-4 flex flex-col items-center font-intro"},Q=["src"],X={class:"flex grow flex-col"},Y=["textContent"],Z=["textContent"],tt=["textContent"],xt={__name:"MobileSidebar",props:{open:Boolean,recentSection:Object,collections:Array,work:Object,sectionHero:Object,works:Array},emits:["close"],setup(s,{emit:g}){const o=s,y=e=>e.chapter?route("read.section-chapter",{author:o.work.author.url,work:o.work.url,book:o.work.l1.toLowerCase()+"-"+e.book,chapter:o.work.l2.toLowerCase()+"-"+e.chapter,line_start:e.line_start,line_end:e.line_end}):route("read.section",{author:o.work.author.url,work:o.work.url,book:o.work.l1.toLowerCase()+"-"+e.book,line_start:e.line_start,line_end:e.line_end}),b=e=>e.chapter?o.work.l1+" "+e.book+", "+o.work.l2+" "+e.chapter:o.work.l1+" "+e.book+", "+L(o.work.l4,e.line_end-e.line_start)+" "+e.line_start+"-"+e.line_end,m=g;return(e,n)=>{const f=w("Link");return a(),p(l($),{as:"template",show:o.open},{default:u(()=>[t("div",{class:"relative z-40 lg:hidden",onClose:n[1]||(n[1]=r=>m("close"))},[d(l(_),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:u(()=>n[2]||(n[2]=[t("div",{class:"fixed inset-0 bg-black opacity-25"},null,-1)])),_:1}),t("div",B,[d(l(_),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:u(()=>[t("div",D,[t("div",N,[t("button",{type:"button",class:"-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-slate-100 focus:ring-2 focus:ring-indigo-500 focus:outline-hidden",onClick:n[0]||(n[0]=r=>m("close"))},[n[3]||(n[3]=t("span",{class:"sr-only"},"Close menu",-1)),d(l(H),{class:"h-6 w-6","aria-hidden":"true"})])]),t("div",O,[l(v)().props.authenticated?x("",!0):(a(),c("section",z,[d(k)])),t("div",A,[t("h5",E,[s.recentSection?(a(),c("span",M,"Most Recent Section")):(a(),c("span",V,"Start Reading"))]),t("div",F,[d(f,{class:"group flex cursor-pointer flex-row items-center rounded-lg px-2 py-4 transition duration-250 ease-in-out hover:bg-slate-100",href:y(s.sectionHero)},{default:u(()=>[t("div",P,[t("h3",{class:"line-clamp-1 text-lg leading-5 font-bold text-gray-900",textContent:i(b(s.sectionHero))},null,8,R),t("span",T,i(s.sectionHero.token_count)+" words",1),t("p",I,[s.sectionHero.description?(a(),c("span",U,i(s.sectionHero.description),1)):(a(),c("span",W,i(s.sectionHero.verse),1))])]),t("div",q,[d(l(j),{class:"h-8 w-8 stroke-2 text-slate-400 transition duration-150 ease-in-out group-hover:text-slate-500"})])]),_:1},8,["href"])]),s.recentSection?(a(),c("p",G," Last visited on "+i(l(h).fromISO(s.recentSection.last_accessed).toLocaleString(l(h).DATE_MED)),1)):x("",!0)]),t("section",J,[n[4]||(n[4]=t("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Check out other works ",-1)),t("div",K,[(a(!0),c(S,null,C(s.works,r=>(a(),p(f,{key:r.id,href:`/read/${r.author.url}/${r.url}`,class:"group flex w-full cursor-pointer flex-row items-center rounded-lg px-2 py-4 transition duration-150 ease-in-out hover:bg-slate-100"},{default:u(()=>[t("img",{class:"mr-2 h-16 w-16 rounded-full",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${r.image}`,alt:""},null,8,Q),t("div",X,[t("h4",{class:"font-intro text-lg leading-5 font-bold text-gray-900",textContent:i(r.name)},null,8,Y),t("p",{class:"text-xs font-semibold text-gray-500",textContent:i(r.author.name)},null,8,Z),t("p",{class:"mt-1 line-clamp-2 text-xs font-medium text-gray-700",textContent:i(r.description)},null,8,tt)])]),_:2},1032,["href"]))),128))])])])])]),_:1})])],32)]),_:1},8,["show"])}}};export{xt as default};
