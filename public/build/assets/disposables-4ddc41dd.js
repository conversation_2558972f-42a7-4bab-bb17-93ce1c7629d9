import{t as a}from"./micro-task-89dcd6af.js";function o(){let i=[],r={addEventListener(e,t,n,s){return e.addEventListener(t,n,s),r.add(()=>e.removeEventListener(t,n,s))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){r.requestAnimationFrame(()=>{r.requestAnimationFrame(...e)})},setTimeout(...e){let t=setTimeout(...e);r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return a(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,n){let s=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:s})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return i.push(e),()=>{let t=i.indexOf(e);if(t>=0)for(let n of i.splice(t,1))n()}},dispose(){for(let e of i.splice(0))e()}};return r}export{o};
