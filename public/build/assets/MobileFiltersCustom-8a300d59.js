import{e as y,p as h,o as _,c as w,w as n,u as i,a as e,b as r,q as l,v as d,j as a,g as k}from"./app-f0078ddb.js";import{_ as V}from"./CopyToClipboard-21badf5d.js";import{_ as U}from"./ButtonItem-718c0517.js";import{r as C}from"./XMarkIcon-9bc7c0bd.js";import{r as x}from"./CheckCircleIcon-d86d1232.js";import{r as f}from"./StarIcon-155a2a28.js";import{h as b,S as j}from"./transition-a0923044.js";/* empty css            */import"./clipboard-a66b13b3.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";const B={class:"fixed inset-0 z-40 flex"},F={class:"relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-6 shadow-xl"},z={class:"flex items-center justify-between px-4"},L={class:"mt-4"},N={class:"border-t border-gray-200 px-4 pb-6"},S={class:"pt-6"},A={class:"mt-8 flex text-sm"},M={class:"flex-1 text-right"},T={class:"grid grid-cols-1 divide-y"},q={class:"ml-4 grid grid-cols-2 gap-3 py-4"},D={class:"relative flex items-center"},G={class:"flex h-5 items-center"},R={class:"ml-3 text-sm"},Y={for:"learned-mobile",class:"font-medium text-gray-700"},E={class:"relative flex items-center"},H={class:"flex h-5 items-center"},I={class:"ml-3 text-sm"},J={for:"unlearned-mobile",class:"font-medium text-gray-700"},K={class:"relative flex items-center"},O={class:"flex h-5 items-center"},P={class:"ml-3 text-sm"},Q={for:"starred-mobile",class:"font-medium text-gray-700"},W={class:"relative flex items-center"},X={class:"flex h-5 items-center"},Z={class:"ml-3 text-sm"},$={for:"unstarred-mobile",class:"font-medium text-gray-700"},ee={class:"grid grid-cols-1 divide-y"},se={class:"ml-4 grid grid-cols-2 gap-3 py-4"},te={class:"relative flex items-center"},oe={class:"flex h-5 items-center"},ie={class:"relative flex items-center"},le={class:"flex h-5 items-center"},de={class:"relative flex items-center"},ae={class:"flex h-5 items-center"},re={class:"relative flex items-center"},ne={class:"flex h-5 items-center"},me={class:"relative flex items-center"},ue={class:"flex h-5 items-center"},ce={class:"relative flex items-center"},ve={class:"flex h-5 items-center"},xe={class:"grid grid-cols-1 divide-y"},fe={class:"ml-4 grid grid-cols-2 gap-3 py-4"},be={class:"relative flex items-center"},ge={class:"flex h-5 items-center"},pe={class:"relative flex items-center"},ye={class:"flex h-5 items-center"},he={class:"relative flex items-center"},_e={class:"flex h-5 items-center"},we={class:"relative flex items-center"},ke={class:"flex h-5 items-center"},Ve={class:"relative flex items-center"},Ue={class:"flex h-5 items-center"},Re={__name:"MobileFiltersCustom",props:{open:Boolean,array:Array,clipboardList:String},emits:["close","update:array"],setup(u,{emit:g}){const c=u;let t=y(c.array);const p=()=>{t.value=[]};h(()=>t,v=>{m("update:array",v.value)},{deep:!0});const m=g;return(v,s)=>(_(),w(i(j),{as:"template",show:c.open},{default:n(()=>[e("div",{as:"div",class:"relative z-40 lg:hidden",onClose:s[17]||(s[17]=o=>m("close"))},[r(i(b),{as:"template",enter:"transition-opacity ease-linear duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"transition-opacity ease-linear duration-300","leave-from":"opacity-100","leave-to":"opacity-0"},{default:n(()=>s[18]||(s[18]=[e("div",{class:"fixed inset-0 bg-black opacity-25"},null,-1)])),_:1}),e("div",B,[r(i(b),{as:"template",enter:"transition ease-in-out duration-300 transform","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transition ease-in-out duration-300 transform","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:n(()=>[e("div",F,[e("div",z,[s[20]||(s[20]=e("h2",{class:"text-lg font-medium text-gray-900"},"Filters",-1)),e("button",{type:"button",class:"-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400 hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:outline-hidden",onClick:s[0]||(s[0]=o=>m("close"))},[s[19]||(s[19]=e("span",{class:"sr-only"},"Close menu",-1)),r(i(C),{class:"h-6 w-6","aria-hidden":"true"})])]),e("div",L,[e("div",N,[e("div",S,[e("div",A,[s[21]||(s[21]=e("div",{class:"flex-1 text-left"},[e("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),e("div",M,[e("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:s[1]||(s[1]=o=>p())}," Clear ")])]),e("div",T,[e("div",q,[e("div",D,[e("div",G,[l(e("input",{id:"learned-mobile","onUpdate:modelValue":s[2]||(s[2]=o=>a(t)?t.value=o:t=o),value:"learned",name:"learned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),e("div",R,[e("label",Y,[r(i(x),{class:"h-6 w-6 stroke-2 text-blue-500","aria-hidden":"true"})])])]),e("div",E,[e("div",H,[l(e("input",{id:"unlearned-mobile","onUpdate:modelValue":s[3]||(s[3]=o=>a(t)?t.value=o:t=o),value:"unlearned",name:"unlearned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),e("div",I,[e("label",J,[r(i(x),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])]),e("div",K,[e("div",O,[l(e("input",{id:"starred-mobile","onUpdate:modelValue":s[4]||(s[4]=o=>a(t)?t.value=o:t=o),value:"starred",name:"starred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),e("div",P,[e("label",Q,[r(i(f),{class:"h-6 w-6 stroke-2 text-green-600","aria-hidden":"true"})])])]),e("div",W,[e("div",X,[l(e("input",{id:"unstarred-mobile","onUpdate:modelValue":s[5]||(s[5]=o=>a(t)?t.value=o:t=o),value:"unstarred",name:"unstarred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),e("div",Z,[e("label",$,[r(i(f),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])])])]),e("div",ee,[e("div",se,[e("div",te,[e("div",oe,[l(e("input",{id:"basic-mobile","onUpdate:modelValue":s[6]||(s[6]=o=>a(t)?t.value=o:t=o),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[22]||(s[22]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"basic-mobile",class:"font-semibold text-blue-600"},"basic ")],-1))]),e("div",ie,[e("div",le,[l(e("input",{id:"intermediate-mobile","onUpdate:modelValue":s[7]||(s[7]=o=>a(t)?t.value=o:t=o),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[23]||(s[23]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"intermediate-mobile",class:"font-semibold text-rose-600"},"intermediate ")],-1))]),e("div",de,[e("div",ae,[l(e("input",{id:"advanced-mobile","onUpdate:modelValue":s[8]||(s[8]=o=>a(t)?t.value=o:t=o),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[24]||(s[24]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"advanced-mobile",class:"font-semibold text-orange-600"},"advanced ")],-1))]),e("div",re,[e("div",ne,[l(e("input",{id:"uncommon-mobile","onUpdate:modelValue":s[9]||(s[9]=o=>a(t)?t.value=o:t=o),value:"uncommon",name:"uncommon",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[25]||(s[25]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"uncommon-mobile",class:"font-semibold text-violet-600"},"uncommon ")],-1))]),e("div",me,[e("div",ue,[l(e("input",{id:"core-mobile","onUpdate:modelValue":s[10]||(s[10]=o=>a(t)?t.value=o:t=o),value:"core",name:"core",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[26]||(s[26]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"core-mobile",class:"font-semibold text-gray-600"},"core ")],-1))]),e("div",ce,[e("div",ve,[l(e("input",{id:"noncore-mobile","onUpdate:modelValue":s[11]||(s[11]=o=>a(t)?t.value=o:t=o),value:"noncore",name:"noncore",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[27]||(s[27]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"noncore-mobile",class:"font-semibold text-gray-600"},"non-core ")],-1))])])]),e("div",xe,[e("div",fe,[e("div",be,[e("div",ge,[l(e("input",{id:"nouns-mobile","onUpdate:modelValue":s[12]||(s[12]=o=>a(t)?t.value=o:t=o),value:"nouns",name:"nouns",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[28]||(s[28]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"nouns-mobile",class:"font-semibold text-gray-600"},"nouns ")],-1))]),e("div",pe,[e("div",ye,[l(e("input",{id:"verbs-mobile","onUpdate:modelValue":s[13]||(s[13]=o=>a(t)?t.value=o:t=o),value:"verbs",name:"verbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[29]||(s[29]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"verbs-mobile",class:"font-semibold text-gray-600"},"verbs ")],-1))]),e("div",he,[e("div",_e,[l(e("input",{id:"adjectives-mobile","onUpdate:modelValue":s[14]||(s[14]=o=>a(t)?t.value=o:t=o),value:"adjectives",name:"adjectives",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[30]||(s[30]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adjectives-mobile",class:"font-semibold text-gray-600"},"adjectives ")],-1))]),e("div",we,[e("div",ke,[l(e("input",{id:"adverbs-mobile","onUpdate:modelValue":s[15]||(s[15]=o=>a(t)?t.value=o:t=o),value:"adverbs",name:"adverbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[31]||(s[31]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"adverbs-mobile",class:"font-semibold text-gray-600"},"adverbs ")],-1))]),e("div",Ve,[e("div",Ue,[l(e("input",{id:"other-mobile","onUpdate:modelValue":s[16]||(s[16]=o=>a(t)?t.value=o:t=o),value:"other",name:"other",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[d,i(t)]])]),s[32]||(s[32]=e("div",{class:"ml-3 text-sm"},[e("label",{for:"other-mobile",class:"font-semibold text-gray-600"},"other ")],-1))])])])])])]),r(V,{data:u.clipboardList,message:"Copied",class:"px-4"},{default:n(()=>[r(U,{size:"sm",color:"lightGray",class:"w-full"},{default:n(()=>s[33]||(s[33]=[k("Copy This List to Your Clipboard ")])),_:1})]),_:1},8,["data"])])]),_:1})])],32)]),_:1},8,["show"]))}};export{Re as default};
