import{e as y,l as M,p as O,i as N,o as r,c as q,w as L,b as d,a as t,n as w,g as j,d as i,E as h,t as u,F as V,h as A,f as b,u as m,q as _,v as k,j as I,W as B}from"./app-f0078ddb.js";import{_ as U}from"./AppLayout-33f062bc.js";import{_ as P}from"./Breadcrumbs-c96e9207.js";import{_ as W}from"./ButtonItem-718c0517.js";import{_ as E}from"./WordItem-d0f526f8.js";import{D as f}from"./datetime-8ddd27a0.js";import{u as H}from"./useIntersect-6e15125e.js";import{_ as Q}from"./Footer-0988dcd8.js";import{I as R}from"./InfinitasIcon-1a3ae135.js";import Y from"./Source-54301355.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./StarIcon-155a2a28.js";import"./Modal-cd0db00e.js";const G={class:"relative isolate bg-white pb-16"},J={class:"p-8"},K={class:"flex flex-row items-center justify-between"},X={class:"mx-auto max-w-2xl text-center lg:max-w-4xl"},Z={class:"flex w-full items-center"},tt={key:0,class:"mt-8 flex flex-col gap-4 lg:col-span-3"},et={class:"mt-16 h-64 w-full rounded-2xl p-4"},st={class:"flex h-4 w-full overflow-hidden rounded-full bg-gray-200"},lt={class:"mt-4 grid grid-cols-2"},ot={class:"text-3xl text-orange-600"},rt={class:"text-3xl text-lime-600"},it={class:"mt-4 text-3xl text-sky-600"},at={class:"mt-4 text-3xl text-indigo-600"},nt={class:"flow-root"},dt={role:"list",class:"grid grid-cols-7 px-2"},ut={class:"relative flex items-center justify-center"},ct={key:0,class:"absolute top-1/2 left-0 -z-10 -ml-px h-0.5 w-1/2 -translate-y-1/2 transform bg-slate-300"},mt={class:"flex items-center justify-center"},gt={key:1,class:"absolute top-1/2 right-0 -z-10 -ml-px h-0.5 w-1/2 -translate-y-1/2 transform bg-slate-300"},xt={class:"font-base mt-4 text-center text-lg text-gray-600"},ft={key:0},pt={key:1},vt={key:0,class:"mt-32 grid grid-cols-1 gap-8 lg:grid-cols-5"},yt={class:"order-2 -m-2 flex rounded-xl bg-gray-900/5 p-2 ring-1 ring-gray-900/10 ring-inset lg:order-1 lg:col-span-3 lg:-m-4 lg:rounded-3xl lg:p-4"},wt={key:0,class:"w-full rounded-lg bg-white px-6 py-4 lg:rounded-[.75rem]"},ht={class:"-mt-4 w-16 grow self-center text-right text-sm text-gray-600"},bt={key:1,class:"flex w-full flex-col items-center rounded-xl bg-white px-6 py-4 py-14 text-center text-sm sm:px-14"},_t={class:"order-1 -mt-4 lg:order-2 lg:col-span-2",id:"sticky"},kt={class:"sticky top-20 flex flex-col gap-4"},It={class:"rounded-xl bg-white p-2 lg:p-4"},Dt={class:"grid grid-cols-1 gap-3 px-4 py-4"},St={class:"relative flex items-center"},Lt={class:"flex h-5 items-center"},Mt={class:"relative flex items-center"},Ot={class:"flex h-5 items-center"},jt={class:"relative flex items-center"},Vt={class:"flex h-5 items-center"},At={class:"relative flex items-center"},Ct={class:"flex h-5 items-center"},be={__name:"Index",props:{user_stats:Array,weekly_activity:Object,streak:Number,words:Object,filters:Array,source:String,works:Array},setup(a){const C=[{name:"Infinitas",href:"/infinitas",current:!0}],n=a,D=y(null);let c=n.words?y(n.words):null,g=n.words?y(c.value.data):null;const T=M(()=>c.value.next_page_url!==null);D!==null&&H(D,()=>{T.value&&axios.get(c.value.next_page_url).then(o=>{g.value=[...g.value,...o.data.data],c.value=o.data})},{rootMargin:"0px 0px 300px 0px"});let l=y(n.filters);const $=()=>{l.value=[]};O(()=>n.words,o=>{c.value=o,g.value=c.value.data},{deep:!0}),O(()=>l,()=>{B.get("/infinitas",{filters:l.value},{only:["words"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0});const p=o=>{const e=n.user_stats.find(x=>x.interval===o);return e?e.total:0},v=o=>{const e=n.user_stats.find(x=>x.interval===o);return e?`${e.total/z.value*100}%`:"0%"},z=M(()=>{if(n.user_stats)return n.user_stats.reduce((o,e)=>o+e.total,0)}),F=o=>{let e=f.fromSQL(o).toLocal();switch(e.toISODate()){case f.now().toLocal().toISODate():return"Today";case f.now().plus({days:1}).toLocal().toISODate():return"Tomorrow";default:return e.toISODate()<f.now().toLocal().toISODate()?"Today":e.toISODate()>f.now().plus({years:1}).toLocal().toISODate()?e.toFormat("MMM d, yyyy"):e.toFormat("MMM d")}};return(o,e)=>{const x=N("Head");return r(),q(U,null,{default:L(()=>[d(x,null,{default:L(()=>e[4]||(e[4]=[t("title",null,"Infinitas",-1)])),_:1}),t("div",G,[e[19]||(e[19]=t("div",{class:"absolute inset-x-0 -top-3 -z-10 mt-32 transform-gpu overflow-hidden px-36 blur-3xl sm:mt-56","aria-hidden":"true"},[t("div",{class:"mx-auto aspect-1155/678 w-[72.1875rem] bg-linear-to-tr from-indigo-400 to-sky-200 opacity-30",style:{"clip-path":`polygon(
              74.1% 44.1%,
              100% 61.6%,
              97.5% 26.9%,
              85.5% 0.1%,
              80.7% 2%,
              72.5% 32.5%,
              60.2% 62.4%,
              52.4% 68.1%,
              47.5% 58.3%,
              45.2% 34.5%,
              27.5% 76.7%,
              0.1% 64.9%,
              17.9% 100%,
              27.6% 76.8%,
              76.1% 97.7%,
              74.1% 44.1%
            )`}})],-1)),t("div",J,[t("div",K,[d(P,{class:"lg:col-span-9 xl:grid-cols-10",pages:C})]),t("div",{class:w(["mt-12 grid grid-cols-1",a.user_stats?"lg:grid-cols-8":"lg:grid-cols-2"])},[t("div",{class:w(["mt-24 p-4",a.user_stats?"lg:col-span-5":"lg:col-span-2"])},[t("div",X,[d(R,{class:"inline w-32"}),e[5]||(e[5]=t("p",{class:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"}," Infinitas ",-1))]),e[7]||(e[7]=t("p",{class:"mx-auto mt-6 max-w-2xl text-center text-lg leading-7 text-gray-600"},[t("span",{class:"font-bold uppercase"},"Infinitas"),j(" provides you with an endless stream of vocabulary questions, custom tailored to your level and reading experience, and focused on the most important and common words in Latin. ")],-1)),t("div",Z,[d(W,{class:"mx-auto mt-8 justify-around",size:"md",color:"indigo",link:"/infinitas/attempt"},{default:L(()=>e[6]||(e[6]=[j("Infinite Practice ")])),_:1})])],2),a.user_stats?(r(),i("div",tt,[t("div",et,[t("div",st,[t("div",{class:"h-full bg-orange-300",style:h(`width: ${v(1)}`)},null,4),t("div",{class:"h-full bg-lime-300",style:h(`width: ${v(7)}`)},null,4),t("div",{class:"h-full bg-sky-300",style:h(`width: ${v(16)}`)},null,4),t("div",{class:"h-full bg-indigo-300",style:h(`width: ${v(30)}`)},null,4)]),t("div",lt,[t("div",null,[t("h2",ot,u(p(1))+" words ",1),e[8]||(e[8]=t("p",{class:"text-lg text-gray-600"},"level 1",-1))]),t("div",null,[t("h2",rt,u(p(7))+" words ",1),e[9]||(e[9]=t("p",{class:"text-lg text-gray-600"},"level 2",-1))]),t("div",null,[t("h2",it,u(p(16))+" words ",1),e[10]||(e[10]=t("p",{class:"text-lg text-gray-600"},"level 3",-1))]),t("div",null,[t("h2",at,u(p(30))+" words ",1),e[11]||(e[11]=t("p",{class:"text-lg text-gray-600"},"mastered",-1))])])]),t("div",nt,[t("ul",dt,[(r(!0),i(V,null,A(a.weekly_activity,(s,S)=>(r(),i("li",{key:S},[t("div",ut,[S!==0?(r(),i("span",ct)):b("",!0),t("div",mt,[t("span",{class:w(["flex h-10 w-10 items-center justify-center rounded-full text-lg font-bold text-white shadow-lg",s.active?"bg-blue-500":"bg-gray-400"])},u(s.abbreviation),3)]),S!==a.weekly_activity.length-1?(r(),i("span",gt)):b("",!0)])]))),128))])]),t("div",xt,[a.streak>0?(r(),i("span",ft,u(a.streak)+" day streak",1)):(r(),i("span",pt,"Add words to the queue and start your next streak"))])])):b("",!0)],2),a.words?(r(),i("div",vt,[t("div",yt,[m(g).length>0?(r(),i("div",wt,[(r(!0),i(V,null,A(m(g),s=>(r(),i("div",{key:s.id,class:"flex flex-row"},[t("div",{class:w(["h-16 w-1 self-center",s.interval==1?"bg-orange-300":s.interval==7?"bg-lime-300":s.interval==16?"bg-sky-300":"bg-indigo-300"])},null,2),d(E,{word:s,class:"truncate pl-4"},null,8,["word"]),t("div",ht,u(F(s.next_review_at)),1)]))),128)),t("div",{ref_key:"landmark",ref:D},null,512)])):(r(),i("div",bt,e[12]||(e[12]=[t("div",null,[t("p",{class:"mt-4 font-semibold text-gray-900"},"No results found"),t("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ")],-1)])))]),t("div",_t,[t("div",kt,[d(Y,{source:a.source,works:a.works},null,8,["source","works"]),t("div",It,[t("div",{class:"flex flex-row items-center"},[e[13]||(e[13]=t("h3",{class:"text-2xl font-bold text-gray-900"},"Your Queue",-1)),t("div",{class:"flex-1 text-right"},[t("button",{class:"font-bold text-blue-500 uppercase opacity-100 hover:text-blue-600",onClick:$}," Clear ")])]),e[18]||(e[18]=t("p",{class:"mt-2 text-sm text-gray-600"}," The words you see here are the words that are currently in your queue, along with the date that you will see them next. ",-1)),t("div",Dt,[t("div",St,[t("div",Lt,[_(t("input",{id:"tier1","onUpdate:modelValue":e[0]||(e[0]=s=>I(l)?l.value=s:l=s),value:"1",name:"tier1",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,m(l)]])]),e[14]||(e[14]=t("div",{class:"ml-3 text-base"},[t("label",{for:"tier1",class:"font-semibold text-orange-500"},"Level 1 - 1 day interval ")],-1))]),t("div",Mt,[t("div",Ot,[_(t("input",{id:"tier2","onUpdate:modelValue":e[1]||(e[1]=s=>I(l)?l.value=s:l=s),value:"7",name:"tier2",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,m(l)]])]),e[15]||(e[15]=t("div",{class:"ml-3 text-base"},[t("label",{for:"tier2",class:"font-semibold text-lime-500"},"Level 2 - 7 day interval ")],-1))]),t("div",jt,[t("div",Vt,[_(t("input",{id:"tier3","onUpdate:modelValue":e[2]||(e[2]=s=>I(l)?l.value=s:l=s),value:"16",name:"tier3",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,m(l)]])]),e[16]||(e[16]=t("div",{class:"ml-3 text-base"},[t("label",{for:"tier3",class:"font-semibold text-sky-500"},"Level 3 - 16 day interval ")],-1))]),t("div",At,[t("div",Ct,[_(t("input",{id:"tier4","onUpdate:modelValue":e[3]||(e[3]=s=>I(l)?l.value=s:l=s),value:"30",name:"tier4",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[k,m(l)]])]),e[17]||(e[17]=t("div",{class:"ml-3 text-base"},[t("label",{for:"tier4",class:"font-semibold text-indigo-500"},"Mastered - 30 day interval ")],-1))])])])])])])):b("",!0)]),d(Q)])]),_:1})}}};export{be as default};
