import{_ as y}from"./AppLayout-33f062bc.js";import{_ as w}from"./Breadcrumbs-c96e9207.js";import{o as n,d as c,a as t,l,c as _,w as m,b as o,t as i,h as v,u as p,g as x,F as b,P as g}from"./app-f0078ddb.js";import{_ as u}from"./ButtonItem-718c0517.js";import{r as k}from"./CheckCircleIcon-5b898cc1.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";/* empty css            */function P(a,s){return n(),c("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"fill-rule":"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z","clip-rule":"evenodd"})])}const B={class:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8"},L={class:"mt-8"},S={class:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"},D={class:"overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"},A={class:"mt-1 text-3xl font-semibold tracking-tight text-gray-900"},C={class:"overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"},M={class:"mt-1 text-3xl font-semibold tracking-tight text-green-600"},T={class:"overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"},$={class:"mt-1 text-3xl font-semibold tracking-tight text-blue-600"},N={class:"overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"},V={class:"mt-1 text-3xl font-semibold tracking-tight text-indigo-600"},j={class:"mt-8 overflow-hidden rounded-lg bg-white shadow"},z={class:"border-t border-gray-200"},E={class:"overflow-x-auto"},F={class:"min-w-full divide-y divide-gray-200"},G={class:"divide-y divide-gray-200 bg-white"},W={class:"px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900"},Z={class:"px-6 py-4 text-sm whitespace-nowrap text-gray-500"},H={key:0,class:"flex items-center text-green-600"},I={key:1,class:"flex items-center text-red-600"},O={class:"px-6 py-4 text-sm whitespace-nowrap text-gray-500"},R={class:"px-6 py-4 text-sm whitespace-nowrap text-gray-500"},X={class:"mt-8 flex justify-between"},St={__name:"DeclensionSummary",props:{session:Object,attempts:Array},setup(a){const s=a,f=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:"Declension Practice",href:"/practice/grammar/declension",current:!1},{name:"Summary",href:"#",current:!0}],h=l(()=>s.session.attempts===0?0:Math.round(s.session.correct/s.session.attempts*100));return l(()=>s.attempts.filter(d=>d.correct).length),l(()=>s.attempts.filter(d=>!d.correct).length),l(()=>{if(s.attempts.length===0)return 0;const d=s.attempts.reduce((e,r)=>e+r.time,0);return Math.round(d/s.attempts.length/10)}),(d,e)=>(n(),_(y,null,{default:m(()=>[t("div",B,[o(w,{pages:f}),t("div",L,[e[10]||(e[10]=t("h1",{class:"text-3xl font-bold text-gray-900"},"Practice Summary",-1)),t("div",S,[t("div",D,[e[0]||(e[0]=t("dt",{class:"truncate text-sm font-medium text-gray-500"}," Total Words ",-1)),t("dd",A,i(a.session.attempts),1)]),t("div",C,[e[1]||(e[1]=t("dt",{class:"truncate text-sm font-medium text-gray-500"},"Correct",-1)),t("dd",M,i(a.session.correct),1)]),t("div",T,[e[2]||(e[2]=t("dt",{class:"truncate text-sm font-medium text-gray-500"},"Accuracy",-1)),t("dd",$,i(h.value)+"% ",1)]),t("div",N,[e[3]||(e[3]=t("dt",{class:"truncate text-sm font-medium text-gray-500"}," XP Earned ",-1)),t("dd",V,i(a.session.xp_earned),1)])]),t("div",j,[e[7]||(e[7]=t("div",{class:"px-4 py-5 sm:px-6"},[t("h2",{class:"text-lg leading-6 font-medium text-gray-900"}," Attempt History "),t("p",{class:"mt-1 max-w-2xl text-sm text-gray-500"}," Your most recent declension practice attempts. ")],-1)),t("div",z,[t("div",E,[t("table",F,[e[6]||(e[6]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"}," Word "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"}," Result "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"}," Time (seconds) "),t("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"}," Date ")])],-1)),t("tbody",G,[(n(!0),c(b,null,v(a.attempts,r=>(n(),c("tr",{key:r.id},[t("td",W,i(r.word),1),t("td",Z,[r.correct?(n(),c("span",H,[o(p(k),{class:"mr-1 h-5 w-5"}),e[4]||(e[4]=x(" Correct "))])):(n(),c("span",I,[o(p(P),{class:"mr-1 h-5 w-5"}),e[5]||(e[5]=x(" Incorrect "))]))]),t("td",O,i(Math.round(r.time/10)),1),t("td",R,i(new Date(r.created_at).toLocaleString()),1)]))),128))])])])])]),t("div",X,[o(p(g),{href:"/practice/grammar"},{default:m(()=>[o(u,{color:"gray",size:"md"},{default:m(()=>e[8]||(e[8]=[x(" Back to Grammar Practice ")])),_:1})]),_:1}),o(p(g),{href:"/practice/grammar/declension"},{default:m(()=>[o(u,{color:"indigo",size:"md"},{default:m(()=>e[9]||(e[9]=[x(" Practice Again ")])),_:1})]),_:1})])])])]),_:1}))}};export{St as default};
