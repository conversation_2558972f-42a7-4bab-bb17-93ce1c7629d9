import{e as k,p as N,A as z,B as I,o as p,d as v,a as o,r as j,t as A,b as m,w as g,u as h,T as M,n as V,g as $}from"./app-f0078ddb.js";import{d as B}from"./vuedraggable.umd-aab17b5c.js";import{_ as K}from"./ButtonItem-718c0517.js";import O from"./NextButtonSmall-9e6ffefc.js";import{r as w}from"./replaceMacra-3b9666ed.js";import{_ as U}from"./_plugin-vue_export-helper-c27b6911.js";import{r as Q}from"./CheckCircleIcon-d86d1232.js";import{r as R}from"./XCircleIcon-63af2b2a.js";/* empty css            */const W={class:"flex-1"},F={class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900"},G={class:"text-center text-lg font-medium text-gray-600"},H={class:"relative mt-12 grid w-full touch-manipulation grid-cols-1 justify-items-center sm:px-16"},J={class:"h-24 w-full"},P={class:"flex h-24 w-full items-center justify-center rounded-2xl border-2 border-dashed border-gray-400 py-2"},X={class:"grid h-24 grid-cols-2"},Y={class:"flex w-full items-center justify-end"},Z={class:"mx-px rounded-l-full border-2 border-gray-400 bg-white py-1 py-2 pr-2 pl-4 text-2xl font-medium text-gray-800 shadow-lg select-none"},ee={class:"h-24 w-full"},se=["onClick"],te={class:"mt-6 flex justify-center space-x-4"},ne=["onClick"],oe={class:"mt-8 text-center"},le={key:0},re={key:0,class:"flex items-center justify-center text-blue-500"},ie={key:1,class:"flex items-center justify-center text-red-600"},ae={key:1,class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8 xl:px-10"},de={__name:"Drag",props:{options:Array,isAnswer:Boolean,isCorrect:Boolean,disabled:Boolean,isVocabulary:{type:Boolean,default:!1},questionKey:String,stem:String,originalKey:String},emits:["submit","next-question","i-know-this"],setup(r,{emit:S}){const l=r,u=k([]),a=k([]),C=S;N(()=>l.options,s=>{u.value=s.map((e,t)=>({id:t,data:e}))},{immediate:!0});const b=k([]);function _(){document.removeEventListener("keydown",y);const s=a.value.map(d=>d.data);b.value=[...s];const e=[...l.options],t=l.originalKey.split("|"),n=[],c=[];if(t.forEach((d,f)=>{s[f]===d?n.push({id:f,data:d}):(n.push({id:f,data:d}),s[f]&&c.push({id:f+1e3,data:s[f]}))}),l.stem){const d=w(l.stem);n.length&&w(n[0].data)===d&&n.shift()}const i=new Set(n.map(d=>w(d.data))),x=e.filter(d=>!i.has(w(d)));a.value=n,u.value=x.map((d,f)=>({id:f+1e3,data:d})),C("submit",{answer:l.stem+s.join(""),key:l.questionKey,isArray:!1})}z(()=>{document.addEventListener("keydown",y)}),I(()=>{document.removeEventListener("keydown",y)});function y(s){s.preventDefault(),s.which==13&&_()}const T=()=>{C("next-question")};function D(s,e){const n=l.originalKey.split("|").length;let c;l.isAnswer?c=l.stem?n-1:n:c=a.value.length;let i=[];if(l.stem?e===0&&c===1?i.push("rounded-r-full pl-2 rounded-l-none pr-4"):e===c-1?i.push("rounded-r-full pr-4 rounded-l-none pl-2"):i.push("rounded-l-none pl-2","rounded-r-none pr-2"):e===0&&c===1?i.push("rounded-full px-4"):e===0?i.push("rounded-l-full pl-4 rounded-r-none pr-2"):e===c-1?i.push("rounded-r-full pr-4 rounded-l-none pl-2"):i.push("rounded-l-none pl-2","rounded-r-none pr-2"),!l.isAnswer)i.push("bg-purple-100 border-purple-400");else if(l.isCorrect)i.push("bg-teal-100 border-teal-400");else{const x=b.value[e];s===x?i.push("bg-teal-100 border-teal-400"):i.push("bg-red-100 border-red-400")}return i.join(" ")}function E(s){let e=[];if(a.value.includes(s)){const t=a.value.indexOf(s);t===0?e.push("rounded-l-full pl-4"):e.push("rounded-l-none pl-2"),t!==a.value.length-1?e.push("rounded-r-none pr-2"):e.push("rounded-r-full pr-4")}else e.push("rounded-r-full rounded-l-full pr-4 pl-4");return l.isAnswer&&b.value.some(n=>n===s)&&e.push("bg-red-100 border-red-400"),e.join(" ")}function q(){a.value=[],u.value=l.options.map((s,e)=>({id:e,data:s}))}function L(s){const e=a.value.find(n=>n.id===s.id),t=u.value.find(n=>n.id===s.id);e?(a.value=a.value.filter(n=>n.id!==s.id),u.value.push(e)):t&&(u.value=u.value.filter(n=>n.id!==s.id),a.value.push(t))}return(s,e)=>(p(),v("div",W,[o("h1",F,[j(s.$slots,"stem",{},void 0,!0)]),o("h3",G,[j(s.$slots,"instructions",{},void 0,!0)]),o("div",H,[o("div",J,[o("div",P,[o("div",X,[o("div",Y,[o("div",Z,A(r.stem),1)]),o("div",ee,[m(h(B),{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=t=>a.value=t),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"relative flex h-24 w-full items-center justify-start rounded-2xl py-2","ghost-class":"ghost",animation:200,disabled:r.isAnswer},{item:g(({element:t,index:n})=>[o("div",{class:V(["mx-px inline-flex items-center border-2 py-2 text-2xl font-medium shadow-lg select-none",[D(t.data,n),{"cursor-move hover:bg-purple-200":!r.isAnswer,"pointer-events-none":r.isAnswer}]]),onClick:c=>L(t)},A(t.data),11,se)]),_:1},8,["modelValue","disabled"])])])])]),e[6]||(e[6]=o("div",{class:"my-8 w-full border border-gray-400"},null,-1)),o("div",te,[m(h(B),{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=t=>u.value=t),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"flex h-24 w-full items-start justify-center","ghost-class":"ghost",animation:200,disabled:r.isAnswer},{item:g(({element:t})=>[o("div",{class:V(["mx-px inline-flex items-center border-2 border-purple-400 bg-purple-100 py-2 text-2xl font-medium shadow-lg select-none",[E(t.data),{"cursor-move hover:bg-purple-200":!r.isAnswer,"pointer-events-none":r.isAnswer}]]),onClick:n=>L(t)},A(t.data),11,ne)]),_:1},8,["modelValue","disabled"])])]),o("div",oe,[m(M,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95 ","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:g(()=>[r.isAnswer?(p(),v("div",le,[r.isCorrect?(p(),v("div",re,[m(h(Q),{class:"mr-2 inline-block h-10 w-10"}),e[7]||(e[7]=o("p",{class:"inline-block text-2xl font-semibold"},"correct",-1))])):(p(),v("div",ie,[m(h(R),{class:"mr-2 inline-block h-10 w-10"}),e[8]||(e[8]=o("p",{class:"inline-block text-2xl font-semibold"},"incorrect",-1))])),m(O,{"is-answer":r.isAnswer,"is-correct":r.isCorrect,disabled:r.disabled,"is-vocabulary":r.isVocabulary,onIKnowThis:e[2]||(e[2]=t=>s.iKnowThis()),onNextQuestion:e[3]||(e[3]=t=>T())},null,8,["is-answer","is-correct","disabled","is-vocabulary"])])):(p(),v("div",ae,[m(K,{size:"lg",class:"w-full",color:"white",onClick:e[4]||(e[4]=t=>q())},{default:g(()=>e[9]||(e[9]=[$("Clear")])),_:1}),m(K,{size:"lg",class:"w-full",color:"pink",onClick:e[5]||(e[5]=t=>_())},{default:g(()=>e[10]||(e[10]=[$("Submit")])),_:1})]))]),_:1})])]))}},be=U(de,[["__scopeId","data-v-ab67f947"]]);export{be as default};
