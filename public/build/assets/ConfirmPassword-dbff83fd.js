import{C as m,e as u,o as p,c as d,w as e,b as a,u as t,m as f,a as r,n as c,g as w,s as _}from"./app-f0078ddb.js";import{A as g}from"./AuthenticationCard-8e343f6c.js";import{_ as b}from"./InputError-7edb5cf8.js";import{_ as x}from"./InputLabel-3b7f7747.js";import{_ as y}from"./PrimaryButton-f16ada05.js";import{_ as v}from"./TextInput-940981ae.js";import{A as C}from"./AuthLayout-f6b59921.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";const V={class:"flex justify-end mt-4"},j={__name:"ConfirmPassword",setup(A){const o=m({password:""}),i=u(null),l=()=>{o.post(route("password.confirm"),{onFinish:()=>{o.reset(),i.value.focus()}})};return(k,s)=>(p(),d(C,null,{default:e(()=>[a(t(f),{title:"Secure Area"}),a(g,null,{logo:e(()=>s[1]||(s[1]=[r("img",{class:"mx-auto h-10 w-auto",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/img/latintutorial-colosseum.svg",alt:"LatinTutorial"},null,-1)])),default:e(()=>[s[3]||(s[3]=r("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),r("form",{onSubmit:_(l,["prevent"])},[r("div",null,[a(x,{for:"password",value:"Password"}),a(v,{id:"password",ref_key:"passwordInput",ref:i,modelValue:t(o).password,"onUpdate:modelValue":s[0]||(s[0]=n=>t(o).password=n),type:"password",class:"mt-1 block w-full",required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),a(b,{class:"mt-2",message:t(o).errors.password},null,8,["message"])]),r("div",V,[a(y,{class:c(["ml-4",{"opacity-25":t(o).processing}]),disabled:t(o).processing},{default:e(()=>s[2]||(s[2]=[w(" Confirm ")])),_:1},8,["class","disabled"])])],32)]),_:1})]),_:1}))}};export{j as default};
