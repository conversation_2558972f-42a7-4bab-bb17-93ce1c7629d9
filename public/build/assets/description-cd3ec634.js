import{i as g,A as h}from"./render-c34c346a.js";import{e as l,I as j,l as b,H as D,A as O,B as w,u as x,K as y}from"./app-f0078ddb.js";let u=Symbol("DescriptionContext");function S(){let t=y(u,null);if(t===null)throw new Error("Missing parent");return t}function K({slot:t=l({}),name:o="Description",props:i={}}={}){let e=l([]);function n(r){return e.value.push(r),()=>{let s=e.value.indexOf(r);s!==-1&&e.value.splice(s,1)}}return j(u,{register:n,slot:t,name:o,props:i}),b(()=>e.value.length>0?e.value.join(" "):void 0)}let M=D({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(t,{attrs:o,slots:i}){var e;let n=(e=t.id)!=null?e:`headlessui-description-${g()}`,r=S();return O(()=>w(r.register(n))),()=>{let{name:s="Description",slot:a=l({}),props:p={}}=r,{...c}=t,d={...Object.entries(p).reduce((f,[m,v])=>Object.assign(f,{[m]:x(v)}),{}),id:n};return h({ourProps:d,theirProps:c,slot:a.value,attrs:o,slots:i,name:s})}}});export{M as K,K as k};
