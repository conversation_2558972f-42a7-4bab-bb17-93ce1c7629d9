import{e as y,l as D,i as B,o as n,d as c,a as e,F as w,h as M,c as b,b as m,w as d,u,Q as A,g as r,t as i,E as f,f as N,W as R}from"./app-f0078ddb.js";import{_ as V}from"./ClassroomModal-05d10768.js";import{r as k}from"./XCircleIcon-63af2b2a.js";import{r as W}from"./ChevronRightIcon-a926c707.js";/* empty css            */import"./ButtonItem-718c0517.js";import"./XMarkIcon-9bc7c0bd.js";import"./transition-a0923044.js";import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./dialog-86f7bd91.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";const j={class:"overflow-hidden"},E={role:"list",class:"divide-y divide-gray-200"},F={class:"block transition duration-150 hover:bg-slate-200/25"},P={class:"flex items-center px-4 py-4 sm:px-6"},U={class:"flex min-w-0 flex-1 items-center"},z={class:"shrink-0"},I=["src"],O={class:"flex min-w-0 flex-1 flex-row px-4 md:gap-8"},Q={class:"w-64"},X={key:1,class:"truncate text-lg font-medium text-indigo-600"},q={class:"mt-1 flex items-center text-sm font-medium text-gray-500"},G={class:"truncate"},H={class:"hidden w-80 self-center md:block"},J={class:"text-sm font-medium text-gray-600"},K={class:"text-orange-600"},Y={class:"text-lime-600"},Z={class:"text-sky-600"},ee={class:"text-indigo-600"},te={class:"hidden self-center md:block"},se={class:"text-sm font-medium text-gray-600"},oe={class:"mt-1 text-sm font-medium text-gray-600"},ie={key:0,class:"flex"},le={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100"},ne={class:"font-bold"},Le={__name:"StudentList",props:{students:Array,team:String,archived:Boolean},setup(a){let x=a;const S=o=>{let s=new Date(Date.parse(o)),l=s.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}),t=s.toLocaleTimeString("en-US",{hour:"numeric",minute:"numeric",hour12:!0});return`${l} at ${t}`};let p=y(!1),v=y("");const $=o=>{v.value=o,p.value=!0},g=()=>{setTimeout(()=>{v.value=""},400),p.value=!1},_=o=>o.infinitas.level_1+o.infinitas.level_2+o.infinitas.level_3+o.infinitas.level_4,L=D(()=>Math.max(...x.students.map(_))),h=(o,s)=>{let l=_(o);return l>0?`${s/l*100}%`:"0%"},T=o=>{let s=_(o);return s>0?`${s/L.value*100}%`:"0%"},C=()=>{R.post("/api/remove-student",{student:v.value.id,team:x.team},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{g()}})};return(o,s)=>{const l=B("Link");return n(),c(w,null,[e("div",j,[e("ul",E,[(n(!0),c(w,null,M(a.students,t=>(n(),c("li",{key:t.id},[e("div",F,[e("div",P,[e("div",U,[e("div",z,[e("img",{class:"h-12 w-12 rounded-full",src:t.photo_url,alt:""},null,8,I)]),e("div",O,[e("div",Q,[a.archived?(n(),c("p",X,i(t.name),1)):(n(),b(l,{key:0,href:`/classes/${a.team}/students/${t.slug}`,class:"truncate text-lg font-medium text-indigo-600"},{default:d(()=>[r(i(t.name),1)]),_:2},1032,["href"])),e("p",q,[e("span",G,i(t.email),1)])]),e("div",H,[s[3]||(s[3]=e("p",{class:"mb-2 text-sm font-semibold text-gray-600"}," Learned Words ",-1)),e("div",{class:"flex h-4 w-full overflow-hidden rounded-full bg-gray-200",style:f(`width: ${T(t)}`)},[e("div",{class:"h-full bg-orange-300",style:f(`width: ${h(t,t.infinitas.level_1)}`)},null,4),e("div",{class:"h-full bg-lime-300",style:f(`width: ${h(t,t.infinitas.level_2)}`)},null,4),e("div",{class:"h-full bg-sky-300",style:f(`width: ${h(t,t.infinitas.level_3)}`)},null,4),e("div",{class:"h-full bg-indigo-300",style:f(`width: ${h(t,t.infinitas.level_4)}`)},null,4)],4),e("p",J,[e("span",K,i(t.infinitas.level_1),1),s[0]||(s[0]=r(" / ")),e("span",Y,i(t.infinitas.level_2),1),s[1]||(s[1]=r(" / ")),e("span",Z,i(t.infinitas.level_3),1),s[2]||(s[2]=r(" / ")),e("span",ee,i(t.infinitas.level_4),1)])]),e("div",te,[e("p",se," Last active on "+i(" "+S(t.last_active_at)),1),e("p",oe,i(t.xp.toLocaleString())+" XP ",1)])])]),a.archived?N("",!0):(n(),c("div",ie,[m(u(k),{class:"h-6 w-6 cursor-pointer self-center stroke-2 text-gray-400 transition duration-150 ease-in-out hover:text-red-400","aria-hidden":"true",onClick:re=>$(t)},null,8,["onClick"]),m(l,{class:"ml-3",href:`/classes/${a.team}/students/${t.slug}`},{default:d(()=>[m(u(W),{class:"h-6 w-6 cursor-pointer self-center text-gray-400 transition duration-150 ease-in-out hover:text-blue-400","aria-hidden":"true"})]),_:2},1032,["href"])]))])])]))),128))])]),(n(),b(A,{to:"body"},[m(V,{open:u(p),onCloseModal:g},{title:d(()=>s[4]||(s[4]=[r(" Remove this Student ")])),icon:d(()=>[e("div",le,[m(u(k),{class:"h-6 w-6 text-red-600","aria-hidden":"true"})])]),main:d(()=>[s[5]||(s[5]=r(" Are you sure you wish to remove ")),e("span",ne,i(u(v).name),1),s[6]||(s[6]=r(" from this classroom? "))]),actionButton:d(()=>[e("button",{type:"button",class:"inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:text-sm",onClick:C}," Remove ")]),_:1},8,["open"])]))],64)}}};export{Le as default};
