import{o as r,c as N,w as s,a as o,r as _,d as u,C as w,e as $,b as n,f as x,g as i,u as d,F as h,h as C,t as b,n as A}from"./app-f0078ddb.js";import{_ as L}from"./ActionMessage-0a1272b7.js";import{_ as z,a as U,b as S}from"./DialogModal-b2ffd0f0.js";import{_ as T}from"./Checkbox-85576c2c.js";import{_ as W}from"./FormSection-45b6b921.js";import{_ as E}from"./InputError-7edb5cf8.js";import{_ as B}from"./InputLabel-3b7f7747.js";import{_ as I}from"./PrimaryButton-f16ada05.js";import{S as H}from"./SectionBorder-b49c1148.js";import{_ as Y}from"./TextInput-940981ae.js";/* empty css            */import"./SectionTitle-05f6d081.js";import"./_plugin-vue_export-helper-c27b6911.js";const q={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},G={class:"sm:flex sm:items-start"},J={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},K={class:"text-lg font-medium text-gray-900"},O={class:"mt-4 text-sm text-gray-600"},Q={class:"flex flex-row justify-end px-6 py-4 bg-gray-100 text-right"},R={__name:"ConfirmationModal",props:{show:{type:Boolean,default:!1},maxWidth:{type:String,default:"2xl"},closeable:{type:Boolean,default:!0}},emits:["close"],setup(l,{emit:g}){const a=g,m=()=>{a("close")};return(c,p)=>(r(),N(z,{show:l.show,"max-width":l.maxWidth,closeable:l.closeable,onClose:m},{default:s(()=>[o("div",q,[o("div",G,[p[0]||(p[0]=o("div",{class:"mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"},[o("svg",{class:"h-6 w-6 text-red-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"})])],-1)),o("div",J,[o("h3",K,[_(c.$slots,"title")]),o("div",O,[_(c.$slots,"content")])])])]),o("div",Q,[_(c.$slots,"footer")])]),_:3},8,["show","max-width","closeable"]))}},X=["type"],Z={__name:"DangerButton",props:{type:{type:String,default:"button"}},setup(l){return(g,a)=>(r(),u("button",{type:l.type,class:"inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-hidden focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"},[_(g.$slots,"default")],8,X))}},ee=["type"],P={__name:"SecondaryButton",props:{type:{type:String,default:"button"}},setup(l){return(g,a)=>(r(),u("button",{type:l.type,class:"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-xs hover:bg-gray-50 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150"},[_(g.$slots,"default")],8,ee))}},se={class:"col-span-6 sm:col-span-4"},te={key:0,class:"col-span-6"},oe={class:"mt-2 grid grid-cols-1 md:grid-cols-2 gap-4"},ne={class:"flex items-center"},le={class:"ml-2 text-sm text-gray-600"},ie={key:0},re={class:"mt-10 sm:mt-0"},ae={class:"space-y-6"},de={class:"break-all"},ue={class:"flex items-center ml-2"},me={key:0,class:"text-sm text-gray-400"},ce=["onClick"],pe=["onClick"],fe={key:0,class:"mt-4 bg-gray-100 px-4 py-2 rounded-sm font-mono text-sm text-gray-500 break-all"},ge={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ve={class:"flex items-center"},ye={class:"ml-2 text-sm text-gray-600"},Ie={__name:"ApiTokenManager",props:{tokens:Array,availablePermissions:Array,defaultPermissions:Array},setup(l){const a=w({name:"",permissions:l.defaultPermissions}),m=w({permissions:[]}),c=w({}),p=$(!1),v=$(null),y=$(null),j=()=>{a.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{p.value=!0,a.reset()}})},D=f=>{m.permissions=f.abilities,v.value=f},F=()=>{m.put(route("api-tokens.update",v.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>v.value=null})},V=f=>{y.value=f},M=()=>{c.delete(route("api-tokens.destroy",y.value),{preserveScroll:!0,preserveState:!0,onSuccess:()=>y.value=null})};return(f,e)=>(r(),u("div",null,[n(W,{onSubmitted:j},{title:s(()=>e[9]||(e[9]=[i(" Create API Token ")])),description:s(()=>e[10]||(e[10]=[i(" API tokens allow third-party services to authenticate with our application on your behalf. ")])),form:s(()=>[o("div",se,[n(B,{for:"name",value:"Name"}),n(Y,{id:"name",modelValue:d(a).name,"onUpdate:modelValue":e[0]||(e[0]=t=>d(a).name=t),type:"text",class:"mt-1 block w-full",autofocus:""},null,8,["modelValue"]),n(E,{message:d(a).errors.name,class:"mt-2"},null,8,["message"])]),l.availablePermissions.length>0?(r(),u("div",te,[n(B,{for:"permissions",value:"Permissions"}),o("div",oe,[(r(!0),u(h,null,C(l.availablePermissions,t=>(r(),u("div",{key:t},[o("label",ne,[n(T,{checked:d(a).permissions,"onUpdate:checked":e[1]||(e[1]=k=>d(a).permissions=k),value:t},null,8,["checked","value"]),o("span",le,b(t),1)])]))),128))])])):x("",!0)]),actions:s(()=>[n(L,{on:d(a).recentlySuccessful,class:"mr-3"},{default:s(()=>e[11]||(e[11]=[i(" Created. ")])),_:1},8,["on"]),n(I,{class:A({"opacity-25":d(a).processing}),disabled:d(a).processing},{default:s(()=>e[12]||(e[12]=[i(" Create ")])),_:1},8,["class","disabled"])]),_:1}),l.tokens.length>0?(r(),u("div",ie,[n(H),o("div",re,[n(U,null,{title:s(()=>e[13]||(e[13]=[i(" Manage API Tokens ")])),description:s(()=>e[14]||(e[14]=[i(" You may delete any of your existing tokens if they are no longer needed. ")])),content:s(()=>[o("div",ae,[(r(!0),u(h,null,C(l.tokens,t=>(r(),u("div",{key:t.id,class:"flex items-center justify-between"},[o("div",de,b(t.name),1),o("div",ue,[t.last_used_ago?(r(),u("div",me," Last used "+b(t.last_used_ago),1)):x("",!0),l.availablePermissions.length>0?(r(),u("button",{key:1,class:"cursor-pointer ml-6 text-sm text-gray-400 underline",onClick:k=>D(t)}," Permissions ",8,ce)):x("",!0),o("button",{class:"cursor-pointer ml-6 text-sm text-red-500",onClick:k=>V(t)}," Delete ",8,pe)])]))),128))])]),_:1})])])):x("",!0),n(S,{show:p.value,onClose:e[3]||(e[3]=t=>p.value=!1)},{title:s(()=>e[15]||(e[15]=[i(" API Token ")])),content:s(()=>[e[16]||(e[16]=o("div",null," Please copy your new API token. For your security, it won't be shown again. ",-1)),f.$page.props.jetstream.flash.token?(r(),u("div",fe,b(f.$page.props.jetstream.flash.token),1)):x("",!0)]),footer:s(()=>[n(P,{onClick:e[2]||(e[2]=t=>p.value=!1)},{default:s(()=>e[17]||(e[17]=[i(" Close ")])),_:1})]),_:1},8,["show"]),n(S,{show:v.value!=null,onClose:e[6]||(e[6]=t=>v.value=null)},{title:s(()=>e[18]||(e[18]=[i(" API Token Permissions ")])),content:s(()=>[o("div",ge,[(r(!0),u(h,null,C(l.availablePermissions,t=>(r(),u("div",{key:t},[o("label",ve,[n(T,{checked:d(m).permissions,"onUpdate:checked":e[4]||(e[4]=k=>d(m).permissions=k),value:t},null,8,["checked","value"]),o("span",ye,b(t),1)])]))),128))])]),footer:s(()=>[n(P,{onClick:e[5]||(e[5]=t=>v.value=null)},{default:s(()=>e[19]||(e[19]=[i(" Cancel ")])),_:1}),n(I,{class:A(["ml-3",{"opacity-25":d(m).processing}]),disabled:d(m).processing,onClick:F},{default:s(()=>e[20]||(e[20]=[i(" Save ")])),_:1},8,["class","disabled"])]),_:1},8,["show"]),n(R,{show:y.value!=null,onClose:e[8]||(e[8]=t=>y.value=null)},{title:s(()=>e[21]||(e[21]=[i(" Delete API Token ")])),content:s(()=>e[22]||(e[22]=[i(" Are you sure you would like to delete this API token? ")])),footer:s(()=>[n(P,{onClick:e[7]||(e[7]=t=>y.value=null)},{default:s(()=>e[23]||(e[23]=[i(" Cancel ")])),_:1}),n(Z,{class:A(["ml-3",{"opacity-25":d(c).processing}]),disabled:d(c).processing,onClick:M},{default:s(()=>e[24]||(e[24]=[i(" Delete ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}};export{Ie as default};
