import{e as u,A as _,a3 as v,o as r,d as t,t as l,a as e,F as b,h as p}from"./app-f0078ddb.js";/* empty css            */const x={class:"declension-table"},h={key:0,class:"py-4 text-center"},f={key:1,class:"py-4 text-center text-red-500"},m={key:2,class:"grid grid-cols-1 gap-8 lg:grid-cols-2"},k={class:"w-full border-collapse border border-gray-300"},w={class:"border border-gray-300 p-2 text-center font-bold"},F={class:"border border-gray-300 p-2 text-center"},C={class:"w-full border-collapse border border-gray-300"},A={class:"border border-gray-300 p-2 text-center font-bold"},B={class:"border border-gray-300 p-2 text-center"},N={key:3,class:"py-4 text-center"},L={__name:"DeclensionTable",props:{wordId:{type:Number,required:!0}},setup(g){const y=g,a=u(null),n=u(!0),c=u(null);return _(async()=>{try{n.value=!0;const d=await v.get(`/api/decline?word_id=${y.wordId}`);a.value=d.data.forms}catch(d){c.value="Failed to load declensions",console.error(d)}finally{n.value=!1}}),(d,o)=>(r(),t("div",x,[n.value?(r(),t("div",h,"Loading declensions...")):c.value?(r(),t("div",f,l(c.value),1)):a.value?(r(),t("div",m,[e("div",null,[o[1]||(o[1]=e("h2",{class:"mb-4 text-center text-xl font-bold"},"Singular",-1)),e("table",k,[o[0]||(o[0]=e("thead",null,[e("tr",null,[e("th",{class:"border border-gray-300 p-2"},"Case"),e("th",{class:"border border-gray-300 p-2"},"Form")])],-1)),e("tbody",null,[(r(!0),t(b,null,p(a.value.singular,(i,s)=>(r(),t("tr",{key:s},[e("td",w,l(s.charAt(0).toUpperCase()+s.slice(1)),1),e("td",F,l(i),1)]))),128))])])]),e("div",null,[o[3]||(o[3]=e("h2",{class:"mb-4 text-center text-xl font-bold"},"Plural",-1)),e("table",C,[o[2]||(o[2]=e("thead",null,[e("tr",null,[e("th",{class:"border border-gray-300 p-2"},"Case"),e("th",{class:"border border-gray-300 p-2"},"Form")])],-1)),e("tbody",null,[(r(!0),t(b,null,p(a.value.plural,(i,s)=>(r(),t("tr",{key:s},[e("td",A,l(s.charAt(0).toUpperCase()+s.slice(1)),1),e("td",B,l(i),1)]))),128))])])])])):(r(),t("div",N,"No declension data available"))]))}};export{L as default};
