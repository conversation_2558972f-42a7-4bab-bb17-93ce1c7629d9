import{e as c,C as w,o as y,c as _,w as o,g as t,a as i,b as r,u as l,a9 as v,n as g}from"./app-f0078ddb.js";import{a as k,b as x}from"./DialogModal-b2ffd0f0.js";import{_ as d}from"./ButtonItem-718c0517.js";import{_ as C}from"./InputError-7edb5cf8.js";import{_ as b}from"./TextInput-940981ae.js";/* empty css            */import"./SectionTitle-05f6d081.js";import"./_plugin-vue_export-helper-c27b6911.js";const D={class:"mt-5"},V={class:"mt-4"},O={__name:"DeleteUserForm",setup(A){const a=c(!1),n=c(null),s=w({password:""}),p=()=>{a.value=!0,setTimeout(()=>n.value.focus(),250)},m=()=>{s.delete(route("current-user.destroy"),{preserveScroll:!0,onSuccess:()=>u(),onError:()=>n.value.focus(),onFinish:()=>s.reset()})},u=()=>{a.value=!1,s.reset()};return(U,e)=>(y(),_(k,null,{title:o(()=>e[1]||(e[1]=[t(" Delete Account ")])),description:o(()=>e[2]||(e[2]=[t(" Permanently delete your account. ")])),content:o(()=>[e[8]||(e[8]=i("div",{class:"max-w-xl text-sm text-gray-600"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ",-1)),i("div",D,[r(d,{onClick:p,color:"red",size:"sm"},{default:o(()=>e[3]||(e[3]=[t(" Delete Account ")])),_:1})]),r(x,{show:a.value,onClose:u},{title:o(()=>e[4]||(e[4]=[t(" Delete Account ")])),content:o(()=>[e[5]||(e[5]=t(" Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ")),i("div",V,[r(b,{ref_key:"passwordInput",ref:n,modelValue:l(s).password,"onUpdate:modelValue":e[0]||(e[0]=f=>l(s).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",autocomplete:"current-password",onKeyup:v(m,["enter"])},null,8,["modelValue"]),r(C,{message:l(s).errors.password,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[r(d,{color:"white",size:"sm",onClick:u},{default:o(()=>e[6]||(e[6]=[t(" Cancel ")])),_:1}),r(d,{class:g(["ml-3",{"opacity-25":l(s).processing}]),color:"red",size:"sm",disabled:l(s).processing,onClick:m},{default:o(()=>e[7]||(e[7]=[t(" Delete Account ")])),_:1},8,["class","disabled"])]),_:1},8,["show"])]),_:1}))}};export{O as default};
