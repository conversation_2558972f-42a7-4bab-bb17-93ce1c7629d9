import{C as l}from"./clipboard-a66b13b3.js";import{e as i,a2 as u,o as p,d as m,r as f,n as y}from"./app-f0078ddb.js";const b=e=>{const t=(e==null?void 0:e.appendToBody)===void 0?!0:e.appendToBody;return{toClipboard(s,r){return new Promise((a,o)=>{const n=document.createElement("button"),d=new l(n,{text:()=>s,action:()=>"copy",container:r!==void 0?r:document.body});d.on("success",c=>{d.destroy(),a(c)}),d.on("error",c=>{d.destroy(),o(c)}),t&&document.body.appendChild(n),n.click(),t&&document.body.removeChild(n)})}}},k={__name:"CopyToClipboard",props:{data:String,message:String,fullWidth:{type:Boolean,default:!0}},setup(e){const t=e,{toClipboard:s}=b(),r=async()=>{try{await s(t.data)}catch(o){console.error(o)}},a=i(null);return u(a,{arrow:!1,content:t.message,placement:"bottom",onShown(o){setTimeout(()=>{o.hide()},1e3)}}),(o,n)=>(p(),m("button",{ref_key:"btn",ref:a,onClick:r,class:y(e.fullWidth?"w-full":"")},[f(o.$slots,"default")],2))}};export{k as _};
