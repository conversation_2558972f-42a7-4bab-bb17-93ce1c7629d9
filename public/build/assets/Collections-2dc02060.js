import{C as v,e as V,o as m,c as $,w as g,a as s,b as c,s as k,u as o,q as p,x as f,g as u,d as b,t as d,h as N,F as S}from"./app-f0078ddb.js";import{_ as T}from"./AppLayout-33f062bc.js";import{_ as B}from"./Breadcrumbs-c96e9207.js";import{_ as D}from"./DropdownWorks-53c01eb4.js";import{_ as y}from"./DropdownGeneral-ce7a4558.js";import{_ as w}from"./ButtonItem-718c0517.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./listbox-f702e976.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";const L={class:""},A={class:"p-8"},F={class:"mt-6 grid grid-cols-3 gap-4"},R={class:"px-4 py-5 sm:p-6"},K={class:"mt-4 grid grid-cols-1 gap-4"},M={class:"mt-1"},q={class:"mt-1"},z={class:"mt-1"},E={class:"mt-1"},W={class:"px-4 py-5 sm:p-6"},j={class:"mt-8"},G={class:"mt-8"},H={class:"mt-8 grid grid-cols-2 gap-8"},I={for:"start",class:"block text-sm font-medium text-gray-700"},J={key:0},O={key:1},P={class:"mt-1"},Q={for:"last",class:"block text-sm font-medium text-gray-700"},X={key:0},Y={key:1},Z={class:"mt-1"},tt={class:"px-4 py-5 sm:p-6"},st={class:"mt-8 grid max-h-[28rem] gap-4 overflow-y-auto overflow-y-scroll rounded-lg"},et={class:"flex items-center"},ot=["src"],lt={class:"ml-3 block flex flex-col truncate"},rt={class:"truncate text-base font-semibold"},nt={class:"text-sm font-medium text-gray-500"},it={class:"text-sm font-medium text-gray-900"},dt={class:"text-indigo-500"},at={class:"font-bold"},mt={class:"text-pink-500"},ut={class:"font-bold"},Pt={__name:"Collections",props:{collections:{type:Array},works:{type:Array}},setup(x){const a=x,_=[{name:"Dev",href:"/dev",current:!1},{name:"Add a Collection",href:"#",current:!0}],r=v({name:null,description:null,reference:null,url:null,work:a.works[0].id});let l=V(a.collections.length>0?a.collections[0]:null);const C=n=>{r.work=n.id},h=n=>{l.value=n.value,i.collection_id=n.value.id},U=n=>{i.book_id=n.value.id},i=v({collection_id:a.collections.length>0?a.collections[0].id:null,book_id:a.collections.length>0?a.collections[0].books[0].id:null,start:null,end:null});return(n,t)=>(m(),$(T,null,{default:g(()=>[s("main",L,[s("div",A,[c(B,{class:"lg:col-span-9 xl:grid-cols-10",pages:_}),s("div",F,[s("div",null,[s("form",{onSubmit:t[5]||(t[5]=k(e=>o(r).post("/dev/collections",{preserveScroll:!0,onSuccess:()=>o(r).reset()}),["prevent"]))},[s("div",R,[t[18]||(t[18]=s("h3",{class:"text-center text-3xl font-bold"}," Create a Collection ",-1)),c(D,{list:x.works,class:"mt-8","onUpdate:work":t[0]||(t[0]=e=>C(e))},null,8,["list"]),s("div",K,[s("div",null,[t[13]||(t[13]=s("label",{for:"name",class:"block text-sm font-medium text-gray-700"}," Collection Name ",-1)),s("div",M,[p(s("input",{id:"name","onUpdate:modelValue":t[1]||(t[1]=e=>o(r).name=e),type:"text",name:"name",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"Name of the Collection"},null,512),[[f,o(r).name]])])]),s("div",null,[t[14]||(t[14]=s("label",{for:"description",class:"block text-sm font-medium text-gray-700"}," Description ",-1)),s("div",q,[p(s("textarea",{id:"description","onUpdate:modelValue":t[2]||(t[2]=e=>o(r).description=e),name:"description",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"Describe the Collection"},null,512),[[f,o(r).description]])])]),s("div",null,[t[15]||(t[15]=s("label",{for:"reference",class:"block text-sm font-medium text-gray-700"}," Reference ",-1)),s("div",z,[p(s("input",{id:"reference","onUpdate:modelValue":t[3]||(t[3]=e=>o(r).reference=e),type:"text",name:"reference",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"Line references for the Collection"},null,512),[[f,o(r).reference]])])]),s("div",null,[t[16]||(t[16]=s("label",{for:"url",class:"block text-sm font-medium text-gray-700"}," URL ",-1)),s("div",E,[p(s("input",{id:"url","onUpdate:modelValue":t[4]||(t[4]=e=>o(r).url=e),type:"text",name:"url",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"URL for the Collection"},null,512),[[f,o(r).url]])])])]),c(w,{color:"indigo",size:"md",type:"submit",class:"mt-8 w-full"},{default:g(()=>t[17]||(t[17]=[u(" Create ")])),_:1})])],32)]),s("div",null,[s("form",{onSubmit:t[12]||(t[12]=k(e=>{o(i).post(`/dev/collections/${o(l).url}/tag`,{preserveScroll:!0,onSuccess:()=>{o(i).reset("start","end")}})},["prevent"]))},[s("div",W,[t[22]||(t[22]=s("h3",{class:"text-center text-3xl font-bold"},"Tag Verses",-1)),s("div",j,[c(y,{title:"Select the Collection",list:x.collections,"onUpdate:item":t[6]||(t[6]=e=>h(e)),current:o(l)},null,8,["list","current"])]),s("div",G,[c(y,{title:`Select the ${o(l).work.l1}`,list:o(l).books,"onUpdate:item":t[7]||(t[7]=e=>U(e)),current:o(l).books[0]},null,8,["title","list","current"])]),s("div",H,[s("div",null,[s("label",I,[t[19]||(t[19]=u("First ")),o(l).work.l2?(m(),b("span",J,d(o(l).work.l2),1)):(m(),b("span",O,d(o(l).work.l4),1))]),s("div",P,[p(s("input",{id:"start","onUpdate:modelValue":t[8]||(t[8]=e=>o(i).start=e),type:"text",name:"start",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 101",onKeypress:t[9]||(t[9]=(...e)=>n.onlyNumber&&n.onlyNumber(...e))},null,544),[[f,o(i).start]])])]),s("div",null,[s("label",Q,[t[20]||(t[20]=u("Last ")),o(l).work.l2?(m(),b("span",X,d(o(l).work.l2),1)):(m(),b("span",Y,d(o(l).work.l4),1))]),s("div",Z,[p(s("input",{id:"last","onUpdate:modelValue":t[10]||(t[10]=e=>o(i).end=e),type:"text",name:"last",class:"block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-500 focus:ring-teal-500 sm:text-sm",placeholder:"e.g., 157",onKeypress:t[11]||(t[11]=(...e)=>n.onlyNumber&&n.onlyNumber(...e))},null,544),[[f,o(i).end]])])])]),c(w,{class:"mt-8 mb-8 w-full",color:"pink",type:"submit"},{default:g(()=>t[21]||(t[21]=[u("Tag These Verses")])),_:1})])],32)]),s("div",null,[s("div",tt,[t[26]||(t[26]=s("h3",{class:"text-center text-3xl font-bold"}," Current Collections ",-1)),s("div",st,[(m(!0),b(S,null,N(x.collections,e=>(m(),b("div",{key:e.id,class:"mt-4"},[s("span",et,[s("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/${e.image}`,class:"h-16 w-16 shrink-0 rounded-full"},null,8,ot),s("div",lt,[s("span",rt,d(e.name),1),s("span",nt,d(e.reference),1),s("div",it,[s("span",dt,[s("span",at,d(e.verses_count),1),t[23]||(t[23]=u(" verses"))]),t[25]||(t[25]=u(" | ")),s("span",mt,[s("span",ut,d(e.sections_count),1),t[24]||(t[24]=u(" sections"))])])])])]))),128))])])])])])])]),_:1}))}};export{Pt as default};
