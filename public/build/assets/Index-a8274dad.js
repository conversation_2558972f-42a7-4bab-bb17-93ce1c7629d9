import{e as u,l as j,p as b,i as B,o,c as _,w as k,b as h,a as e,u as l,d as a,F as w,h as A,q as c,v as g,j as x,t as D,f as M,W as C}from"./app-f0078ddb.js";import{_ as N}from"./AppLayout-33f062bc.js";import{_ as L}from"./Breadcrumbs-c96e9207.js";import O from"./AssignmentItemStudent-2daa0e28.js";import W from"./AssignmentItemOwner-5f5b8c19.js";import{u as H}from"./useIntersect-6e15125e.js";import{_ as E}from"./Footer-0988dcd8.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./datetime-8ddd27a0.js";import"./ClassroomModal-05d10768.js";import"./InfinitasIcon-1a3ae135.js";import"./CheckBadgeIcon-e7792a17.js";import"./ProgressBar-b7203293.js";import"./UtilityDropdown-2d786282.js";import"./CopyToClipboard-21badf5d.js";import"./clipboard-a66b13b3.js";import"./AssignModule-74fae4e9.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";import"./Combobox-f427c07d.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./AssignReading-df4942fb.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./DropdownBooks-b7db1f80.js";import"./RectangleGroupIcon-c6b3f31f.js";const K={class:"p-8 pb-16"},P={class:"mt-16 w-full","aria-labelledby":"Content"},R={class:"mx-2 grid grid-cols-1 gap-8 lg:grid-cols-5"},T={class:"order-2 lg:order-1 lg:col-span-3"},z={class:"-m-2 flex rounded-xl bg-gray-900/5 p-2 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-2xl lg:p-4"},G={key:0,class:"w-full rounded-lg bg-white px-6 py-4 lg:rounded-xl"},J={key:1,class:"flex w-full items-center rounded-xl bg-white px-6 py-4 text-center text-sm sm:px-14"},Q={class:"order-1 -mt-4 lg:order-2 lg:col-span-2",id:"sticky"},X={class:"sticky top-20 rounded-xl bg-white p-2 lg:p-4"},Y={class:"grid grid-cols-1 gap-3 px-4 py-4"},Z={class:"relative flex items-center"},$={class:"flex h-5 items-center"},ee={class:"relative flex items-center"},te={class:"flex h-5 items-center"},se={class:"relative flex items-center"},re={class:"flex h-5 items-center"},oe={class:"relative flex items-center"},le={class:"flex h-5 items-center"},ie={key:0,class:"mt-8"},ae={class:"grid grid-cols-1 gap-3 px-4 py-4"},ne={class:"relative flex items-center"},de={class:"flex h-5 items-center"},me=["id","value","name"],pe={class:"ml-3 text-base"},ue=["for"],gt={__name:"Index",props:{assignments:{type:Object,required:!0},teams:{type:Array,required:!0},completedAssignments:{type:Array,required:!0},filters:{type:Array,required:!1},classFilters:{type:Array,required:!1},errors:{type:Object,required:!1},works:{type:Array,required:!1},books:{type:Array,required:!1}},setup(m){const f=m;let F=[{name:"Assignments",href:"#",current:!0}];const y=u(null);let n=u(f.assignments||{data:[],next_page_url:null}),d=u(n.value.data||[]);const q=j(()=>n.value.next_page_url!==null),V=()=>{q.value&&axios.get(n.value.next_page_url).then(p=>{d.value=[...d.value,...p.data.data],n.value=p.data})};y.value&&H(y,V,{rootMargin:"0px 0px 300px 0px"});let r=u(f.filters),i=u(f.classFilters);const S=()=>{r.value=[]},I=()=>{i.value=[]};return b(()=>r,()=>{C.get("/assignments",{filters:r.value,classFilters:i.value},{only:["assignments, filters","classFilters"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),b(()=>i,()=>{C.get("/assignments",{filters:r.value,classFilters:i.value},{only:["assignments, filters","classFilters"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),b(()=>f.assignments,p=>{n.value=p,d.value=n.value.data},{deep:!0}),(p,t)=>{const U=B("Head");return o(),_(N,null,{default:k(()=>[h(U,null,{default:k(()=>t[5]||(t[5]=[e("title",null,"Assignments",-1)])),_:1}),e("main",null,[e("div",K,[h(L,{class:"lg:col-span-9 xl:grid-cols-10",pages:l(F)},null,8,["pages"]),t[15]||(t[15]=e("section",{class:"mt-8 w-full py-6","aria-labelledby":"Description"},[e("h1",{class:"text-4xl font-bold text-gray-900"},"Assignments"),e("p",{class:"mt-4 text-base font-normal text-gray-600"}," Keep track of your work with assignments. ")],-1)),e("section",P,[e("div",R,[e("div",T,[e("div",z,[l(d)&&l(d).length>0?(o(),a("div",G,[(o(!0),a(w,null,A(l(d),(s,v)=>(o(),a("div",{key:v,class:"flex flex-row"},[s.is_student?(o(),_(O,{key:0,assignment:s,class:"w-full"},null,8,["assignment"])):(o(),_(W,{key:1,assignment:s.data,team:s.team,students:s.students,dashboard:!0,works:m.works,books:m.books},null,8,["assignment","team","students","works","books"]))]))),128)),e("div",{ref_key:"landmark",ref:y},null,512)])):(o(),a("div",J,t[6]||(t[6]=[e("div",{class:"mx-auto py-8"},[e("p",{class:"font-semibold text-gray-900"}," No assignments found "),e("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything. Please try again. ")],-1)])))])]),e("div",Q,[e("div",X,[e("div",{class:"flex flex-row items-center"},[t[7]||(t[7]=e("h3",{class:"text-2xl font-bold text-gray-900"},"Assignments",-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold uppercase text-blue-500 opacity-100 hover:text-blue-600",onClick:S}," Clear ")])]),t[14]||(t[14]=e("p",{class:"mt-2 text-sm text-gray-600"}," Filter your assignments to better manage what you need to and have done. ",-1)),e("div",Y,[e("div",Z,[e("div",$,[c(e("input",{id:"to-do","onUpdate:modelValue":t[0]||(t[0]=s=>x(r)?r.value=s:r=s),value:"to-do",name:"to-do",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,l(r)]])]),t[8]||(t[8]=e("div",{class:"ml-3 text-base"},[e("label",{for:"to-do",class:"cursor-pointer font-semibold text-gray-900"},"To do")],-1))]),e("div",ee,[e("div",te,[c(e("input",{id:"in-progress","onUpdate:modelValue":t[1]||(t[1]=s=>x(r)?r.value=s:r=s),value:"in-progress",name:"in-progress",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,l(r)]])]),t[9]||(t[9]=e("div",{class:"ml-3 text-base"},[e("label",{for:"in-progress",class:"cursor-pointer font-semibold text-gray-900"},"In progress")],-1))]),e("div",se,[e("div",re,[c(e("input",{id:"completed","onUpdate:modelValue":t[2]||(t[2]=s=>x(r)?r.value=s:r=s),value:"completed",name:"completed",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,l(r)]])]),t[10]||(t[10]=e("div",{class:"ml-3 text-base"},[e("label",{for:"completed",class:"cursor-pointer font-semibold text-gray-900"},"Completed")],-1))]),e("div",oe,[e("div",le,[c(e("input",{id:"late","onUpdate:modelValue":t[3]||(t[3]=s=>x(r)?r.value=s:r=s),value:"late",name:"late",type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,l(r)]])]),t[11]||(t[11]=e("div",{class:"ml-3 text-base"},[e("label",{for:"late",class:"cursor-pointer font-semibold text-gray-900"},"Done Late")],-1))])]),m.teams.length>0?(o(),a("div",ie,[e("div",{class:"flex flex-row items-center"},[t[12]||(t[12]=e("h3",{class:"text-2xl font-bold text-gray-900"},"Classes",-1)),e("div",{class:"flex-1 text-right"},[e("button",{class:"font-bold uppercase text-blue-500 opacity-100 hover:text-blue-600",onClick:I}," Clear ")])]),t[13]||(t[13]=e("p",{class:"mt-2 text-sm text-gray-600"}," Filter your assignments by your class. ",-1)),e("div",ae,[(o(!0),a(w,null,A(m.teams,s=>(o(),a("div",ne,[e("div",de,[c(e("input",{id:s.slug,"onUpdate:modelValue":t[4]||(t[4]=v=>x(i)?i.value=v:i=v),value:s.slug,name:s.slug,type:"checkbox",class:"h-4 w-4 cursor-pointer rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,me),[[g,l(i)]])]),e("div",pe,[e("label",{for:s.slug,class:"cursor-pointer font-semibold text-gray-900"},D(s.name),9,ue)])]))),256))])])):M("",!0)])])])]),h(E)])])]),_:1})}}};export{gt as default};
