import{l as a,i as g,o as b,c as n,w as u,r as i,n as d,d as h}from"./app-f0078ddb.js";const c=["type","disabled"],v={__name:"ButtonItem",props:{type:{type:String,default:"submit"},size:{default:"md"},color:{default:"primary"},link:{},data:{},method:{default:"get"},disabled:{default:!1}},setup(e){const t=e,r=a(()=>({xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm leading-4",md:"px-4 py-2 text-sm",lg:"px-4 py-1 text-base",xl:"px-6 py-2 text-lg"})[t.size]),o=a(()=>({primary:"text-white bg-blue-600 hover:bg-blue-700",secondary:"text-blue-700 bg-blue-100 ",white:"text-slate-700 border border-slate-300 bg-white hover:bg-slate-100",gray:"text-slate-900 bg-slate-200 hover:bg-slate-300",red:"bg-red-500 text-white hover:bg-red-600",green:"text-white bg-green-600 hover:bg-green-700",black:"text-white bg-slate-800 hover:bg-slate-700 border border-slate-800 hover:border-slate-700",purple:"bg-purple-600 text-white hover:bg-purple-700",indigo:"bg-indigo-600 text-white hover:bg-indigo-700",indigoOutline:"text-indigo-600 bg-white border border-indigo-600 hover:bg-indigo-50",orange:"bg-orange-500 text-white hover:bg-orange-600",pink:"text-white bg-pink-500 hover:bg-pink-600",blue:"text-white bg-blue-500 hover:bg-blue-600",blueOutline:"text-blue-600 border border-blue-600 hover:bg-blue-50",teal:"text-white bg-teal-500 hover:bg-teal-600",tealOutline:"text-teal-600 border border-teal-600 hover:bg-teal-50",sky:"bg-sky-500 text-white hover:bg-sky-600",skyOutline:"text-sky-600 border border-sky-600 hover:bg-sky-100",amber:"bg-amber-300 text-black hover:bg-amber-400",lightPurple:"bg-purple-100 text-purple-700 hover:bg-purple-200 ",lightOrange:"bg-orange-100 text-orange-600 hover:bg-orange-200 ",lightGray:"bg-slate-100 text-slate-700 hover:bg-slate-200 ",lightSky:"bg-sky-100 text-sky-700 hover:bg-sky-200 ",lightBlue:"bg-blue-100 text-blue-700 hover:bg-blue-200 ",lightRed:"bg-red-100 text-red-700 hover:bg-red-200 ",lightGreen:"bg-emerald-100 text-emerald-700 hover:bg-emerald-200 "})[t.color]);return(l,x)=>{const s=g("Link");return e.link?(b(),n(s,{key:0,as:"button",href:e.link,method:e.method,data:e.data,disabled:e.disabled,class:d(["cursor-pointer items-center rounded-lg text-center font-bold transition duration-150 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75",[r.value,o.value]])},{default:u(()=>[i(l.$slots,"default")]),_:3},8,["href","method","data","disabled","class"])):(b(),h("button",{key:1,type:e.type,class:d(["cursor-pointer items-center rounded-lg font-bold transition duration-150 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-75",[r.value,o.value]]),disabled:e.disabled},[i(l.$slots,"default")],10,c))}}};export{v as _};
