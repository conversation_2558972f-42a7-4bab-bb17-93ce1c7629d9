.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;white-space:normal;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}.tippy-box[data-theme~=light]{color:#26323d;box-shadow:0 0 20px 4px #9aa1b126,0 4px 80px -8px #24282f40,0 4px 4px -2px #5b5e6926;background-color:#fff}.tippy-box[data-theme~=light][data-placement^=top]>.tippy-arrow:before{border-top-color:#fff}.tippy-box[data-theme~=light][data-placement^=bottom]>.tippy-arrow:before{border-bottom-color:#fff}.tippy-box[data-theme~=light][data-placement^=left]>.tippy-arrow:before{border-left-color:#fff}.tippy-box[data-theme~=light][data-placement^=right]>.tippy-arrow:before{border-right-color:#fff}.tippy-box[data-theme~=light]>.tippy-backdrop{background-color:#fff}.tippy-box[data-theme~=light]>.tippy-svg-arrow{fill:#fff}.vc-popover-content-wrapper{--popover-horizontal-content-offset: 8px;--popover-vertical-content-offset: 10px;--popover-caret-horizontal-offset: 18px;--popover-caret-vertical-offset: 8px;position:absolute;display:block;outline:none;z-index:10}.vc-popover-content-wrapper:not(.is-interactive){pointer-events:none}.vc-popover-content{position:relative;color:var(--vc-popover-content-color);font-weight:var(--vc-font-medium);background-color:var(--vc-popover-content-bg);border:1px solid;border-color:var(--vc-popover-content-border);border-radius:var(--vc-rounded-lg);padding:4px;outline:none;z-index:10;box-shadow:var(--vc-shadow-lg)}.vc-popover-content.direction-bottom{margin-top:var(--popover-vertical-content-offset)}.vc-popover-content.direction-top{margin-bottom:var(--popover-vertical-content-offset)}.vc-popover-content.direction-left{margin-right:var(--popover-horizontal-content-offset)}.vc-popover-content.direction-right{margin-left:var(--popover-horizontal-content-offset)}.vc-popover-caret{content:"";position:absolute;display:block;width:12px;height:12px;border-top:inherit;border-left:inherit;background-color:inherit;z-index:-1}.vc-popover-caret.direction-bottom{top:0}.vc-popover-caret.direction-bottom.align-left{-webkit-transform:translateY(-50%) rotate(45deg);transform:translateY(-50%) rotate(45deg)}.vc-popover-caret.direction-bottom.align-center{-webkit-transform:translateX(-50%) translateY(-50%) rotate(45deg);transform:translate(-50%) translateY(-50%) rotate(45deg)}.vc-popover-caret.direction-bottom.align-right{-webkit-transform:translateY(-50%) rotate(45deg);transform:translateY(-50%) rotate(45deg)}.vc-popover-caret.direction-top{top:100%}.vc-popover-caret.direction-top.align-left{-webkit-transform:translateY(-50%) rotate(-135deg);transform:translateY(-50%) rotate(-135deg)}.vc-popover-caret.direction-top.align-center{-webkit-transform:translateX(-50%) translateY(-50%) rotate(-135deg);transform:translate(-50%) translateY(-50%) rotate(-135deg)}.vc-popover-caret.direction-top.align-right{-webkit-transform:translateY(-50%) rotate(-135deg);transform:translateY(-50%) rotate(-135deg)}.vc-popover-caret.direction-left{left:100%}.vc-popover-caret.direction-left.align-top{-webkit-transform:translateX(-50%) rotate(135deg);transform:translate(-50%) rotate(135deg)}.vc-popover-caret.direction-left.align-middle{-webkit-transform:translateY(-50%) translateX(-50%) rotate(135deg);transform:translateY(-50%) translate(-50%) rotate(135deg)}.vc-popover-caret.direction-left.align-bottom{-webkit-transform:translateX(-50%) rotate(135deg);transform:translate(-50%) rotate(135deg)}.vc-popover-caret.direction-right{left:0}.vc-popover-caret.direction-right.align-top{-webkit-transform:translateX(-50%) rotate(-45deg);transform:translate(-50%) rotate(-45deg)}.vc-popover-caret.direction-right.align-middle{-webkit-transform:translateY(-50%) translateX(-50%) rotate(-45deg);transform:translateY(-50%) translate(-50%) rotate(-45deg)}.vc-popover-caret.direction-right.align-bottom{-webkit-transform:translateX(-50%) rotate(-45deg);transform:translate(-50%) rotate(-45deg)}.vc-popover-caret.align-left{left:var(--popover-caret-horizontal-offset)}.vc-popover-caret.align-center{left:50%}.vc-popover-caret.align-right{right:var(--popover-caret-horizontal-offset)}.vc-popover-caret.align-top{top:var(--popover-caret-vertical-offset)}.vc-popover-caret.align-middle{top:50%}.vc-popover-caret.align-bottom{bottom:var(--popover-caret-vertical-offset)}.vc-day-popover-row{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-ms-flex-align:center;align-items:center;transition:var(--vc-day-content-transition)}.vc-day-popover-row-indicator{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-flex-grow:0;-ms-flex-positive:0;flex-grow:0;width:15px}.vc-day-popover-row-indicator span{transition:var(--vc-day-content-transition)}.vc-day-popover-row-label{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-flex-wrap:none;-ms-flex-wrap:none;flex-wrap:none;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;width:-webkit-max-content;width:max-content;margin-left:4px;margin-right:4px;font-size:var(--vc-text-xs);line-height:var(--vc-leading-normal)}.vc-day-popover-row-highlight{width:8px;height:5px;border-radius:3px}.vc-day-popover-row-bar{width:10px;height:3px}.vc-base-icon{display:inline-block;stroke:currentColor;stroke-width:2;fill:none}.vc-header{display:grid;grid-gap:4px;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:30px;margin-top:10px;padding-left:10px;padding-right:10px}.vc-header.is-lg{font-size:var(--vc-text-lg)}.vc-header.is-xl{font-size:var(--vc-text-xl)}.vc-header.is-2xl{font-size:var(--vc-text-2xl)}.vc-header .vc-title-wrapper{grid-row:1;grid-column:title}.vc-header .vc-prev{grid-row:1;grid-column:prev}.vc-header .vc-next{grid-row:1;grid-column:next}.vc-header .vc-title,.vc-header .vc-prev,.vc-header .vc-next{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-ms-flex-align:center;align-items:center;border:0;border-radius:var(--vc-rounded);pointer-events:auto;-webkit-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vc-header .vc-title{color:var(--vc-header-title-color);font-weight:var(--vc-font-semibold);white-space:nowrap;padding:0 8px;margin:0;line-height:30px}.vc-header .vc-title:hover{opacity:.75}.vc-header .vc-arrow{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;color:var(--vc-header-arrow-color);width:28px;height:30px;margin:0;padding:0}.vc-header .vc-arrow:hover{background:var(--vc-header-arrow-hover-bg)}.vc-header .vc-arrow:disabled{opacity:.25;pointer-events:none}.vc-nav-header{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:space-between;-ms-flex-pack:justify;justify-content:space-between}.vc-nav-title,.vc-nav-arrow,.vc-nav-item{font-size:var(--vc-text-sm);margin:0;cursor:pointer;-webkit-user-select:none;-ms-user-select:none;user-select:none;border:0;border-radius:var(--vc-rounded);white-space:nowrap}.vc-nav-title:hover,.vc-nav-arrow:hover,.vc-nav-item:hover{background-color:var(--vc-nav-hover-bg)}.vc-nav-title:disabled,.vc-nav-arrow:disabled,.vc-nav-item:disabled{opacity:.25;pointer-events:none}.vc-nav-title{color:var(--vc-nav-title-color);font-weight:var(--vc-font-bold);line-height:var(--vc-leading-snug);height:30px;padding:0 6px}.vc-nav-arrow{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;color:var(--vc-header-arrow-color);width:26px;height:30px;padding:0}.vc-nav-items{display:grid;grid-template-columns:repeat(3,1fr);grid-row-gap:2px;grid-column-gap:5px;margin-top:2px}.vc-nav-item{width:48px;text-align:center;font-weight:var(--vc-font-semibold);line-height:var(--vc-leading-snug);padding:6px 0}.vc-nav-item.is-active{color:var(--vc-nav-item-active-color);background-color:var(--vc-nav-item-active-bg);font-weight:var(--vc-font-bold)}.vc-nav-item.is-active:not(:focus){box-shadow:var(--vc-nav-item-active-box-shadow)}.vc-nav-item.is-current{color:var(--vc-nav-item-current-color)}.vc-day{position:relative;min-height:32px;z-index:1}.vc-monthly .is-not-in-month *{opacity:0;pointer-events:none}.vc-day-layer{position:absolute;left:0;right:0;top:0;bottom:0;pointer-events:none}.vc-day-box-center-center{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-transform-origin:50% 50%;transform-origin:50% 50%}.vc-day-box-left-center{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:flex-start;-ms-flex-pack:start;justify-content:flex-start;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-transform-origin:0% 50%;transform-origin:0% 50%}.vc-day-box-right-center{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:flex-end;-ms-flex-pack:end;justify-content:flex-end;-webkit-align-items:center;-ms-flex-align:center;align-items:center;-webkit-transform-origin:100% 50%;transform-origin:100% 50%}.vc-day-box-center-bottom{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:flex-end;-ms-flex-align:end;align-items:flex-end}.vc-day-content{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;font-size:var(--vc-text-sm);font-weight:var(--vc-font-medium);width:28px;height:28px;line-height:28px;border-radius:var(--vc-rounded-full);-webkit-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vc-day-content:hover{background-color:var(--vc-day-content-hover-bg)}.vc-day-content.vc-disabled{color:var(--vc-day-content-disabled-color)}.vc-content:not(.vc-base){font-weight:var(--vc-font-bold);color:var(--vc-content-color)}.vc-highlights{overflow:hidden;pointer-events:none;z-index:-1}.vc-highlight{width:28px;height:28px}.vc-highlight.vc-highlight-base-start{width:50%!important;border-radius:0!important;border-right-width:0!important}.vc-highlight.vc-highlight-base-end{width:50%!important;border-radius:0!important;border-left-width:0!important}.vc-highlight.vc-highlight-base-middle{width:100%;border-radius:0!important;border-left-width:0!important;border-right-width:0!important;margin:0 -1px}.vc-highlight-bg-outline,.vc-highlight-bg-none{background-color:var(--vc-highlight-outline-bg);border:2px solid;border-color:var(--vc-highlight-outline-border);border-radius:var(--vc-rounded-full)}.vc-highlight-bg-light{background-color:var(--vc-highlight-light-bg);border-radius:var(--vc-rounded-full)}.vc-highlight-bg-solid{background-color:var(--vc-highlight-solid-bg);border-radius:var(--vc-rounded-full)}.vc-highlight-content-outline,.vc-highlight-content-none{font-weight:var(--vc-font-bold);color:var(--vc-highlight-outline-content-color)}.vc-highlight-content-light{font-weight:var(--vc-font-bold);color:var(--vc-highlight-light-content-color)}.vc-highlight-content-solid{font-weight:var(--vc-font-bold);color:var(--vc-highlight-solid-content-color)}.vc-dots{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center}.vc-dot{width:5px;height:5px;border-radius:9999px;transition:var(--vc-day-content-transition)}.vc-dot:not(:last-child){margin-right:3px}.vc-bars{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:flex-start;-ms-flex-pack:start;justify-content:flex-start;-webkit-align-items:center;-ms-flex-align:center;align-items:center;width:75%}.vc-bar{-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;height:3px;transition:var(--vc-day-content-transition)}.vc-dot{background-color:var(--vc-dot-bg)}.vc-bar{background-color:var(--vc-bar-bg)}.vc-pane{min-width:250px}.vc-weeknumber{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;position:absolute}.vc-weeknumber.is-left{left:calc(var(--vc-weeknumber-offset-inside) * -1)}.vc-weeknumber.is-right{right:calc(var(--vc-weeknumber-offset-inside) * -1)}.vc-weeknumber.is-left-outside{left:calc(var(--vc-weeknumber-offset-outside) * -1)}.vc-weeknumber.is-right-outside{right:calc(var(--vc-weeknumber-offset-outside) * -1)}.vc-weeknumber-content{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;font-size:var(--vc-text-xs);font-weight:var(--vc-font-medium);font-style:italic;width:28px;height:28px;margin-top:2px;color:var(--vc-weeknumber-color);-webkit-user-select:none;-ms-user-select:none;user-select:none}.vc-weeks{position:relative;-webkit-overflow-scrolling:touch;padding:6px;min-width:232px}.vc-weeks.vc-show-weeknumbers-left{margin-left:var(--vc-weeknumber-offset-inside)}.vc-weeks.vc-show-weeknumbers-right{margin-right:var(--vc-weeknumber-offset-inside)}.vc-weekday{text-align:center;color:var(--vc-weekday-color);font-size:var(--vc-text-sm);font-weight:var(--vc-font-bold);line-height:14px;padding-top:4px;padding-bottom:8px;cursor:default;-webkit-user-select:none;-ms-user-select:none;user-select:none}.vc-week,.vc-weekdays{display:grid;grid-template-columns:repeat(7,1fr);position:relative}.vc-pane-container{width:100%;position:relative}.vc-pane-container.in-transition{overflow:hidden}.vc-pane-layout{display:grid}.vc-pane-header-wrapper{position:absolute;top:0;width:100%;pointer-events:none}.vc-day-popover-container{font-size:var(--vc-text-xs);font-weight:var(--vc-font-medium)}.vc-day-popover-header{font-size:var(--vc-text-xs);color:var(--vc-day-popover-header-color);font-weight:var(--vc-font-semibold);text-align:center}.vc-base-select{position:relative;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:30px;font-size:var(--vc-text-base);font-weight:var(--vc-font-medium)}.vc-base-select.vc-has-icon select{padding:0 27px 0 9px}.vc-base-select.vc-has-icon .vc-base-sizer{padding:0 28px 0 10px}.vc-base-select.vc-fit-content select{position:absolute;top:0;left:0;width:100%}.vc-base-select .vc-base-icon{position:absolute;top:6px;right:4px;opacity:.6;pointer-events:none}.vc-base-select .vc-base-sizer{font-size:var(--vc-text-base);font-weight:var(--vc-font-medium);color:transparent;padding:0 8px;margin:0}.vc-base-select select{display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;color:var(--vc-select-color);display:block;-webkit-appearance:none;-moz-appearance:none;appearance:none;background-color:var(--vc-select-bg);border-radius:var(--vc-rounded);height:30px;width:-webkit-max-content;width:max-content;padding:0 7px;margin:0;line-height:var(--leading-none);text-indent:0px;background-image:none;cursor:pointer;text-align:center}.vc-base-select select:hover{background-color:var(--vc-select-hover-bg)}.vc-base-select select.vc-align-left{text-align:left}.vc-base-select select.vc-align-right{text-align:right}.vc-time-picker{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding:8px 4px}.vc-time-picker.vc-invalid{pointer-events:none;opacity:.5}.vc-time-picker.vc-attached{border-top:1px solid var(--vc-time-picker-border)}.vc-time-picker>*+*{margin-top:4px}.vc-time-header{display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-ms-flex-align:center;align-items:center;font-size:var(--vc-text-sm);font-weight:var(--vc-font-semibold);text-transform:uppercase;margin-top:-4px;padding-left:4px;padding-right:4px;line-height:21px}.vc-time-select-group{display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;-webkit-align-items:center;-ms-flex-align:center;align-items:center;padding:0 4px;background:var(--vc-time-select-group-bg);border-radius:var(--vc-rounded-md);border:1px solid var(--vc-time-select-group-border)}.vc-time-select-group .vc-base-icon{margin-right:4px;color:var(--vc-time-select-group-icon-color)}.vc-time-select-group select{background:transparent;padding:0 4px}.vc-time-weekday{color:var(--vc-time-weekday-color);letter-spacing:var(--tracking-wide)}.vc-time-month{color:var(--vc-time-month-color);margin-left:8px}.vc-time-day{color:var(--vc-time-day-color);margin-left:4px}.vc-time-year{color:var(--vc-time-year-color);margin-left:8px}.vc-time-colon{margin:0 1px 2px 2px}.vc-time-decimal{margin:0 0 0 1px}.vc-none-enter-active,.vc-none-leave-active{transition-duration:0s}.vc-fade-enter-active,.vc-fade-leave-active,.vc-slide-left-enter-active,.vc-slide-left-leave-active,.vc-slide-right-enter-active,.vc-slide-right-leave-active,.vc-slide-up-enter-active,.vc-slide-up-leave-active,.vc-slide-down-enter-active,.vc-slide-down-leave-active,.vc-slide-fade-enter-active,.vc-slide-fade-leave-active{transition:opacity var(--vc-slide-duration) var(--vc-slide-timing),-webkit-transform var(--vc-slide-duration) var(--vc-slide-timing);transition:transform var(--vc-slide-duration) var(--vc-slide-timing),opacity var(--vc-slide-duration) var(--vc-slide-timing);transition:transform var(--vc-slide-duration) var(--vc-slide-timing),opacity var(--vc-slide-duration) var(--vc-slide-timing),-webkit-transform var(--vc-slide-duration) var(--vc-slide-timing);-webkit-backface-visibility:hidden;backface-visibility:hidden;pointer-events:none}.vc-none-leave-active,.vc-fade-leave-active,.vc-slide-left-leave-active,.vc-slide-right-leave-active,.vc-slide-up-leave-active,.vc-slide-down-leave-active{position:absolute!important;width:100%}.vc-none-enter-from,.vc-none-leave-to,.vc-fade-enter-from,.vc-fade-leave-to,.vc-slide-left-enter-from,.vc-slide-left-leave-to,.vc-slide-right-enter-from,.vc-slide-right-leave-to,.vc-slide-up-enter-from,.vc-slide-up-leave-to,.vc-slide-down-enter-from,.vc-slide-down-leave-to,.vc-slide-fade-enter-from,.vc-slide-fade-leave-to{opacity:0}.vc-slide-left-enter-from,.vc-slide-right-leave-to,.vc-slide-fade-enter-from.direction-left,.vc-slide-fade-leave-to.direction-left{-webkit-transform:translateX(var(--vc-slide-translate));transform:translate(var(--vc-slide-translate))}.vc-slide-right-enter-from,.vc-slide-left-leave-to,.vc-slide-fade-enter-from.direction-right,.vc-slide-fade-leave-to.direction-right{-webkit-transform:translateX(calc(-1 * var(--vc-slide-translate)));transform:translate(calc(-1 * var(--vc-slide-translate)))}.vc-slide-up-enter-from,.vc-slide-down-leave-to,.vc-slide-fade-enter-from.direction-top,.vc-slide-fade-leave-to.direction-top{-webkit-transform:translateY(var(--vc-slide-translate));transform:translateY(var(--vc-slide-translate))}.vc-slide-down-enter-from,.vc-slide-up-leave-to,.vc-slide-fade-enter-from.direction-bottom,.vc-slide-fade-leave-to.direction-bottom{-webkit-transform:translateY(calc(-1 * var(--vc-slide-translate)));transform:translateY(calc(-1 * var(--vc-slide-translate)))}:root{--vc-white: #ffffff;--vc-black: #000000;--vc-gray-50: #f8fafc;--vc-gray-100: #f1f5f9;--vc-gray-200: #e2e8f0;--vc-gray-300: #cbd5e1;--vc-gray-400: #94a3b8;--vc-gray-500: #64748b;--vc-gray-600: #475569;--vc-gray-700: #334155;--vc-gray-800: #1e293b;--vc-gray-900: #0f172a;--vc-font-family: BlinkMacSystemFont, -apple-system, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", "Helvetica", "Arial", sans-serif;--vc-font-normal: 400;--vc-font-medium: 500;--vc-font-semibold: 600;--vc-font-bold: 700;--vc-text-2xs: 10px;--vc-text-xs: 12px;--vc-text-sm: 14px;--vc-text-base: 16px;--vc-text-lg: 18px;--vc-text-xl: 20px;--vc-text-2xl: 24px;--vc-leading-none: 1;--vc-leading-tight: 1.25;--vc-leading-snug: 1.375;--vc-leading-normal: 1.5;--vc-rounded: .25rem;--vc-rounded-md: .375rem;--vc-rounded-lg: .5rem;--vc-rounded-full: 9999px;--vc-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px 0 rgba(0, 0, 0, .06);--vc-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05);--vc-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, .06);--vc-slide-translate: 22px;--vc-slide-duration: .15s;--vc-slide-timing: ease;--vc-day-content-transition: all .13s ease-in;--vc-weeknumber-offset-inside: 26px;--vc-weeknumber-offset-outside: 34px}.vc-gray{--vc-accent-50: var(--vc-gray-50);--vc-accent-100: var(--vc-gray-100);--vc-accent-200: var(--vc-gray-200);--vc-accent-300: var(--vc-gray-300);--vc-accent-400: var(--vc-gray-400);--vc-accent-500: var(--vc-gray-500);--vc-accent-600: var(--vc-gray-600);--vc-accent-700: var(--vc-gray-700);--vc-accent-800: var(--vc-gray-800);--vc-accent-900: var(--vc-gray-900)}.vc-red{--vc-accent-50: #fef2f2;--vc-accent-100: #fee2e2;--vc-accent-200: #fecaca;--vc-accent-300: #fca5a5;--vc-accent-400: #f87171;--vc-accent-500: #ef4444;--vc-accent-600: #dc2626;--vc-accent-700: #b91c1c;--vc-accent-800: #991b1b;--vc-accent-900: #7f1d1d}.vc-orange{--vc-accent-50: #fff7ed;--vc-accent-100: #ffedd5;--vc-accent-200: #fed7aa;--vc-accent-300: #fdba74;--vc-accent-400: #fb923c;--vc-accent-500: #f97316;--vc-accent-600: #ea580c;--vc-accent-700: #c2410c;--vc-accent-800: #9a3412;--vc-accent-900: #7c2d12}.vc-yellow{--vc-accent-50: #fefce8;--vc-accent-100: #fef9c3;--vc-accent-200: #fef08a;--vc-accent-300: #fde047;--vc-accent-400: #facc15;--vc-accent-500: #eab308;--vc-accent-600: #ca8a04;--vc-accent-700: #a16207;--vc-accent-800: #854d0e;--vc-accent-900: #713f12}.vc-green{--vc-accent-50: #f0fdf4;--vc-accent-100: #dcfce7;--vc-accent-200: #bbf7d0;--vc-accent-300: #86efac;--vc-accent-400: #4ade80;--vc-accent-500: #22c55e;--vc-accent-600: #16a34a;--vc-accent-700: #15803d;--vc-accent-800: #166534;--vc-accent-900: #14532d}.vc-teal{--vc-accent-50: #f0fdfa;--vc-accent-100: #ccfbf1;--vc-accent-200: #99f6e4;--vc-accent-300: #5eead4;--vc-accent-400: #2dd4bf;--vc-accent-500: #14b8a6;--vc-accent-600: #0d9488;--vc-accent-700: #0f766e;--vc-accent-800: #115e59;--vc-accent-900: #134e4a}.vc-blue{--vc-accent-50: #eff6ff;--vc-accent-100: #dbeafe;--vc-accent-200: #bfdbfe;--vc-accent-300: #93c5fd;--vc-accent-400: #60a5fa;--vc-accent-500: #3b82f6;--vc-accent-600: #2563eb;--vc-accent-700: #1d4ed8;--vc-accent-800: #1e40af;--vc-accent-900: #1e3a8a}.vc-indigo{--vc-accent-50: #eef2ff;--vc-accent-100: #e0e7ff;--vc-accent-200: #c7d2fe;--vc-accent-300: #a5b4fc;--vc-accent-400: #818cf8;--vc-accent-500: #6366f1;--vc-accent-600: #4f46e5;--vc-accent-700: #4338ca;--vc-accent-800: #3730a3;--vc-accent-900: #312e81}.vc-purple{--vc-accent-50: #faf5ff;--vc-accent-100: #f3e8ff;--vc-accent-200: #e9d5ff;--vc-accent-300: #d8b4fe;--vc-accent-400: #c084fc;--vc-accent-500: #a855f7;--vc-accent-600: #9333ea;--vc-accent-700: #7e22ce;--vc-accent-800: #6b21a8;--vc-accent-900: #581c87}.vc-pink{--vc-accent-50: #fdf2f8;--vc-accent-100: #fce7f3;--vc-accent-200: #fbcfe8;--vc-accent-300: #f9a8d4;--vc-accent-400: #f472b6;--vc-accent-500: #ec4899;--vc-accent-600: #db2777;--vc-accent-700: #be185d;--vc-accent-800: #9d174d;--vc-accent-900: #831843}.vc-focus:focus-within{outline:0;box-shadow:var(--vc-focus-ring)}.vc-light{--vc-color: var(--vc-gray-900);--vc-bg: var(--vc-white);--vc-border: var(--vc-gray-300);--vc-hover-bg: hsla(211, 25%, 84%, .3);--vc-focus-ring: 0 0 0 2px rgb(59, 131, 246, .4);--vc-header-arrow-color: var(--vc-gray-500);--vc-header-arrow-hover-bg: var(--vc-gray-200);--vc-header-title-color: var(--vc-gray-900);--vc-weekday-color: var(--vc-gray-500);--vc-weeknumber-color: var(--vc-gray-400);--vc-nav-hover-bg: var(--vc-gray-200);--vc-nav-title-color: var(--vc-gray-900);--vc-nav-item-hover-box-shadow: none;--vc-nav-item-active-color: var(--vc-white);--vc-nav-item-active-bg: var(--vc-accent-500);--vc-nav-item-active-box-shadow: var(--vc-shadow);--vc-nav-item-current-color: var(--vc-accent-600);--vc-day-popover-container-color: var(--vc-white);--vc-day-popover-container-bg: var(--vc-gray-800);--vc-day-popover-container-border: var(--vc-gray-700);--vc-day-popover-header-color: var(--vc-gray-700);--vc-popover-content-color: var(--vc-gray-900);--vc-popover-content-bg: var(--vc-gray-50);--vc-popover-content-border: var(--vc-gray-300);--vc-time-picker-border: var(--vc-gray-300);--vc-time-weekday-color: var(--vc-gray-700);--vc-time-month-color: var(--vc-accent-600);--vc-time-day-color: var(--vc-accent-600);--vc-time-year-color: var(--vc-gray-500);--vc-time-select-group-bg: var(--vc-gray-50);--vc-time-select-group-border: var(--vc-gray-300);--vc-time-select-group-icon-color: var(--vc-accent-500);--vc-select-color: var(--vc-gray-900);--vc-select-bg: var(--vc-gray-100);--vc-select-hover-bg: var(--vc-gray-200);--vc-day-content-hover-bg: var(--vc-hover-bg);--vc-day-content-disabled-color: var(--vc-gray-400)}.vc-light.vc-attr,.vc-light .vc-attr{--vc-content-color: var(--vc-accent-600);--vc-highlight-outline-bg: var(--vc-white);--vc-highlight-outline-border: var(--vc-accent-600);--vc-highlight-outline-content-color: var(--vc-accent-700);--vc-highlight-light-bg: var(--vc-accent-200);--vc-highlight-light-content-color: var(--vc-accent-900);--vc-highlight-solid-bg: var(--vc-accent-600);--vc-highlight-solid-content-color: var(--vc-white);--vc-dot-bg: var(--vc-accent-600);--vc-bar-bg: var(--vc-accent-600)}.vc-dark{--vc-color: var(--vc-white);--vc-bg: var(--vc-gray-900);--vc-border: var(--vc-gray-700);--vc-hover-bg: hsla(216, 15%, 52%, .3);--vc-focus-ring: 0 0 0 2px rgb(59 130 246 / .7);--vc-header-arrow-color: var(--vc-gray-300);--vc-header-arrow-hover-bg: var(--vc-gray-800);--vc-header-title-color: var(--vc-gray-100);--vc-weekday-color: var(--vc-accent-200);--vc-weeknumber-color: var(--vc-gray-500);--vc-nav-hover-bg: var(--vc-gray-700);--vc-nav-title-color: var(--vc-gray-100);--vc-nav-item-hover-box-shadow: none;--vc-nav-item-active-color: var(--vc-white);--vc-nav-item-active-bg: var(--vc-accent-500);--vc-nav-item-active-box-shadow: none;--vc-nav-item-current-color: var(--vc-accent-400);--vc-day-popover-container-color: var(--vc-gray-800);--vc-day-popover-container-bg: var(--vc-white);--vc-day-popover-container-border: var(--vc-gray-100);--vc-day-popover-header-color: var(--vc-gray-300);--vc-popover-content-color: var(--vc-white);--vc-popover-content-bg: var(--vc-gray-800);--vc-popover-content-border: var(--vc-gray-700);--vc-time-picker-border: var(--vc-gray-700);--vc-time-weekday-color: var(--vc-gray-400);--vc-time-month-color: var(--vc-accent-400);--vc-time-day-color: var(--vc-accent-400);--vc-time-year-color: var(--vc-gray-500);--vc-time-select-group-bg: var(--vc-gray-700);--vc-time-select-group-border: var(--vc-gray-500);--vc-time-select-group-icon-color: var(--vc-accent-400);--vc-select-color: var(--vc-gray-200);--vc-select-bg: var(--vc-gray-700);--vc-select-hover-bg: var(--vc-gray-600);--vc-day-content-hover-bg: var(--vc-hover-bg);--vc-day-content-disabled-color: var(--vc-gray-600)}.vc-dark.vc-attr,.vc-dark .vc-attr{--vc-content-color: var(--vc-accent-500);--vc-highlight-outline-bg: var(--vc-gray-900);--vc-highlight-outline-border: var(--vc-accent-300);--vc-highlight-outline-content-color: var(--vc-accent-200);--vc-highlight-light-bg: var(--vc-accent-800);--vc-highlight-light-content-color: var(--vc-accent-100);--vc-highlight-solid-bg: var(--vc-accent-500);--vc-highlight-solid-content-color: var(--vc-white);--vc-dot-bg: var(--vc-accent-500);--vc-bar-bg: var(--vc-accent-500)}.vc-container{position:relative;display:-webkit-inline-flex;display:-ms-inline-flexbox;display:inline-flex;width:-webkit-max-content;width:max-content;height:-webkit-max-content;height:max-content;font-family:var(--vc-font-family);color:var(--vc-color);background-color:var(--vc-bg);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-tap-highlight-color:transparent}.vc-container,.vc-container *{box-sizing:border-box}.vc-container:focus,.vc-container *:focus{outline:none}.vc-container .vc-container{border:none}.vc-bordered{border:1px solid;border-color:var(--vc-border);border-radius:var(--vc-rounded-lg)}.vc-expanded{min-width:100%}.vc-transparent{background-color:transparent}.vc-date-picker-content{padding:0;background-color:var(--vc-bg)}.vc-date-picker-content .vc-container{border:0}
