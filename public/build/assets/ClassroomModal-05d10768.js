import{l as y,o as r,c as d,w as l,b as n,a as t,u as a,n as g,d as v,f as u,s as w,r as i}from"./app-f0078ddb.js";import{_ as x}from"./ButtonItem-718c0517.js";import{r as h}from"./XMarkIcon-9bc7c0bd.js";import{h as f,S as b}from"./transition-a0923044.js";import{Y as C}from"./dialog-86f7bd91.js";const $={class:"flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0"},k={class:"absolute top-0 right-0 hidden pt-4 pr-4 sm:block"},B={class:"mt-3 text-center sm:mt-2"},S={class:"text-lg leading-6 font-medium text-gray-900"},z={class:"mt-2 text-sm"},M={class:"mt-5 flex gap-2 sm:mt-6"},Y={__name:"ClassroomModal",props:{open:Boolean,closeButtonText:{type:String,default:"Cancel"},modalSize:{type:String,default:"sm"},showClose:{type:Boolean,default:!0},showXClose:{type:Boolean,default:!0}},emits:["close-modal","submit-form"],setup(o,{emit:T}){const c=o,p=y(()=>({sm:"sm:max-w-lg",md:"sm:max-w-xl",lg:"sm:max-w-2xl",xl:"sm:max-w-3xl"})[c.modalSize]);return(s,e)=>(r(),d(a(b),{as:"template",show:o.open},{default:l(()=>[n(a(C),{as:"div",class:"fixed inset-0 z-10 overflow-y-auto",onClose:e[3]||(e[3]=m=>s.$emit("close-modal"))},{default:l(()=>[t("div",$,[n(a(f),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:l(()=>e[4]||(e[4]=[t("div",{class:"fixed inset-0 bg-gray-500/75 transition-opacity"},null,-1)])),_:1}),e[6]||(e[6]=t("span",{class:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true"},"​",-1)),n(a(f),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:l(()=>[t("div",{class:g(["inline-block transform rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:p-6 sm:align-middle",p.value])},[t("div",k,[o.showXClose?(r(),v("button",{key:0,type:"button",class:"rounded-md bg-white text-gray-400 hover:text-gray-500 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden",onClick:e[0]||(e[0]=m=>s.$emit("close-modal"))},[e[5]||(e[5]=t("span",{class:"sr-only"},"Close",-1)),n(a(h),{class:"h-6 w-6","aria-hidden":"true"})])):u("",!0)]),t("form",{onSubmit:e[2]||(e[2]=w(m=>s.$emit("submit-form"),["prevent"]))},[t("div",null,[i(s.$slots,"icon"),t("div",B,[t("h3",S,[i(s.$slots,"title")]),t("div",z,[i(s.$slots,"main")])])]),t("div",M,[i(s.$slots,"actionButton"),o.showClose?(r(),d(x,{key:0,color:"white",size:"sm",class:"w-full",onClick:e[1]||(e[1]=m=>s.$emit("close-modal")),innerHTML:o.closeButtonText},null,8,["innerHTML"])):u("",!0)])],32)],2)]),_:3})])]),_:3})]),_:3},8,["show"]))}};export{Y as _};
