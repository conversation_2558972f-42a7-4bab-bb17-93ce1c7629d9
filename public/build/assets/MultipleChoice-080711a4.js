import{e as q,A as C,B as $,o as a,d as l,a as n,r as m,F as B,h as E,b as d,w,T as g,n as b,t as k,u as v,f as p}from"./app-f0078ddb.js";import L from"./NextButtonSmall-9e6ffefc.js";import{r as D}from"./CheckCircleIcon-d86d1232.js";import{r as N}from"./XCircleIcon-63af2b2a.js";/* empty css            */const T={class:"flex-1"},V={class:"mt-4 mb-4 text-center text-3xl font-semibold whitespace-pre-line text-gray-900"},j={class:"text-center text-lg font-medium text-gray-600"},S={class:"relative my-6 grid w-full grid-cols-1 justify-items-center select-none xl:px-10"},F={class:"grid w-full grid-cols-1 gap-2 sm:grid-cols-4 sm:gap-4"},M=["disabled","onClick"],Q={class:"line-clamp-2"},z={key:0,class:"absolute top-1 right-2"},I={key:0},U={key:0,class:"flex items-center justify-center text-blue-500"},G={key:1,class:"flex items-center justify-center text-red-600"},X={__name:"MultipleChoice",props:{options:Array,isAnswer:Boolean,questionKey:String,attempts:Number,isCorrect:Boolean,disabled:Boolean,isVocabulary:{type:Boolean,required:!1,default:!1}},emits:["submit","next-question","i-know-this"],setup(e,{emit:x}){const o=e,f=x,c=t=>{document.removeEventListener("keydown",y),f("submit",{answer:t,key:o.questionKey,isArray:!1})};let u=q(!1);C(()=>{document.addEventListener("keydown",y),document.addEventListener("keyup",h),o.attempts==0&&(u.value=!0,setTimeout(()=>{u.value=!1},2e3))}),$(()=>{document.removeEventListener("keydown",y),document.removeEventListener("keyup",h)});function h(t){(t.which===17||t.which===18||t.which===224)&&(u.value=!1)}function y(t){switch((t.ctrlKey||t.metaKey||t.altKey)&&(u.value=!0),t.which){case 49:t.preventDefault(),c(o.options[0]);break;case 50:t.preventDefault(),c(o.options[1]);break;case 51:t.preventDefault(),c(o.options[2]);break;case 52:t.preventDefault(),c(o.options[3]);break}}const A=()=>{f("next-question")},K=()=>{f("i-know-this")};return(t,i)=>(a(),l("div",T,[n("h1",V,[m(t.$slots,"stem")]),n("h3",j,[m(t.$slots,"instructions")]),m(t.$slots,"latin"),m(t.$slots,"english"),n("div",S,[n("div",F,[(a(!0),l(B,null,E(o.options,(r,s)=>(a(),l("div",{key:r,class:b(["mt-2 sm:col-span-2",{"sm:col-start-2":s===2&&o.options.length===3}])},[n("button",{class:b(["h-16 w-full transform items-center rounded-lg border-2 px-6 text-xl font-semibold shadow-md transition-colors duration-250 ease-in-out focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden md:h-24",[{"border-lime-400 bg-lime-100 hover:border-lime-500":s===0&&!e.isAnswer||s===0&&e.isAnswer&&r==o.questionKey},{"border-teal-400 bg-teal-100 hover:border-teal-500":s===1&&!e.isAnswer||s===1&&e.isAnswer&&r==o.questionKey},{"border-purple-400 bg-purple-100 hover:border-purple-500":s===2&&!e.isAnswer||s===2&&e.isAnswer&&r==o.questionKey},{"border-sky-400 bg-sky-100 hover:border-sky-500":s===3&&!e.isAnswer||s===3&&e.isAnswer&&r==o.questionKey},{"border-gray-400 bg-gray-50 text-gray-500":e.isAnswer&&r!=o.questionKey},e.isAnswer?"cursor-default":"cursor-pointer"]]),disabled:e.isAnswer,onClick:H=>c(r)},[n("p",Q,k(r),1),d(g,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:w(()=>[v(u)&&!e.isAnswer?(a(),l("div",z,[n("span",{class:b(["text-2xl font-bold",[{"text-lime-400":s===0},{"text-teal-400":s===1},{"text-purple-400":s===2},{"text-sky-400":s===3}]])},k(s+1),3)])):p("",!0)]),_:2},1024)],10,M)],2))),128))])]),d(g,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95 ","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:w(()=>[e.isAnswer?(a(),l("div",I,[e.isCorrect?(a(),l("div",U,[d(v(D),{class:"mr-2 inline-block h-10 w-10"}),i[2]||(i[2]=n("p",{class:"inline-block text-2xl font-bold uppercase"},"correct",-1))])):(a(),l("div",G,[d(v(N),{class:"mr-2 inline-block h-10 w-10"}),i[3]||(i[3]=n("p",{class:"inline-block text-2xl font-bold uppercase"},"incorrect",-1))])),d(L,{"is-answer":e.isAnswer,"is-correct":e.isCorrect,disabled:e.disabled,"is-vocabulary":e.isVocabulary,onIKnowThis:i[0]||(i[0]=r=>K()),onNextQuestion:i[1]||(i[1]=r=>A())},null,8,["is-answer","is-correct","disabled","is-vocabulary"])])):p("",!0)]),_:1})]))}};export{X as default};
