import{_ as f}from"./ButtonItem-718c0517.js";import{l as y,o as g,c as x,w as e,b as s,a as t,u as a,r as i,n as h,g as v,t as _}from"./app-f0078ddb.js";import{h as m,S as b}from"./transition-a0923044.js";import{V as w,Y as B}from"./dialog-86f7bd91.js";const k={class:"flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0"},S={class:"inline-block transform rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle"},C={class:"mt-3 text-center sm:mt-0 sm:text-left"},V={class:"mt-2"},M={class:"text-sm text-gray-500"},D={__name:"Modal",props:{open:Boolean,modalTitle:String,modalDescription:String,closeButtonText:{default:"Cancel",type:String},buttons:{default:2,type:Number}},emits:["closeModal"],setup(o,{emit:c}){const d=o;function n(){p("closeModal")}const p=c,u=y(()=>"sm:grid-cols-"+d.buttons);return(l,r)=>(g(),x(a(b),{as:"template",show:o.open},{default:e(()=>[s(a(B),{as:"div",static:"",class:"fixed inset-0 z-50 overflow-y-auto",open:o.open,onClose:n},{default:e(()=>[t("div",k,[s(a(m),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:e(()=>[t("div",{class:"fixed inset-0 bg-gray-500/75 transition-opacity",onClick:n})]),_:1}),r[0]||(r[0]=t("span",{class:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true"},"​",-1)),s(a(m),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:e(()=>[t("div",S,[t("div",null,[t("div",C,[s(a(w),{as:"h3",class:"text-lg leading-6 font-medium text-gray-900"},{default:e(()=>[i(l.$slots,"title")]),_:3}),t("div",V,[t("p",M,[i(l.$slots,"content")])])])]),t("div",{class:h([u.value,"mt-5 grid gap-2 sm:mt-6 sm:grid-flow-row-dense sm:gap-3"])},[i(l.$slots,"actionButton"),s(f,{ref:"cancelButtonRef",type:"button",color:"gray",onClick:n},{default:e(()=>[v(_(o.closeButtonText),1)]),_:1},512)],2)])]),_:3})])]),_:3},8,["open"])]),_:3},8,["show"]))}};export{D as _};
