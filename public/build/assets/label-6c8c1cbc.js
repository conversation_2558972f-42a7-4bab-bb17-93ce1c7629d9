import{i as g,A as h}from"./render-c34c346a.js";import{e as j,I as L,l as S,H as k,A as y,B as C,u as E,K as O}from"./app-f0078ddb.js";let i=Symbol("LabelContext");function p(){let t=O(i,null);if(t===null){let l=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(l,p),l}return t}function A({slot:t={},name:l="Label",props:a={}}={}){let e=j([]);function n(r){return e.value.push(r),()=>{let o=e.value.indexOf(r);o!==-1&&e.value.splice(o,1)}}return L(i,{register:n,slot:t,name:l,props:a}),S(()=>e.value.length>0?e.value.join(" "):void 0)}let B=k({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(t,{slots:l,attrs:a}){var e;let n=(e=t.id)!=null?e:`headlessui-label-${g()}`,r=p();return y(()=>C(r.register(n))),()=>{let{name:o="Label",slot:d={},props:c={}}=r,{passive:m,...u}=t,s={...Object.entries(c).reduce((f,[b,v])=>Object.assign(f,{[b]:E(v)}),{}),id:n};return m&&(delete s.onClick,delete s.htmlFor,delete u.onClick),h({ourProps:s,theirProps:u,slot:d,attrs:a,slots:l,name:o})}}});export{A as E,B as K};
