import{V as Yn,l as p,H as de,i as Ge,o as P,d as x,r as pr,a4 as or,a5 as mn,b as V,F as fe,c as we,a6 as Ht,e as te,p as me,A as Kr,z as nr,a7 as Cr,I as mr,u as b,h as Ce,n as re,w as le,f as ie,G as Ts,K as yr,a as U,t as be,j as Jt,B as Cn,a8 as ui,g as Sn,T as Ys,E as ir,s as ci,J as Cs,a9 as la,q as Ss,a1 as fi,aa as di}from"./app-f0078ddb.js";var $e="top",Ie="bottom",xe="right",_e="left",An="auto",gr=[$e,Ie,xe,_e],xt="start",cr="end",vi="clippingParents",As="viewport",Qt="popper",hi="reference",ua=gr.reduce(function(e,t){return e.concat([t+"-"+xt,t+"-"+cr])},[]),Is=[].concat(gr,[An]).reduce(function(e,t){return e.concat([t,t+"-"+xt,t+"-"+cr])},[]),pi="beforeRead",mi="read",yi="afterRead",gi="beforeMain",bi="main",wi="afterMain",Di="beforeWrite",$i="write",_i="afterWrite",Mi=[pi,mi,yi,gi,bi,wi,Di,$i,_i];function ze(e){return e?(e.nodeName||"").toLowerCase():null}function Oe(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Dt(e){var t=Oe(e).Element;return e instanceof t||e instanceof Element}function Se(e){var t=Oe(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function In(e){if(typeof ShadowRoot>"u")return!1;var t=Oe(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function ki(e){var t=e.state;Object.keys(t.elements).forEach(function(r){var n=t.styles[r]||{},a=t.attributes[r]||{},s=t.elements[r];!Se(s)||!ze(s)||(Object.assign(s.style,n),Object.keys(a).forEach(function(o){var i=a[o];i===!1?s.removeAttribute(o):s.setAttribute(o,i===!0?"":i)}))})}function Oi(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(n){var a=t.elements[n],s=t.attributes[n]||{},o=Object.keys(t.styles.hasOwnProperty(n)?t.styles[n]:r[n]),i=o.reduce(function(l,u){return l[u]="",l},{});!Se(a)||!ze(a)||(Object.assign(a.style,i),Object.keys(s).forEach(function(l){a.removeAttribute(l)}))})}}const Pi={name:"applyStyles",enabled:!0,phase:"write",fn:ki,effect:Oi,requires:["computeStyles"]};function Be(e){return e.split("-")[0]}var wt=Math.max,Nr=Math.min,Et=Math.round;function yn(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function xs(){return!/^((?!chrome|android).)*safari/i.test(yn())}function Nt(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);var n=e.getBoundingClientRect(),a=1,s=1;t&&Se(e)&&(a=e.offsetWidth>0&&Et(n.width)/e.offsetWidth||1,s=e.offsetHeight>0&&Et(n.height)/e.offsetHeight||1);var o=Dt(e)?Oe(e):window,i=o.visualViewport,l=!xs()&&r,u=(n.left+(l&&i?i.offsetLeft:0))/a,c=(n.top+(l&&i?i.offsetTop:0))/s,d=n.width/a,h=n.height/s;return{width:d,height:h,top:c,right:u+d,bottom:c+h,left:u,x:u,y:c}}function xn(e){var t=Nt(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function Es(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&In(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Ze(e){return Oe(e).getComputedStyle(e)}function Ti(e){return["table","td","th"].indexOf(ze(e))>=0}function dt(e){return((Dt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Gr(e){return ze(e)==="html"?e:e.assignedSlot||e.parentNode||(In(e)?e.host:null)||dt(e)}function ca(e){return!Se(e)||Ze(e).position==="fixed"?null:e.offsetParent}function Yi(e){var t=/firefox/i.test(yn()),r=/Trident/i.test(yn());if(r&&Se(e)){var n=Ze(e);if(n.position==="fixed")return null}var a=Gr(e);for(In(a)&&(a=a.host);Se(a)&&["html","body"].indexOf(ze(a))<0;){var s=Ze(a);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||t&&s.willChange==="filter"||t&&s.filter&&s.filter!=="none")return a;a=a.parentNode}return null}function br(e){for(var t=Oe(e),r=ca(e);r&&Ti(r)&&Ze(r).position==="static";)r=ca(r);return r&&(ze(r)==="html"||ze(r)==="body"&&Ze(r).position==="static")?t:r||Yi(e)||t}function En(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function lr(e,t,r){return wt(e,Nr(t,r))}function Ci(e,t,r){var n=lr(e,t,r);return n>r?r:n}function Ns(){return{top:0,right:0,bottom:0,left:0}}function Ls(e){return Object.assign({},Ns(),e)}function Rs(e,t){return t.reduce(function(r,n){return r[n]=e,r},{})}var Si=function(t,r){return t=typeof t=="function"?t(Object.assign({},r.rects,{placement:r.placement})):t,Ls(typeof t!="number"?t:Rs(t,gr))};function Ai(e){var t,r=e.state,n=e.name,a=e.options,s=r.elements.arrow,o=r.modifiersData.popperOffsets,i=Be(r.placement),l=En(i),u=[_e,xe].indexOf(i)>=0,c=u?"height":"width";if(!(!s||!o)){var d=Si(a.padding,r),h=xn(s),v=l==="y"?$e:_e,m=l==="y"?Ie:xe,_=r.rects.reference[c]+r.rects.reference[l]-o[l]-r.rects.popper[c],D=o[l]-r.rects.reference[l],y=br(s),C=y?l==="y"?y.clientHeight||0:y.clientWidth||0:0,L=_/2-D/2,M=d[v],O=C-h[c]-d[m],E=C/2-h[c]/2+L,T=lr(M,E,O),N=l;r.modifiersData[n]=(t={},t[N]=T,t.centerOffset=T-E,t)}}function Ii(e){var t=e.state,r=e.options,n=r.element,a=n===void 0?"[data-popper-arrow]":n;a!=null&&(typeof a=="string"&&(a=t.elements.popper.querySelector(a),!a)||Es(t.elements.popper,a)&&(t.elements.arrow=a))}const xi={name:"arrow",enabled:!0,phase:"main",fn:Ai,effect:Ii,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Lt(e){return e.split("-")[1]}var Ei={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ni(e,t){var r=e.x,n=e.y,a=t.devicePixelRatio||1;return{x:Et(r*a)/a||0,y:Et(n*a)/a||0}}function fa(e){var t,r=e.popper,n=e.popperRect,a=e.placement,s=e.variation,o=e.offsets,i=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,d=e.isFixed,h=o.x,v=h===void 0?0:h,m=o.y,_=m===void 0?0:m,D=typeof c=="function"?c({x:v,y:_}):{x:v,y:_};v=D.x,_=D.y;var y=o.hasOwnProperty("x"),C=o.hasOwnProperty("y"),L=_e,M=$e,O=window;if(u){var E=br(r),T="clientHeight",N="clientWidth";if(E===Oe(r)&&(E=dt(r),Ze(E).position!=="static"&&i==="absolute"&&(T="scrollHeight",N="scrollWidth")),E=E,a===$e||(a===_e||a===xe)&&s===cr){M=Ie;var H=d&&E===O&&O.visualViewport?O.visualViewport.height:E[T];_-=H-n.height,_*=l?1:-1}if(a===_e||(a===$e||a===Ie)&&s===cr){L=xe;var W=d&&E===O&&O.visualViewport?O.visualViewport.width:E[N];v-=W-n.width,v*=l?1:-1}}var j=Object.assign({position:i},u&&Ei),Y=c===!0?Ni({x:v,y:_},Oe(r)):{x:v,y:_};if(v=Y.x,_=Y.y,l){var R;return Object.assign({},j,(R={},R[M]=C?"0":"",R[L]=y?"0":"",R.transform=(O.devicePixelRatio||1)<=1?"translate("+v+"px, "+_+"px)":"translate3d("+v+"px, "+_+"px, 0)",R))}return Object.assign({},j,(t={},t[M]=C?_+"px":"",t[L]=y?v+"px":"",t.transform="",t))}function Li(e){var t=e.state,r=e.options,n=r.gpuAcceleration,a=n===void 0?!0:n,s=r.adaptive,o=s===void 0?!0:s,i=r.roundOffsets,l=i===void 0?!0:i,u={placement:Be(t.placement),variation:Lt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,fa(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:o,roundOffsets:l})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,fa(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Ri={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Li,data:{}};var Or={passive:!0};function Fi(e){var t=e.state,r=e.instance,n=e.options,a=n.scroll,s=a===void 0?!0:a,o=n.resize,i=o===void 0?!0:o,l=Oe(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&u.forEach(function(c){c.addEventListener("scroll",r.update,Or)}),i&&l.addEventListener("resize",r.update,Or),function(){s&&u.forEach(function(c){c.removeEventListener("scroll",r.update,Or)}),i&&l.removeEventListener("resize",r.update,Or)}}const Hi={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Fi,data:{}};var Wi={left:"right",right:"left",bottom:"top",top:"bottom"};function Sr(e){return e.replace(/left|right|bottom|top/g,function(t){return Wi[t]})}var ji={start:"end",end:"start"};function da(e){return e.replace(/start|end/g,function(t){return ji[t]})}function Nn(e){var t=Oe(e),r=t.pageXOffset,n=t.pageYOffset;return{scrollLeft:r,scrollTop:n}}function Ln(e){return Nt(dt(e)).left+Nn(e).scrollLeft}function Bi(e,t){var r=Oe(e),n=dt(e),a=r.visualViewport,s=n.clientWidth,o=n.clientHeight,i=0,l=0;if(a){s=a.width,o=a.height;var u=xs();(u||!u&&t==="fixed")&&(i=a.offsetLeft,l=a.offsetTop)}return{width:s,height:o,x:i+Ln(e),y:l}}function zi(e){var t,r=dt(e),n=Nn(e),a=(t=e.ownerDocument)==null?void 0:t.body,s=wt(r.scrollWidth,r.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),o=wt(r.scrollHeight,r.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),i=-n.scrollLeft+Ln(e),l=-n.scrollTop;return Ze(a||r).direction==="rtl"&&(i+=wt(r.clientWidth,a?a.clientWidth:0)-s),{width:s,height:o,x:i,y:l}}function Rn(e){var t=Ze(e),r=t.overflow,n=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+a+n)}function Fs(e){return["html","body","#document"].indexOf(ze(e))>=0?e.ownerDocument.body:Se(e)&&Rn(e)?e:Fs(Gr(e))}function ur(e,t){var r;t===void 0&&(t=[]);var n=Fs(e),a=n===((r=e.ownerDocument)==null?void 0:r.body),s=Oe(n),o=a?[s].concat(s.visualViewport||[],Rn(n)?n:[]):n,i=t.concat(o);return a?i:i.concat(ur(Gr(o)))}function gn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Vi(e,t){var r=Nt(e,!1,t==="fixed");return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}function va(e,t,r){return t===As?gn(Bi(e,r)):Dt(t)?Vi(t,r):gn(zi(dt(e)))}function Ui(e){var t=ur(Gr(e)),r=["absolute","fixed"].indexOf(Ze(e).position)>=0,n=r&&Se(e)?br(e):e;return Dt(n)?t.filter(function(a){return Dt(a)&&Es(a,n)&&ze(a)!=="body"}):[]}function Ki(e,t,r,n){var a=t==="clippingParents"?Ui(e):[].concat(t),s=[].concat(a,[r]),o=s[0],i=s.reduce(function(l,u){var c=va(e,u,n);return l.top=wt(c.top,l.top),l.right=Nr(c.right,l.right),l.bottom=Nr(c.bottom,l.bottom),l.left=wt(c.left,l.left),l},va(e,o,n));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function Hs(e){var t=e.reference,r=e.element,n=e.placement,a=n?Be(n):null,s=n?Lt(n):null,o=t.x+t.width/2-r.width/2,i=t.y+t.height/2-r.height/2,l;switch(a){case $e:l={x:o,y:t.y-r.height};break;case Ie:l={x:o,y:t.y+t.height};break;case xe:l={x:t.x+t.width,y:i};break;case _e:l={x:t.x-r.width,y:i};break;default:l={x:t.x,y:t.y}}var u=a?En(a):null;if(u!=null){var c=u==="y"?"height":"width";switch(s){case xt:l[u]=l[u]-(t[c]/2-r[c]/2);break;case cr:l[u]=l[u]+(t[c]/2-r[c]/2);break}}return l}function fr(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=n===void 0?e.placement:n,s=r.strategy,o=s===void 0?e.strategy:s,i=r.boundary,l=i===void 0?vi:i,u=r.rootBoundary,c=u===void 0?As:u,d=r.elementContext,h=d===void 0?Qt:d,v=r.altBoundary,m=v===void 0?!1:v,_=r.padding,D=_===void 0?0:_,y=Ls(typeof D!="number"?D:Rs(D,gr)),C=h===Qt?hi:Qt,L=e.rects.popper,M=e.elements[m?C:h],O=Ki(Dt(M)?M:M.contextElement||dt(e.elements.popper),l,c,o),E=Nt(e.elements.reference),T=Hs({reference:E,element:L,strategy:"absolute",placement:a}),N=gn(Object.assign({},L,T)),H=h===Qt?N:E,W={top:O.top-H.top+y.top,bottom:H.bottom-O.bottom+y.bottom,left:O.left-H.left+y.left,right:H.right-O.right+y.right},j=e.modifiersData.offset;if(h===Qt&&j){var Y=j[a];Object.keys(W).forEach(function(R){var K=[xe,Ie].indexOf(R)>=0?1:-1,I=[$e,Ie].indexOf(R)>=0?"y":"x";W[R]+=Y[I]*K})}return W}function Gi(e,t){t===void 0&&(t={});var r=t,n=r.placement,a=r.boundary,s=r.rootBoundary,o=r.padding,i=r.flipVariations,l=r.allowedAutoPlacements,u=l===void 0?Is:l,c=Lt(n),d=c?i?ua:ua.filter(function(m){return Lt(m)===c}):gr,h=d.filter(function(m){return u.indexOf(m)>=0});h.length===0&&(h=d);var v=h.reduce(function(m,_){return m[_]=fr(e,{placement:_,boundary:a,rootBoundary:s,padding:o})[Be(_)],m},{});return Object.keys(v).sort(function(m,_){return v[m]-v[_]})}function qi(e){if(Be(e)===An)return[];var t=Sr(e);return[da(e),t,da(t)]}function Zi(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var a=r.mainAxis,s=a===void 0?!0:a,o=r.altAxis,i=o===void 0?!0:o,l=r.fallbackPlacements,u=r.padding,c=r.boundary,d=r.rootBoundary,h=r.altBoundary,v=r.flipVariations,m=v===void 0?!0:v,_=r.allowedAutoPlacements,D=t.options.placement,y=Be(D),C=y===D,L=l||(C||!m?[Sr(D)]:qi(D)),M=[D].concat(L).reduce(function(G,ae){return G.concat(Be(ae)===An?Gi(t,{placement:ae,boundary:c,rootBoundary:d,padding:u,flipVariations:m,allowedAutoPlacements:_}):ae)},[]),O=t.rects.reference,E=t.rects.popper,T=new Map,N=!0,H=M[0],W=0;W<M.length;W++){var j=M[W],Y=Be(j),R=Lt(j)===xt,K=[$e,Ie].indexOf(Y)>=0,I=K?"width":"height",z=fr(t,{placement:j,boundary:c,rootBoundary:d,altBoundary:h,padding:u}),Z=K?R?xe:_e:R?Ie:$e;O[I]>E[I]&&(Z=Sr(Z));var oe=Sr(Z),Q=[];if(s&&Q.push(z[Y]<=0),i&&Q.push(z[Z]<=0,z[oe]<=0),Q.every(function(G){return G})){H=j,N=!1;break}T.set(j,Q)}if(N)for(var $=m?3:1,B=function(ae){var ue=M.find(function(se){var ce=T.get(se);if(ce)return ce.slice(0,ae).every(function(Me){return Me})});if(ue)return H=ue,"break"},A=$;A>0;A--){var ne=B(A);if(ne==="break")break}t.placement!==H&&(t.modifiersData[n]._skip=!0,t.placement=H,t.reset=!0)}}const Xi={name:"flip",enabled:!0,phase:"main",fn:Zi,requiresIfExists:["offset"],data:{_skip:!1}};function ha(e,t,r){return r===void 0&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function pa(e){return[$e,xe,Ie,_e].some(function(t){return e[t]>=0})}function Ji(e){var t=e.state,r=e.name,n=t.rects.reference,a=t.rects.popper,s=t.modifiersData.preventOverflow,o=fr(t,{elementContext:"reference"}),i=fr(t,{altBoundary:!0}),l=ha(o,n),u=ha(i,a,s),c=pa(l),d=pa(u);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}const Qi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ji};function el(e,t,r){var n=Be(e),a=[_e,$e].indexOf(n)>=0?-1:1,s=typeof r=="function"?r(Object.assign({},t,{placement:e})):r,o=s[0],i=s[1];return o=o||0,i=(i||0)*a,[_e,xe].indexOf(n)>=0?{x:i,y:o}:{x:o,y:i}}function tl(e){var t=e.state,r=e.options,n=e.name,a=r.offset,s=a===void 0?[0,0]:a,o=Is.reduce(function(c,d){return c[d]=el(d,t.rects,s),c},{}),i=o[t.placement],l=i.x,u=i.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[n]=o}const rl={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:tl};function nl(e){var t=e.state,r=e.name;t.modifiersData[r]=Hs({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const al={name:"popperOffsets",enabled:!0,phase:"read",fn:nl,data:{}};function sl(e){return e==="x"?"y":"x"}function ol(e){var t=e.state,r=e.options,n=e.name,a=r.mainAxis,s=a===void 0?!0:a,o=r.altAxis,i=o===void 0?!1:o,l=r.boundary,u=r.rootBoundary,c=r.altBoundary,d=r.padding,h=r.tether,v=h===void 0?!0:h,m=r.tetherOffset,_=m===void 0?0:m,D=fr(t,{boundary:l,rootBoundary:u,padding:d,altBoundary:c}),y=Be(t.placement),C=Lt(t.placement),L=!C,M=En(y),O=sl(M),E=t.modifiersData.popperOffsets,T=t.rects.reference,N=t.rects.popper,H=typeof _=="function"?_(Object.assign({},t.rects,{placement:t.placement})):_,W=typeof H=="number"?{mainAxis:H,altAxis:H}:Object.assign({mainAxis:0,altAxis:0},H),j=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,Y={x:0,y:0};if(E){if(s){var R,K=M==="y"?$e:_e,I=M==="y"?Ie:xe,z=M==="y"?"height":"width",Z=E[M],oe=Z+D[K],Q=Z-D[I],$=v?-N[z]/2:0,B=C===xt?T[z]:N[z],A=C===xt?-N[z]:-T[z],ne=t.elements.arrow,G=v&&ne?xn(ne):{width:0,height:0},ae=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Ns(),ue=ae[K],se=ae[I],ce=lr(0,T[z],G[z]),Me=L?T[z]/2-$-ce-ue-W.mainAxis:B-ce-ue-W.mainAxis,Ke=L?-T[z]/2+$+ce+se+W.mainAxis:A+ce+se+W.mainAxis,Ee=t.elements.arrow&&br(t.elements.arrow),Pe=Ee?M==="y"?Ee.clientTop||0:Ee.clientLeft||0:0,Te=(R=j==null?void 0:j[M])!=null?R:0,tt=Z+Me-Te-Pe,Ot=Z+Ke-Te,He=lr(v?Nr(oe,tt):oe,Z,v?wt(Q,Ot):Q);E[M]=He,Y[M]=He-Z}if(i){var ht,pt=M==="x"?$e:_e,mt=M==="x"?Ie:xe,Ne=E[O],rt=O==="y"?"height":"width",nt=Ne+D[pt],at=Ne-D[mt],st=[$e,_e].indexOf(y)!==-1,ot=(ht=j==null?void 0:j[O])!=null?ht:0,it=st?nt:Ne-T[rt]-N[rt]-ot+W.altAxis,yt=st?Ne+T[rt]+N[rt]-ot-W.altAxis:at,Pt=v&&st?Ci(it,Ne,yt):lr(v?it:nt,Ne,v?yt:at);E[O]=Pt,Y[O]=Pt-Ne}t.modifiersData[n]=Y}}const il={name:"preventOverflow",enabled:!0,phase:"main",fn:ol,requiresIfExists:["offset"]};function ll(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function ul(e){return e===Oe(e)||!Se(e)?Nn(e):ll(e)}function cl(e){var t=e.getBoundingClientRect(),r=Et(t.width)/e.offsetWidth||1,n=Et(t.height)/e.offsetHeight||1;return r!==1||n!==1}function fl(e,t,r){r===void 0&&(r=!1);var n=Se(t),a=Se(t)&&cl(t),s=dt(t),o=Nt(e,a,r),i={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(n||!n&&!r)&&((ze(t)!=="body"||Rn(s))&&(i=ul(t)),Se(t)?(l=Nt(t,!0),l.x+=t.clientLeft,l.y+=t.clientTop):s&&(l.x=Ln(s))),{x:o.left+i.scrollLeft-l.x,y:o.top+i.scrollTop-l.y,width:o.width,height:o.height}}function dl(e){var t=new Map,r=new Set,n=[];e.forEach(function(s){t.set(s.name,s)});function a(s){r.add(s.name);var o=[].concat(s.requires||[],s.requiresIfExists||[]);o.forEach(function(i){if(!r.has(i)){var l=t.get(i);l&&a(l)}}),n.push(s)}return e.forEach(function(s){r.has(s.name)||a(s)}),n}function vl(e){var t=dl(e);return Mi.reduce(function(r,n){return r.concat(t.filter(function(a){return a.phase===n}))},[])}function hl(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}function pl(e){var t=e.reduce(function(r,n){var a=r[n.name];return r[n.name]=a?Object.assign({},a,n,{options:Object.assign({},a.options,n.options),data:Object.assign({},a.data,n.data)}):n,r},{});return Object.keys(t).map(function(r){return t[r]})}var ma={placement:"bottom",modifiers:[],strategy:"absolute"};function ya(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function ml(e){e===void 0&&(e={});var t=e,r=t.defaultModifiers,n=r===void 0?[]:r,a=t.defaultOptions,s=a===void 0?ma:a;return function(i,l,u){u===void 0&&(u=s);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},ma,s),modifiersData:{},elements:{reference:i,popper:l},attributes:{},styles:{}},d=[],h=!1,v={state:c,setOptions:function(y){var C=typeof y=="function"?y(c.options):y;_(),c.options=Object.assign({},s,c.options,C),c.scrollParents={reference:Dt(i)?ur(i):i.contextElement?ur(i.contextElement):[],popper:ur(l)};var L=vl(pl([].concat(n,c.options.modifiers)));return c.orderedModifiers=L.filter(function(M){return M.enabled}),m(),v.update()},forceUpdate:function(){if(!h){var y=c.elements,C=y.reference,L=y.popper;if(ya(C,L)){c.rects={reference:fl(C,br(L),c.options.strategy==="fixed"),popper:xn(L)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(W){return c.modifiersData[W.name]=Object.assign({},W.data)});for(var M=0;M<c.orderedModifiers.length;M++){if(c.reset===!0){c.reset=!1,M=-1;continue}var O=c.orderedModifiers[M],E=O.fn,T=O.options,N=T===void 0?{}:T,H=O.name;typeof E=="function"&&(c=E({state:c,options:N,name:H,instance:v})||c)}}}},update:hl(function(){return new Promise(function(D){v.forceUpdate(),D(c)})}),destroy:function(){_(),h=!0}};if(!ya(i,l))return v;v.setOptions(u).then(function(D){!h&&u.onFirstUpdate&&u.onFirstUpdate(D)});function m(){c.orderedModifiers.forEach(function(D){var y=D.name,C=D.options,L=C===void 0?{}:C,M=D.effect;if(typeof M=="function"){var O=M({state:c,name:y,instance:v,options:L}),E=function(){};d.push(O||E)}})}function _(){d.forEach(function(D){return D()}),d=[]}return v}}var yl=[Hi,al,Ri,Pi,rl,Xi,il,xi,Qi],gl=ml({defaultModifiers:yl}),bl=Object.defineProperty,wl=(e,t,r)=>t in e?bl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,S=(e,t,r)=>(wl(e,typeof t!="symbol"?t+"":t,r),r),Pr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ws(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Dl=Object.prototype,$l=Dl.hasOwnProperty;function _l(e,t){return e!=null&&$l.call(e,t)}var Ml=_l,kl=Array.isArray,Re=kl,Ol=typeof Pr=="object"&&Pr&&Pr.Object===Object&&Pr,js=Ol,Pl=js,Tl=typeof self=="object"&&self&&self.Object===Object&&self,Yl=Pl||Tl||Function("return this")(),Ve=Yl,Cl=Ve,Sl=Cl.Symbol,qr=Sl,ga=qr,Bs=Object.prototype,Al=Bs.hasOwnProperty,Il=Bs.toString,er=ga?ga.toStringTag:void 0;function xl(e){var t=Al.call(e,er),r=e[er];try{e[er]=void 0;var n=!0}catch{}var a=Il.call(e);return n&&(t?e[er]=r:delete e[er]),a}var El=xl,Nl=Object.prototype,Ll=Nl.toString;function Rl(e){return Ll.call(e)}var Fl=Rl,ba=qr,Hl=El,Wl=Fl,jl="[object Null]",Bl="[object Undefined]",wa=ba?ba.toStringTag:void 0;function zl(e){return e==null?e===void 0?Bl:jl:wa&&wa in Object(e)?Hl(e):Wl(e)}var Ue=zl;function Vl(e){return e!=null&&typeof e=="object"}var Fe=Vl,Ul=Ue,Kl=Fe,Gl="[object Symbol]";function ql(e){return typeof e=="symbol"||Kl(e)&&Ul(e)==Gl}var Fn=ql,Zl=Re,Xl=Fn,Jl=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ql=/^\w*$/;function eu(e,t){if(Zl(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Xl(e)?!0:Ql.test(e)||!Jl.test(e)||t!=null&&e in Object(t)}var Hn=eu;function tu(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Qe=tu,ru=Ue,nu=Qe,au="[object AsyncFunction]",su="[object Function]",ou="[object GeneratorFunction]",iu="[object Proxy]";function lu(e){if(!nu(e))return!1;var t=ru(e);return t==su||t==ou||t==au||t==iu}var _t=lu,uu=Ve,cu=uu["__core-js_shared__"],fu=cu,ln=fu,Da=function(){var e=/[^.]+$/.exec(ln&&ln.keys&&ln.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function du(e){return!!Da&&Da in e}var vu=du,hu=Function.prototype,pu=hu.toString;function mu(e){if(e!=null){try{return pu.call(e)}catch{}try{return e+""}catch{}}return""}var zs=mu,yu=_t,gu=vu,bu=Qe,wu=zs,Du=/[\\^$.*+?()[\]{}|]/g,$u=/^\[object .+?Constructor\]$/,_u=Function.prototype,Mu=Object.prototype,ku=_u.toString,Ou=Mu.hasOwnProperty,Pu=RegExp("^"+ku.call(Ou).replace(Du,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Tu(e){if(!bu(e)||gu(e))return!1;var t=yu(e)?Pu:$u;return t.test(wu(e))}var Yu=Tu;function Cu(e,t){return e==null?void 0:e[t]}var Su=Cu,Au=Yu,Iu=Su;function xu(e,t){var r=Iu(e,t);return Au(r)?r:void 0}var Mt=xu,Eu=Mt,Nu=Eu(Object,"create"),Zr=Nu,$a=Zr;function Lu(){this.__data__=$a?$a(null):{},this.size=0}var Ru=Lu;function Fu(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Hu=Fu,Wu=Zr,ju="__lodash_hash_undefined__",Bu=Object.prototype,zu=Bu.hasOwnProperty;function Vu(e){var t=this.__data__;if(Wu){var r=t[e];return r===ju?void 0:r}return zu.call(t,e)?t[e]:void 0}var Uu=Vu,Ku=Zr,Gu=Object.prototype,qu=Gu.hasOwnProperty;function Zu(e){var t=this.__data__;return Ku?t[e]!==void 0:qu.call(t,e)}var Xu=Zu,Ju=Zr,Qu="__lodash_hash_undefined__";function ec(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Ju&&t===void 0?Qu:t,this}var tc=ec,rc=Ru,nc=Hu,ac=Uu,sc=Xu,oc=tc;function Wt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Wt.prototype.clear=rc;Wt.prototype.delete=nc;Wt.prototype.get=ac;Wt.prototype.has=sc;Wt.prototype.set=oc;var ic=Wt;function lc(){this.__data__=[],this.size=0}var uc=lc;function cc(e,t){return e===t||e!==e&&t!==t}var jt=cc,fc=jt;function dc(e,t){for(var r=e.length;r--;)if(fc(e[r][0],t))return r;return-1}var Xr=dc,vc=Xr,hc=Array.prototype,pc=hc.splice;function mc(e){var t=this.__data__,r=vc(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():pc.call(t,r,1),--this.size,!0}var yc=mc,gc=Xr;function bc(e){var t=this.__data__,r=gc(t,e);return r<0?void 0:t[r][1]}var wc=bc,Dc=Xr;function $c(e){return Dc(this.__data__,e)>-1}var _c=$c,Mc=Xr;function kc(e,t){var r=this.__data__,n=Mc(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var Oc=kc,Pc=uc,Tc=yc,Yc=wc,Cc=_c,Sc=Oc;function Bt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Bt.prototype.clear=Pc;Bt.prototype.delete=Tc;Bt.prototype.get=Yc;Bt.prototype.has=Cc;Bt.prototype.set=Sc;var Jr=Bt,Ac=Mt,Ic=Ve,xc=Ac(Ic,"Map"),Wn=xc,_a=ic,Ec=Jr,Nc=Wn;function Lc(){this.size=0,this.__data__={hash:new _a,map:new(Nc||Ec),string:new _a}}var Rc=Lc;function Fc(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var Hc=Fc,Wc=Hc;function jc(e,t){var r=e.__data__;return Wc(t)?r[typeof t=="string"?"string":"hash"]:r.map}var Qr=jc,Bc=Qr;function zc(e){var t=Bc(this,e).delete(e);return this.size-=t?1:0,t}var Vc=zc,Uc=Qr;function Kc(e){return Uc(this,e).get(e)}var Gc=Kc,qc=Qr;function Zc(e){return qc(this,e).has(e)}var Xc=Zc,Jc=Qr;function Qc(e,t){var r=Jc(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var ef=Qc,tf=Rc,rf=Vc,nf=Gc,af=Xc,sf=ef;function zt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}zt.prototype.clear=tf;zt.prototype.delete=rf;zt.prototype.get=nf;zt.prototype.has=af;zt.prototype.set=sf;var jn=zt,Vs=jn,of="Expected a function";function Bn(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(of);var r=function(){var n=arguments,a=t?t.apply(this,n):n[0],s=r.cache;if(s.has(a))return s.get(a);var o=e.apply(this,n);return r.cache=s.set(a,o)||s,o};return r.cache=new(Bn.Cache||Vs),r}Bn.Cache=Vs;var lf=Bn,uf=lf,cf=500;function ff(e){var t=uf(e,function(n){return r.size===cf&&r.clear(),n}),r=t.cache;return t}var df=ff,vf=df,hf=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,pf=/\\(\\)?/g,mf=vf(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(hf,function(r,n,a,s){t.push(a?s.replace(pf,"$1"):n||r)}),t}),yf=mf;function gf(e,t){for(var r=-1,n=e==null?0:e.length,a=Array(n);++r<n;)a[r]=t(e[r],r,e);return a}var bf=gf,Ma=qr,wf=bf,Df=Re,$f=Fn,_f=1/0,ka=Ma?Ma.prototype:void 0,Oa=ka?ka.toString:void 0;function Us(e){if(typeof e=="string")return e;if(Df(e))return wf(e,Us)+"";if($f(e))return Oa?Oa.call(e):"";var t=e+"";return t=="0"&&1/e==-_f?"-0":t}var Mf=Us,kf=Mf;function Of(e){return e==null?"":kf(e)}var Pf=Of,Tf=Re,Yf=Hn,Cf=yf,Sf=Pf;function Af(e,t){return Tf(e)?e:Yf(e,t)?[e]:Cf(Sf(e))}var Ks=Af,If=Ue,xf=Fe,Ef="[object Arguments]";function Nf(e){return xf(e)&&If(e)==Ef}var Lf=Nf,Pa=Lf,Rf=Fe,Gs=Object.prototype,Ff=Gs.hasOwnProperty,Hf=Gs.propertyIsEnumerable,Wf=Pa(function(){return arguments}())?Pa:function(e){return Rf(e)&&Ff.call(e,"callee")&&!Hf.call(e,"callee")},zn=Wf,jf=9007199254740991,Bf=/^(?:0|[1-9]\d*)$/;function zf(e,t){var r=typeof e;return t=t??jf,!!t&&(r=="number"||r!="symbol"&&Bf.test(e))&&e>-1&&e%1==0&&e<t}var Vn=zf,Vf=9007199254740991;function Uf(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Vf}var Un=Uf,Kf=Fn,Gf=1/0;function qf(e){if(typeof e=="string"||Kf(e))return e;var t=e+"";return t=="0"&&1/e==-Gf?"-0":t}var en=qf,Zf=Ks,Xf=zn,Jf=Re,Qf=Vn,ed=Un,td=en;function rd(e,t,r){t=Zf(t,e);for(var n=-1,a=t.length,s=!1;++n<a;){var o=td(t[n]);if(!(s=e!=null&&r(e,o)))break;e=e[o]}return s||++n!=a?s:(a=e==null?0:e.length,!!a&&ed(a)&&Qf(o,a)&&(Jf(e)||Xf(e)))}var qs=rd,nd=Ml,ad=qs;function sd(e,t){return e!=null&&ad(e,t,nd)}var Zs=sd,od=Ue,id=Fe,ld="[object Date]";function ud(e){return id(e)&&od(e)==ld}var cd=ud;function fd(e){return function(t){return e(t)}}var Xs=fd,dr={},dd={get exports(){return dr},set exports(e){dr=e}};(function(e,t){var r=js,n=t&&!t.nodeType&&t,a=n&&!0&&e&&!e.nodeType&&e,s=a&&a.exports===n,o=s&&r.process,i=function(){try{var l=a&&a.require&&a.require("util").types;return l||o&&o.binding&&o.binding("util")}catch{}}();e.exports=i})(dd,dr);var vd=cd,hd=Xs,Ta=dr,Ya=Ta&&Ta.isDate,pd=Ya?hd(Ya):vd,md=pd,yd=Ue,gd=Re,bd=Fe,wd="[object String]";function Dd(e){return typeof e=="string"||!gd(e)&&bd(e)&&yd(e)==wd}var We=Dd;function $d(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var Js=$d,_d=Jr;function Md(){this.__data__=new _d,this.size=0}var kd=Md;function Od(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var Pd=Od;function Td(e){return this.__data__.get(e)}var Yd=Td;function Cd(e){return this.__data__.has(e)}var Sd=Cd,Ad=Jr,Id=Wn,xd=jn,Ed=200;function Nd(e,t){var r=this.__data__;if(r instanceof Ad){var n=r.__data__;if(!Id||n.length<Ed-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new xd(n)}return r.set(e,t),this.size=r.size,this}var Ld=Nd,Rd=Jr,Fd=kd,Hd=Pd,Wd=Yd,jd=Sd,Bd=Ld;function Vt(e){var t=this.__data__=new Rd(e);this.size=t.size}Vt.prototype.clear=Fd;Vt.prototype.delete=Hd;Vt.prototype.get=Wd;Vt.prototype.has=jd;Vt.prototype.set=Bd;var Kn=Vt,zd="__lodash_hash_undefined__";function Vd(e){return this.__data__.set(e,zd),this}var Ud=Vd;function Kd(e){return this.__data__.has(e)}var Gd=Kd,qd=jn,Zd=Ud,Xd=Gd;function Lr(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new qd;++t<r;)this.add(e[t])}Lr.prototype.add=Lr.prototype.push=Zd;Lr.prototype.has=Xd;var Jd=Lr;function Qd(e,t){return e.has(t)}var ev=Qd,tv=Jd,rv=Js,nv=ev,av=1,sv=2;function ov(e,t,r,n,a,s){var o=r&av,i=e.length,l=t.length;if(i!=l&&!(o&&l>i))return!1;var u=s.get(e),c=s.get(t);if(u&&c)return u==t&&c==e;var d=-1,h=!0,v=r&sv?new tv:void 0;for(s.set(e,t),s.set(t,e);++d<i;){var m=e[d],_=t[d];if(n)var D=o?n(_,m,d,t,e,s):n(m,_,d,e,t,s);if(D!==void 0){if(D)continue;h=!1;break}if(v){if(!rv(t,function(y,C){if(!nv(v,C)&&(m===y||a(m,y,r,n,s)))return v.push(C)})){h=!1;break}}else if(!(m===_||a(m,_,r,n,s))){h=!1;break}}return s.delete(e),s.delete(t),h}var Qs=ov,iv=Ve,lv=iv.Uint8Array,eo=lv;function uv(e){var t=-1,r=Array(e.size);return e.forEach(function(n,a){r[++t]=[a,n]}),r}var cv=uv;function fv(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var dv=fv,Ca=qr,Sa=eo,vv=jt,hv=Qs,pv=cv,mv=dv,yv=1,gv=2,bv="[object Boolean]",wv="[object Date]",Dv="[object Error]",$v="[object Map]",_v="[object Number]",Mv="[object RegExp]",kv="[object Set]",Ov="[object String]",Pv="[object Symbol]",Tv="[object ArrayBuffer]",Yv="[object DataView]",Aa=Ca?Ca.prototype:void 0,un=Aa?Aa.valueOf:void 0;function Cv(e,t,r,n,a,s,o){switch(r){case Yv:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Tv:return!(e.byteLength!=t.byteLength||!s(new Sa(e),new Sa(t)));case bv:case wv:case _v:return vv(+e,+t);case Dv:return e.name==t.name&&e.message==t.message;case Mv:case Ov:return e==t+"";case $v:var i=pv;case kv:var l=n&yv;if(i||(i=mv),e.size!=t.size&&!l)return!1;var u=o.get(e);if(u)return u==t;n|=gv,o.set(e,t);var c=hv(i(e),i(t),n,a,s,o);return o.delete(e),c;case Pv:if(un)return un.call(e)==un.call(t)}return!1}var Sv=Cv;function Av(e,t){for(var r=-1,n=t.length,a=e.length;++r<n;)e[a+r]=t[r];return e}var Iv=Av,xv=Iv,Ev=Re;function Nv(e,t,r){var n=t(e);return Ev(e)?n:xv(n,r(e))}var Lv=Nv;function Rv(e,t){for(var r=-1,n=e==null?0:e.length,a=0,s=[];++r<n;){var o=e[r];t(o,r,e)&&(s[a++]=o)}return s}var Fv=Rv;function Hv(){return[]}var Wv=Hv,jv=Fv,Bv=Wv,zv=Object.prototype,Vv=zv.propertyIsEnumerable,Ia=Object.getOwnPropertySymbols,Uv=Ia?function(e){return e==null?[]:(e=Object(e),jv(Ia(e),function(t){return Vv.call(e,t)}))}:Bv,Kv=Uv;function Gv(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var qv=Gv,Rt={},Zv={get exports(){return Rt},set exports(e){Rt=e}};function Xv(){return!1}var Jv=Xv;(function(e,t){var r=Ve,n=Jv,a=t&&!t.nodeType&&t,s=a&&!0&&e&&!e.nodeType&&e,o=s&&s.exports===a,i=o?r.Buffer:void 0,l=i?i.isBuffer:void 0,u=l||n;e.exports=u})(Zv,Rt);var Qv=Ue,eh=Un,th=Fe,rh="[object Arguments]",nh="[object Array]",ah="[object Boolean]",sh="[object Date]",oh="[object Error]",ih="[object Function]",lh="[object Map]",uh="[object Number]",ch="[object Object]",fh="[object RegExp]",dh="[object Set]",vh="[object String]",hh="[object WeakMap]",ph="[object ArrayBuffer]",mh="[object DataView]",yh="[object Float32Array]",gh="[object Float64Array]",bh="[object Int8Array]",wh="[object Int16Array]",Dh="[object Int32Array]",$h="[object Uint8Array]",_h="[object Uint8ClampedArray]",Mh="[object Uint16Array]",kh="[object Uint32Array]",J={};J[yh]=J[gh]=J[bh]=J[wh]=J[Dh]=J[$h]=J[_h]=J[Mh]=J[kh]=!0;J[rh]=J[nh]=J[ph]=J[ah]=J[mh]=J[sh]=J[oh]=J[ih]=J[lh]=J[uh]=J[ch]=J[fh]=J[dh]=J[vh]=J[hh]=!1;function Oh(e){return th(e)&&eh(e.length)&&!!J[Qv(e)]}var Ph=Oh,Th=Ph,Yh=Xs,xa=dr,Ea=xa&&xa.isTypedArray,Ch=Ea?Yh(Ea):Th,Gn=Ch,Sh=qv,Ah=zn,Ih=Re,xh=Rt,Eh=Vn,Nh=Gn,Lh=Object.prototype,Rh=Lh.hasOwnProperty;function Fh(e,t){var r=Ih(e),n=!r&&Ah(e),a=!r&&!n&&xh(e),s=!r&&!n&&!a&&Nh(e),o=r||n||a||s,i=o?Sh(e.length,String):[],l=i.length;for(var u in e)(t||Rh.call(e,u))&&!(o&&(u=="length"||a&&(u=="offset"||u=="parent")||s&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||Eh(u,l)))&&i.push(u);return i}var to=Fh,Hh=Object.prototype;function Wh(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||Hh;return e===r}var qn=Wh;function jh(e,t){return function(r){return e(t(r))}}var ro=jh,Bh=ro,zh=Bh(Object.keys,Object),Vh=zh,Uh=qn,Kh=Vh,Gh=Object.prototype,qh=Gh.hasOwnProperty;function Zh(e){if(!Uh(e))return Kh(e);var t=[];for(var r in Object(e))qh.call(e,r)&&r!="constructor"&&t.push(r);return t}var Xh=Zh,Jh=_t,Qh=Un;function ep(e){return e!=null&&Qh(e.length)&&!Jh(e)}var wr=ep,tp=to,rp=Xh,np=wr;function ap(e){return np(e)?tp(e):rp(e)}var Zn=ap,sp=Lv,op=Kv,ip=Zn;function lp(e){return sp(e,ip,op)}var up=lp,Na=up,cp=1,fp=Object.prototype,dp=fp.hasOwnProperty;function vp(e,t,r,n,a,s){var o=r&cp,i=Na(e),l=i.length,u=Na(t),c=u.length;if(l!=c&&!o)return!1;for(var d=l;d--;){var h=i[d];if(!(o?h in t:dp.call(t,h)))return!1}var v=s.get(e),m=s.get(t);if(v&&m)return v==t&&m==e;var _=!0;s.set(e,t),s.set(t,e);for(var D=o;++d<l;){h=i[d];var y=e[h],C=t[h];if(n)var L=o?n(C,y,h,t,e,s):n(y,C,h,e,t,s);if(!(L===void 0?y===C||a(y,C,r,n,s):L)){_=!1;break}D||(D=h=="constructor")}if(_&&!D){var M=e.constructor,O=t.constructor;M!=O&&"constructor"in e&&"constructor"in t&&!(typeof M=="function"&&M instanceof M&&typeof O=="function"&&O instanceof O)&&(_=!1)}return s.delete(e),s.delete(t),_}var hp=vp,pp=Mt,mp=Ve,yp=pp(mp,"DataView"),gp=yp,bp=Mt,wp=Ve,Dp=bp(wp,"Promise"),$p=Dp,_p=Mt,Mp=Ve,kp=_p(Mp,"Set"),Op=kp,Pp=Mt,Tp=Ve,Yp=Pp(Tp,"WeakMap"),Cp=Yp,bn=gp,wn=Wn,Dn=$p,$n=Op,_n=Cp,no=Ue,Ut=zs,La="[object Map]",Sp="[object Object]",Ra="[object Promise]",Fa="[object Set]",Ha="[object WeakMap]",Wa="[object DataView]",Ap=Ut(bn),Ip=Ut(wn),xp=Ut(Dn),Ep=Ut($n),Np=Ut(_n),gt=no;(bn&&gt(new bn(new ArrayBuffer(1)))!=Wa||wn&&gt(new wn)!=La||Dn&&gt(Dn.resolve())!=Ra||$n&&gt(new $n)!=Fa||_n&&gt(new _n)!=Ha)&&(gt=function(e){var t=no(e),r=t==Sp?e.constructor:void 0,n=r?Ut(r):"";if(n)switch(n){case Ap:return Wa;case Ip:return La;case xp:return Ra;case Ep:return Fa;case Np:return Ha}return t});var Lp=gt,cn=Kn,Rp=Qs,Fp=Sv,Hp=hp,ja=Lp,Ba=Re,za=Rt,Wp=Gn,jp=1,Va="[object Arguments]",Ua="[object Array]",Tr="[object Object]",Bp=Object.prototype,Ka=Bp.hasOwnProperty;function zp(e,t,r,n,a,s){var o=Ba(e),i=Ba(t),l=o?Ua:ja(e),u=i?Ua:ja(t);l=l==Va?Tr:l,u=u==Va?Tr:u;var c=l==Tr,d=u==Tr,h=l==u;if(h&&za(e)){if(!za(t))return!1;o=!0,c=!1}if(h&&!c)return s||(s=new cn),o||Wp(e)?Rp(e,t,r,n,a,s):Fp(e,t,l,r,n,a,s);if(!(r&jp)){var v=c&&Ka.call(e,"__wrapped__"),m=d&&Ka.call(t,"__wrapped__");if(v||m){var _=v?e.value():e,D=m?t.value():t;return s||(s=new cn),a(_,D,r,n,s)}}return h?(s||(s=new cn),Hp(e,t,r,n,a,s)):!1}var Vp=zp,Up=Vp,Ga=Fe;function ao(e,t,r,n,a){return e===t?!0:e==null||t==null||!Ga(e)&&!Ga(t)?e!==e&&t!==t:Up(e,t,r,n,ao,a)}var so=ao,Kp=Kn,Gp=so,qp=1,Zp=2;function Xp(e,t,r,n){var a=r.length,s=a,o=!n;if(e==null)return!s;for(e=Object(e);a--;){var i=r[a];if(o&&i[2]?i[1]!==e[i[0]]:!(i[0]in e))return!1}for(;++a<s;){i=r[a];var l=i[0],u=e[l],c=i[1];if(o&&i[2]){if(u===void 0&&!(l in e))return!1}else{var d=new Kp;if(n)var h=n(u,c,l,e,t,d);if(!(h===void 0?Gp(c,u,qp|Zp,n,d):h))return!1}}return!0}var Jp=Xp,Qp=Qe;function em(e){return e===e&&!Qp(e)}var oo=em,tm=oo,rm=Zn;function nm(e){for(var t=rm(e),r=t.length;r--;){var n=t[r],a=e[n];t[r]=[n,a,tm(a)]}return t}var am=nm;function sm(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var io=sm,om=Jp,im=am,lm=io;function um(e){var t=im(e);return t.length==1&&t[0][2]?lm(t[0][0],t[0][1]):function(r){return r===e||om(r,e,t)}}var cm=um,fm=Ks,dm=en;function vm(e,t){t=fm(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[dm(t[r++])];return r&&r==n?e:void 0}var lo=vm,hm=lo;function pm(e,t,r){var n=e==null?void 0:hm(e,t);return n===void 0?r:n}var bt=pm;function mm(e,t){return e!=null&&t in Object(e)}var ym=mm,gm=ym,bm=qs;function wm(e,t){return e!=null&&bm(e,t,gm)}var Dm=wm,$m=so,_m=bt,Mm=Dm,km=Hn,Om=oo,Pm=io,Tm=en,Ym=1,Cm=2;function Sm(e,t){return km(e)&&Om(t)?Pm(Tm(e),t):function(r){var n=_m(r,e);return n===void 0&&n===t?Mm(r,e):$m(t,n,Ym|Cm)}}var Am=Sm;function Im(e){return e}var Xn=Im;function xm(e){return function(t){return t==null?void 0:t[e]}}var Em=xm,Nm=lo;function Lm(e){return function(t){return Nm(t,e)}}var Rm=Lm,Fm=Em,Hm=Rm,Wm=Hn,jm=en;function Bm(e){return Wm(e)?Fm(jm(e)):Hm(e)}var zm=Bm,Vm=cm,Um=Am,Km=Xn,Gm=Re,qm=zm;function Zm(e){return typeof e=="function"?e:e==null?Km:typeof e=="object"?Gm(e)?Um(e[0],e[1]):Vm(e):qm(e)}var uo=Zm;function Xm(e){return function(t,r,n){for(var a=-1,s=Object(t),o=n(t),i=o.length;i--;){var l=o[e?i:++a];if(r(s[l],l,s)===!1)break}return t}}var Jm=Xm,Qm=Jm,ey=Qm(),co=ey,ty=co,ry=Zn;function ny(e,t){return e&&ty(e,t,ry)}var fo=ny,ay=wr;function sy(e,t){return function(r,n){if(r==null)return r;if(!ay(r))return e(r,n);for(var a=r.length,s=t?a:-1,o=Object(r);(t?s--:++s<a)&&n(o[s],s,o)!==!1;);return r}}var oy=sy,iy=fo,ly=oy,uy=ly(iy),cy=uy,fy=cy;function dy(e,t){var r;return fy(e,function(n,a,s){return r=t(n,a,s),!r}),!!r}var vy=dy,hy=jt,py=wr,my=Vn,yy=Qe;function gy(e,t,r){if(!yy(r))return!1;var n=typeof t;return(n=="number"?py(r)&&my(t,r.length):n=="string"&&t in r)?hy(r[t],e):!1}var Jn=gy,by=Js,wy=uo,Dy=vy,$y=Re,_y=Jn;function My(e,t,r){var n=$y(e)?by:Dy;return r&&_y(e,t,r)&&(t=void 0),n(e,wy(t))}var ky=My,Oy=Ue,Py=Fe,Ty="[object Boolean]";function Yy(e){return e===!0||e===!1||Py(e)&&Oy(e)==Ty}var Cy=Yy,Sy=Ue,Ay=Fe,Iy="[object Number]";function xy(e){return typeof e=="number"||Ay(e)&&Sy(e)==Iy}var Le=xy,Ey=Mt,Ny=function(){try{var e=Ey(Object,"defineProperty");return e({},"",{}),e}catch{}}(),vo=Ny,qa=vo;function Ly(e,t,r){t=="__proto__"&&qa?qa(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var tn=Ly,Ry=tn,Fy=jt,Hy=Object.prototype,Wy=Hy.hasOwnProperty;function jy(e,t,r){var n=e[t];(!(Wy.call(e,t)&&Fy(n,r))||r===void 0&&!(t in e))&&Ry(e,t,r)}var By=jy,zy=tn,Vy=fo,Uy=uo;function Ky(e,t){var r={};return t=Uy(t),Vy(e,function(n,a,s){zy(r,a,t(n,a,s))}),r}var Gy=Ky;function qy(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var ho=qy,Zy=ho,Za=Math.max;function Xy(e,t,r){return t=Za(t===void 0?e.length-1:t,0),function(){for(var n=arguments,a=-1,s=Za(n.length-t,0),o=Array(s);++a<s;)o[a]=n[t+a];a=-1;for(var i=Array(t+1);++a<t;)i[a]=n[a];return i[t]=r(o),Zy(e,this,i)}}var Jy=Xy;function Qy(e){return function(){return e}}var eg=Qy,tg=eg,Xa=vo,rg=Xn,ng=Xa?function(e,t){return Xa(e,"toString",{configurable:!0,enumerable:!1,value:tg(t),writable:!0})}:rg,ag=ng,sg=800,og=16,ig=Date.now;function lg(e){var t=0,r=0;return function(){var n=ig(),a=og-(n-r);if(r=n,a>0){if(++t>=sg)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var ug=lg,cg=ag,fg=ug,dg=fg(cg),vg=dg,hg=Xn,pg=Jy,mg=vg;function yg(e,t){return mg(pg(e,t,hg),e+"")}var Qn=yg;function gg(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var bg=gg,wg=Qe,Dg=qn,$g=bg,_g=Object.prototype,Mg=_g.hasOwnProperty;function kg(e){if(!wg(e))return $g(e);var t=Dg(e),r=[];for(var n in e)n=="constructor"&&(t||!Mg.call(e,n))||r.push(n);return r}var Og=kg,Pg=to,Tg=Og,Yg=wr;function Cg(e){return Yg(e)?Pg(e,!0):Tg(e)}var ea=Cg,Sg=Qn,Ag=jt,Ig=Jn,xg=ea,po=Object.prototype,Eg=po.hasOwnProperty,Ng=Sg(function(e,t){e=Object(e);var r=-1,n=t.length,a=n>2?t[2]:void 0;for(a&&Ig(t[0],t[1],a)&&(n=1);++r<n;)for(var s=t[r],o=xg(s),i=-1,l=o.length;++i<l;){var u=o[i],c=e[u];(c===void 0||Ag(c,po[u])&&!Eg.call(e,u))&&(e[u]=s[u])}return e}),Ja=Ng,Lg=tn,Rg=jt;function Fg(e,t,r){(r!==void 0&&!Rg(e[t],r)||r===void 0&&!(t in e))&&Lg(e,t,r)}var mo=Fg,Rr={},Hg={get exports(){return Rr},set exports(e){Rr=e}};(function(e,t){var r=Ve,n=t&&!t.nodeType&&t,a=n&&!0&&e&&!e.nodeType&&e,s=a&&a.exports===n,o=s?r.Buffer:void 0,i=o?o.allocUnsafe:void 0;function l(u,c){if(c)return u.slice();var d=u.length,h=i?i(d):new u.constructor(d);return u.copy(h),h}e.exports=l})(Hg,Rr);var Qa=eo;function Wg(e){var t=new e.constructor(e.byteLength);return new Qa(t).set(new Qa(e)),t}var jg=Wg,Bg=jg;function zg(e,t){var r=t?Bg(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}var Vg=zg;function Ug(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}var Kg=Ug,Gg=Qe,es=Object.create,qg=function(){function e(){}return function(t){if(!Gg(t))return{};if(es)return es(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}(),Zg=qg,Xg=ro,Jg=Xg(Object.getPrototypeOf,Object),yo=Jg,Qg=Zg,eb=yo,tb=qn;function rb(e){return typeof e.constructor=="function"&&!tb(e)?Qg(eb(e)):{}}var nb=rb,ab=wr,sb=Fe;function ob(e){return sb(e)&&ab(e)}var ib=ob,lb=Ue,ub=yo,cb=Fe,fb="[object Object]",db=Function.prototype,vb=Object.prototype,go=db.toString,hb=vb.hasOwnProperty,pb=go.call(Object);function mb(e){if(!cb(e)||lb(e)!=fb)return!1;var t=ub(e);if(t===null)return!0;var r=hb.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&go.call(r)==pb}var yb=mb;function gb(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var bo=gb,bb=By,wb=tn;function Db(e,t,r,n){var a=!r;r||(r={});for(var s=-1,o=t.length;++s<o;){var i=t[s],l=n?n(r[i],e[i],i,r,e):void 0;l===void 0&&(l=e[i]),a?wb(r,i,l):bb(r,i,l)}return r}var $b=Db,_b=$b,Mb=ea;function kb(e){return _b(e,Mb(e))}var Ob=kb,ts=mo,Pb=Rr,Tb=Vg,Yb=Kg,Cb=nb,rs=zn,ns=Re,Sb=ib,Ab=Rt,Ib=_t,xb=Qe,Eb=yb,Nb=Gn,as=bo,Lb=Ob;function Rb(e,t,r,n,a,s,o){var i=as(e,r),l=as(t,r),u=o.get(l);if(u){ts(e,r,u);return}var c=s?s(i,l,r+"",e,t,o):void 0,d=c===void 0;if(d){var h=ns(l),v=!h&&Ab(l),m=!h&&!v&&Nb(l);c=l,h||v||m?ns(i)?c=i:Sb(i)?c=Yb(i):v?(d=!1,c=Pb(l,!0)):m?(d=!1,c=Tb(l,!0)):c=[]:Eb(l)||rs(l)?(c=i,rs(i)?c=Lb(i):(!xb(i)||Ib(i))&&(c=Cb(l))):d=!1}d&&(o.set(l,c),a(c,l,n,s,o),o.delete(l)),ts(e,r,c)}var Fb=Rb,Hb=Kn,Wb=mo,jb=co,Bb=Fb,zb=Qe,Vb=ea,Ub=bo;function wo(e,t,r,n,a){e!==t&&jb(t,function(s,o){if(a||(a=new Hb),zb(s))Bb(e,t,o,r,wo,n,a);else{var i=n?n(Ub(e,o),s,o+"",e,t,a):void 0;i===void 0&&(i=s),Wb(e,o,i)}},Vb)}var Do=wo,Kb=Do,ss=Qe;function $o(e,t,r,n,a,s){return ss(e)&&ss(t)&&(s.set(t,e),Kb(e,t,void 0,$o,s),s.delete(t)),e}var Gb=$o,qb=Qn,Zb=Jn;function Xb(e){return qb(function(t,r){var n=-1,a=r.length,s=a>1?r[a-1]:void 0,o=a>2?r[2]:void 0;for(s=e.length>3&&typeof s=="function"?(a--,s):void 0,o&&Zb(r[0],r[1],o)&&(s=a<3?void 0:s,a=1),t=Object(t);++n<a;){var i=r[n];i&&e(t,i,n,s)}return t})}var Jb=Xb,Qb=Do,e1=Jb,t1=e1(function(e,t,r,n){Qb(e,t,r,n)}),r1=t1,n1=ho,a1=Qn,s1=Gb,o1=r1,i1=a1(function(e){return e.push(void 0,s1),n1(o1,void 0,e)}),vr=i1;function l1(e){return e&&e.length?e[0]:void 0}var _o=l1;function u1(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var St=u1;const c1=e=>Object.prototype.toString.call(e).slice(8,-1),At=e=>md(e)&&!isNaN(e.getTime()),Xe=e=>c1(e)==="Object",Mo=Zs,os=(e,t)=>ky(t,r=>Zs(e,r)),q=(e,t,r="0")=>{for(e=e!=null?String(e):"",t=t||2;e.length<t;)e=`${r}${e}`;return e},Ae=e=>Array.isArray(e),qe=e=>Ae(e)&&e.length>0,Fr=e=>e==null?null:document&&We(e)?document.querySelector(e):e.$el??e,ut=(e,t,r,n=void 0)=>{e.removeEventListener(t,r,n)},ct=(e,t,r,n=void 0)=>(e.addEventListener(t,r,n),()=>ut(e,t,r,n)),Ar=(e,t)=>!!e&&!!t&&(e===t||e.contains(t)),Yr=(e,t)=>{(e.key===" "||e.key==="Enter")&&(t(e),e.preventDefault())},ko=(e,...t)=>{const r={};let n;for(n in e)t.includes(n)||(r[n]=e[n]);return r},Oo=(e,t)=>{const r={};return t.forEach(n=>{n in e&&(r[n]=e[n])}),r};function f1(e,t,r){return Math.min(Math.max(e,t),r)}var Hr={},d1={get exports(){return Hr},set exports(e){Hr=e}};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=r;function r(n){if(n===null||n===!0||n===!1)return NaN;var a=Number(n);return isNaN(a)?a:a<0?Math.ceil(a):Math.floor(a)}e.exports=t.default})(d1,Hr);const v1=Ws(Hr);var Wr={},h1={get exports(){return Wr},set exports(e){Wr=e}};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=r;function r(n){var a=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return a.setUTCFullYear(n.getFullYear()),n.getTime()-a.getTime()}e.exports=t.default})(h1,Wr);const is=Ws(Wr);function p1(e,t){var r=b1(t);return r.formatToParts?y1(r,e):g1(r,e)}var m1={year:0,month:1,day:2,hour:3,minute:4,second:5};function y1(e,t){try{for(var r=e.formatToParts(t),n=[],a=0;a<r.length;a++){var s=m1[r[a].type];s>=0&&(n[s]=parseInt(r[a].value,10))}return n}catch(o){if(o instanceof RangeError)return[NaN];throw o}}function g1(e,t){var r=e.format(t).replace(/\u200E/g,""),n=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(r);return[n[3],n[1],n[2],n[4],n[5],n[6]]}var fn={};function b1(e){if(!fn[e]){var t=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:"America/New_York",year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date("2014-06-25T04:00:00.123Z")),r=t==="06/25/2014, 00:00:00"||t==="‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00";fn[e]=r?new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}return fn[e]}function Po(e,t,r,n,a,s,o){var i=new Date(0);return i.setUTCFullYear(e,t,r),i.setUTCHours(n,a,s,o),i}var ls=36e5,w1=6e4,dn={timezone:/([Z+-].*)$/,timezoneZ:/^(Z)$/,timezoneHH:/^([+-]\d{2})$/,timezoneHHMM:/^([+-]\d{2}):?(\d{2})$/};function D1(e,t,r){var n,a;if(!e||(n=dn.timezoneZ.exec(e),n))return 0;var s;if(n=dn.timezoneHH.exec(e),n)return s=parseInt(n[1],10),us(s)?-(s*ls):NaN;if(n=dn.timezoneHHMM.exec(e),n){s=parseInt(n[1],10);var o=parseInt(n[2],10);return us(s,o)?(a=Math.abs(s)*ls+o*w1,s>0?-a:a):NaN}if(M1(e)){t=new Date(t||Date.now());var i=r?t:$1(t),l=Mn(i,e),u=r?l:_1(t,l,e);return-u}return NaN}function $1(e){return Po(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}function Mn(e,t){var r=p1(e,t),n=Po(r[0],r[1]-1,r[2],r[3]%24,r[4],r[5],0).getTime(),a=e.getTime(),s=a%1e3;return a-=s>=0?s:1e3+s,n-a}function _1(e,t,r){var n=e.getTime(),a=n-t,s=Mn(new Date(a),r);if(t===s)return t;a-=s-t;var o=Mn(new Date(a),r);return s===o?s:Math.max(s,o)}function us(e,t){return-23<=e&&e<=23&&(t==null||0<=t&&t<=59)}var cs={};function M1(e){if(cs[e])return!0;try{return new Intl.DateTimeFormat(void 0,{timeZone:e}),cs[e]=!0,!0}catch{return!1}}var k1=/(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/;const O1=k1;var vn=36e5,fs=6e4,P1=2,ge={dateTimePattern:/^([0-9W+-]+)(T| )(.*)/,datePattern:/^([0-9W+-]+)(.*)/,plainTime:/:/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timeZone:O1};function T1(e,t){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");if(e===null)return new Date(NaN);var r=t||{},n=r.additionalDigits==null?P1:v1(r.additionalDigits);if(n!==2&&n!==1&&n!==0)throw new RangeError("additionalDigits must be 0, 1 or 2");if(e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]")return new Date(e.getTime());if(typeof e=="number"||Object.prototype.toString.call(e)==="[object Number]")return new Date(e);if(!(typeof e=="string"||Object.prototype.toString.call(e)==="[object String]"))return new Date(NaN);var a=Y1(e),s=C1(a.date,n),o=s.year,i=s.restDateString,l=S1(i,o);if(isNaN(l))return new Date(NaN);if(l){var u=l.getTime(),c=0,d;if(a.time&&(c=A1(a.time),isNaN(c)))return new Date(NaN);if(a.timeZone||r.timeZone){if(d=D1(a.timeZone||r.timeZone,new Date(u+c)),isNaN(d))return new Date(NaN)}else d=is(new Date(u+c)),d=is(new Date(u+c+d));return new Date(u+c+d)}else return new Date(NaN)}function Y1(e){var t={},r=ge.dateTimePattern.exec(e),n;if(r?(t.date=r[1],n=r[3]):(r=ge.datePattern.exec(e),r?(t.date=r[1],n=r[2]):(t.date=null,n=e)),n){var a=ge.timeZone.exec(n);a?(t.time=n.replace(a[1],""),t.timeZone=a[1].trim()):t.time=n}return t}function C1(e,t){var r=ge.YYY[t],n=ge.YYYYY[t],a;if(a=ge.YYYY.exec(e)||n.exec(e),a){var s=a[1];return{year:parseInt(s,10),restDateString:e.slice(s.length)}}if(a=ge.YY.exec(e)||r.exec(e),a){var o=a[1];return{year:parseInt(o,10)*100,restDateString:e.slice(o.length)}}return{year:null}}function S1(e,t){if(t===null)return null;var r,n,a,s;if(e.length===0)return n=new Date(0),n.setUTCFullYear(t),n;if(r=ge.MM.exec(e),r)return n=new Date(0),a=parseInt(r[1],10)-1,vs(t,a)?(n.setUTCFullYear(t,a),n):new Date(NaN);if(r=ge.DDD.exec(e),r){n=new Date(0);var o=parseInt(r[1],10);return E1(t,o)?(n.setUTCFullYear(t,0,o),n):new Date(NaN)}if(r=ge.MMDD.exec(e),r){n=new Date(0),a=parseInt(r[1],10)-1;var i=parseInt(r[2],10);return vs(t,a,i)?(n.setUTCFullYear(t,a,i),n):new Date(NaN)}if(r=ge.Www.exec(e),r)return s=parseInt(r[1],10)-1,hs(t,s)?ds(t,s):new Date(NaN);if(r=ge.WwwD.exec(e),r){s=parseInt(r[1],10)-1;var l=parseInt(r[2],10)-1;return hs(t,s,l)?ds(t,s,l):new Date(NaN)}return null}function A1(e){var t,r,n;if(t=ge.HH.exec(e),t)return r=parseFloat(t[1].replace(",",".")),hn(r)?r%24*vn:NaN;if(t=ge.HHMM.exec(e),t)return r=parseInt(t[1],10),n=parseFloat(t[2].replace(",",".")),hn(r,n)?r%24*vn+n*fs:NaN;if(t=ge.HHMMSS.exec(e),t){r=parseInt(t[1],10),n=parseInt(t[2],10);var a=parseFloat(t[3].replace(",","."));return hn(r,n,a)?r%24*vn+n*fs+a*1e3:NaN}return null}function ds(e,t,r){t=t||0,r=r||0;var n=new Date(0);n.setUTCFullYear(e,0,4);var a=n.getUTCDay()||7,s=t*7+r+1-a;return n.setUTCDate(n.getUTCDate()+s),n}var I1=[31,28,31,30,31,30,31,31,30,31,30,31],x1=[31,29,31,30,31,30,31,31,30,31,30,31];function To(e){return e%400===0||e%4===0&&e%100!==0}function vs(e,t,r){if(t<0||t>11)return!1;if(r!=null){if(r<1)return!1;var n=To(e);if(n&&r>x1[t]||!n&&r>I1[t])return!1}return!0}function E1(e,t){if(t<1)return!1;var r=To(e);return!(r&&t>366||!r&&t>365)}function hs(e,t,r){return!(t<0||t>52||r!=null&&(r<0||r>6))}function hn(e,t,r){return!(e!=null&&(e<0||e>=25)||t!=null&&(t<0||t>=60)||r!=null&&(r<0||r>=60))}function he(e,t){if(t.length<e)throw new TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")}function Ir(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ir=function(r){return typeof r}:Ir=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Ir(e)}function et(e){he(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||Ir(e)==="object"&&t==="[object Date]"?new Date(e.getTime()):typeof e=="number"||t==="[object Number]"?new Date(e):((typeof e=="string"||t==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function Kt(e){if(e===null||e===!0||e===!1)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)}var N1={};function ta(){return N1}function $t(e,t){var r,n,a,s,o,i,l,u;he(1,arguments);var c=ta(),d=Kt((r=(n=(a=(s=t==null?void 0:t.weekStartsOn)!==null&&s!==void 0?s:t==null||(o=t.locale)===null||o===void 0||(i=o.options)===null||i===void 0?void 0:i.weekStartsOn)!==null&&a!==void 0?a:c.weekStartsOn)!==null&&n!==void 0?n:(l=c.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.weekStartsOn)!==null&&r!==void 0?r:0);if(!(d>=0&&d<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var h=et(e),v=h.getDay(),m=(v<d?7:0)+v-d;return h.setDate(h.getDate()-m),h.setHours(0,0,0,0),h}function ps(e){var t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),e.getTime()-t.getTime()}var L1=6048e5;function R1(e,t,r){he(2,arguments);var n=$t(e,r),a=$t(t,r),s=n.getTime()-ps(n),o=a.getTime()-ps(a);return Math.round((s-o)/L1)}function F1(e){he(1,arguments);var t=et(e),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(0,0,0,0),t}function H1(e){he(1,arguments);var t=et(e);return t.setDate(1),t.setHours(0,0,0,0),t}function W1(e,t){return he(1,arguments),R1(F1(e),H1(e),t)+1}function j1(e,t){var r,n,a,s,o,i,l,u;he(1,arguments);var c=et(e),d=c.getFullYear(),h=ta(),v=Kt((r=(n=(a=(s=t==null?void 0:t.firstWeekContainsDate)!==null&&s!==void 0?s:t==null||(o=t.locale)===null||o===void 0||(i=o.options)===null||i===void 0?void 0:i.firstWeekContainsDate)!==null&&a!==void 0?a:h.firstWeekContainsDate)!==null&&n!==void 0?n:(l=h.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&r!==void 0?r:1);if(!(v>=1&&v<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var m=new Date(0);m.setFullYear(d+1,0,v),m.setHours(0,0,0,0);var _=$t(m,t),D=new Date(0);D.setFullYear(d,0,v),D.setHours(0,0,0,0);var y=$t(D,t);return c.getTime()>=_.getTime()?d+1:c.getTime()>=y.getTime()?d:d-1}function B1(e,t){var r,n,a,s,o,i,l,u;he(1,arguments);var c=ta(),d=Kt((r=(n=(a=(s=t==null?void 0:t.firstWeekContainsDate)!==null&&s!==void 0?s:t==null||(o=t.locale)===null||o===void 0||(i=o.options)===null||i===void 0?void 0:i.firstWeekContainsDate)!==null&&a!==void 0?a:c.firstWeekContainsDate)!==null&&n!==void 0?n:(l=c.locale)===null||l===void 0||(u=l.options)===null||u===void 0?void 0:u.firstWeekContainsDate)!==null&&r!==void 0?r:1),h=j1(e,t),v=new Date(0);v.setFullYear(h,0,d),v.setHours(0,0,0,0);var m=$t(v,t);return m}var z1=6048e5;function V1(e,t){he(1,arguments);var r=et(e),n=$t(r,t).getTime()-B1(r,t).getTime();return Math.round(n/z1)+1}function jr(e){return he(1,arguments),$t(e,{weekStartsOn:1})}function U1(e){he(1,arguments);var t=et(e),r=t.getFullYear(),n=new Date(0);n.setFullYear(r+1,0,4),n.setHours(0,0,0,0);var a=jr(n),s=new Date(0);s.setFullYear(r,0,4),s.setHours(0,0,0,0);var o=jr(s);return t.getTime()>=a.getTime()?r+1:t.getTime()>=o.getTime()?r:r-1}function K1(e){he(1,arguments);var t=U1(e),r=new Date(0);r.setFullYear(t,0,4),r.setHours(0,0,0,0);var n=jr(r);return n}var G1=6048e5;function q1(e){he(1,arguments);var t=et(e),r=jr(t).getTime()-K1(t).getTime();return Math.round(r/G1)+1}function ye(e,t){he(2,arguments);var r=et(e),n=Kt(t);return isNaN(n)?new Date(NaN):(n&&r.setDate(r.getDate()+n),r)}function Br(e,t){he(2,arguments);var r=et(e),n=Kt(t);if(isNaN(n))return new Date(NaN);if(!n)return r;var a=r.getDate(),s=new Date(r.getTime());s.setMonth(r.getMonth()+n+1,0);var o=s.getDate();return a>=o?s:(r.setFullYear(s.getFullYear(),s.getMonth(),a),r)}function ms(e,t){he(2,arguments);var r=Kt(t);return Br(e,r*12)}const Z1={daily:["year","month","day"],weekly:["year","month","week"],monthly:["year","month"]};function X1({monthComps:e,prevMonthComps:t,nextMonthComps:r},n){const a=[],{firstDayOfWeek:s,firstWeekday:o,isoWeeknumbers:i,weeknumbers:l,numDays:u,numWeeks:c}=e,d=o+(o<s?ve:0)-s;let h=!0,v=!1,m=!1,_=0;const D=new Intl.DateTimeFormat(n.id,{weekday:"long",year:"numeric",month:"short",day:"numeric"});let y=t.numDays-d+1,C=t.numDays-y+1,L=Math.floor((y-1)/ve+1),M=1,O=t.numWeeks,E=1,T=t.month,N=t.year;const H=new Date,W=H.getDate(),j=H.getMonth()+1,Y=H.getFullYear();for(let R=1;R<=Bw;R++){for(let K=1,I=s;K<=ve;K++,I+=I===ve?1-ve:1){h&&I===o&&(y=1,C=e.numDays,L=Math.floor((y-1)/ve+1),M=Math.floor((u-y)/ve+1),O=1,E=c,T=e.month,N=e.year,h=!1,v=!0);const z=n.getDateFromParams(N,T,y,0,0,0,0),Z=n.getDateFromParams(N,T,y,12,0,0,0),oe=n.getDateFromParams(N,T,y,23,59,59,999),Q=z,$=`${q(N,4)}-${q(T,2)}-${q(y,2)}`,B=K,A=ve-K,ne=l[R-1],G=i[R-1],ae=y===W&&T===j&&N===Y,ue=v&&y===1,se=v&&y===u,ce=R===1,Me=R===c,Ke=K===1,Ee=K===ve,Pe=Ho(N,T,y);a.push({locale:n,id:$,position:++_,label:y.toString(),ariaLabel:D.format(new Date(N,T-1,y)),day:y,dayFromEnd:C,weekday:I,weekdayPosition:B,weekdayPositionFromEnd:A,weekdayOrdinal:L,weekdayOrdinalFromEnd:M,week:O,weekFromEnd:E,weekPosition:R,weeknumber:ne,isoWeeknumber:G,month:T,year:N,date:Q,startDate:z,endDate:oe,noonDate:Z,dayIndex:Pe,isToday:ae,isFirstDay:ue,isLastDay:se,isDisabled:!v,isFocusable:!v,isFocused:!1,inMonth:v,inPrevMonth:h,inNextMonth:m,onTop:ce,onBottom:Me,onLeft:Ke,onRight:Ee,classes:[`id-${$}`,`day-${y}`,`day-from-end-${C}`,`weekday-${I}`,`weekday-position-${B}`,`weekday-ordinal-${L}`,`weekday-ordinal-from-end-${M}`,`week-${O}`,`week-from-end-${E}`,{"is-today":ae,"is-first-day":ue,"is-last-day":se,"in-month":v,"in-prev-month":h,"in-next-month":m,"on-top":ce,"on-bottom":Me,"on-left":Ke,"on-right":Ee}]}),v&&se?(v=!1,m=!0,y=1,C=u,L=1,M=Math.floor((u-y)/ve+1),O=1,E=r.numWeeks,T=r.month,N=r.year):(y++,C--,L=Math.floor((y-1)/ve+1),M=Math.floor((u-y)/ve+1))}O++,E--}return a}function J1(e,t,r,n){const a=e.reduce((s,o,i)=>{const l=Math.floor(i/7);let u=s[l];return u||(u={id:`week-${l+1}`,title:"",week:o.week,weekPosition:o.weekPosition,weeknumber:o.weeknumber,isoWeeknumber:o.isoWeeknumber,weeknumberDisplay:t?o.weeknumber:r?o.isoWeeknumber:void 0,days:[]},s[l]=u),u.days.push(o),s},Array(e.length/ve));return a.forEach(s=>{const o=s.days[0],i=s.days[s.days.length-1];o.month===i.month?s.title=`${n.formatDate(o.date,"MMMM YYYY")}`:o.year===i.year?s.title=`${n.formatDate(o.date,"MMM")} - ${n.formatDate(i.date,"MMM YYYY")}`:s.title=`${n.formatDate(o.date,"MMM YYYY")} - ${n.formatDate(i.date,"MMM YYYY")}`}),a}function Q1(e,t){return e.days.map(r=>({label:t.formatDate(r.date,t.masks.weekdays),weekday:r.weekday}))}function ew(e,t){return`${t}.${q(e,2)}`}function Yo(e,t,r){return Oo(r.getDateParts(r.toDate(e)),Z1[t])}function Co({day:e,week:t,month:r,year:n},a,s,o){if(s==="daily"&&e){const i=new Date(n,r-1,e),l=ye(i,a);return{day:l.getDate(),month:l.getMonth()+1,year:l.getFullYear()}}else if(s==="weekly"&&t){const l=o.getMonthParts(r,n).firstDayOfMonth,u=ye(l,(t-1+a)*7),c=o.getDateParts(u);return{week:c.week,month:c.month,year:c.year}}else{const i=new Date(n,r-1,1),l=Br(i,a);return{month:l.getMonth()+1,year:l.getFullYear()}}}function je(e){return e!=null&&e.month!=null&&e.year!=null}function kn(e,t){return!je(e)||!je(t)?!1:(e=e,t=t,e.year!==t.year?e.year<t.year:e.month&&t.month&&e.month!==t.month?e.month<t.month:e.week&&t.week&&e.week!==t.week?e.week<t.week:e.day&&t.day&&e.day!==t.day?e.day<t.day:!1)}function zr(e,t){return!je(e)||!je(t)?!1:(e=e,t=t,e.year!==t.year?e.year>t.year:e.month&&t.month&&e.month!==t.month?e.month>t.month:e.week&&t.week&&e.week!==t.week?e.week>t.week:e.day&&t.day&&e.day!==t.day?e.day>t.day:!1)}function tw(e,t,r){return(e||!1)&&!kn(e,t)&&!zr(e,r)}function rw(e,t){return!e&&t||e&&!t?!1:!e&&!t?!0:(e=e,t=t,e.year===t.year&&e.month===t.month&&e.week===t.week&&e.day===t.day)}function nw(e,t,r,n){if(!je(e)||!je(t))return[];const a=[];for(;!zr(e,t);)a.push(e),e=Co(e,1,r,n);return a}function So(e){const{day:t,week:r,month:n,year:a}=e;let s=`${a}-${q(n,2)}`;return r&&(s=`${s}-w${r}`),t&&(s=`${s}-${q(t,2)}`),s}function aw(e,t){const{month:r,year:n,showWeeknumbers:a,showIsoWeeknumbers:s}=e,o=new Date(n,r-1,15),i=t.getMonthParts(r,n),l=t.getPrevMonthParts(r,n),u=t.getNextMonthParts(r,n),c=X1({monthComps:i,prevMonthComps:l,nextMonthComps:u},t),d=J1(c,a,s,t),h=Q1(d[0],t);return{id:So(e),month:r,year:n,monthTitle:t.formatDate(o,t.masks.title),shortMonthLabel:t.formatDate(o,"MMM"),monthLabel:t.formatDate(o,"MMMM"),shortYearLabel:n.toString().substring(2),yearLabel:n.toString(),monthComps:i,prevMonthComps:l,nextMonthComps:u,days:c,weeks:d,weekdays:h}}function sw(e,t){const{day:r,week:n,view:a,trimWeeks:s}=e,o={...t,...e,title:"",viewDays:[],viewWeeks:[]};switch(a){case"daily":{let i=o.days.find(u=>u.inMonth);r?i=o.days.find(u=>u.day===r&&u.inMonth)||i:n&&(i=o.days.find(u=>u.week===n&&u.inMonth));const l=o.weeks[i.week-1];o.viewWeeks=[l],o.viewDays=[i],o.week=i.week,o.weekTitle=l.title,o.day=i.day,o.dayTitle=i.ariaLabel,o.title=o.dayTitle;break}case"weekly":{o.week=n||1;const i=o.weeks[o.week-1];o.viewWeeks=[i],o.viewDays=i.days,o.weekTitle=i.title,o.title=o.weekTitle;break}default:{o.title=o.monthTitle,o.viewWeeks=o.weeks.slice(0,s?o.monthComps.numWeeks:void 0),o.viewDays=o.days;break}}return o}class ys{constructor(t,r,n){S(this,"keys",[]),S(this,"store",{}),this.size=t,this.createKey=r,this.createItem=n}get(...t){const r=this.createKey(...t);return this.store[r]}getOrSet(...t){const r=this.createKey(...t);if(this.store[r])return this.store[r];const n=this.createItem(...t);if(this.keys.length>=this.size){const a=this.keys.shift();a!=null&&delete this.store[a]}return this.keys.push(r),this.store[r]=n,n}}class It{constructor(t,r=new Vr){S(this,"order"),S(this,"locale"),S(this,"start",null),S(this,"end",null),S(this,"repeat",null);var n;this.locale=r;const{start:a,end:s,span:o,order:i,repeat:l}=t;At(a)&&(this.start=r.getDateParts(a)),At(s)?this.end=r.getDateParts(s):this.start!=null&&o&&(this.end=r.getDateParts(ye(this.start.date,o-1))),this.order=i??0,l&&(this.repeat=new Ur({from:(n=this.start)==null?void 0:n.date,...l},{locale:this.locale}))}static fromMany(t,r){return(Ae(t)?t:[t]).filter(n=>n).map(n=>It.from(n,r))}static from(t,r){if(t instanceof It)return t;const n={start:null,end:null};return t!=null&&(Ae(t)?(n.start=t[0]??null,n.end=t[1]??null):Xe(t)?Object.assign(n,t):(n.start=t,n.end=t)),n.start!=null&&(n.start=new Date(n.start)),n.end!=null&&(n.end=new Date(n.end)),new It(n,r)}get opts(){const{order:t,locale:r}=this;return{order:t,locale:r}}get hasRepeat(){return!!this.repeat}get isSingleDay(){const{start:t,end:r}=this;return t&&r&&t.year===r.year&&t.month===r.month&&t.day===r.day}get isMultiDay(){return!this.isSingleDay}get daySpan(){return this.start==null||this.end==null?this.hasRepeat?1:1/0:this.end.dayIndex-this.start.dayIndex}startsOnDay(t){var r,n;return((r=this.start)==null?void 0:r.dayIndex)===t.dayIndex||!!((n=this.repeat)!=null&&n.passes(t))}intersectsDay(t){return this.intersectsDayRange(t,t)}intersectsRange(t){var r,n;return this.intersectsDayRange(((r=t.start)==null?void 0:r.dayIndex)??-1/0,((n=t.end)==null?void 0:n.dayIndex)??1/0)}intersectsDayRange(t,r){return!(this.start&&this.start.dayIndex>r||this.end&&this.end.dayIndex<t)}}class ow{constructor(){S(this,"records",{})}render(t,r,n){var a,s,o,i;let l=null;const u=n[0].dayIndex,c=n[n.length-1].dayIndex;return r.hasRepeat?n.forEach(d=>{var h,v;if(r.startsOnDay(d)){const m=r.daySpan<1/0?r.daySpan:1;l={startDay:d.dayIndex,startTime:((h=r.start)==null?void 0:h.time)??0,endDay:d.dayIndex+m-1,endTime:((v=r.end)==null?void 0:v.time)??xr},this.getRangeRecords(t).push(l)}}):r.intersectsDayRange(u,c)&&(l={startDay:((a=r.start)==null?void 0:a.dayIndex)??-1/0,startTime:((s=r.start)==null?void 0:s.time)??-1/0,endDay:((o=r.end)==null?void 0:o.dayIndex)??1/0,endTime:((i=r.end)==null?void 0:i.time)??1/0},this.getRangeRecords(t).push(l)),l}getRangeRecords(t){let r=this.records[t.key];return r||(r={ranges:[],data:t},this.records[t.key]=r),r.ranges}getCell(t,r){return this.getCells(r).find(s=>s.data.key===t)}cellExists(t,r){const n=this.records[t];return n==null?!1:n.ranges.some(a=>a.startDay<=r&&a.endDay>=r)}getCells(t){const r=Object.values(this.records),n=[],{dayIndex:a}=t;return r.forEach(({data:s,ranges:o})=>{o.filter(i=>i.startDay<=a&&i.endDay>=a).forEach(i=>{const l=a===i.startDay,u=a===i.endDay,c=l?i.startTime:0,d=new Date(t.startDate.getTime()+c),h=u?i.endTime:xr,v=new Date(t.endDate.getTime()+h),m=c===0&&h===xr,_=s.order||0;n.push({...i,data:s,onStart:l,onEnd:u,startTime:c,startDate:d,endTime:h,endDate:v,allDay:m,order:_})})}),n.sort((s,o)=>s.order-o.order),n}}const Je={ar:{dow:7,L:"D/‏M/‏YYYY"},bg:{dow:2,L:"D.MM.YYYY"},ca:{dow:2,L:"DD/MM/YYYY"},"zh-CN":{dow:2,L:"YYYY/MM/DD"},"zh-TW":{dow:1,L:"YYYY/MM/DD"},hr:{dow:2,L:"DD.MM.YYYY"},cs:{dow:2,L:"DD.MM.YYYY"},da:{dow:2,L:"DD.MM.YYYY"},nl:{dow:2,L:"DD-MM-YYYY"},"en-US":{dow:1,L:"MM/DD/YYYY"},"en-AU":{dow:2,L:"DD/MM/YYYY"},"en-CA":{dow:1,L:"YYYY-MM-DD"},"en-GB":{dow:2,L:"DD/MM/YYYY"},"en-IE":{dow:2,L:"DD-MM-YYYY"},"en-NZ":{dow:2,L:"DD/MM/YYYY"},"en-ZA":{dow:1,L:"YYYY/MM/DD"},eo:{dow:2,L:"YYYY-MM-DD"},et:{dow:2,L:"DD.MM.YYYY"},fi:{dow:2,L:"DD.MM.YYYY"},fr:{dow:2,L:"DD/MM/YYYY"},"fr-CA":{dow:1,L:"YYYY-MM-DD"},"fr-CH":{dow:2,L:"DD.MM.YYYY"},de:{dow:2,L:"DD.MM.YYYY"},he:{dow:1,L:"DD.MM.YYYY"},id:{dow:2,L:"DD/MM/YYYY"},it:{dow:2,L:"DD/MM/YYYY"},ja:{dow:1,L:"YYYY年M月D日"},ko:{dow:1,L:"YYYY.MM.DD"},lv:{dow:2,L:"DD.MM.YYYY"},lt:{dow:2,L:"DD.MM.YYYY"},mk:{dow:2,L:"D.MM.YYYY"},nb:{dow:2,L:"D. MMMM YYYY"},nn:{dow:2,L:"D. MMMM YYYY"},pl:{dow:2,L:"DD.MM.YYYY"},pt:{dow:2,L:"DD/MM/YYYY"},ro:{dow:2,L:"DD.MM.YYYY"},ru:{dow:2,L:"DD.MM.YYYY"},sk:{dow:2,L:"DD.MM.YYYY"},"es-ES":{dow:2,L:"DD/MM/YYYY"},"es-MX":{dow:2,L:"DD/MM/YYYY"},sv:{dow:2,L:"YYYY-MM-DD"},th:{dow:1,L:"DD/MM/YYYY"},tr:{dow:2,L:"DD.MM.YYYY"},uk:{dow:2,L:"DD.MM.YYYY"},vi:{dow:2,L:"DD/MM/YYYY"}};Je.en=Je["en-US"];Je.es=Je["es-ES"];Je.no=Je.nb;Je.zh=Je["zh-CN"];const iw=Object.entries(Je).reduce((e,[t,{dow:r,L:n}])=>(e[t]={id:t,firstDayOfWeek:r,masks:{L:n}},e),{}),lw="MMMM YYYY",uw="W",cw="MMM",fw="h A",dw=["L","YYYY-MM-DD","YYYY/MM/DD"],vw=["L h:mm A","YYYY-MM-DD h:mm A","YYYY/MM/DD h:mm A"],hw=["L HH:mm","YYYY-MM-DD HH:mm","YYYY/MM/DD HH:mm"],pw=["h:mm A"],mw=["HH:mm"],yw="WWW, MMM D, YYYY",gw=["L","YYYY-MM-DD","YYYY/MM/DD"],bw="iso",ww="YYYY-MM-DDTHH:mm:ss.SSSZ",Dw={title:lw,weekdays:uw,navMonths:cw,hours:fw,input:dw,inputDateTime:vw,inputDateTime24hr:hw,inputTime:pw,inputTime24hr:mw,dayPopover:yw,data:gw,model:bw,iso:ww},$w=300,_w=60,Mw=80,kw={maxSwipeTime:$w,minHorizontalSwipeDistance:_w,maxVerticalSwipeDistance:Mw},Ow={componentPrefix:"V",color:"blue",isDark:!1,navVisibility:"click",titlePosition:"center",transition:"slide-h",touch:kw,masks:Dw,locales:iw,datePicker:{updateOnInput:!0,inputDebounce:1e3,popover:{visibility:"hover-focus",placement:"bottom-start",isInteractive:!0}}},On=Yn(Ow),Pw=p(()=>Gy(On.locales,e=>(e.masks=vr(e.masks,On.masks),e))),ft=e=>typeof window<"u"&&Mo(window.__vcalendar__,e)?bt(window.__vcalendar__,e):bt(On,e),Tw=12,Yw=5;function Cw(e,t){const r=new Intl.DateTimeFormat().resolvedOptions().locale;let n;We(e)?n=e:Mo(e,"id")&&(n=e.id),n=(n||r).toLowerCase();const a=Object.keys(t),s=l=>a.find(u=>u.toLowerCase()===l);n=s(n)||s(n.substring(0,2))||r;const o={...t["en-IE"],...t[n],id:n,monthCacheSize:Tw,pageCacheSize:Yw};return Xe(e)?vr(e,o):o}class Vr{constructor(t=void 0,r){S(this,"id"),S(this,"daysInWeek"),S(this,"firstDayOfWeek"),S(this,"masks"),S(this,"timezone"),S(this,"hourLabels"),S(this,"dayNames"),S(this,"dayNamesShort"),S(this,"dayNamesShorter"),S(this,"dayNamesNarrow"),S(this,"monthNames"),S(this,"monthNamesShort"),S(this,"relativeTimeNames"),S(this,"amPm",["am","pm"]),S(this,"monthCache"),S(this,"pageCache");const{id:n,firstDayOfWeek:a,masks:s,monthCacheSize:o,pageCacheSize:i}=Cw(t,Pw.value);this.monthCache=new ys(o,Jw,Qw),this.pageCache=new ys(i,So,aw),this.id=n,this.daysInWeek=ve,this.firstDayOfWeek=f1(a,1,ve),this.masks=s,this.timezone=r||void 0,this.hourLabels=this.getHourLabels(),this.dayNames=pn("long",this.id),this.dayNamesShort=pn("short",this.id),this.dayNamesShorter=this.dayNamesShort.map(l=>l.substring(0,2)),this.dayNamesNarrow=pn("narrow",this.id),this.monthNames=_s("long",this.id),this.monthNamesShort=_s("short",this.id),this.relativeTimeNames=rD(this.id)}formatDate(t,r){return iD(t,r,this)}parseDate(t,r){return Ms(t,r,this)}toDate(t,r={}){const n=new Date(NaN);let a=n;const{fillDate:s,mask:o,patch:i,rules:l}=r;if(Le(t)?(r.type="number",a=new Date(+t)):We(t)?(r.type="string",a=t?Ms(t,o||"iso",this):n):At(t)?(r.type="date",a=new Date(t.getTime())):ra(t)&&(r.type="object",a=this.getDateFromParts(t)),a&&(i||l)){let u=this.getDateParts(a);if(i&&s!=null){const c=this.getDateParts(this.toDate(s));u=this.getDateParts(this.toDate({...c,...Oo(u,jw[i])}))}l&&(u=oD(u,l)),a=this.getDateFromParts(u)}return a||n}toDateOrNull(t,r={}){const n=this.toDate(t,r);return isNaN(n.getTime())?null:n}fromDate(t,{type:r,mask:n}={}){switch(r){case"number":return t?t.getTime():NaN;case"string":return t?this.formatDate(t,n||"iso"):"";case"object":return t?this.getDateParts(t):null;default:return t?new Date(t):null}}range(t){return It.from(t,this)}ranges(t){return It.fromMany(t,this)}getDateParts(t){return Xw(t,this)}getDateFromParts(t){return jo(t,this.timezone)}getDateFromParams(t,r,n,a,s,o,i){return this.getDateFromParts({year:t,month:r,day:n,hours:a,minutes:s,seconds:o,milliseconds:i})}getPage(t){const r=this.pageCache.getOrSet(t,this);return sw(t,r)}getMonthParts(t,r){const{firstDayOfWeek:n}=this;return this.monthCache.getOrSet(t,r,n)}getThisMonthParts(){const t=new Date;return this.getMonthParts(t.getMonth()+1,t.getFullYear())}getPrevMonthParts(t,r){return t===1?this.getMonthParts(12,r-1):this.getMonthParts(t-1,r)}getNextMonthParts(t,r){return t===12?this.getMonthParts(1,r+1):this.getMonthParts(t+1,r)}getHourLabels(){return tD().map(t=>this.formatDate(t,this.masks.hours))}getDayId(t){return this.formatDate(t,"YYYY-MM-DD")}}var Ct=(e=>(e.Any="any",e.All="all",e))(Ct||{}),Ao=(e=>(e.Days="days",e.Weeks="weeks",e.Months="months",e.Years="years",e))(Ao||{}),Io=(e=>(e.Days="days",e.Weekdays="weekdays",e.Weeks="weeks",e.Months="months",e.Years="years",e))(Io||{}),xo=(e=>(e.OrdinalWeekdays="ordinalWeekdays",e))(xo||{});class Sw{constructor(t,r,n){S(this,"validated",!0),this.type=t,this.interval=r,this.from=n,this.from||(console.error('A valid "from" date is required for date interval rule. This rule will be skipped.'),this.validated=!1)}passes(t){if(!this.validated)return!0;const{date:r}=t;switch(this.type){case"days":return na(this.from.date,r)%this.interval===0;case"weeks":return qw(this.from.date,r)%this.interval===0;case"months":return Zw(this.from.date,r)%this.interval===0;case"years":return Wo(this.from.date,r)%this.interval===0;default:return!1}}}class Gt{constructor(t,r,n,a){S(this,"components",[]),this.type=t,this.validator=n,this.getter=a,this.components=this.normalizeComponents(r)}static create(t,r){switch(t){case"days":return new Aw(r);case"weekdays":return new Iw(r);case"weeks":return new xw(r);case"months":return new Ew(r);case"years":return new Nw(r)}}normalizeComponents(t){if(this.validator(t))return[t];if(!Ae(t))return[];const r=[];return t.forEach(n=>{if(!this.validator(n)){console.error(`Component value ${n} in invalid for "${this.type}" rule. This rule will be skipped.`);return}r.push(n)}),r}passes(t){return this.getter(t).some(a=>this.components.includes(a))}}class Aw extends Gt{constructor(t){super("days",t,Fw,({day:r,dayFromEnd:n})=>[r,-n])}}class Iw extends Gt{constructor(t){super("weekdays",t,Pn,({weekday:r})=>[r])}}class xw extends Gt{constructor(t){super("weeks",t,Hw,({week:r,weekFromEnd:n})=>[r,-n])}}class Ew extends Gt{constructor(t){super("months",t,Ww,({month:r})=>[r])}}class Nw extends Gt{constructor(t){super("years",t,Le,({year:r})=>[r])}}class Lw{constructor(t,r){S(this,"components"),this.type=t,this.components=this.normalizeComponents(r)}normalizeArrayConfig(t){const r=[];return t.forEach((n,a)=>{if(Le(n)){if(a===0)return;if(!gs(t[0])){console.error(`Ordinal range for "${this.type}" rule is from -5 to -1 or 1 to 5. This rule will be skipped.`);return}if(!Pn(n)){console.error(`Acceptable range for "${this.type}" rule is from 1 to 5. This rule will be skipped`);return}r.push([t[0],n])}else Ae(n)&&r.push(...this.normalizeArrayConfig(n))}),r}normalizeComponents(t){const r=[];return t.forEach((n,a)=>{if(Le(n)){if(a===0)return;if(!gs(t[0])){console.error(`Ordinal range for "${this.type}" rule is from -5 to -1 or 1 to 5. This rule will be skipped.`);return}if(!Pn(n)){console.error(`Acceptable range for "${this.type}" rule is from 1 to 5. This rule will be skipped`);return}r.push([t[0],n])}else Ae(n)&&r.push(...this.normalizeArrayConfig(n))}),r}passes(t){const{weekday:r,weekdayOrdinal:n,weekdayOrdinalFromEnd:a}=t;return this.components.some(([s,o])=>(s===n||s===-a)&&r===o)}}class Rw{constructor(t){S(this,"type","function"),S(this,"validated",!0),this.fn=t,_t(t)||(console.error("The function rule requires a valid function. This rule will be skipped."),this.validated=!1)}passes(t){return this.validated?this.fn(t):!0}}class Ur{constructor(t,r={},n){S(this,"validated",!0),S(this,"config"),S(this,"type",Ct.Any),S(this,"from"),S(this,"until"),S(this,"rules",[]),S(this,"locale",new Vr),this.parent=n,r.locale&&(this.locale=r.locale),this.config=t,_t(t)?(this.type=Ct.All,this.rules=[new Rw(t)]):Ae(t)?(this.type=Ct.Any,this.rules=t.map(a=>new Ur(a,r,this))):Xe(t)?(this.type=Ct.All,this.from=t.from?this.locale.getDateParts(t.from):n==null?void 0:n.from,this.until=t.until?this.locale.getDateParts(t.until):n==null?void 0:n.until,this.rules=this.getObjectRules(t)):(console.error("Rule group configuration must be an object or an array."),this.validated=!1)}getObjectRules(t){const r=[];if(t.every&&(We(t.every)&&(t.every=[1,`${t.every}s`]),Ae(t.every))){const[n=1,a=Ao.Days]=t.every;r.push(new Sw(a,n,this.from))}return Object.values(Io).forEach(n=>{n in t&&r.push(Gt.create(n,t[n]))}),Object.values(xo).forEach(n=>{n in t&&r.push(new Lw(n,t[n]))}),t.on!=null&&(Ae(t.on)||(t.on=[t.on]),r.push(new Ur(t.on,{locale:this.locale},this.parent))),r}passes(t){return this.validated?this.from&&t.dayIndex<=this.from.dayIndex||this.until&&t.dayIndex>=this.until.dayIndex?!1:this.type===Ct.Any?this.rules.some(r=>r.passes(t)):this.rules.every(r=>r.passes(t)):!0}}function Fw(e){return Le(e)?e>=1&&e<=31:!1}function Pn(e){return Le(e)?e>=1&&e<=7:!1}function Hw(e){return Le(e)?e>=-6&&e<=-1||e>=1&&e<=6:!1}function Ww(e){return Le(e)?e>=1&&e<=12:!1}function gs(e){return!(!Le(e)||e<-5||e>5||e===0)}const jw={dateTime:["year","month","day","hours","minutes","seconds","milliseconds"],date:["year","month","day"],time:["hours","minutes","seconds","milliseconds"]},ve=7,Bw=6,Eo=1e3,No=Eo*60,Lo=No*60,xr=Lo*24,zw=[31,28,31,30,31,30,31,31,30,31,30,31],Vw=["L","iso"],ar={milliseconds:[0,999,3],seconds:[0,59,2],minutes:[0,59,2],hours:[0,23,2]},Ro=/d{1,2}|W{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|Z{1,4}|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,Uw=/\[([^]*?)\]/gm,bs={D(e){return e.day},DD(e){return q(e.day,2)},d(e){return e.weekday-1},dd(e){return q(e.weekday-1,2)},W(e,t){return t.dayNamesNarrow[e.weekday-1]},WW(e,t){return t.dayNamesShorter[e.weekday-1]},WWW(e,t){return t.dayNamesShort[e.weekday-1]},WWWW(e,t){return t.dayNames[e.weekday-1]},M(e){return e.month},MM(e){return q(e.month,2)},MMM(e,t){return t.monthNamesShort[e.month-1]},MMMM(e,t){return t.monthNames[e.month-1]},YY(e){return String(e.year).substr(2)},YYYY(e){return q(e.year,4)},h(e){return e.hours%12||12},hh(e){return q(e.hours%12||12,2)},H(e){return e.hours},HH(e){return q(e.hours,2)},m(e){return e.minutes},mm(e){return q(e.minutes,2)},s(e){return e.seconds},ss(e){return q(e.seconds,2)},S(e){return Math.round(e.milliseconds/100)},SS(e){return q(Math.round(e.milliseconds/10),2)},SSS(e){return q(e.milliseconds,3)},a(e,t){return e.hours<12?t.amPm[0]:t.amPm[1]},A(e,t){return e.hours<12?t.amPm[0].toUpperCase():t.amPm[1].toUpperCase()},Z(){return"Z"},ZZ(e){const t=e.timezoneOffset;return`${t>0?"-":"+"}${q(Math.floor(Math.abs(t)/60),2)}`},ZZZ(e){const t=e.timezoneOffset;return`${t>0?"-":"+"}${q(Math.floor(Math.abs(t)/60)*100+Math.abs(t)%60,4)}`},ZZZZ(e){const t=e.timezoneOffset;return`${t>0?"-":"+"}${q(Math.floor(Math.abs(t)/60),2)}:${q(Math.abs(t)%60,2)}`}},lt=/\d\d?/,Kw=/\d{3}/,Gw=/\d{4}/,tr=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF/]+(\s*?[\u0600-\u06FF]+){1,2}/i,ws=()=>{},Ds=e=>(t,r,n)=>{const a=n[e].indexOf(r.charAt(0).toUpperCase()+r.substr(1).toLowerCase());~a&&(t.month=a)},X={D:[lt,(e,t)=>{e.day=t}],Do:[new RegExp(lt.source+tr.source),(e,t)=>{e.day=parseInt(t,10)}],d:[lt,ws],W:[tr,ws],M:[lt,(e,t)=>{e.month=t-1}],MMM:[tr,Ds("monthNamesShort")],MMMM:[tr,Ds("monthNames")],YY:[lt,(e,t)=>{const n=+new Date().getFullYear().toString().substr(0,2);e.year=+`${t>68?n-1:n}${t}`}],YYYY:[Gw,(e,t)=>{e.year=t}],S:[/\d/,(e,t)=>{e.milliseconds=t*100}],SS:[/\d{2}/,(e,t)=>{e.milliseconds=t*10}],SSS:[Kw,(e,t)=>{e.milliseconds=t}],h:[lt,(e,t)=>{e.hours=t}],m:[lt,(e,t)=>{e.minutes=t}],s:[lt,(e,t)=>{e.seconds=t}],a:[tr,(e,t,r)=>{const n=t.toLowerCase();n===r.amPm[0]?e.isPm=!1:n===r.amPm[1]&&(e.isPm=!0)}],Z:[/[^\s]*?[+-]\d\d:?\d\d|[^\s]*?Z?/,(e,t)=>{t==="Z"&&(t="+00:00");const r=`${t}`.match(/([+-]|\d\d)/gi);if(r){const n=+r[1]*60+parseInt(r[2],10);e.timezoneOffset=r[0]==="+"?n:-n}}]};X.DD=X.D;X.dd=X.d;X.WWWW=X.WWW=X.WW=X.W;X.MM=X.M;X.mm=X.m;X.hh=X.H=X.HH=X.h;X.ss=X.s;X.A=X.a;X.ZZZZ=X.ZZZ=X.ZZ=X.Z;function Fo(e,t){return(qe(e)&&e||[We(e)&&e||"YYYY-MM-DD"]).map(r=>Vw.reduce((n,a)=>n.replace(a,t.masks[a]||""),r))}function ra(e){return Xe(e)&&"year"in e&&"month"in e&&"day"in e}function $s(e,t=1){const r=e.getDay()+1,n=r>=t?t-r:-(7-(t-r));return ye(e,n)}function Ho(e,t,r){const n=Date.UTC(e,t-1,r);return na(new Date(0),new Date(n))}function na(e,t){return Math.round((t.getTime()-e.getTime())/xr)}function qw(e,t){return Math.ceil(na($s(e),$s(t))/7)}function Wo(e,t){return t.getUTCFullYear()-e.getUTCFullYear()}function Zw(e,t){return Wo(e,t)*12+(t.getMonth()-e.getMonth())}function jo(e,t=""){const r=new Date,{year:n=r.getFullYear(),month:a=r.getMonth()+1,day:s=r.getDate(),hours:o=0,minutes:i=0,seconds:l=0,milliseconds:u=0}=e;if(t){const c=`${q(n,4)}-${q(a,2)}-${q(s,2)}T${q(o,2)}:${q(i,2)}:${q(l,2)}.${q(u,3)}`;return T1(c,{timeZone:t})}return new Date(n,a-1,s,o,i,l,u)}function Xw(e,t){let r=new Date(e.getTime());t.timezone&&(r=new Date(e.toLocaleString("en-US",{timeZone:t.timezone})),r.setMilliseconds(e.getMilliseconds()));const n=r.getMilliseconds(),a=r.getSeconds(),s=r.getMinutes(),o=r.getHours(),i=n+a*Eo+s*No+o*Lo,l=r.getMonth()+1,u=r.getFullYear(),c=t.getMonthParts(l,u),d=r.getDate(),h=c.numDays-d+1,v=r.getDay()+1,m=Math.floor((d-1)/7+1),_=Math.floor((c.numDays-d)/7+1),D=Math.ceil((d+Math.abs(c.firstWeekday-c.firstDayOfWeek))/7),y=c.numWeeks-D+1,C=c.weeknumbers[D],L=Ho(u,l,d);return{milliseconds:n,seconds:a,minutes:s,hours:o,time:i,day:d,dayFromEnd:h,weekday:v,weekdayOrdinal:m,weekdayOrdinalFromEnd:_,week:D,weekFromEnd:y,weeknumber:C,month:l,year:u,date:r,dateTime:r.getTime(),dayIndex:L,timezoneOffset:0,isValid:!0}}function Jw(e,t,r){return`${t}-${e}-${r}`}function Qw(e,t,r){const n=t%4===0&&t%100!==0||t%400===0,a=new Date(t,e-1,1),s=a.getDay()+1,o=e===2&&n?29:zw[e-1],i=r-1,l=W1(a,{weekStartsOn:i}),u=[],c=[];for(let d=0;d<l;d++){const h=ye(a,d*7);u.push(V1(h,{weekStartsOn:i})),c.push(q1(h))}return{firstDayOfWeek:r,firstDayOfMonth:a,inLeapYear:n,firstWeekday:s,numDays:o,numWeeks:l,month:e,year:t,weeknumbers:u,isoWeeknumbers:c}}function eD(){const e=[];for(let a=0;a<ve;a++)e.push(jo({year:2020,month:1,day:5+a,hours:12}));return e}function pn(e,t=void 0){const r=new Intl.DateTimeFormat(t,{weekday:e});return eD().map(n=>r.format(n))}function tD(){const e=[];for(let t=0;t<=24;t++)e.push(new Date(2e3,0,1,t));return e}function rD(e=void 0){const t=["second","minute","hour","day","week","month","quarter","year"],r=new Intl.RelativeTimeFormat(e);return t.reduce((n,a)=>{const s=r.formatToParts(100,a);return n[a]=s[1].unit,n},{})}function Bo(){const e=[];for(let t=0;t<12;t++)e.push(new Date(2e3,t,15));return e}function _s(e,t=void 0){const r=new Intl.DateTimeFormat(t,{month:e,timeZone:"UTC"});return Bo().map(n=>r.format(n))}function nD(e,t,r){return Le(t)?t===e:Ae(t)?t.includes(e):_t(t)?t(e,r):!(t.min!=null&&t.min>e||t.max!=null&&t.max<e||t.interval!=null&&e%t.interval!==0)}function sr(e,t,r){const n=[],[a,s,o]=t;for(let i=a;i<=s;i++)(r==null||nD(i,r,e))&&n.push({value:i,label:q(i,o)});return n}function aD(e,t){return{milliseconds:sr(e,ar.milliseconds,t.milliseconds),seconds:sr(e,ar.seconds,t.seconds),minutes:sr(e,ar.minutes,t.minutes),hours:sr(e,ar.hours,t.hours)}}function sD(e,t,r,n){const s=sr(e,t,n).reduce((o,i)=>{if(i.disabled)return o;if(isNaN(o))return i.value;const l=Math.abs(o-r);return Math.abs(i.value-r)<l?i.value:o},NaN);return isNaN(s)?r:s}function oD(e,t){const r={...e};return Object.entries(t).forEach(([n,a])=>{const s=ar[n],o=e[n];r[n]=sD(e,s,o,a)}),r}function Ms(e,t,r){return Fo(t,r).map(a=>{if(typeof a!="string")throw new Error("Invalid mask");let s=e;if(s.length>1e3)return!1;let o=!0;const i={};if(a.replace(Ro,c=>{if(X[c]){const d=X[c],h=s.search(d[0]);~h?s.replace(d[0],v=>(d[1](i,v,r),s=s.substr(h+v.length),v)):o=!1}return X[c]?"":c.slice(1,c.length-1)}),!o)return!1;const l=new Date;i.hours!=null&&(i.isPm===!0&&+i.hours!=12?i.hours=+i.hours+12:i.isPm===!1&&+i.hours==12&&(i.hours=0));let u;return i.timezoneOffset!=null?(i.minutes=+(i.minutes||0)-+i.timezoneOffset,u=new Date(Date.UTC(i.year||l.getFullYear(),i.month||0,i.day||1,i.hours||0,i.minutes||0,i.seconds||0,i.milliseconds||0))):u=r.getDateFromParts({year:i.year||l.getFullYear(),month:(i.month||0)+1,day:i.day||1,hours:i.hours||0,minutes:i.minutes||0,seconds:i.seconds||0,milliseconds:i.milliseconds||0}),u}).find(a=>a)||new Date(e)}function iD(e,t,r){if(e==null)return"";let n=Fo(t,r)[0];/Z$/.test(n)&&(r.timezone="utc");const a=[];n=n.replace(Uw,(o,i)=>(a.push(i),"??"));const s=r.getDateParts(e);return n=n.replace(Ro,o=>o in bs?bs[o](s,r):o.slice(1,o.length-1)),n.replace(/\?\?/g,()=>a.shift())}let lD=0;class zo{constructor(t,r,n){S(this,"key",""),S(this,"hashcode",""),S(this,"highlight",null),S(this,"content",null),S(this,"dot",null),S(this,"bar",null),S(this,"event",null),S(this,"popover",null),S(this,"customData",null),S(this,"ranges"),S(this,"hasRanges",!1),S(this,"order",0),S(this,"pinPage",!1),S(this,"maxRepeatSpan",0),S(this,"locale");const{dates:a}=Object.assign(this,{hashcode:"",order:0,pinPage:!1},t);this.key||(this.key=++lD),this.locale=n,r.normalizeGlyphs(this),this.ranges=n.ranges(a??[]),this.hasRanges=!!qe(this.ranges),this.maxRepeatSpan=this.ranges.filter(s=>s.hasRepeat).map(s=>s.daySpan).reduce((s,o)=>Math.max(s,o),0)}intersectsRange({start:t,end:r}){if(t==null||r==null)return!1;const n=this.ranges.filter(o=>!o.hasRepeat);for(const o of n)if(o.intersectsDayRange(t.dayIndex,r.dayIndex))return!0;const a=this.ranges.filter(o=>o.hasRepeat);if(!a.length)return!1;let s=t;for(this.maxRepeatSpan>1&&(s=this.locale.getDateParts(ye(s.date,-this.maxRepeatSpan)));s.dayIndex<=r.dayIndex;){for(const o of a)if(o.startsOnDay(s))return!0;s=this.locale.getDateParts(ye(s.date,1))}return!1}}function Tn(e){document&&document.dispatchEvent(new CustomEvent("show-popover",{detail:e}))}function hr(e){document&&document.dispatchEvent(new CustomEvent("hide-popover",{detail:e}))}function Vo(e){document&&document.dispatchEvent(new CustomEvent("toggle-popover",{detail:e}))}function Uo(e){const{visibility:t}=e,r=t==="click",n=t==="hover",a=t==="hover-focus",s=t==="focus";e.autoHide=!r;let o=!1,i=!1;const l=m=>{r&&(Vo({...e,target:e.target||m.currentTarget}),m.stopPropagation())},u=m=>{o||(o=!0,(n||a)&&Tn({...e,target:e.target||m.currentTarget}))},c=()=>{o&&(o=!1,(n||a&&!i)&&hr(e))},d=m=>{i||(i=!0,(s||a)&&Tn({...e,target:e.target||m.currentTarget}))},h=m=>{i&&!Ar(m.currentTarget,m.relatedTarget)&&(i=!1,(s||a&&!o)&&hr(e))},v={};switch(e.visibility){case"click":v.click=l;break;case"hover":v.mousemove=u,v.mouseleave=c;break;case"focus":v.focusin=d,v.focusout=h;break;case"hover-focus":v.mousemove=u,v.mouseleave=c,v.focusin=d,v.focusout=h;break}return v}const ks=e=>{const t=Fr(e);if(t==null)return;const r=t.popoverHandlers;!r||!r.length||(r.forEach(n=>n()),delete t.popoverHandlers)},Os=(e,t)=>{const r=Fr(e);if(r==null)return;const n=[],a=Uo(t);Object.entries(a).forEach(([s,o])=>{n.push(ct(r,s,o))}),r.popoverHandlers=n},Ko={mounted(e,t){const{value:r}=t;r&&Os(e,r)},updated(e,t){const{oldValue:r,value:n}=t,a=r==null?void 0:r.visibility,s=n==null?void 0:n.visibility;a!==s&&(a&&(ks(e),s||hr(r)),s&&Os(e,n))},unmounted(e){ks(e)}},uD=(e,t,{maxSwipeTime:r,minHorizontalSwipeDistance:n,maxVerticalSwipeDistance:a})=>{if(!e||!e.addEventListener||!_t(t))return null;let s=0,o=0,i=null,l=!1;function u(d){const h=d.changedTouches[0];s=h.screenX,o=h.screenY,i=new Date().getTime(),l=!0}function c(d){if(!l||!i)return;l=!1;const h=d.changedTouches[0],v=h.screenX-s,m=h.screenY-o;if(new Date().getTime()-i<r&&Math.abs(v)>=n&&Math.abs(m)<=a){const D={toLeft:!1,toRight:!1};v<0?D.toLeft=!0:D.toRight=!0,t(D)}}return ct(e,"touchstart",u,{passive:!0}),ct(e,"touchend",c,{passive:!0}),()=>{ut(e,"touchstart",u),ut(e,"touchend",c)}},Er={},cD=(e,t=10)=>{Er[e]=Date.now()+t},fD=(e,t)=>{if(e in Er){const r=Er[e];if(Date.now()<r)return;delete Er[e]}t()};function Go(){return typeof window<"u"}function dD(e){return Go()&&e in window}function vD(e){const t=te(!1),r=p(()=>t.value?"dark":"light");let n,a;function s(v){t.value=v.matches}function o(){dD("matchMedia")&&(n=window.matchMedia("(prefers-color-scheme: dark)"),n.addEventListener("change",s),t.value=n.matches)}function i(){const{selector:v=":root",darkClass:m="dark"}=e.value,_=document.querySelector(v);t.value=_.classList.contains(m)}function l(v){const{selector:m=":root",darkClass:_="dark"}=v;if(Go()&&m&&_){const D=document.querySelector(m);D&&(a=new MutationObserver(i),a.observe(D,{attributes:!0,attributeFilter:["class"]}),t.value=D.classList.contains(_))}}function u(){d();const v=typeof e.value;v==="string"&&e.value.toLowerCase()==="system"?o():v==="object"?l(e.value):t.value=!!e.value}const c=me(()=>e.value,()=>u(),{immediate:!0});function d(){n&&(n.removeEventListener("change",s),n=void 0),a&&(a.disconnect(),a=void 0)}function h(){d(),c()}return Cn(()=>h()),{isDark:t,displayMode:r,cleanup:h}}const hD=["base","start","end","startEnd"],pD=["class","wrapperClass","contentClass","style","contentStyle","color","fillMode"],mD={base:{},start:{},end:{}};function aa(e,t,r=mD){let n=e,a={};t===!0||We(t)?(n=We(t)?t:n,a={...r}):Xe(t)&&(os(t,hD)?a={...t}:a={base:{...t},start:{...t},end:{...t}});const s=vr(a,{start:a.startEnd,end:a.startEnd},r);return Object.entries(s).forEach(([o,i])=>{let l=n;i===!0||We(i)?(l=We(i)?i:l,s[o]={color:l}):Xe(i)&&(os(i,pD)?s[o]={...i}:s[o]={}),vr(s[o],{color:l})}),s}class yD{constructor(){S(this,"type","highlight")}normalizeConfig(t,r){return aa(t,r,{base:{fillMode:"light"},start:{fillMode:"solid"},end:{fillMode:"solid"}})}prepareRender(t){t.highlights=[],t.content||(t.content=[])}render({data:t,onStart:r,onEnd:n},a){const{key:s,highlight:o}=t;if(!o)return;const{highlights:i}=a,{base:l,start:u,end:c}=o;r&&n?i.push({...u,key:s,wrapperClass:`vc-day-layer vc-day-box-center-center vc-attr vc-${u.color}`,class:[`vc-highlight vc-highlight-bg-${u.fillMode}`,u.class],contentClass:[`vc-attr vc-highlight-content-${u.fillMode} vc-${u.color}`,u.contentClass]}):r?(i.push({...l,key:`${s}-base`,wrapperClass:`vc-day-layer vc-day-box-right-center vc-attr vc-${l.color}`,class:[`vc-highlight vc-highlight-base-start vc-highlight-bg-${l.fillMode}`,l.class]}),i.push({...u,key:s,wrapperClass:`vc-day-layer vc-day-box-center-center vc-attr vc-${u.color}`,class:[`vc-highlight vc-highlight-bg-${u.fillMode}`,u.class],contentClass:[`vc-attr vc-highlight-content-${u.fillMode} vc-${u.color}`,u.contentClass]})):n?(i.push({...l,key:`${s}-base`,wrapperClass:`vc-day-layer vc-day-box-left-center vc-attr vc-${l.color}`,class:[`vc-highlight vc-highlight-base-end vc-highlight-bg-${l.fillMode}`,l.class]}),i.push({...c,key:s,wrapperClass:`vc-day-layer vc-day-box-center-center vc-attr vc-${c.color}`,class:[`vc-highlight vc-highlight-bg-${c.fillMode}`,c.class],contentClass:[`vc-attr vc-highlight-content-${c.fillMode} vc-${c.color}`,c.contentClass]})):i.push({...l,key:`${s}-middle`,wrapperClass:`vc-day-layer vc-day-box-center-center vc-attr vc-${l.color}`,class:[`vc-highlight vc-highlight-base-middle vc-highlight-bg-${l.fillMode}`,l.class],contentClass:[`vc-attr vc-highlight-content-${l.fillMode} vc-${l.color}`,l.contentClass]})}}class sa{constructor(t,r){S(this,"type",""),S(this,"collectionType",""),this.type=t,this.collectionType=r}normalizeConfig(t,r){return aa(t,r)}prepareRender(t){t[this.collectionType]=[]}render({data:t,onStart:r,onEnd:n},a){const{key:s}=t,o=t[this.type];if(!s||!o)return;const i=a[this.collectionType],{base:l,start:u,end:c}=o;r?i.push({...u,key:s,class:[`vc-${this.type} vc-${this.type}-start vc-${u.color} vc-attr`,u.class]}):n?i.push({...c,key:s,class:[`vc-${this.type} vc-${this.type}-end vc-${c.color} vc-attr`,c.class]}):i.push({...l,key:s,class:[`vc-${this.type} vc-${this.type}-base vc-${l.color} vc-attr`,l.class]})}}class gD extends sa{constructor(){super("content","content")}normalizeConfig(t,r){return aa("base",r)}}class bD extends sa{constructor(){super("dot","dots")}}class wD extends sa{constructor(){super("bar","bars")}}class DD{constructor(t){S(this,"color"),S(this,"renderers",[new gD,new yD,new bD,new wD]),this.color=t}normalizeGlyphs(t){this.renderers.forEach(r=>{const n=r.type;t[n]!=null&&(t[n]=r.normalizeConfig(this.color,t[n]))})}prepareRender(t={}){return this.renderers.forEach(r=>{r.prepareRender(t)}),t}render(t,r){this.renderers.forEach(n=>{n.render(t,r)})}}const qo=Symbol("__vc_base_context__"),Zo={color:{type:String,default:()=>ft("color")},isDark:{type:[Boolean,String,Object],default:()=>ft("isDark")},firstDayOfWeek:Number,masks:Object,locale:[String,Object],timezone:String,minDate:null,maxDate:null,disabledDates:null};function Xo(e){const t=p(()=>e.color??""),r=p(()=>e.isDark??!1),{displayMode:n}=vD(r),a=p(()=>new DD(t.value)),s=p(()=>{if(e.locale instanceof Vr)return e.locale;const h=Xe(e.locale)?e.locale:{id:e.locale,firstDayOfWeek:e.firstDayOfWeek,masks:e.masks};return new Vr(h,e.timezone)}),o=p(()=>s.value.masks),i=p(()=>e.minDate),l=p(()=>e.maxDate),u=p(()=>{const h=e.disabledDates?[...e.disabledDates]:[];return i.value!=null&&h.push({start:null,end:ye(s.value.toDate(i.value),-1)}),l.value!=null&&h.push({start:ye(s.value.toDate(l.value),1),end:null}),s.value.ranges(h)}),c=p(()=>new zo({key:"disabled",dates:u.value,order:100},a.value,s.value)),d={color:t,isDark:r,displayMode:n,theme:a,locale:s,masks:o,minDate:i,maxDate:l,disabledDates:u,disabledAttribute:c};return mr(qo,d),d}function $D(e){return yr(qo,()=>Xo(e),!0)}function Jo(e){return`__vc_slot_${e}__`}function Qo(e,t={}){Object.keys(e).forEach(r=>{mr(Jo(t[r]??r),e[r])})}function ei(e){return yr(Jo(e),null)}const _D={...Zo,view:{type:String,default:"monthly",validator(e){return["daily","weekly","monthly"].includes(e)}},rows:{type:Number,default:1},columns:{type:Number,default:1},step:Number,titlePosition:{type:String,default:()=>ft("titlePosition")},navVisibility:{type:String,default:()=>ft("navVisibility")},showWeeknumbers:[Boolean,String],showIsoWeeknumbers:[Boolean,String],expanded:Boolean,borderless:Boolean,transparent:Boolean,initialPage:Object,initialPagePosition:{type:Number,default:1},minPage:Object,maxPage:Object,transition:String,attributes:Array,trimWeeks:Boolean,disablePageSwipe:Boolean},MD=["dayclick","daymouseenter","daymouseleave","dayfocusin","dayfocusout","daykeydown","weeknumberclick","transition-start","transition-end","did-move","update:view","update:pages"],ti=Symbol("__vc_calendar_context__");function kD(e,{slots:t,emit:r}){const n=te(null),a=te(null),s=te(new Date().getDate()),o=te(!1),i=te(Symbol()),l=te(Symbol()),u=te(e.view),c=te([]),d=te("");let h=null,v=null;Qo(t);const{theme:m,color:_,displayMode:D,locale:y,masks:C,minDate:L,maxDate:M,disabledAttribute:O,disabledDates:E}=$D(e),T=p(()=>e.rows*e.columns),N=p(()=>e.step||T.value),H=p(()=>_o(c.value)??null),W=p(()=>St(c.value)??null),j=p(()=>e.minPage||(L.value?A(L.value):null)),Y=p(()=>e.maxPage||(M.value?A(M.value):null)),R=p(()=>e.navVisibility),K=p(()=>!!e.showWeeknumbers),I=p(()=>!!e.showIsoWeeknumbers),z=p(()=>u.value==="monthly"),Z=p(()=>u.value==="weekly"),oe=p(()=>u.value==="daily"),Q=()=>{o.value=!0,r("transition-start")},$=()=>{o.value=!1,r("transition-end"),h&&(h.resolve(!0),h=null)},B=(w,f,g=u.value)=>Co(w,f,g,y.value),A=w=>Yo(w,u.value,y.value),ne=w=>{!O.value||!Me.value||(w.isDisabled=Me.value.cellExists(O.value.key,w.dayIndex))},G=w=>{w.isFocusable=w.inMonth&&w.day===s.value},ae=(w,f)=>{for(const g of w)for(const k of g.days)if(f(k)===!1)return},ue=p(()=>c.value.reduce((w,f)=>(w.push(...f.viewDays),w),[])),se=p(()=>{const w=[];return(e.attributes||[]).forEach((f,g)=>{!f||!f.dates||w.push(new zo({...f,order:f.order||0},m.value,y.value))}),O.value&&w.push(O.value),w}),ce=p(()=>qe(se.value)),Me=p(()=>{const w=new ow;return se.value.forEach(f=>{f.ranges.forEach(g=>{w.render(f,g,ue.value)})}),w}),Ke=p(()=>ue.value.reduce((w,f)=>(w[f.dayIndex]={day:f,cells:[]},w[f.dayIndex].cells.push(...Me.value.getCells(f)),w),{})),Ee=(w,f)=>{const g=e.showWeeknumbers||e.showIsoWeeknumbers;return g==null?"":Cy(g)?g?"left":"":g.startsWith("right")?f>1?"right":g:w>1?"left":g},Pe=()=>{var w,f;if(!ce.value)return null;const g=se.value.find(ee=>ee.pinPage)||se.value[0];if(!g||!g.hasRanges)return null;const[k]=g.ranges,F=((w=k.start)==null?void 0:w.date)||((f=k.end)==null?void 0:f.date);return F?A(F):null},Te=()=>{if(je(H.value))return H.value;const w=Pe();return je(w)?w:A(new Date)},tt=(w,f={})=>{const{view:g=u.value,position:k=1,force:F}=f,ee=k>0?1-k:-(T.value+k);let pe=B(w,ee,g),ke=B(pe,T.value-1,g);return F||(kn(pe,j.value)?pe=j.value:zr(ke,Y.value)&&(pe=B(Y.value,1-T.value)),ke=B(pe,T.value-1)),{fromPage:pe,toPage:ke}},Ot=(w,f,g="")=>{if(g==="none"||g==="fade")return g;if((w==null?void 0:w.view)!==(f==null?void 0:f.view))return"fade";const k=zr(f,w),F=kn(f,w);return!k&&!F?"fade":g==="slide-v"?F?"slide-down":"slide-up":F?"slide-right":"slide-left"},He=(w={})=>new Promise((f,g)=>{const{position:k=1,force:F=!1,transition:ee}=w,pe=je(w.page)?w.page:Te(),{fromPage:ke}=tt(pe,{position:k,force:F}),Tt=[];for(let Yt=0;Yt<T.value;Yt++){const sn=B(ke,Yt),De=Yt+1,on=Math.ceil(De/e.columns),kr=e.rows-on+1,Zt=De%e.columns||e.columns,Xt=e.columns-Zt+1,li=Ee(Zt,Xt);Tt.push(y.value.getPage({...sn,view:u.value,titlePosition:e.titlePosition,trimWeeks:e.trimWeeks,position:De,row:on,rowFromEnd:kr,column:Zt,columnFromEnd:Xt,showWeeknumbers:K.value,showIsoWeeknumbers:I.value,weeknumberPosition:li}))}d.value=Ot(c.value[0],Tt[0],ee),c.value=Tt,d.value&&d.value!=="none"?h={resolve:f,reject:g}:f(!0)}),ht=w=>{const f=H.value??A(new Date);return B(f,w)},pt=(w,f={})=>{const g=je(w)?w:A(w);return Object.assign(f,tt(g,{...f,force:!0})),nw(f.fromPage,f.toPage,u.value,y.value).map(F=>tw(F,j.value,Y.value)).some(F=>F)},mt=(w,f={})=>pt(ht(w),f),Ne=p(()=>mt(-N.value)),rt=p(()=>mt(N.value)),nt=async(w,f={})=>!f.force&&!pt(w,f)?!1:(f.fromPage&&!rw(f.fromPage,H.value)&&(hr({id:i.value,hideDelay:0}),f.view&&(cD("view",10),u.value=f.view),await He({...f,page:f.fromPage,position:1,force:!0}),r("did-move",c.value)),!0),at=(w,f={})=>nt(ht(w),f),st=()=>at(-N.value),ot=()=>at(N.value),it=w=>{const f=z.value?".in-month":"",g=`.id-${y.value.getDayId(w)}${f}`,k=`${g}.vc-focusable, ${g} .vc-focusable`,F=n.value;if(F){const ee=F.querySelector(k);if(ee)return ee.focus(),!0}return!1},yt=async(w,f={})=>it(w)?!0:(await nt(w,f),it(w)),Pt=(w,f)=>{s.value=w.day,r("dayclick",w,f)},rn=(w,f)=>{r("daymouseenter",w,f)},qt=(w,f)=>{r("daymouseleave",w,f)},nn=(w,f)=>{s.value=w.day,a.value=w,w.isFocused=!0,r("dayfocusin",w,f)},Dr=(w,f)=>{a.value=null,w.isFocused=!1,r("dayfocusout",w,f)},$r=(w,f)=>{r("daykeydown",w,f);const g=w.noonDate;let k=null;switch(f.key){case"ArrowLeft":{k=ye(g,-1);break}case"ArrowRight":{k=ye(g,1);break}case"ArrowUp":{k=ye(g,-7);break}case"ArrowDown":{k=ye(g,7);break}case"Home":{k=ye(g,-w.weekdayPosition+1);break}case"End":{k=ye(g,w.weekdayPositionFromEnd);break}case"PageUp":{f.altKey?k=ms(g,-1):k=Br(g,-1);break}case"PageDown":{f.altKey?k=ms(g,1):k=Br(g,1);break}}k&&(f.preventDefault(),yt(k).catch())},an=w=>{const f=a.value;f!=null&&$r(f,w)},_r=(w,f)=>{r("weeknumberclick",w,f)};He({page:e.initialPage,position:e.initialPagePosition}),Kr(()=>{!e.disablePageSwipe&&n.value&&(v=uD(n.value,({toLeft:w=!1,toRight:f=!1})=>{w?ot():f&&st()},ft("touch")))}),Cn(()=>{c.value=[],v&&v()}),me(()=>y.value,()=>{He()}),me(()=>T.value,()=>He()),me(()=>e.view,()=>u.value=e.view),me(()=>u.value,()=>{fD("view",()=>{He()}),r("update:view",u.value)}),me(()=>s.value,()=>{ae(c.value,w=>G(w))}),Cs(()=>{r("update:pages",c.value),ae(c.value,w=>{ne(w),G(w)})});const Mr={emit:r,containerRef:n,focusedDay:a,inTransition:o,navPopoverId:i,dayPopoverId:l,view:u,pages:c,transitionName:d,theme:m,color:_,displayMode:D,locale:y,masks:C,attributes:se,disabledAttribute:O,disabledDates:E,attributeContext:Me,days:ue,dayCells:Ke,count:T,step:N,firstPage:H,lastPage:W,canMovePrev:Ne,canMoveNext:rt,minPage:j,maxPage:Y,isMonthly:z,isWeekly:Z,isDaily:oe,navVisibility:R,showWeeknumbers:K,showIsoWeeknumbers:I,getDateAddress:A,canMove:pt,canMoveBy:mt,move:nt,moveBy:at,movePrev:st,moveNext:ot,onTransitionBeforeEnter:Q,onTransitionAfterEnter:$,tryFocusDate:it,focusDate:yt,onKeydown:an,onDayKeydown:$r,onDayClick:Pt,onDayMouseenter:rn,onDayMouseleave:qt,onDayFocusin:nn,onDayFocusout:Dr,onWeeknumberClick:_r};return mr(ti,Mr),Mr}function kt(){const e=yr(ti);if(e)return e;throw new Error("Calendar context missing. Please verify this component is nested within a valid context provider.")}const OD=de({inheritAttrs:!1,emits:["before-show","after-show","before-hide","after-hide"],props:{id:{type:[Number,String,Symbol],required:!0},showDelay:{type:Number,default:0},hideDelay:{type:Number,default:110},boundarySelector:{type:String}},setup(e,{emit:t}){let r;const n=te();let a=null,s=null;const o=Yn({isVisible:!1,target:null,data:null,transition:"slide-fade",placement:"bottom",direction:"",positionFixed:!1,modifiers:[],isInteractive:!0,visibility:"click",isHovered:!1,isFocused:!1,autoHide:!1,force:!1});function i($){$&&(o.direction=$.split("-")[0])}function l({placement:$,options:B}){i($||(B==null?void 0:B.placement))}const u=p(()=>({placement:o.placement,strategy:o.positionFixed?"fixed":"absolute",boundary:"",modifiers:[{name:"onUpdate",enabled:!0,phase:"afterWrite",fn:l},...o.modifiers||[]],onFirstUpdate:l})),c=p(()=>{const $=o.direction==="left"||o.direction==="right";let B="";if(o.placement){const A=o.placement.split("-");A.length>1&&(B=A[1])}return["start","top","left"].includes(B)?$?"top":"left":["end","bottom","right"].includes(B)?$?"bottom":"right":$?"middle":"center"});function d(){s&&(s.destroy(),s=null)}function h(){nr(()=>{const $=Fr(o.target);!$||!n.value||(s&&s.state.elements.reference!==$&&d(),s?s.update():s=gl($,n.value,u.value))})}function v($){Object.assign(o,ko($,"force"))}function m($,B){clearTimeout(r),$>0?r=setTimeout(B,$):B()}function _($){return!$||!s?!1:Fr($)===s.state.elements.reference}async function D($={}){o.force||($.force&&(o.force=!0),m($.showDelay??e.showDelay,()=>{o.isVisible&&(o.force=!1),v({...$,isVisible:!0}),h()}))}function y($={}){s&&($.target&&!_($.target)||o.force||($.force&&(o.force=!0),m($.hideDelay??e.hideDelay,()=>{o.isVisible||(o.force=!1),o.isVisible=!1})))}function C($={}){$.target!=null&&(o.isVisible&&_($.target)?y($):D($))}function L($){if(!s)return;const B=s.state.elements.reference;if(!n.value||!B)return;const A=$.target;Ar(n.value,A)||Ar(B,A)||y({force:!0})}function M($){($.key==="Esc"||$.key==="Escape")&&y()}function O({detail:$}){!$.id||$.id!==e.id||D($)}function E({detail:$}){!$.id||$.id!==e.id||y($)}function T({detail:$}){!$.id||$.id!==e.id||C($)}function N(){ct(document,"keydown",M),ct(document,"click",L),ct(document,"show-popover",O),ct(document,"hide-popover",E),ct(document,"toggle-popover",T)}function H(){ut(document,"keydown",M),ut(document,"click",L),ut(document,"show-popover",O),ut(document,"hide-popover",E),ut(document,"toggle-popover",T)}function W($){t("before-show",$)}function j($){o.force=!1,t("after-show",$)}function Y($){t("before-hide",$)}function R($){o.force=!1,d(),t("after-hide",$)}function K($){$.stopPropagation()}function I(){o.isHovered=!0,o.isInteractive&&["hover","hover-focus"].includes(o.visibility)&&D()}function z(){if(o.isHovered=!1,!s)return;const $=s.state.elements.reference;o.autoHide&&!o.isFocused&&(!$||$!==document.activeElement)&&["hover","hover-focus"].includes(o.visibility)&&y()}function Z(){o.isFocused=!0,o.isInteractive&&["focus","hover-focus"].includes(o.visibility)&&D()}function oe($){["focus","hover-focus"].includes(o.visibility)&&(!$.relatedTarget||!Ar(n.value,$.relatedTarget))&&(o.isFocused=!1,!o.isHovered&&o.autoHide&&y())}function Q(){a!=null&&(a.disconnect(),a=null)}return me(()=>n.value,$=>{Q(),$&&(a=new ResizeObserver(()=>{s&&s.update()}),a.observe($))}),me(()=>o.placement,i,{immediate:!0}),Kr(()=>{N()}),Cn(()=>{d(),Q(),H()}),{...ui(o),popoverRef:n,alignment:c,hide:y,setupPopper:h,beforeEnter:W,afterEnter:j,beforeLeave:Y,afterLeave:R,onClick:K,onMouseOver:I,onMouseLeave:z,onFocusIn:Z,onFocusOut:oe}}}),vt=(e,t)=>{const r=e.__vccOpts||e;for(const[n,a]of t)r[n]=a;return r};function PD(e,t,r,n,a,s){return P(),x("div",{class:re(["vc-popover-content-wrapper",{"is-interactive":e.isInteractive}]),ref:"popoverRef",onClick:t[0]||(t[0]=(...o)=>e.onClick&&e.onClick(...o)),onMouseover:t[1]||(t[1]=(...o)=>e.onMouseOver&&e.onMouseOver(...o)),onMouseleave:t[2]||(t[2]=(...o)=>e.onMouseLeave&&e.onMouseLeave(...o)),onFocusin:t[3]||(t[3]=(...o)=>e.onFocusIn&&e.onFocusIn(...o)),onFocusout:t[4]||(t[4]=(...o)=>e.onFocusOut&&e.onFocusOut(...o))},[V(Ys,{name:`vc-${e.transition}`,appear:"",onBeforeEnter:e.beforeEnter,onAfterEnter:e.afterEnter,onBeforeLeave:e.beforeLeave,onAfterLeave:e.afterLeave},{default:le(()=>[e.isVisible?(P(),x("div",Ht({key:0,tabindex:"-1",class:`vc-popover-content direction-${e.direction}`},e.$attrs),[pr(e.$slots,"default",{direction:e.direction,alignment:e.alignment,data:e.data,hide:e.hide},()=>[Sn(be(e.data),1)]),U("span",{class:re(["vc-popover-caret",`direction-${e.direction}`,`align-${e.alignment}`])},null,2)],16)):ie("",!0)]),_:3},8,["name","onBeforeEnter","onAfterEnter","onBeforeLeave","onAfterLeave"])],34)}const oa=vt(OD,[["render",PD]]),TD={class:"vc-day-popover-row"},YD={key:0,class:"vc-day-popover-row-indicator"},CD={class:"vc-day-popover-row-label"},SD=de({__name:"PopoverRow",props:{attribute:null},setup(e){const t=e,r=p(()=>{const{content:n,highlight:a,dot:s,bar:o,popover:i}=t.attribute;return i&&i.hideIndicator?null:n?{class:`vc-bar vc-day-popover-row-bar vc-attr vc-${n.base.color}`}:a?{class:`vc-highlight-bg-solid vc-day-popover-row-highlight vc-attr vc-${a.base.color}`}:s?{class:`vc-dot vc-attr vc-${s.base.color}`}:o?{class:`vc-bar vc-day-popover-row-bar vc-attr vc-${o.base.color}`}:null});return(n,a)=>(P(),x("div",TD,[b(r)?(P(),x("div",YD,[U("span",{class:re(b(r).class)},null,2)])):ie("",!0),U("div",CD,[pr(n.$slots,"default",{},()=>[Sn(be(e.attribute.popover?e.attribute.popover.label:"No content provided"),1)])])]))}}),AD={inheritAttrs:!1},Ye=de({...AD,__name:"CalendarSlot",props:{name:null},setup(e){const r=ei(e.name);return(n,a)=>b(r)?(P(),we(Ts(b(r)),or(Ht({key:0},n.$attrs)),null,16)):pr(n.$slots,"default",{key:1})}}),ID={class:"vc-day-popover-container"},xD={key:0,class:"vc-day-popover-header"},ED=de({__name:"CalendarDayPopover",setup(e){const{dayPopoverId:t,displayMode:r,color:n,masks:a,locale:s}=kt();function o(l,u){return s.value.formatDate(l,u)}function i(l){return s.value.formatDate(l.date,a.value.dayPopover)}return(l,u)=>(P(),we(oa,{id:b(t),class:re([`vc-${b(n)}`,`vc-${b(r)}`])},{default:le(({data:{day:c,attributes:d},hide:h})=>[V(Ye,{name:"day-popover",day:c,"day-title":i(c),attributes:d,format:o,masks:b(a),hide:h},{default:le(()=>[U("div",ID,[b(a).dayPopover?(P(),x("div",xD,be(i(c)),1)):ie("",!0),(P(!0),x(fe,null,Ce(d,v=>(P(),we(SD,{key:v.key,attribute:v},null,8,["attribute"]))),128))])]),_:2},1032,["day","day-title","attributes","masks","hide"])]),_:1},8,["id","class"]))}}),ND={},LD={"stroke-linecap":"round","stroke-linejoin":"round",viewBox:"0 0 24 24"},RD=U("polyline",{points:"9 18 15 12 9 6"},null,-1),FD=[RD];function HD(e,t){return P(),x("svg",LD,FD)}const WD=vt(ND,[["render",HD]]),jD={},BD={"stroke-linecap":"round","stroke-linejoin":"round",viewBox:"0 0 24 24"},zD=U("polyline",{points:"15 18 9 12 15 6"},null,-1),VD=[zD];function UD(e,t){return P(),x("svg",BD,VD)}const KD=vt(jD,[["render",UD]]),GD={},qD={"stroke-linecap":"round","stroke-linejoin":"round",viewBox:"0 0 24 24"},ZD=U("polyline",{points:"6 9 12 15 18 9"},null,-1),XD=[ZD];function JD(e,t){return P(),x("svg",qD,XD)}const QD=vt(GD,[["render",JD]]),e0={},t0={fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",viewBox:"0 0 24 24"},r0=U("path",{d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1),n0=[r0];function a0(e,t){return P(),x("svg",t0,n0)}const s0=vt(e0,[["render",a0]]),o0=Object.freeze(Object.defineProperty({__proto__:null,IconChevronDown:QD,IconChevronLeft:KD,IconChevronRight:WD,IconClock:s0},Symbol.toStringTag,{value:"Module"})),Ft=de({__name:"BaseIcon",props:{name:{type:String,required:!0},width:{type:String},height:{type:String},size:{type:String,default:"26"},viewBox:{type:String}},setup(e){const t=e,r=p(()=>t.width||t.size),n=p(()=>t.height||t.size),a=p(()=>o0[`Icon${t.name}`]);return(s,o)=>(P(),we(Ts(b(a)),{width:b(r),height:b(n),class:"vc-base-icon"},null,8,["width","height"]))}}),i0=["disabled"],l0={key:1,class:"vc-title-wrapper"},u0={type:"button",class:"vc-title"},c0=["disabled"],ri=de({__name:"CalendarHeader",props:{page:null,layout:null,isLg:{type:Boolean},isXl:{type:Boolean},is2xl:{type:Boolean},hideTitle:{type:Boolean},hideArrows:{type:Boolean}},setup(e){const t=e,{navPopoverId:r,navVisibility:n,canMovePrev:a,movePrev:s,canMoveNext:o,moveNext:i}=kt(),l=p(()=>{switch(t.page.titlePosition){case"left":return"bottom-start";case"right":return"bottom-end";default:return"bottom"}}),u=p(()=>{const{page:_}=t;return{id:r.value,visibility:n.value,placement:l.value,modifiers:[{name:"flip",options:{fallbackPlacements:["bottom"]}}],data:{page:_},isInteractive:!0}}),c=p(()=>t.page.titlePosition.includes("left")),d=p(()=>t.page.titlePosition.includes("right")),h=p(()=>t.layout?t.layout:c.value?"tu-pn":d.value?"pn-tu":"p-tu-n;"),v=p(()=>({prev:h.value.includes("p")&&!t.hideArrows,title:h.value.includes("t")&&!t.hideTitle,next:h.value.includes("n")&&!t.hideArrows})),m=p(()=>({gridTemplateColumns:h.value.split("").map(D=>{switch(D){case"p":return"[prev] auto";case"n":return"[next] auto";case"t":return"[title] auto";case"-":return"1fr";default:return""}}).join(" ")}));return(_,D)=>(P(),x("div",{class:re(["vc-header",{"is-lg":e.isLg,"is-xl":e.isXl,"is-2xl":e.is2xl}]),style:ir(b(m))},[b(v).prev?(P(),x("button",{key:0,type:"button",class:"vc-arrow vc-prev vc-focus",disabled:!b(a),onClick:D[0]||(D[0]=(...y)=>b(s)&&b(s)(...y)),onKeydown:D[1]||(D[1]=la((...y)=>b(s)&&b(s)(...y),["space","enter"]))},[V(Ye,{name:"header-prev-button",disabled:!b(a)},{default:le(()=>[V(Ft,{name:"ChevronLeft",size:"24"})]),_:1},8,["disabled"])],40,i0)):ie("",!0),b(v).title?(P(),x("div",l0,[V(Ye,{name:"header-title-wrapper"},{default:le(()=>[Ss((P(),x("button",u0,[V(Ye,{name:"header-title",title:e.page.title},{default:le(()=>[U("span",null,be(e.page.title),1)]),_:1},8,["title"])])),[[b(Ko),b(u)]])]),_:1})])):ie("",!0),b(v).next?(P(),x("button",{key:2,type:"button",class:"vc-arrow vc-next vc-focus",disabled:!b(o),onClick:D[2]||(D[2]=(...y)=>b(i)&&b(i)(...y)),onKeydown:D[3]||(D[3]=la((...y)=>b(i)&&b(i)(...y),["space","enter"]))},[V(Ye,{name:"header-next-button",disabled:!b(o)},{default:le(()=>[V(Ft,{name:"ChevronRight",size:"24"})]),_:1},8,["disabled"])],40,c0)):ie("",!0)],6))}}),ni=Symbol("__vc_page_context__");function f0(e){const{locale:t,getDateAddress:r,canMove:n}=kt();function a(i,l){const{month:u,year:c}=r(new Date);return Bo().map((d,h)=>{const v=h+1;return{month:v,year:i,id:ew(v,i),label:t.value.formatDate(d,l),ariaLabel:t.value.formatDate(d,"MMMM"),isActive:v===e.value.month&&i===e.value.year,isCurrent:v===u&&i===c,isDisabled:!n({month:v,year:i},{position:e.value.position})}})}function s(i,l){const{year:u}=r(new Date),{position:c}=e.value,d=[];for(let h=i;h<=l;h+=1){const v=[...Array(12).keys()].some(m=>n({month:m+1,year:h},{position:c}));d.push({year:h,id:h.toString(),label:h.toString(),ariaLabel:h.toString(),isActive:h===e.value.year,isCurrent:h===u,isDisabled:!v})}return d}const o={page:e,getMonthItems:a,getYearItems:s};return mr(ni,o),o}function ai(){const e=yr(ni);if(e)return e;throw new Error("Page context missing. Please verify this component is nested within a valid context provider.")}const d0={class:"vc-nav-header"},v0=["disabled"],h0=["disabled"],p0={class:"vc-nav-items"},m0=["data-id","aria-label","disabled","onClick","onKeydown"],y0=de({__name:"CalendarNav",setup(e){const{masks:t,move:r}=kt(),{page:n,getMonthItems:a,getYearItems:s}=ai(),o=te(!0),i=12,l=te(n.value.year),u=te(h(n.value.year)),c=te(null);function d(){setTimeout(()=>{if(c.value==null)return;const A=c.value.querySelector(".vc-nav-item:not(:disabled)");A&&A.focus()},10)}function h(A){return Math.floor(A/i)}function v(){o.value=!o.value}function m(A){return A*i}function _(A){return i*(A+1)-1}function D(){Z.value&&(o.value&&C(),M())}function y(){oe.value&&(o.value&&L(),O())}function C(){l.value--}function L(){l.value++}function M(){u.value--}function O(){u.value++}const E=p(()=>a(l.value,t.value.navMonths).map(A=>({...A,click:()=>r({month:A.month,year:A.year},{position:n.value.position})}))),T=p(()=>a(l.value-1,t.value.navMonths)),N=p(()=>T.value.some(A=>!A.isDisabled)),H=p(()=>a(l.value+1,t.value.navMonths)),W=p(()=>H.value.some(A=>!A.isDisabled)),j=p(()=>s(m(u.value),_(u.value)).map(A=>({...A,click:()=>{l.value=A.year,o.value=!0,d()}}))),Y=p(()=>s(m(u.value-1),_(u.value-1))),R=p(()=>Y.value.some(A=>!A.isDisabled)),K=p(()=>s(m(u.value+1),_(u.value+1))),I=p(()=>K.value.some(A=>!A.isDisabled)),z=p(()=>o.value?E.value:j.value),Z=p(()=>o.value?N.value:R.value),oe=p(()=>o.value?W.value:I.value),Q=p(()=>_o(j.value.map(A=>A.year))),$=p(()=>St(j.value.map(A=>A.year))),B=p(()=>o.value?l.value:`${Q.value} - ${$.value}`);return Cs(()=>{l.value=n.value.year,d()}),me(()=>l.value,A=>u.value=h(A)),Kr(()=>d()),(A,ne)=>(P(),x("div",{class:"vc-nav-container",ref_key:"navContainer",ref:c},[U("div",d0,[U("button",{type:"button",class:"vc-nav-arrow is-left vc-focus",disabled:!b(Z),onClick:D,onKeydown:ne[0]||(ne[0]=G=>b(Yr)(G,D))},[V(Ye,{name:"nav-prev-button",move:D,disabled:!b(Z)},{default:le(()=>[V(Ft,{name:"ChevronLeft",width:"22px",height:"24px"})]),_:1},8,["disabled"])],40,v0),U("button",{type:"button",class:"vc-nav-title vc-focus",onClick:v,onKeydown:ne[1]||(ne[1]=G=>b(Yr)(G,v))},be(b(B)),33),U("button",{type:"button",class:"vc-nav-arrow is-right vc-focus",disabled:!b(oe),onClick:y,onKeydown:ne[2]||(ne[2]=G=>b(Yr)(G,y))},[V(Ye,{name:"nav-next-button",move:y,disabled:!b(oe)},{default:le(()=>[V(Ft,{name:"ChevronRight",width:"22px",height:"24px"})]),_:1},8,["disabled"])],40,h0)]),U("div",p0,[(P(!0),x(fe,null,Ce(b(z),G=>(P(),x("button",{key:G.label,type:"button","data-id":G.id,"aria-label":G.ariaLabel,class:re(["vc-nav-item vc-focus",[G.isActive?"is-active":G.isCurrent?"is-current":""]]),disabled:G.isDisabled,onClick:G.click,onKeydown:ae=>b(Yr)(ae,G.click)},be(G.label),43,m0))),128))])],512))}}),si=de({__name:"CalendarPageProvider",props:{page:null},setup(e){return f0(Cr(e,"page")),(r,n)=>pr(r.$slots,"default")}}),g0=de({__name:"CalendarNavPopover",setup(e){const{navPopoverId:t,color:r,displayMode:n}=kt();return(a,s)=>(P(),we(oa,{id:b(t),class:re(["vc-nav-popover-container",`vc-${b(r)}`,`vc-${b(n)}`])},{default:le(({data:o})=>[V(si,{page:o.page},{default:le(()=>[V(Ye,{name:"nav"},{default:le(()=>[V(y0)]),_:1})]),_:2},1032,["page"])]),_:1},8,["id","class"]))}}),b0=de({directives:{popover:Ko},components:{CalendarSlot:Ye},props:{day:{type:Object,required:!0}},setup(e){const{locale:t,theme:r,attributeContext:n,dayPopoverId:a,onDayClick:s,onDayMouseenter:o,onDayMouseleave:i,onDayFocusin:l,onDayFocusout:u,onDayKeydown:c}=kt(),d=p(()=>e.day),h=p(()=>n.value.getCells(d.value)),v=p(()=>h.value.map(I=>I.data)),m=p(()=>({...d.value,attributes:v.value,attributeCells:h.value}));function _({data:I},{popovers:z}){const{key:Z,customData:oe,popover:Q}=I;if(!Q)return;const $=Ja({key:Z,customData:oe,attribute:I},{...Q},{visibility:Q.label?"hover":"click",placement:"bottom",isInteractive:!Q.label});z.splice(0,0,$)}const D=p(()=>{const I={...r.value.prepareRender({}),popovers:[]};return h.value.forEach(z=>{r.value.render(z,I),_(z,I)}),I}),y=p(()=>D.value.highlights),C=p(()=>!!qe(y.value)),L=p(()=>D.value.content),M=p(()=>D.value.dots),O=p(()=>!!qe(M.value)),E=p(()=>D.value.bars),T=p(()=>!!qe(E.value)),N=p(()=>D.value.popovers),H=p(()=>N.value.map(I=>I.attribute)),W=ei("day-content"),j=p(()=>["vc-day",...d.value.classes,{"vc-day-box-center-center":!W},{"is-not-in-month":!e.day.inMonth}]),Y=p(()=>{let I;d.value.isFocusable?I="0":I="-1";const z=["vc-day-content vc-focusable vc-focus vc-attr",{"vc-disabled":d.value.isDisabled},bt(St(y.value),"contentClass"),bt(St(L.value),"class")||""],Z={...bt(St(y.value),"contentStyle"),...bt(St(L.value),"style")};return{class:z,style:Z,tabindex:I,"aria-label":d.value.ariaLabel,"aria-disabled":!!d.value.isDisabled,role:"button"}}),R=p(()=>({click(I){s(m.value,I)},mouseenter(I){o(m.value,I)},mouseleave(I){i(m.value,I)},focusin(I){l(m.value,I)},focusout(I){u(m.value,I)},keydown(I){c(m.value,I)}})),K=p(()=>qe(N.value)?Ja({id:a.value,data:{day:d,attributes:H.value}},...N.value):null);return{attributes:v,attributeCells:h,bars:E,dayClasses:j,dayContentProps:Y,dayContentEvents:R,dayPopover:K,glyphs:D,dots:M,hasDots:O,hasBars:T,highlights:y,hasHighlights:C,locale:t,popovers:N}}}),w0={key:0,class:"vc-highlights vc-day-layer"},D0={key:1,class:"vc-day-layer vc-day-box-center-bottom"},$0={class:"vc-dots"},_0={key:2,class:"vc-day-layer vc-day-box-center-bottom"},M0={class:"vc-bars"};function k0(e,t,r,n,a,s){const o=Ge("CalendarSlot"),i=fi("popover");return P(),x("div",{class:re(e.dayClasses)},[e.hasHighlights?(P(),x("div",w0,[(P(!0),x(fe,null,Ce(e.highlights,({key:l,wrapperClass:u,class:c,style:d})=>(P(),x("div",{key:l,class:re(u)},[U("div",{class:re(c),style:ir(d)},null,6)],2))),128))])):ie("",!0),V(o,{name:"day-content",day:e.day,attributes:e.attributes,"attribute-cells":e.attributeCells,dayProps:e.dayContentProps,dayEvents:e.dayContentEvents,locale:e.locale},{default:le(()=>[Ss((P(),x("div",Ht(e.dayContentProps,di(e.dayContentEvents,!0)),[Sn(be(e.day.label),1)],16)),[[i,e.dayPopover]])]),_:1},8,["day","attributes","attribute-cells","dayProps","dayEvents","locale"]),e.hasDots?(P(),x("div",D0,[U("div",$0,[(P(!0),x(fe,null,Ce(e.dots,({key:l,class:u,style:c})=>(P(),x("span",{key:l,class:re(u),style:ir(c)},null,6))),128))])])):ie("",!0),e.hasBars?(P(),x("div",_0,[U("div",M0,[(P(!0),x(fe,null,Ce(e.bars,({key:l,class:u,style:c})=>(P(),x("span",{key:l,class:re(u),style:ir(c)},null,6))),128))])])):ie("",!0)],2)}const O0=vt(b0,[["render",k0]]),P0={class:"vc-weekdays"},T0=["onClick"],Y0={inheritAttrs:!1},C0=de({...Y0,__name:"CalendarPage",setup(e){const{page:t}=ai(),{onWeeknumberClick:r}=kt();return(n,a)=>(P(),x("div",{class:re(["vc-pane",`row-${b(t).row}`,`row-from-end-${b(t).rowFromEnd}`,`column-${b(t).column}`,`column-from-end-${b(t).columnFromEnd}`]),ref:"pane"},[V(ri,{page:b(t),"is-lg":"","hide-arrows":""},null,8,["page"]),U("div",{class:re(["vc-weeks",{[`vc-show-weeknumbers-${b(t).weeknumberPosition}`]:b(t).weeknumberPosition}])},[U("div",P0,[(P(!0),x(fe,null,Ce(b(t).weekdays,({weekday:s,label:o},i)=>(P(),x("div",{key:i,class:re(`vc-weekday vc-weekday-${s}`)},be(o),3))),128))]),(P(!0),x(fe,null,Ce(b(t).viewWeeks,s=>(P(),x("div",{key:`weeknumber-${s.weeknumber}`,class:"vc-week"},[b(t).weeknumberPosition?(P(),x("div",{key:0,class:re(["vc-weeknumber",`is-${b(t).weeknumberPosition}`])},[U("span",{class:re(["vc-weeknumber-content"]),onClick:o=>b(r)(s,o)},be(s.weeknumberDisplay),9,T0)],2)):ie("",!0),(P(!0),x(fe,null,Ce(s.days,o=>(P(),we(O0,{key:o.id,day:o},null,8,["day"]))),128))]))),128))],2)],2))}}),S0=de({components:{CalendarHeader:ri,CalendarPage:C0,CalendarNavPopover:g0,CalendarDayPopover:ED,CalendarPageProvider:si,CalendarSlot:Ye},props:_D,emit:MD,setup(e,{emit:t,slots:r}){return kD(e,{emit:t,slots:r})}}),A0={class:"vc-pane-header-wrapper"};function I0(e,t,r,n,a,s){const o=Ge("CalendarHeader"),i=Ge("CalendarPage"),l=Ge("CalendarSlot"),u=Ge("CalendarPageProvider"),c=Ge("CalendarDayPopover"),d=Ge("CalendarNavPopover");return P(),x(fe,null,[U("div",Ht({"data-helptext":"Press the arrow keys to navigate by day, Home and End to navigate to week ends, PageUp and PageDown to navigate by month, Alt+PageUp and Alt+PageDown to navigate by year"},e.$attrs,{class:["vc-container",`vc-${e.view}`,`vc-${e.color}`,`vc-${e.displayMode}`,{"vc-expanded":e.expanded,"vc-bordered":!e.borderless,"vc-transparent":e.transparent}],onMouseup:t[0]||(t[0]=ci(()=>{},["prevent"])),ref:"containerRef"}),[U("div",{class:re(["vc-pane-container",{"in-transition":e.inTransition}])},[U("div",A0,[e.firstPage?(P(),we(o,{key:0,page:e.firstPage,"is-lg":"","hide-title":""},null,8,["page"])):ie("",!0)]),V(Ys,{name:`vc-${e.transitionName}`,onBeforeEnter:e.onTransitionBeforeEnter,onAfterEnter:e.onTransitionAfterEnter},{default:le(()=>[(P(),x("div",{key:e.pages[0].id,class:"vc-pane-layout",style:ir({gridTemplateColumns:`repeat(${e.columns}, 1fr)`})},[(P(!0),x(fe,null,Ce(e.pages,h=>(P(),we(u,{key:h.id,page:h},{default:le(()=>[V(l,{name:"page",page:h},{default:le(()=>[V(i)]),_:2},1032,["page"])]),_:2},1032,["page"]))),128))],4))]),_:1},8,["name","onBeforeEnter","onAfterEnter"]),V(l,{name:"footer"})],2)],16),V(c),V(d)],64)}const x0=vt(S0,[["render",I0]]),oi=Symbol("__vc_date_picker_context__"),E0={...Zo,mode:{type:String,default:"date"},modelValue:{type:[Number,String,Date,Object]},modelModifiers:{type:Object,default:()=>({})},rules:[String,Object],is24hr:Boolean,hideTimeHeader:Boolean,timeAccuracy:{type:Number,default:2},isRequired:Boolean,isRange:Boolean,updateOnInput:{type:Boolean,default:()=>ft("datePicker.updateOnInput")},inputDebounce:{type:Number,default:()=>ft("datePicker.inputDebounce")},popover:{type:[Boolean,Object],default:!0},dragAttribute:Object,selectAttribute:Object,attributes:[Object,Array]},N0=["update:modelValue","drag","dayclick","daykeydown","popover-will-show","popover-did-show","popover-will-hide","popover-did-hide"];function L0(e,{emit:t,slots:r}){Qo(r,{footer:"dp-footer"});const n=Xo(e),{locale:a,masks:s,disabledAttribute:o}=n,i=te(!1),l=te(Symbol()),u=te(null),c=te(null),d=te(["",""]),h=te(null),v=te(null);let m,_,D=!0;const y=p(()=>e.isRange||e.modelModifiers.range===!0),C=p(()=>y.value&&u.value!=null?u.value.start:null),L=p(()=>y.value&&u.value!=null?u.value.end:null),M=p(()=>e.mode.toLowerCase()==="date"),O=p(()=>e.mode.toLowerCase()==="datetime"),E=p(()=>e.mode.toLowerCase()==="time"),T=p(()=>!!c.value),N=p(()=>{let f="date";e.modelModifiers.number&&(f="number"),e.modelModifiers.string&&(f="string");const g=s.value.modelValue||"iso";return ne({type:f,mask:g})}),H=p(()=>mt(c.value??u.value)),W=p(()=>E.value?e.is24hr?s.value.inputTime24hr:s.value.inputTime:O.value?e.is24hr?s.value.inputDateTime24hr:s.value.inputDateTime:s.value.input),j=p(()=>/[Hh]/g.test(W.value)),Y=p(()=>/[dD]{1,2}|Do|W{1,4}|M{1,4}|YY(?:YY)?/g.test(W.value)),R=p(()=>{if(j.value&&Y.value)return"dateTime";if(Y.value)return"date";if(j.value)return"time"}),K=p(()=>{var f;const g=((f=h.value)==null?void 0:f.$el.previousElementSibling)??void 0;return vr({},e.popover,ft("datePicker.popover"),{target:g})}),I=p(()=>Uo({...K.value,id:l.value})),z=p(()=>y.value?{start:d.value[0],end:d.value[1]}:d.value[0]),Z=p(()=>{const f=["start","end"].map(g=>({input:He(g),change:ht(g),keyup:pt,...e.popover&&I.value}));return y.value?{start:f[0],end:f[1]}:f[0]}),oe=p(()=>{if(!se(u.value))return null;const f={key:"select-drag",...e.selectAttribute,dates:u.value,pinPage:!0},{dot:g,bar:k,highlight:F,content:ee}=f;return!g&&!k&&!F&&!ee&&(f.highlight=!0),f}),Q=p(()=>{if(!y.value||!se(c.value))return null;const f={key:"select-drag",...e.dragAttribute,dates:c.value},{dot:g,bar:k,highlight:F,content:ee}=f;return!g&&!k&&!F&&!ee&&(f.highlight={startEnd:{fillMode:"outline"}}),f}),$=p(()=>{const f=Ae(e.attributes)?[...e.attributes]:[];return Q.value?f.unshift(Q.value):oe.value&&f.unshift(oe.value),f}),B=p(()=>ne(e.rules==="auto"?A():e.rules??{}));function A(){const f={ms:[0,999],sec:[0,59],min:[0,59],hr:[0,23]},g=M.value?0:e.timeAccuracy;return[0,1].map(k=>{switch(g){case 0:return{hours:f.hr[k],minutes:f.min[k],seconds:f.sec[k],milliseconds:f.ms[k]};case 1:return{minutes:f.min[k],seconds:f.sec[k],milliseconds:f.ms[k]};case 3:return{milliseconds:f.ms[k]};case 4:return{};default:return{seconds:f.sec[k],milliseconds:f.ms[k]}}})}function ne(f){return Ae(f)?f.length===1?[f[0],f[0]]:f:[f,f]}function G(f){return ne(f).map((g,k)=>({...g,rules:B.value[k]}))}function ae(f){return f==null?!1:Le(f)?!isNaN(f):At(f)?!isNaN(f.getTime()):We(f)?f!=="":ra(f)}function ue(f){return Xe(f)&&"start"in f&&"end"in f&&ae(f.start??null)&&ae(f.end??null)}function se(f){return ue(f)||ae(f)}function ce(f,g){if(f==null&&g==null)return!0;if(f==null||g==null)return!1;const k=At(f),F=At(g);return k&&F?f.getTime()===g.getTime():k||F?!1:ce(f.start,g.start)&&ce(f.end,g.end)}function Me(f){return!se(f)||!o.value?!1:o.value.intersectsRange(a.value.range(f))}function Ke(f,g,k,F){if(!se(f))return null;if(ue(f)){const ee=a.value.toDate(f.start,{...g[0],fillDate:C.value??void 0,patch:k}),pe=a.value.toDate(f.end,{...g[1],fillDate:L.value??void 0,patch:k});return Dr({start:ee,end:pe},F)}return a.value.toDateOrNull(f,{...g[0],fillDate:u.value,patch:k})}function Ee(f,g){return ue(f)?{start:a.value.fromDate(f.start,g[0]),end:a.value.fromDate(f.end,g[1])}:y.value?null:a.value.fromDate(f,g[0])}function Pe(f,g={}){return clearTimeout(m),new Promise(k=>{const{debounce:F=0,...ee}=g;F>0?m=window.setTimeout(()=>{k(Te(f,ee))},F):k(Te(f,ee))})}function Te(f,{config:g=N.value,patch:k="dateTime",clearIfEqual:F=!1,formatInput:ee=!0,hidePopover:pe=!1,dragging:ke=T.value,targetPriority:Tt,moveToValue:Yt=!1}={}){const sn=G(g);let De=Ke(f,sn,k,Tt);if(Me(De)){if(ke)return null;De=u.value,pe=!1}else De==null&&e.isRequired?De=u.value:De!=null&&ce(u.value,De)&&F&&(De=null);const kr=ke?c:u,Zt=!ce(kr.value,De);kr.value=De,ke||(c.value=null);const Xt=Ee(De,N.value);return Zt&&(D=!1,t(ke?"drag":"update:modelValue",Xt),nr(()=>D=!0)),pe&&!ke&&qt(),ee&&tt(),Yt&&nr(()=>_r(Tt??"start")),Xt}function tt(){nr(()=>{const f=G({type:"string",mask:W.value}),g=Ee(c.value??u.value,f);y.value?d.value=[g&&g.start,g&&g.end]:d.value=[g,""]})}function Ot(f,g,k){d.value.splice(g==="start"?0:1,1,f);const F=y.value?{start:d.value[0],end:d.value[1]||d.value[0]}:f,ee={type:"string",mask:W.value};Pe(F,{...k,config:ee,patch:R.value,targetPriority:g,moveToValue:!0})}function He(f){return g=>{e.updateOnInput&&Ot(g.currentTarget.value,f,{formatInput:!1,hidePopover:!1,debounce:e.inputDebounce})}}function ht(f){return g=>{Ot(g.currentTarget.value,f,{formatInput:!0,hidePopover:!1})}}function pt(f){f.key==="Escape"&&Pe(u.value,{formatInput:!0,hidePopover:!0})}function mt(f){return y.value?[f&&f.start?a.value.getDateParts(f.start):null,f&&f.end?a.value.getDateParts(f.end):null]:[f?a.value.getDateParts(f):null]}function Ne(){c.value=null,tt()}function rt(f){t("popover-will-show",f)}function nt(f){t("popover-did-show",f)}function at(f){Ne(),t("popover-will-hide",f)}function st(f){t("popover-did-hide",f)}function ot(f){const g={patch:"date",formatInput:!0,hidePopover:!0};if(y.value){const k=!T.value;k?_={start:f.startDate,end:f.endDate}:_!=null&&(_.end=f.date),Pe(_,{...g,dragging:k})}else Pe(f.date,{...g,clearIfEqual:!e.isRequired})}function it(f,g){ot(f),t("dayclick",f,g)}function yt(f,g){switch(g.key){case" ":case"Enter":{ot(f),g.preventDefault();break}case"Escape":qt()}t("daykeydown",f,g)}function Pt(f,g){!T.value||_==null||(_.end=f.date,Pe(Dr(_),{patch:"date",formatInput:!0}))}function rn(f={}){Tn({...K.value,...f,isInteractive:!0,id:l.value})}function qt(f={}){hr({hideDelay:10,force:!0,...K.value,...f,id:l.value})}function nn(f){Vo({...K.value,...f,isInteractive:!0,id:l.value})}function Dr(f,g){const{start:k,end:F}=f;if(k>F)switch(g){case"start":return{start:k,end:k};case"end":return{start:F,end:F};default:return{start:F,end:k}}return{start:k,end:F}}async function $r(f,g={}){return v.value==null?!1:v.value.move(f,g)}async function an(f,g={}){return v.value==null?!1:v.value.moveBy(f,g)}async function _r(f,g={}){const k=u.value;if(v.value==null||!se(k))return!1;const F=f!=="end",ee=F?1:-1,pe=ue(k)?F?k.start:k.end:k,ke=Yo(pe,"monthly",a.value);return v.value.move(ke,{position:ee,...g})}me(()=>e.isRange,f=>{f&&console.warn("The `is-range` prop will be deprecated in future releases. Please use the `range` modifier.")},{immediate:!0}),me(()=>y.value,()=>{Te(null,{formatInput:!0})}),me(()=>W.value,()=>tt()),me(()=>e.modelValue,f=>{D&&Te(f,{formatInput:!0,hidePopover:!1})}),me(()=>B.value,()=>{Xe(e.rules)&&Te(e.modelValue,{formatInput:!0,hidePopover:!1})}),me(()=>e.timezone,()=>{Te(u.value,{formatInput:!0})});const Mr=ne(N.value);u.value=Ke(e.modelValue??null,Mr,"dateTime"),Kr(()=>{Te(e.modelValue,{formatInput:!0,hidePopover:!1})}),nr(()=>i.value=!0);const w={...n,showCalendar:i,datePickerPopoverId:l,popoverRef:h,popoverEvents:I,calendarRef:v,isRange:y,isTimeMode:E,isDateTimeMode:O,is24hr:Cr(e,"is24hr"),hideTimeHeader:Cr(e,"hideTimeHeader"),timeAccuracy:Cr(e,"timeAccuracy"),isDragging:T,inputValue:z,inputEvents:Z,dateParts:H,attributes:$,rules:B,move:$r,moveBy:an,moveToValue:_r,updateValue:Pe,showPopover:rn,hidePopover:qt,togglePopover:nn,onDayClick:it,onDayKeydown:yt,onDayMouseEnter:Pt,onPopoverBeforeShow:rt,onPopoverAfterShow:nt,onPopoverBeforeHide:at,onPopoverAfterHide:st};return mr(oi,w),w}function ia(){const e=yr(oi);if(e)return e;throw new Error("DatePicker context missing. Please verify this component is nested within a valid context provider.")}const R0=[{value:0,label:"12"},{value:1,label:"1"},{value:2,label:"2"},{value:3,label:"3"},{value:4,label:"4"},{value:5,label:"5"},{value:6,label:"6"},{value:7,label:"7"},{value:8,label:"8"},{value:9,label:"9"},{value:10,label:"10"},{value:11,label:"11"}],F0=[{value:12,label:"12"},{value:13,label:"1"},{value:14,label:"2"},{value:15,label:"3"},{value:16,label:"4"},{value:17,label:"5"},{value:18,label:"6"},{value:19,label:"7"},{value:20,label:"8"},{value:21,label:"9"},{value:22,label:"10"},{value:23,label:"11"}];function H0(e){const t=ia(),{locale:r,isRange:n,isTimeMode:a,dateParts:s,rules:o,is24hr:i,hideTimeHeader:l,timeAccuracy:u,updateValue:c}=t;function d(Y){Y=Object.assign(v.value,Y);let R=null;if(n.value){const K=h.value?Y:s.value[0],I=h.value?s.value[1]:Y;R={start:K,end:I}}else R=Y;c(R,{patch:"time",targetPriority:h.value?"start":"end",moveToValue:!0})}const h=p(()=>e.position===0),v=p(()=>s.value[e.position]||{isValid:!1}),m=p(()=>ra(v.value)),_=p(()=>!!v.value.isValid),D=p(()=>!l.value&&_.value),y=p(()=>{if(!m.value)return null;let Y=r.value.toDate(v.value);return v.value.hours===24&&(Y=new Date(Y.getTime()-1)),Y}),C=p({get(){return v.value.hours},set(Y){d({hours:Y})}}),L=p({get(){return v.value.minutes},set(Y){d({minutes:Y})}}),M=p({get(){return v.value.seconds},set(Y){d({seconds:Y})}}),O=p({get(){return v.value.milliseconds},set(Y){d({milliseconds:Y})}}),E=p({get(){return v.value.hours<12},set(Y){Y=String(Y).toLowerCase()=="true";let R=C.value;Y&&R>=12?R-=12:!Y&&R<12&&(R+=12),d({hours:R})}}),T=p(()=>aD(v.value,o.value[e.position])),N=p(()=>R0.filter(Y=>T.value.hours.some(R=>R.value===Y.value))),H=p(()=>F0.filter(Y=>T.value.hours.some(R=>R.value===Y.value))),W=p(()=>i.value?T.value.hours:E.value?N.value:H.value),j=p(()=>{const Y=[];return qe(N.value)&&Y.push({value:!0,label:"AM"}),qe(H.value)&&Y.push({value:!1,label:"PM"}),Y});return{...t,showHeader:D,timeAccuracy:u,parts:v,isValid:_,date:y,hours:C,minutes:L,seconds:M,milliseconds:O,options:T,hourOptions:W,isAM:E,isAMOptions:j,is24hr:i}}const W0=["value"],j0=["value","disabled"],B0={key:1,class:"vc-base-sizer","aria-hidden":"true"},z0={inheritAttrs:!1},rr=de({...z0,__name:"BaseSelect",props:{options:null,modelValue:null,alignRight:{type:Boolean},alignLeft:{type:Boolean},showIcon:{type:Boolean},fitContent:{type:Boolean}},emits:["update:modelValue"],setup(e){const t=e,r=p(()=>{const n=t.options.find(a=>a.value===t.modelValue);return n==null?void 0:n.label});return(n,a)=>(P(),x("div",{class:re(["vc-base-select",{"vc-fit-content":e.fitContent,"vc-has-icon":e.showIcon}])},[U("select",Ht(n.$attrs,{value:e.modelValue,class:["vc-focus",{"vc-align-right":e.alignRight,"vc-align-left":e.alignLeft}],onChange:a[0]||(a[0]=s=>n.$emit("update:modelValue",s.target.value))}),[(P(!0),x(fe,null,Ce(e.options,s=>(P(),x("option",{key:s.value,value:s.value,disabled:s.disabled},be(s.label),9,j0))),128))],16,W0),e.showIcon?(P(),we(Ft,{key:0,name:"ChevronDown",size:"18"})):ie("",!0),e.fitContent?(P(),x("div",B0,be(b(r)),1)):ie("",!0)],2))}}),V0={key:0,class:"vc-time-header"},U0={class:"vc-time-weekday"},K0={class:"vc-time-month"},G0={class:"vc-time-day"},q0={class:"vc-time-year"},Z0={class:"vc-time-select-group"},X0=U("span",{class:"vc-time-colon"},":",-1),J0=U("span",{class:"vc-time-colon"},":",-1),Q0=U("span",{class:"vc-time-decimal"},".",-1),Ps=de({__name:"TimePicker",props:{position:null},setup(e,{expose:t}){const n=H0(e);t(n);const{locale:a,isValid:s,date:o,hours:i,minutes:l,seconds:u,milliseconds:c,options:d,hourOptions:h,isTimeMode:v,isAM:m,isAMOptions:_,is24hr:D,showHeader:y,timeAccuracy:C}=n;return(L,M)=>(P(),x("div",{class:re(["vc-time-picker",[{"vc-invalid":!b(s),"vc-attached":!b(v)}]])},[V(Ye,{name:"time-header"},{default:le(()=>[b(y)&&b(o)?(P(),x("div",V0,[U("span",U0,be(b(a).formatDate(b(o),"WWW")),1),U("span",K0,be(b(a).formatDate(b(o),"MMM")),1),U("span",G0,be(b(a).formatDate(b(o),"D")),1),U("span",q0,be(b(a).formatDate(b(o),"YYYY")),1)])):ie("",!0)]),_:1}),U("div",Z0,[V(Ft,{name:"Clock",size:"17"}),V(rr,{modelValue:b(i),"onUpdate:modelValue":M[0]||(M[0]=O=>Jt(i)?i.value=O:null),modelModifiers:{number:!0},options:b(h),class:"vc-time-select-hours","align-right":""},null,8,["modelValue","options"]),b(C)>1?(P(),x(fe,{key:0},[X0,V(rr,{modelValue:b(l),"onUpdate:modelValue":M[1]||(M[1]=O=>Jt(l)?l.value=O:null),modelModifiers:{number:!0},options:b(d).minutes,class:"vc-time-select-minutes","align-left":b(C)===2},null,8,["modelValue","options","align-left"])],64)):ie("",!0),b(C)>2?(P(),x(fe,{key:1},[J0,V(rr,{modelValue:b(u),"onUpdate:modelValue":M[2]||(M[2]=O=>Jt(u)?u.value=O:null),modelModifiers:{number:!0},options:b(d).seconds,class:"vc-time-select-seconds","align-left":b(C)===3},null,8,["modelValue","options","align-left"])],64)):ie("",!0),b(C)>3?(P(),x(fe,{key:2},[Q0,V(rr,{modelValue:b(c),"onUpdate:modelValue":M[3]||(M[3]=O=>Jt(c)?c.value=O:null),modelModifiers:{number:!0},options:b(d).milliseconds,class:"vc-time-select-milliseconds","align-left":""},null,8,["modelValue","options"])],64)):ie("",!0),b(D)?ie("",!0):(P(),we(rr,{key:3,modelValue:b(m),"onUpdate:modelValue":M[4]||(M[4]=O=>Jt(m)?m.value=O:null),options:b(_)},null,8,["modelValue","options"]))])],2))}}),ii=de({__name:"DatePickerBase",setup(e){const{attributes:t,calendarRef:r,color:n,displayMode:a,isDateTimeMode:s,isTimeMode:o,isRange:i,onDayClick:l,onDayMouseEnter:u,onDayKeydown:c}=ia(),d=i.value?[0,1]:[0];return(h,v)=>b(o)?(P(),x("div",{key:0,class:re(`vc-container vc-bordered vc-${b(n)} vc-${b(a)}`)},[(P(!0),x(fe,null,Ce(b(d),m=>(P(),we(Ps,{key:m,position:m},null,8,["position"]))),128))],2)):(P(),we(x0,{key:1,attributes:b(t),ref_key:"calendarRef",ref:r,onDayclick:b(l),onDaymouseenter:b(u),onDaykeydown:b(c)},{footer:le(()=>[b(s)?(P(!0),x(fe,{key:0},Ce(b(d),m=>(P(),we(Ps,{key:m,position:m},null,8,["position"]))),128)):ie("",!0),V(Ye,{name:"dp-footer"})]),_:1},8,["attributes","onDayclick","onDaymouseenter","onDaykeydown"]))}}),e$={inheritAttrs:!1},t$=de({...e$,__name:"DatePickerPopover",setup(e){const{datePickerPopoverId:t,color:r,displayMode:n,popoverRef:a,onPopoverBeforeShow:s,onPopoverAfterShow:o,onPopoverBeforeHide:i,onPopoverAfterHide:l}=ia();return(u,c)=>(P(),we(oa,{id:b(t),placement:"bottom-start",class:re(`vc-date-picker-content vc-${b(r)} vc-${b(n)}`),ref_key:"popoverRef",ref:a,onBeforeShow:b(s),onAfterShow:b(o),onBeforeHide:b(i),onAfterHide:b(l)},{default:le(()=>[V(ii,or(mn(u.$attrs)),null,16)]),_:1},8,["id","class","onBeforeShow","onAfterShow","onBeforeHide","onAfterHide"]))}}),r$=de({inheritAttrs:!1,emits:N0,props:E0,components:{DatePickerBase:ii,DatePickerPopover:t$},setup(e,t){const r=L0(e,t),n=Yn(ko(r,"calendarRef","popoverRef"));return{...r,slotCtx:n}}});function n$(e,t,r,n,a,s){const o=Ge("DatePickerPopover"),i=Ge("DatePickerBase");return e.$slots.default?(P(),x(fe,{key:0},[pr(e.$slots,"default",or(mn(e.slotCtx))),V(o,or(mn(e.$attrs)),null,16)],64)):(P(),we(i,or(Ht({key:1},e.$attrs)),null,16))}const s$=vt(r$,[["render",n$]]);export{s$ as D};
