import{e as p,l as S,bb as R,A as T,W as h,i as H,o as u,c as g,w as f,b as c,a as l,u as r,j as _,d as v,f as L,T as P}from"./app-f0078ddb.js";import{_ as U}from"./AppLayout-33f062bc.js";import{_ as V}from"./Breadcrumbs-c96e9207.js";import C from"./Summary-27ab5eca.js";import $ from"./ReviewSidebar-5ebca430.js";import{D}from"./datetime-8ddd27a0.js";import{_ as E}from"./Footer-0988dcd8.js";import{_ as W}from"./MobileSidebar-5e21b4cd.js";import{C as F}from"./Colosseum-0e8d62a4.js";import{r as M}from"./InformationCircleIcon-716f3ffb.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./SummaryWord-5b872645.js";import"./StarIcon-155a2a28.js";import"./pluralize-d25a928b.js";import"./DropdownGeneral-ce7a4558.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./InfinitasIcon-1a3ae135.js";const q={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},z={class:"px-4 py-8 sm:px-8"},G={class:"flex flex-row justify-between items-center"},J={key:0},K={key:1},Q={key:0,class:"text-sm font-normal text-rose-600 mt-4 px-4"},X={key:1},Y={class:"flex flex-col items-center justify-center gap-6 mt-24 mb-48 animate-pulse"},Z={class:"hidden lg:inline-block bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},zt={__name:"Show",props:{stats:Object,list:Object,learned:Array,starred:Array,sections:Array,sort:String},setup(a){const s=a;let I=[{name:"Practice",href:"/practice",current:!1},{name:"Activities",href:"/practice/activities",current:!1},{name:"Review",href:"#",current:!0}],i=p(s.list),y=p(!1),A=p(s.stats.attempts==i.value.reduce((o,t)=>o+t.total,0)),b={correct:s.stats.correct,incorrect:s.stats.attempts-s.stats.correct,attempts:s.stats.attempts},n=p(!1);const x=S(()=>s.stats.completed_at?D.fromISO(s.stats.completed_at).toLocal().toFormat("'completed at' h:mm a 'on' ccc LLL dd yyyy"):"N/A"),w=S(()=>{var o=[];return s.sections.forEach(t=>{t.sectionList&&o.push(t.sectionList.book_id+":"+t.sectionList.start+":"+t.sectionList.end),t.vocabList&&o.push(t.vocabList.id)}),o}),j=()=>{i.value=s.list},k=o=>{h.get("/practice/vocabulary/attempt/"+s.stats.id,{sort:o},{preserveState:!0,replace:!0,preserveScroll:!0,only:["list"],onSuccess:()=>{j()}})};let m=p(null),d=p(!1);const B=()=>{axios.get(`/api/practice/vocabulary/check-attempts/${s.stats.id}`).then(o=>{o.data.status=="completed"&&(clearInterval(m.value),m.value=null,h.reload({method:"get",only:["list"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{y.value=!1,i.value=s.list,d.value=!0,clearInterval(m.value)}}))}).catch(o=>{})},N=()=>{m.value=setInterval(B,1e3)};return R(()=>{m.value&&clearInterval(m.value)}),T(()=>{s.stats.attempts==i.value.reduce((o,t)=>o+t.total,0)?d.value=!0:(N(),setTimeout(()=>{d.value||h.reload({method:"get",only:["list"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{y.value=!0,i.value=s.list,d.value=!0,clearInterval(m.value)}})},8e3))}),(o,t)=>{const O=H("Head");return u(),g(U,null,{default:f(()=>[c(O,null,{default:f(()=>t[4]||(t[4]=[l("title",null,"Practice Review",-1)])),_:1}),l("main",q,[l("div",z,[l("div",G,[c(V,{class:"lg:col-span-9 xl:grid-cols-10",pages:r(I)},null,8,["pages"]),c(r(M),{class:"lg:hidden w-6 h-6 mt-1 ml-2 transition duration-150 ease-in-out cursor-pointer stroke-2 stroke-current hover:text-slate-500 text-slate-400",onClick:t[0]||(t[0]=e=>_(n)?n.value=!r(n):n=!r(n))})]),t[5]||(t[5]=l("div",{class:"overflow-visible"},[l("div",{class:"mt-8 pb-8"},[l("h1",{class:"text-4xl text-gray-900 font-bold"},"Review")])],-1))]),r(A)?(u(),v("div",J,[r(d)?(u(),g(C,{key:0,stats:r(b),summary:r(i).filter(e=>2*e.correct-e.total<5),"learned-words":r(i).filter(e=>2*e.correct-e.total>=5),learned:a.learned,starred:a.starred},null,8,["stats","summary","learned-words","learned","starred"])):L("",!0)])):(u(),v("div",K,[c(P,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:f(()=>[r(d)?(u(),g(C,{key:0,stats:r(b),summary:r(i).filter(e=>2*e.correct-e.total<5),"learned-words":r(i).filter(e=>2*e.correct-e.total>=5),learned:a.learned,starred:a.starred},{default:f(()=>[r(y)?(u(),v("p",Q," Some of your attempts don't seem to have been processed yet. Please check back later for a more accurate summary. ")):L("",!0)]),_:1},8,["stats","summary","learned-words","learned","starred"])):(u(),v("div",X,[l("div",Y,[c(F,{class:"w-24 h-24 fill-slate-500"}),t[6]||(t[6]=l("h5",{class:"text-3xl font-intro font-semibold text-slate-500"}," Hold on while we process your last few attempts. ",-1))])]))]),_:1})])),c(E)]),c(W,{class:"lg:hidden",show:r(n),onClose:t[2]||(t[2]=e=>_(n)?n.value=!1:n=!1)},{default:f(()=>[c($,{"is-finished":!0,sections:a.sections,description:x.value,"current-list":w.value,stats:a.stats,"total-words":a.list.length,sort:a.sort,"onUpdate:sort":t[1]||(t[1]=e=>k(e))},null,8,["sections","description","current-list","stats","total-words","sort"])]),_:1},8,["show"]),l("aside",Z,[c($,{"is-finished":!0,sections:a.sections,description:x.value,"current-list":w.value,stats:a.stats,"total-words":a.list.length,sort:a.sort,"onUpdate:sort":t[3]||(t[3]=e=>k(e))},null,8,["sections","description","current-list","stats","total-words","sort"])])]),_:1})}}};export{zt as default};
