import{i as c,o as t,d as a,F as o,h as d,a as e,b as l,w as r,t as m}from"./app-f0078ddb.js";/* empty css            */const _={role:"list",class:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3"},u={class:"flex-1 truncate"},p={class:"flex items-center space-x-3"},g={class:"text-md truncate font-medium text-gray-900"},y={__name:"DevGrid",setup(f){const n=[{name:"Define Empty Words",link:"/get-blank-words"},{name:"Tag Adjectives and Nouns",link:"/dev/adjectives-and-nouns"},{name:"Practice Vocabulary",link:"/dev/practice/vocabulary"},{name:"Add a New Text",link:"/dev/add-text"},{name:"Add a Collection",link:"/dev/collections"}];return(v,h)=>{const i=c("Link");return t(),a("ul",_,[(t(),a(o,null,d(n,s=>e("li",{key:s.name,class:"col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow-sm"},[l(i,{href:s.link,class:"flex w-full items-center justify-between space-x-6 p-6"},{default:r(()=>[e("div",u,[e("div",p,[e("h3",g,m(s.name),1)])])]),_:2},1032,["href"])])),64))])}}};export{y as default};
