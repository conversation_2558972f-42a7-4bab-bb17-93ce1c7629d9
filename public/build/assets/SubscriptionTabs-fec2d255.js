import{H as F,e as T,L as R,l as m,I as Y,A as q,p as re,J as ne,F as H,B as ee,K,k as W,o as _,d as B,b as z,w as j,u as O,h as J,c as Q,a as A,t as C,n as X}from"./app-f0078ddb.js";import{o as f,A as N,T as se,i as te,N as Z,u as M}from"./render-c34c346a.js";import{s as ue}from"./use-resolve-button-type-24d8b5c5.js";import{u as oe,f as ae,o as I}from"./keyboard-982fc047.js";import{O as D,T as G,i as ie,P as E,N as w}from"./focus-management-8406d052.js";import{t as de}from"./micro-task-89dcd6af.js";/* empty css            */import"./env-c107754a.js";let ce=F({props:{onFocus:{type:Function,required:!0}},setup(e){let i=T(!0);return()=>i.value?R(ae,{as:"button",type:"button",features:oe.Focusable,onFocus(v){v.preventDefault();let d,o=50;function r(){var l;if(o--<=0){d&&cancelAnimationFrame(d);return}if((l=e.onFocus)!=null&&l.call(e)){i.value=!1,cancelAnimationFrame(d);return}d=requestAnimationFrame(r)}d=requestAnimationFrame(r)}}):null}});var pe=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(pe||{}),ve=(e=>(e[e.Less=-1]="Less",e[e.Equal=0]="Equal",e[e.Greater=1]="Greater",e))(ve||{});let le=Symbol("TabsContext");function L(e){let i=K(le,null);if(i===null){let v=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(v,L),v}return i}let V=Symbol("TabsSSRContext"),fe=F({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:i,attrs:v,emit:d}){var o;let r=T((o=e.selectedIndex)!=null?o:e.defaultIndex),l=T([]),s=T([]),x=m(()=>e.selectedIndex!==null),S=m(()=>x.value?e.selectedIndex:r.value);function h(a){var n;let c=D(u.tabs.value,f),t=D(u.panels.value,f),p=c.filter(y=>{var g;return!((g=f(y))!=null&&g.hasAttribute("disabled"))});if(a<0||a>c.length-1){let y=M(r.value===null?0:Math.sign(a-r.value),{[-1]:()=>1,0:()=>M(Math.sign(a),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),g=M(y,{0:()=>c.indexOf(p[0]),1:()=>c.indexOf(p[p.length-1])});g!==-1&&(r.value=g),u.tabs.value=c,u.panels.value=t}else{let y=c.slice(0,a),g=[...c.slice(a),...y].find(U=>p.includes(U));if(!g)return;let k=(n=c.indexOf(g))!=null?n:u.selectedIndex.value;k===-1&&(k=u.selectedIndex.value),r.value=k,u.tabs.value=c,u.panels.value=t}}let u={selectedIndex:m(()=>{var a,n;return(n=(a=r.value)!=null?a:e.defaultIndex)!=null?n:null}),orientation:m(()=>e.vertical?"vertical":"horizontal"),activation:m(()=>e.manual?"manual":"auto"),tabs:l,panels:s,setSelectedIndex(a){S.value!==a&&d("change",a),x.value||h(a)},registerTab(a){var n;if(l.value.includes(a))return;let c=l.value[r.value];if(l.value.push(a),l.value=D(l.value,f),!x.value){let t=(n=l.value.indexOf(c))!=null?n:r.value;t!==-1&&(r.value=t)}},unregisterTab(a){let n=l.value.indexOf(a);n!==-1&&l.value.splice(n,1)},registerPanel(a){s.value.includes(a)||(s.value.push(a),s.value=D(s.value,f))},unregisterPanel(a){let n=s.value.indexOf(a);n!==-1&&s.value.splice(n,1)}};Y(le,u);let b=T({tabs:[],panels:[]}),$=T(!1);q(()=>{$.value=!0}),Y(V,m(()=>$.value?null:b.value));let P=m(()=>e.selectedIndex);return q(()=>{re([P],()=>{var a;return h((a=e.selectedIndex)!=null?a:e.defaultIndex)},{immediate:!0})}),ne(()=>{if(!x.value||S.value==null||u.tabs.value.length<=0)return;let a=D(u.tabs.value,f);a.some((n,c)=>f(u.tabs.value[c])!==f(n))&&u.setSelectedIndex(a.findIndex(n=>f(n)===f(u.tabs.value[S.value])))}),()=>{let a={selectedIndex:r.value};return R(H,[l.value.length<=0&&R(ce,{onFocus:()=>{for(let n of l.value){let c=f(n);if((c==null?void 0:c.tabIndex)===0)return c.focus(),!0}return!1}}),N({theirProps:{...v,...se(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])},ourProps:{},slot:a,slots:i,attrs:v,name:"TabGroup"})])}}}),be=F({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:i,slots:v}){let d=L("TabList");return()=>{let o={selectedIndex:d.selectedIndex.value},r={role:"tablist","aria-orientation":d.orientation.value};return N({ourProps:r,theirProps:e,slot:o,attrs:i,slots:v,name:"TabList"})}}}),me=F({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:i,slots:v,expose:d}){var o;let r=(o=e.id)!=null?o:`headlessui-tabs-tab-${te()}`,l=L("Tab"),s=T(null);d({el:s,$el:s}),q(()=>l.registerTab(s)),ee(()=>l.unregisterTab(s));let x=K(V),S=m(()=>{if(x.value){let t=x.value.tabs.indexOf(r);return t===-1?x.value.tabs.push(r)-1:t}return-1}),h=m(()=>{let t=l.tabs.value.indexOf(s);return t===-1?S.value:t}),u=m(()=>h.value===l.selectedIndex.value);function b(t){var p;let y=t();if(y===G.Success&&l.activation.value==="auto"){let g=(p=ie(s))==null?void 0:p.activeElement,k=l.tabs.value.findIndex(U=>f(U)===g);k!==-1&&l.setSelectedIndex(k)}return y}function $(t){let p=l.tabs.value.map(y=>f(y)).filter(Boolean);if(t.key===I.Space||t.key===I.Enter){t.preventDefault(),t.stopPropagation(),l.setSelectedIndex(h.value);return}switch(t.key){case I.Home:case I.PageUp:return t.preventDefault(),t.stopPropagation(),b(()=>E(p,w.First));case I.End:case I.PageDown:return t.preventDefault(),t.stopPropagation(),b(()=>E(p,w.Last))}if(b(()=>M(l.orientation.value,{vertical(){return t.key===I.ArrowUp?E(p,w.Previous|w.WrapAround):t.key===I.ArrowDown?E(p,w.Next|w.WrapAround):G.Error},horizontal(){return t.key===I.ArrowLeft?E(p,w.Previous|w.WrapAround):t.key===I.ArrowRight?E(p,w.Next|w.WrapAround):G.Error}}))===G.Success)return t.preventDefault()}let P=T(!1);function a(){var t;P.value||(P.value=!0,!e.disabled&&((t=f(s))==null||t.focus({preventScroll:!0}),l.setSelectedIndex(h.value),de(()=>{P.value=!1})))}function n(t){t.preventDefault()}let c=ue(m(()=>({as:e.as,type:i.type})),s);return()=>{var t,p;let y={selected:u.value,disabled:(t=e.disabled)!=null?t:!1},{...g}=e,k={ref:s,onKeydown:$,onMousedown:n,onClick:a,id:r,role:"tab",type:c.value,"aria-controls":(p=f(l.panels.value[h.value]))==null?void 0:p.id,"aria-selected":u.value,tabIndex:u.value?0:-1,disabled:e.disabled?!0:void 0};return N({ourProps:k,theirProps:g,slot:y,attrs:i,slots:v,name:"Tab"})}}}),xe=F({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:i,attrs:v}){let d=L("TabPanels");return()=>{let o={selectedIndex:d.selectedIndex.value};return N({theirProps:e,ourProps:{},slot:o,attrs:v,slots:i,name:"TabPanels"})}}}),ye=F({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:i,slots:v,expose:d}){var o;let r=(o=e.id)!=null?o:`headlessui-tabs-panel-${te()}`,l=L("TabPanel"),s=T(null);d({el:s,$el:s}),q(()=>l.registerPanel(s)),ee(()=>l.unregisterPanel(s));let x=K(V),S=m(()=>{if(x.value){let b=x.value.panels.indexOf(r);return b===-1?x.value.panels.push(r)-1:b}return-1}),h=m(()=>{let b=l.panels.value.indexOf(s);return b===-1?S.value:b}),u=m(()=>h.value===l.selectedIndex.value);return()=>{var b;let $={selected:u.value},{tabIndex:P,...a}=e,n={ref:s,id:r,role:"tabpanel","aria-labelledby":(b=f(l.tabs.value[h.value]))==null?void 0:b.id,tabIndex:u.value?P:-1};return!u.value&&e.unmount&&!e.static?R(ae,{as:"span","aria-hidden":!0,...n}):N({ourProps:n,theirProps:a,slot:$,attrs:i,slots:v,features:Z.Static|Z.RenderStrategy,visible:u.value,name:"TabPanel"})}}});const ge={class:"w-[26rem] bg-gray-50 px-6 py-8 text-center lg:flex lg:shrink-0 lg:flex-col lg:justify-center lg:p-12"},he={class:"mt-8 text-lg leading-6 font-medium text-gray-900"},Ie={class:"mt-4 flex items-center justify-center text-4xl font-bold tracking-tight text-gray-900"},we={class:"mt-8"},Te=["href"],ke={key:1,class:"mx-auto flex items-center justify-center rounded-md border border-transparent bg-gray-800 px-5 py-2 text-base font-medium text-white shadow-sm hover:bg-gray-900",color:"black",href:"/subscribe/manage"},Be={__name:"SubscriptionTabs",setup(e){const i=T({Yearly:{name:"Yearly billing",price:40,cycle:"year",description:"Save over 30%",type:"yearly",active:!0,link:"subscribe/create?plan=yearly&prefilled_email="+W().props.user.email},Monthly:{name:"Monthly billing",price:5,cycle:"month",type:"monthly",description:"$60 over 12 months",active:!1,link:"subscribe/create?plan=monthly&prefilled_email="+W().props.user.email}});return(v,d)=>(_(),B("div",ge,[z(O(fe),null,{default:j(()=>[z(O(be),{class:"flex space-x-1 rounded-lg bg-gray-200 p-0.5"},{default:j(()=>[(_(!0),B(H,null,J(Object.keys(i.value),o=>(_(),Q(O(me),{key:o,as:"template"},{default:j(({selected:r})=>[A("button",{class:X(["w-full rounded-md py-2 text-sm leading-5 font-semibold","ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden",r?"bg-white text-gray-900 shadow-sm":"text-gray-700"])},C(o),3)]),_:2},1024))),128))]),_:1}),z(O(xe),{class:"mt-2"},{default:j(()=>[(_(!0),B(H,null,J(Object.values(i.value),(o,r)=>(_(),Q(O(ye),{key:r,class:X(["rounded-xl p-3","ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden"])},{default:j(()=>[A("p",he,C(o.description),1),A("div",Ie,[A("span",null," $"+C(o.price)+" / "+C(o.cycle),1),d[0]||(d[0]=A("span",{class:"ml-3 text-xl font-medium tracking-normal text-gray-500"}," USD ",-1))]),A("div",we,[O(W)().props.user.subscribed?(_(),B("a",ke,"Manage Subscription")):(_(),B("a",{key:0,class:"mx-auto flex items-center justify-center rounded-md border border-transparent bg-gray-800 px-5 py-2 text-base font-medium text-white shadow-sm hover:bg-gray-900",color:"black",href:o.link}," Get Access ",8,Te))])]),_:2},1024))),128))]),_:1})]),_:1})]))}};export{Be as default};
