import{e as x,l as E,p as j,i as O,o as n,c as N,w as f,b as d,a as t,t as i,d as a,h as A,g as p,f as h,u as r,n as z,F,T as et,j as c,k as B,q as v,v as g,W}from"./app-f0078ddb.js";import{_ as st}from"./AppLayout-33f062bc.js";import{_ as ot}from"./Breadcrumbs-c96e9207.js";import{_ as rt}from"./WordItem-d0f526f8.js";import{u as it}from"./useIntersect-6e15125e.js";import{_ as lt}from"./DropdownGeneral-ce7a4558.js";import nt from"./MobileFiltersCustom-8a300d59.js";import{P as at}from"./Promotion-3eee0057.js";import{_ as dt}from"./Footer-0988dcd8.js";import{_ as ct}from"./CopyToClipboard-21badf5d.js";import{_ as R}from"./ButtonItem-718c0517.js";import{r as M}from"./StarIcon-155a2a28.js";import{r as I}from"./CheckCircleIcon-d86d1232.js";import{S as ut,r as mt,M as vt,b as gt,g as ft}from"./ChevronDownIcon-660c32b0.js";import{r as pt}from"./FunnelIcon-14ad2bdb.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./use-resolve-button-type-24d8b5c5.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./Colosseum-0e8d62a4.js";import"./ChevronRightIcon-a926c707.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./listbox-f702e976.js";import"./use-text-value-2c18b2b1.js";import"./clipboard-a66b13b3.js";const xt={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},bt={class:"p-8"},yt={class:"mt-8 lg:hidden"},_t={class:"text-xl font-bold text-gray-900"},ht={class:"md:max-w-md lg:hidden"},wt={class:"mt-2 rounded-lg border border-gray-200 bg-white shadow-sm"},kt={key:0,class:"my-2 flex flex-row items-center"},Lt=["src"],Vt={class:"ml-2 flex grow flex-col"},Ut={class:"font-intro text-lg font-bold text-gray-900"},Ct={key:0},St={key:1},jt={class:"text-xs text-gray-600"},At={key:1,class:"my-2 flex flex-row items-center"},Ft={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},Wt={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},Mt={class:"ml-2 flex grow flex-col"},It={class:"font-intro text-lg font-bold text-gray-900"},Pt={class:"text-xs text-gray-600"},Tt={class:"grid grid-cols-2 divide-x divide-gray-200"},Et={class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"},Nt={key:0},zt={"aria-labelledby":"filter-heading",class:"mt-8 block border-t border-gray-200 py-6 lg:hidden"},Bt={class:"flex items-center justify-between"},$t={class:"py-1"},qt=["onClick"],Dt={class:"mt-8"},Ht={class:"grid grid-cols-1 divide-y"},Ot={key:1,class:"px-6 py-14 text-center text-sm sm:px-14"},Rt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},Gt={key:0,class:"mb-8"},Yt={class:"text-xl font-bold text-gray-900"},Jt={class:"mt-2 rounded-lg bg-white shadow-sm"},Kt={key:0,class:"my-2 flex flex-row items-center"},Qt=["src"],Xt={class:"ml-2 flex grow flex-col"},Zt={class:"font-intro text-lg font-bold text-gray-900"},te={key:0},ee={key:1},se={class:"text-xs text-gray-600"},oe={key:1,class:"my-2 flex flex-row items-center"},re={key:0,class:"flex h-12 w-12 items-center justify-center rounded-full bg-green-100 p-2 font-intro text-sm font-bold text-green-700"},ie={key:1,class:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 p-2 font-intro text-sm font-bold text-blue-700"},le={class:"ml-2 flex grow flex-col"},ne={class:"font-intro text-lg font-bold text-gray-900"},ae={class:"text-xs text-gray-600"},de={class:"grid grid-cols-2 divide-x divide-gray-200"},ce={class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"},ue={key:0},me={"aria-labelledby":"sort videos",class:"mt-8"},ve={"aria-labelledby":"filter videos",class:"mt-8"},ge={class:"grid grid-cols-1 divide-y"},fe={key:0,class:"ml-4 grid grid-cols-2 gap-3 py-4"},pe={class:"relative flex items-center"},xe={class:"flex h-5 items-center"},be={class:"ml-3 text-sm"},ye={for:"learned",class:"font-medium text-gray-700"},_e={class:"relative flex items-center"},he={class:"flex h-5 items-center"},we={class:"ml-3 text-sm"},ke={for:"unlearned",class:"font-medium text-gray-700"},Le={class:"relative flex items-center"},Ve={class:"flex h-5 items-center"},Ue={class:"ml-3 text-sm"},Ce={for:"starred",class:"font-medium text-gray-700"},Se={class:"relative flex items-center"},je={class:"flex h-5 items-center"},Ae={class:"ml-3 text-sm"},Fe={for:"unstarred",class:"font-medium text-gray-700"},We={key:1},Me={class:"mt-4"},Ie={class:"text-sm font-medium text-gray-600"},Pe={class:"grid grid-cols-1 divide-y"},Te={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Ee={class:"relative flex items-center"},Ne={class:"flex h-5 items-center"},ze={class:"relative flex items-center"},Be={class:"flex h-5 items-center"},$e={class:"relative flex items-center"},qe={class:"flex h-5 items-center"},De={class:"relative flex items-center"},He={class:"flex h-5 items-center"},Oe={class:"relative flex items-center"},Re={class:"flex h-5 items-center"},Ge={class:"relative flex items-center"},Ye={class:"flex h-5 items-center"},Je={class:"grid grid-cols-1 divide-y"},Ke={class:"ml-4 grid grid-cols-2 gap-3 py-4"},Qe={class:"relative flex items-center"},Xe={class:"flex h-5 items-center"},Ze={class:"relative flex items-center"},ts={class:"flex h-5 items-center"},es={class:"relative flex items-center"},ss={class:"flex h-5 items-center"},os={class:"relative flex items-center"},rs={class:"flex h-5 items-center"},is={class:"relative flex items-center"},ls={class:"flex h-5 items-center"},Zs={__name:"CustomList",props:{words:Object,filters:Array,learned:Array,starred:Array,sort:String,sections:Array,items:Array,clipboardList:Array},setup(k){const u=k;let U=x([]);u.items.forEach(l=>{l.vocabList?U.value.push(l.vocabList.id):U.value.push(l.sectionList.book_id+":"+parseInt(l.sectionList.start)+":"+parseInt(l.sectionList.end))});const P=x(null);let b=x(u.words),w=x(b.value.data);const G=E(()=>b.value.next_page_url!==null),T=x(!1);P!==null&&it(P,()=>{G.value&&axios.get(b.value.next_page_url).then(l=>{w.value=[...w.value,...l.data.data],b.value=l.data})},{rootMargin:"0px 0px 250px 0px"});const Y=()=>{b.value=u.words,w.value=b.value.data},J=[{name:"Words",href:"/words",current:!1},{name:"Custom List",href:"#",current:!0}];let L=x(u.learned),V=x(u.starred),y=[{name:"Relevance",value:"relevance"},{name:"Alphabetical",value:"alphabetical"}];const K=l=>y.find(e=>e.value==l);let m=x(u.sort?K(u.sort):y[0]);const $=E(()=>{var l="";return u.clipboardList.forEach(function(e){var C="";e.gender&&(C=", "+e.gender),l=l+e.latin+C+"	"+e.definition+`\r
`}),l});j(()=>m,l=>{W.get("/words/c/list",{sections:U.value,sort:l.value.value,filters:o.value},{preserveState:!0,replace:!0,preserveScroll:!0,only:["words","clipboardList"],onSuccess:()=>{Y()}})},{deep:!0}),j(()=>u.words,l=>{b.value=l,w.value=b.value.data},{deep:!0});const Q=l=>{L.value.some(e=>e==l.id)?L.value=L.value.filter(e=>e!=l.id):L.value.push(l.id)},X=l=>{V.value.some(e=>e==l.id)?V.value=V.value.filter(e=>e!=l.id):V.value.push(l.id)};let o=x(u.filters);const Z=()=>{o.value=[]};let q=E(()=>o.value.length>0);j(()=>o,()=>{W.get("/words/c/list",{sections:U.value,filters:o.value,sort:m.value.value},{only:["words","clipboardList"],replace:!0,onSuccess:()=>{},preserveState:!0,preserveScroll:!0})},{deep:!0}),j(()=>u.sort,()=>{u.sort&&(m.value=y.find(l=>l.value==u.sort))},{deep:!0});const D=()=>{W.get("/words/custom",{sections:u.sections})},H=()=>{W.get("/practice/vocabulary/attempt",{sections:u.sections,filters:o.value})};return(l,e)=>{const C=O("Head"),tt=O("Link");return n(),N(st,null,{default:f(()=>[d(C,null,{default:f(()=>e[27]||(e[27]=[t("title",null,"Words",-1)])),_:1}),t("main",xt,[t("div",bt,[d(ot,{class:"lg:col-span-9 xl:grid-cols-10",pages:J}),e[36]||(e[36]=t("section",{class:"mt-8 w-full","aria-labelledby":"Description"},[t("h1",{class:"text-4xl font-bold text-gray-900"},"Words"),t("p",{class:"mt-4 text-base font-normal text-gray-600"}," Custom list of words. ")],-1)),t("section",yt,[t("h5",_t,i(k.words.total)+" Total Words ",1)]),t("section",ht,[e[31]||(e[31]=t("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"}," Sections ",-1)),t("div",wt,[(n(!0),a(F,null,A(u.items,(s,_)=>(n(),a("div",{key:_,class:"h-20 w-full px-4 py-2"},[s.sectionList?(n(),a("div",kt,[t("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.sectionList.image_name,class:"h-12 w-12"},null,8,Lt),t("div",Vt,[t("h4",Ut,[p(i(s.sectionList.work)+" "+i(s.sectionList.l1)+" "+i(s.sectionList.book)+".",1),s.sectionList.start===s.sectionList.end?(n(),a("span",Ct,i(s.sectionList.start),1)):(n(),a("span",St,i(s.sectionList.start)+"-"+i(s.sectionList.end),1))]),t("p",jt,i(s.sectionList.word_count)+" words | "+i(s.sectionList.core_count)+" core, "+i(s.sectionList.word_count-s.sectionList.core_count)+" not core ",1)])])):h("",!0),s.vocabList?(n(),a("div",At,[e[28]||(e[28]=t("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),s.vocabList.id=="star"?(n(),a("div",Ft,[d(r(M),{class:"h-8 w-8 stroke-2"})])):s.vocabList.id=="learn"?(n(),a("div",Wt,[d(r(I),{class:"h-8 w-8 stroke-2"})])):(n(),a("div",{key:2,class:z(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${s.vocabList.icon_color}-300 text-${s.vocabList.icon_color}-700`])},i(s.vocabList.abbreviation),3)),t("div",Mt,[t("h4",It,i(s.vocabList.name),1),t("p",Pt,i(s.vocabList.word_count)+" words | "+i(s.vocabList.core_count)+" core, "+i(s.vocabList.word_count-s.vocabList.core_count)+" not core ",1)])])):h("",!0)]))),128)),t("div",Tt,[t("div",{class:"group mt-2 cursor-pointer rounded-bl-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:e[0]||(e[0]=s=>D())},e[29]||(e[29]=[t("div",{class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"}," Edit List ",-1)])),t("div",{class:"group mt-2 cursor-pointer rounded-br-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:e[1]||(e[1]=s=>H())},[t("div",Et,[e[30]||(e[30]=p(" Practice ")),r(q)?(n(),a("span",Nt,"+ Filters")):h("",!0)])])])])]),t("section",zt,[e[34]||(e[34]=t("h2",{id:"filter-heading",class:"sr-only"},"Word filters",-1)),t("div",Bt,[d(r(ft),{as:"div",class:"relative inline-block text-left"},{default:f(()=>[t("div",null,[d(r(ut),{class:"group inline-flex justify-center text-sm font-medium text-gray-700 hover:text-gray-900"},{default:f(()=>[e[32]||(e[32]=p(" Sort ")),d(r(mt),{class:"-mr-1 ml-1 h-5 w-5 shrink-0 text-gray-400 group-hover:text-gray-500","aria-hidden":"true"})]),_:1})]),d(et,{"enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:f(()=>[d(r(vt),{class:"absolute left-0 z-10 mt-2 w-40 origin-top-left rounded-md bg-white shadow-2xl focus:outline-hidden"},{default:f(()=>[t("div",$t,[(n(!0),a(F,null,A(r(y),s=>(n(),N(r(gt),{key:s},{default:f(({active:_})=>[t("a",{onClick:S=>c(m)?m.value=s:m=s,class:z([_?"bg-gray-100":"","block cursor-pointer px-4 py-2 text-sm font-medium text-gray-900"])},i(s.name),11,qt)]),_:2},1024))),128))])]),_:1})]),_:1})]),_:1}),d(R,{color:"white",size:"xs",onClick:e[2]||(e[2]=s=>T.value=!0)},{default:f(()=>[d(r(pt),{class:"mr-1 inline h-4 w-4 stroke-2"}),e[33]||(e[33]=p(" Filters "))]),_:1})])]),d(nt,{show:T.value,array:r(o),"clipboard-list":$.value,words:k.words,"sort-list":r(y),"current-sort":r(m),sections:k.sections,"onUpdate:array":e[3]||(e[3]=s=>c(o)?o.value=s:o=s),"onUpdate:sort":e[4]||(e[4]=s=>c(m)?m.value=s:m=s),onClose:e[5]||(e[5]=s=>T.value=!1),"onUpdate:query":e[6]||(e[6]=s=>l.query=s)},null,8,["show","array","clipboard-list","words","sort-list","current-sort","sections"]),t("section",Dt,[t("div",Ht,[r(w).length>0?(n(!0),a(F,{key:0},A(r(w),(s,_)=>(n(),N(rt,{class:"hover:bg-slate-50 lg:px-4",key:_,word:s,authenticated:r(B)().props.authenticated,"learned-array":r(L),"starred-array":r(V),"onUpdate:toggleLearned":e[7]||(e[7]=S=>Q(S)),"onUpdate:toggleStarred":e[8]||(e[8]=S=>X(S))},null,8,["word","authenticated","learned-array","starred-array"]))),128)):(n(),a("div",Ot,e[35]||(e[35]=[t("p",{class:"mt-4 font-semibold text-gray-900"},"No results found",-1),t("p",{class:"mt-2 text-gray-500"}," We couldn’t find anything with that term. Please try again. ",-1)])))]),t("div",{ref_key:"landmark",ref:P},null,512)])]),d(dt)]),t("aside",Rt,[r(B)().props.authenticated?h("",!0):(n(),a("section",Gt,[d(at)])),t("section",null,[t("h5",Yt,i(k.words.total)+" Total Words ",1)]),t("section",null,[e[40]||(e[40]=t("h5",{class:"mt-8 text-sm font-bold text-gray-500 uppercase"},"Sections",-1)),t("div",Jt,[(n(!0),a(F,null,A(u.items,(s,_)=>(n(),a("div",{key:_,class:"h-20 w-full px-4 py-2"},[s.sectionList?(n(),a("div",Kt,[t("img",{src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/"+s.sectionList.image_name,class:"h-12 w-12"},null,8,Qt),t("div",Xt,[t("h4",Zt,[p(i(s.sectionList.work)+" "+i(s.sectionList.l1)+" "+i(s.sectionList.book)+".",1),s.sectionList.start===s.sectionList.end?(n(),a("span",te,i(s.sectionList.start),1)):(n(),a("span",ee,i(s.sectionList.start)+"-"+i(s.sectionList.end),1))]),t("p",se,i(s.sectionList.word_count)+" words | "+i(s.sectionList.core_count)+" core, "+i(s.sectionList.word_count-s.sectionList.core_count)+" not core ",1)])])):h("",!0),s.vocabList?(n(),a("div",oe,[e[37]||(e[37]=t("div",{class:"hidden from-blue-500 from-orange-500 from-rose-500 to-blue-600 to-orange-600 to-rose-600 opacity-0"},null,-1)),s.vocabList.id=="star"?(n(),a("div",re,[d(r(M),{class:"h-8 w-8 stroke-2"})])):s.vocabList.id=="learn"?(n(),a("div",ie,[d(r(I),{class:"h-8 w-8 stroke-2"})])):(n(),a("div",{key:2,class:z(["flex h-12 w-12 items-center justify-center rounded-full p-2 font-intro text-sm font-bold",`bg-${s.vocabList.icon_color}-300 text-${s.vocabList.icon_color}-700`])},i(s.vocabList.abbreviation),3)),t("div",le,[t("h4",ne,i(s.vocabList.name),1),t("p",ae,i(s.vocabList.word_count)+" words | "+i(s.vocabList.core_count)+" core, "+i(s.vocabList.word_count-s.vocabList.core_count)+" not core ",1)])])):h("",!0)]))),128)),t("div",de,[t("div",{class:"group mt-2 cursor-pointer rounded-bl-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:e[9]||(e[9]=s=>D())},e[38]||(e[38]=[t("div",{class:"cursor-pointer text-center text-sm font-semibold text-gray-600 transition duration-150 ease-in-out group-hover:text-gray-700"}," Edit List ",-1)])),t("div",{class:"group mt-2 cursor-pointer rounded-br-lg bg-gray-100 px-4 py-2 transition duration-150 ease-in-out hover:bg-gray-200",onClick:e[10]||(e[10]=s=>H())},[t("div",ce,[e[39]||(e[39]=p(" Practice ")),r(q)?(n(),a("span",ue,"+ Filters")):h("",!0)])])])])]),t("section",me,[d(lt,{modelValue:r(m),"onUpdate:modelValue":e[11]||(e[11]=s=>c(m)?m.value=s:m=s),current:r(m)?r(m):r(y)[0].value.value,title:"Sort",list:r(y)},null,8,["modelValue","current","list"])]),t("section",ve,[t("div",null,[t("div",{class:"flex text-sm"},[e[41]||(e[41]=t("div",{class:"flex-1 text-left"},[t("h4",{class:"font-bold text-gray-500 uppercase"},"Filters")],-1)),t("div",{class:"flex-1 text-right"},[t("button",{class:"font-bold text-blue-500 uppercase opacity-100",onClick:Z}," Clear ")])]),t("div",ge,[r(B)().props.authenticated?(n(),a("div",fe,[t("div",pe,[t("div",xe,[v(t("input",{id:"learned","onUpdate:modelValue":e[12]||(e[12]=s=>c(o)?o.value=s:o=s),value:"learned",name:"learned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),t("div",be,[t("label",ye,[d(r(I),{class:"h-6 w-6 stroke-2 text-blue-500","aria-hidden":"true"})])])]),t("div",_e,[t("div",he,[v(t("input",{id:"unlearned","onUpdate:modelValue":e[13]||(e[13]=s=>c(o)?o.value=s:o=s),value:"unlearned",name:"unlearned",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),t("div",we,[t("label",ke,[d(r(I),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])]),t("div",Le,[t("div",Ve,[v(t("input",{id:"starred","onUpdate:modelValue":e[14]||(e[14]=s=>c(o)?o.value=s:o=s),value:"starred",name:"starred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),t("div",Ue,[t("label",Ce,[d(r(M),{class:"h-6 w-6 stroke-2 text-green-600","aria-hidden":"true"})])])]),t("div",Se,[t("div",je,[v(t("input",{id:"unstarred","onUpdate:modelValue":e[15]||(e[15]=s=>c(o)?o.value=s:o=s),value:"unstarred",name:"unstarred",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),t("div",Ae,[t("label",Fe,[d(r(M),{class:"h-6 w-6 stroke-2 text-gray-500","aria-hidden":"true"})])])])])):(n(),a("div",We,[t("div",Me,[t("p",Ie,[e[43]||(e[43]=p(" Please ")),d(tt,{class:"text-blue-600 hover:text-blue-700 hover:underline",href:"/login"},{default:f(()=>e[42]||(e[42]=[p("login or make an account ")])),_:1}),e[44]||(e[44]=p(" to access more tools with vocabulary. "))])])]))]),t("div",Pe,[t("div",Te,[t("div",Ee,[t("div",Ne,[v(t("input",{id:"basic","onUpdate:modelValue":e[16]||(e[16]=s=>c(o)?o.value=s:o=s),value:"basic",name:"basic",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[45]||(e[45]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"basic",class:"font-semibold text-emerald-600"},"basic ")],-1))]),t("div",ze,[t("div",Be,[v(t("input",{id:"intermediate","onUpdate:modelValue":e[17]||(e[17]=s=>c(o)?o.value=s:o=s),value:"intermediate",name:"intermediate",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[46]||(e[46]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"intermediate",class:"font-semibold text-blue-600"},"intermediate ")],-1))]),t("div",$e,[t("div",qe,[v(t("input",{id:"advanced","onUpdate:modelValue":e[18]||(e[18]=s=>c(o)?o.value=s:o=s),value:"advanced",name:"advanced",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[47]||(e[47]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"advanced",class:"font-semibold text-orange-600"},"advanced ")],-1))]),t("div",De,[t("div",He,[v(t("input",{id:"uncommon","onUpdate:modelValue":e[19]||(e[19]=s=>c(o)?o.value=s:o=s),value:"uncommon",name:"uncommon",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[48]||(e[48]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"uncommon",class:"font-semibold text-violet-600"},"uncommon ")],-1))]),t("div",Oe,[t("div",Re,[v(t("input",{id:"core","onUpdate:modelValue":e[20]||(e[20]=s=>c(o)?o.value=s:o=s),value:"core",name:"core",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[49]||(e[49]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"core",class:"font-semibold text-gray-600"},"core ")],-1))]),t("div",Ge,[t("div",Ye,[v(t("input",{id:"noncore","onUpdate:modelValue":e[21]||(e[21]=s=>c(o)?o.value=s:o=s),value:"noncore",name:"noncore",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[50]||(e[50]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"noncore",class:"font-semibold text-gray-600"},"non-core ")],-1))])])]),t("div",Je,[t("div",Ke,[t("div",Qe,[t("div",Xe,[v(t("input",{id:"nouns","onUpdate:modelValue":e[22]||(e[22]=s=>c(o)?o.value=s:o=s),value:"nouns",name:"nouns",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[51]||(e[51]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"nouns",class:"font-semibold text-gray-600"},"nouns ")],-1))]),t("div",Ze,[t("div",ts,[v(t("input",{id:"verbs","onUpdate:modelValue":e[23]||(e[23]=s=>c(o)?o.value=s:o=s),value:"verbs",name:"verbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[52]||(e[52]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"verbs",class:"font-semibold text-gray-600"},"verbs ")],-1))]),t("div",es,[t("div",ss,[v(t("input",{id:"adjectives","onUpdate:modelValue":e[24]||(e[24]=s=>c(o)?o.value=s:o=s),value:"adjectives",name:"adjectives",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[53]||(e[53]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"adjectives",class:"font-semibold text-gray-600"},"adjectives ")],-1))]),t("div",os,[t("div",rs,[v(t("input",{id:"adverbs","onUpdate:modelValue":e[25]||(e[25]=s=>c(o)?o.value=s:o=s),value:"adverbs",name:"adverbs",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[54]||(e[54]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"adverbs",class:"font-semibold text-gray-600"},"adverbs ")],-1))]),t("div",is,[t("div",ls,[v(t("input",{id:"other","onUpdate:modelValue":e[26]||(e[26]=s=>c(o)?o.value=s:o=s),value:"other",name:"other",type:"checkbox",class:"h-4 w-4 rounded-sm border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[g,r(o)]])]),e[55]||(e[55]=t("div",{class:"ml-3 text-sm"},[t("label",{for:"other",class:"font-semibold text-gray-600"},"other ")],-1))])])])])]),d(ct,{data:$.value,message:"Copied",class:"mt-8 w-full"},{default:f(()=>[d(R,{size:"sm",color:"lightGray",class:"w-full"},{default:f(()=>e[56]||(e[56]=[p("Copy This List to Your Clipboard ")])),_:1})]),_:1},8,["data"])])]),_:1})}}};export{Zs as default};
