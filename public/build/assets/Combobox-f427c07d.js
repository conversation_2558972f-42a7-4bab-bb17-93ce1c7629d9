import{i as k,n as S,u as N,r as B,l as I}from"./combobox-4401f443.js";import{r as L}from"./ChevronUpDownIcon-aff937c8.js";import{r as F}from"./CheckIcon-4bbdc2ab.js";import{e as c,l as g,o,d as m,b as r,w as i,a as f,u as t,j as p,f as x,F as A,h as j,c as q,n as y,t as z}from"./app-f0078ddb.js";import{S as D}from"./transition-a0923044.js";const E={class:"w-80"},R={class:"relative mt-1"},T={class:"relative w-full cursor-default overflow-hidden rounded-lg border border-gray-300 bg-white text-left focus:outline-hidden focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-orange-300 sm:text-sm"},U={key:0,class:"relative cursor-default px-4 py-2 text-gray-700 select-none"},G={class:"block truncate font-medium"},P={__name:"Combobox",props:{items:Array,color:{type:String,default:"indigo"},defaultItem:[String,Number]},emits:["update:modelValue"],setup(b,{emit:h}){const s=b;let n=s.defaultItem?c(s.items.find(u=>u.id===s.defaultItem)):c({name:"Select an activity",id:null}),a=c("");const w=h,_=g(()=>({indigo:"bg-indigo-500 text-white",orange:"bg-orange-500 text-white",emerald:"bg-emerald-500 text-white"})[s.color]),V=g(()=>({indigo:"text-indigo-600",orange:"text-orange-600",emerald:"text-emerald-600"})[s.color]);let v=g(()=>a.value===""?s.items:s.items.filter(u=>u.name.toLowerCase().replace(/\s+/g,"").includes(a.value.toLowerCase().replace(/\s+/g,""))));return(u,l)=>(o(),m("div",E,[r(t(I),{modelValue:t(n),"onUpdate:modelValue":[l[2]||(l[2]=e=>p(n)?n.value=e:n=e),l[3]||(l[3]=e=>w("update:modelValue",e))]},{default:i(()=>[f("div",R,[f("div",T,[r(t(k),{class:"w-full border-none py-2 pr-10 pl-3 text-sm leading-5 text-gray-900 focus:ring-0",displayValue:e=>e.name,onChange:l[0]||(l[0]=e=>p(a)?a.value=e.target.value:a=e.target.value)},null,8,["displayValue"]),r(t(S),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:i(()=>[r(t(L),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),r(t(D),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:l[1]||(l[1]=e=>p(a)?a.value="":a="")},{default:i(()=>[r(t(N),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg focus:outline-hidden sm:text-sm"},{default:i(()=>[t(v).length===0&&t(a)!==""?(o(),m("div",U," Nothing found. ")):x("",!0),(o(!0),m(A,null,j(t(v),e=>(o(),q(t(B),{as:"template",key:e.id,value:e},{default:i(({selected:C,active:d})=>[f("li",{class:y(["relative cursor-pointer py-2 pr-4 pl-10 text-left select-none",{[_.value]:d,"text-gray-900":!d}])},[f("span",G,z(e.name),1),C?(o(),m("span",{key:0,class:y(["absolute inset-y-0 left-0 flex items-center pl-3",{"text-white":d,[V.value]:!d}])},[r(t(F),{class:"h-5 w-5","aria-hidden":"true"})],2)):x("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]))}};export{P as _};
