import{o as n,d as r,a as l,e as p,A as U,p as D,l as Q,r as j,F as A,h as V,b as i,w as x,u,T as R,z as _,t as y,E as h,n as H,g as B}from"./app-f0078ddb.js";import{d as M}from"./vuedraggable.umd-aab17b5c.js";import{_ as S}from"./ButtonItem-718c0517.js";import L from"./NextButtonSmall-9e6ffefc.js";import{_ as G}from"./_plugin-vue_export-helper-c27b6911.js";import{r as J}from"./CheckCircleIcon-d86d1232.js";import{r as O}from"./XCircleIcon-63af2b2a.js";/* empty css            */function z(d,g){return n(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 17.25 12 21m0 0-3.75-3.75M12 21V3"})])}function I(d,g){return n(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[l("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17.25 8.25 21 12m0 0-3.75 3.75M21 12H3"})])}const P={class:"space-y-6"},X={class:"text-center"},Y={class:"mt-4 text-center text-3xl font-semibold whitespace-pre-line text-gray-900"},Z={class:"text-center text-lg font-medium text-gray-600"},ee={key:0,class:"mx-auto w-full max-w-xl space-y-4"},te=["onClick"],se=["onClick"],oe={key:1,class:"mx-auto w-full max-w-xl space-y-4"},le={class:"text-center"},ae={key:0,class:"-mt-16"},ne={key:0,class:"flex items-center justify-center text-blue-500"},re={key:1,class:"flex items-center justify-center text-red-600"},ie={key:1,class:"grid grid-cols-2 gap-4"},de={__name:"Matching",props:{options:{type:Array,required:!0},isAnswer:{type:Boolean,default:!1},isCorrect:{type:Boolean,default:!1},userAttempt:{type:[Array,String],default:()=>""},disabled:{type:Boolean,default:!0},isVocabulary:{type:Boolean,default:!1}},emits:["submit","next-question","i-know-this"],setup(d,{emit:g}){const f=d,w=g,c=p(f.options.map(o=>o.split(":")[0])),m=p(f.options.map(o=>({value:o.split(":")[1]}))),a=p(c.value.map(()=>[])),b=p("auto"),C=()=>{_(()=>{const o=document.querySelectorAll(".draggable-key");let e=0;o.forEach(s=>{const t=s.getBoundingClientRect().width;t>e&&(e=t)}),b.value=`${e+35}px`})},k=p("auto"),$=()=>{_(()=>{const o=document.querySelectorAll(".stem-text");let e=0;o.forEach(s=>{const t=s.getBoundingClientRect().width;t>e&&(e=t)}),k.value=`${e+24}px`})};U(()=>{C(),$()}),D(()=>f.options,o=>{c.value=o.map(e=>e.split(":")[0]),m.value=o.map(e=>({value:e.split(":")[1]})),a.value=c.value.map(()=>[]),_(()=>{C(),$()})});const E=()=>{const o=a.value.map((e,s)=>{var t;return{stem:c.value[s],key:((t=e[0])==null?void 0:t.value)||null}});w("submit",{answer:o.map(e=>`${e.stem}:${e.key}`).join("|"),key:f.options.map(e=>{const[s,t]=e.split(":");return`${s}:${t}`}).join("|"),isArray:!1})},q=()=>w("next-question"),K=()=>w("i-know-this"),T=Q(()=>{const o={};return c.value.forEach((e,s)=>{var t;o[e]=((t=a.value[s][0])==null?void 0:t.value)||null}),f.options.map(e=>{const[s,t]=e.split(":"),v=o[s]||null;return{stem:s,correctKey:t,userKey:v,isCorrect:v===t}})}),N=o=>{const e=a.value.findIndex(s=>s.some(t=>t.value===o.value));if(e!==-1)a.value[e]=[],m.value.push(o);else{const s=a.value.findIndex(t=>t.length===0);s!==-1&&(a.value[s]=[o],m.value=m.value.filter(t=>t.value!==o.value))}},W=o=>{const e=a.value[o][0];e&&(m.value.push(e),a.value[o]=[])};return(o,e)=>(n(),r("div",P,[l("div",X,[l("h1",Y,[j(o.$slots,"stem",{},void 0,!0)]),l("h3",Z,[j(o.$slots,"instructions",{},void 0,!0)])]),d.isAnswer?(n(),r("div",oe,[(n(!0),r(A,null,V(T.value,(s,t)=>(n(),r("div",{key:t,class:"flex flex-col items-center justify-around sm:flex-row"},[l("div",{class:"stem-text rounded-sm px-4 text-center text-xl font-semibold sm:py-2 sm:text-right",style:h({width:k.value})},y(s.stem),5),i(u(I),{class:"mr-4 hidden w-12 text-gray-500 sm:inline-block"}),i(u(z),{class:"h-10 text-gray-500 sm:hidden"}),l("div",{style:h({width:b.value}),class:"flex h-12 items-center justify-around rounded border border-dashed border-white px-2 sm:justify-start"},[l("div",{class:H(["pointer-events-none inline-flex h-10 items-center rounded-full border-2 px-3 text-lg font-medium text-gray-800 shadow-lg duration-150 select-none",s.isCorrect?"border-teal-400 bg-teal-100":"border-red-400 bg-red-100"])},y(s.correctKey),3)],4)]))),128)),e[4]||(e[4]=l("div",{class:"border-t py-4"},null,-1)),e[5]||(e[5]=l("div",{class:"h-24"},null,-1))])):(n(),r("div",ee,[(n(!0),r(A,null,V(c.value,(s,t)=>(n(),r("div",{key:t,class:"flex flex-col items-center justify-around sm:flex-row"},[l("div",{class:"stem-text rounded-sm px-4 text-center text-xl font-semibold sm:py-2 sm:text-right",style:h({width:k.value})},y(s),5),i(u(I),{class:"mr-4 hidden w-12 text-gray-500 sm:inline-block"}),i(u(z),{class:"h-10 text-gray-500 sm:hidden"}),i(u(M),{modelValue:a.value[t],"onUpdate:modelValue":v=>a.value[t]=v,group:{name:"matching",pull:!0,put:(v,F,ue)=>a.value[t].length===0},"item-key":"value",class:"flex h-12 items-center justify-around rounded-xl border border-dashed border-gray-400 px-2 sm:justify-start",style:h({width:b.value})},{item:x(({element:v})=>[l("div",{class:"inline-flex h-10 cursor-move items-center justify-around rounded-full border-2 border-emerald-400 bg-emerald-100 px-3 text-lg font-medium text-gray-800 shadow-lg transition-colors transition-shadow duration-150 select-none hover:bg-emerald-200 sm:justify-start",onClick:F=>W(t)},y(v.value),9,te)]),_:2},1032,["modelValue","onUpdate:modelValue","group","style"])]))),128)),e[3]||(e[3]=l("div",{class:"border-t py-4"},null,-1)),i(u(M),{modelValue:m.value,"onUpdate:modelValue":e[0]||(e[0]=s=>m.value=s),group:{name:"matching",pull:!0,put:!0},"item-key":"value",class:"flex h-24 w-full items-start justify-center gap-2","ghost-class":"ghost",animation:200,disabled:d.isAnswer},{item:x(({element:s})=>[l("div",{class:"draggable-key inline-flex h-10 cursor-move items-center rounded-full border-2 border-emerald-400 bg-emerald-100 px-3 text-lg font-medium text-gray-800 shadow-lg transition-colors transition-shadow duration-150 select-none hover:bg-emerald-200",onClick:t=>N(s)},y(s.value),9,se)]),_:1},8,["modelValue","disabled"])])),l("div",le,[i(R,{mode:"out-in","enter-active-class":"transition duration-150 ease-out","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition duration-150 ease-in","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:x(()=>[d.isAnswer?(n(),r("div",ae,[d.isCorrect?(n(),r("div",ne,[i(u(J),{class:"mr-2 h-10 w-10"}),e[6]||(e[6]=l("span",{class:"text-2xl font-semibold"},"Correct!",-1))])):(n(),r("div",re,[i(u(O),{class:"mr-2 h-10 w-10"}),e[7]||(e[7]=l("span",{class:"text-2xl font-semibold"},"Incorrect",-1))])),i(L,{"is-answer":d.isAnswer,"is-correct":d.isCorrect,disabled:d.disabled,"is-vocabulary":d.isVocabulary,onNextQuestion:e[1]||(e[1]=s=>q()),onIKnowThis:K},null,8,["is-answer","is-correct","disabled","is-vocabulary"])])):(n(),r("div",ie,[i(S,{class:"w-full",size:"lg",color:"white",onClick:e[2]||(e[2]=s=>a.value=c.value.map(()=>[]))},{default:x(()=>e[8]||(e[8]=[B(" Clear ")])),_:1}),i(S,{class:"w-full",size:"lg",color:"pink",onClick:E},{default:x(()=>e[9]||(e[9]=[B(" Submit ")])),_:1})]))]),_:1})])]))}},ge=G(de,[["__scopeId","data-v-12d367cf"]]);export{ge as default};
