import{o as l,d as n,a as e,e as x,V as W,p as Z,i as q,a1 as de,c as D,w as i,b as a,t as v,j as c,u as m,n as f,g as r,f as p,F as M,h as S,q as Y,x as J,T as me,Q as ce,W as B}from"./app-f0078ddb.js";import{_ as ue}from"./AppLayout-33f062bc.js";import{_ as fe}from"./Breadcrumbs-c96e9207.js";import{_ as N}from"./ClassroomModal-05d10768.js";import ge from"./TabComponent-9cc7727e.js";import K from"./AssignmentItemOwner-5f5b8c19.js";import ve from"./StudentList-d4c36752.js";import z from"./AssignModule-74fae4e9.js";import xe from"./AssignReading-df4942fb.js";import{u as be}from"./useInfiniteScroll-1e8e8e17.js";import{_ as Q}from"./ButtonItem-718c0517.js";import pe from"./PricingComponent-817e2f4a.js";import{D as V}from"./datetime-8ddd27a0.js";import{_ as he}from"./Footer-0988dcd8.js";import{I as ye}from"./InfinitasIcon-1a3ae135.js";import{_ as U}from"./FormSection-45b6b921.js";import{r as we}from"./RectangleGroupIcon-04390470.js";import{r as ke}from"./QueueListIcon-824b634b.js";import{r as Ce}from"./PlayCircleIcon-8bd12a30.js";import{r as $e}from"./TrashIcon-31936c3d.js";import{r as Ae}from"./AcademicCapIcon-c9b1e0d5.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./BookOpenIcon-1746d343.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./ProgressBar-b7203293.js";import"./UtilityDropdown-2d786282.js";import"./CopyToClipboard-21badf5d.js";import"./clipboard-a66b13b3.js";import"./RectangleGroupIcon-c6b3f31f.js";import"./SelectDate-fab0bbcb.js";import"./index-b0adb136.js";import"./Combobox-f427c07d.js";import"./ChevronUpDownIcon-aff937c8.js";import"./CheckIcon-4bbdc2ab.js";import"./DropdownWorks-53c01eb4.js";import"./listbox-f702e976.js";import"./DropdownBooks-b7db1f80.js";import"./XCircleIcon-63af2b2a.js";import"./useIntersect-6e15125e.js";import"./ChevronLeftIcon-2a41c533.js";import"./ChevronRightIcon-0e7ec64c.js";import"./radio-group-97521e36.js";import"./label-6c8c1cbc.js";import"./SectionTitle-05f6d081.js";function je(s,u){return l(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m8.25 3v6.75m0 0-3-3m3 3 3-3M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}function Me(s,u){return l(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z"})])}function Se(s,u){return l(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 3.75v4.5m0-4.5h4.5m-4.5 0L9 9M3.75 20.25v-4.5m0 4.5h4.5m-4.5 0L9 15M20.25 3.75h-4.5m4.5 0v4.5m0-4.5L15 9m5.25 11.25h-4.5m4.5 0v-4.5m0 4.5L15 15"})])}const Le={class:"py-2 md:py-8"},Te={class:"flex flex-row items-center justify-between"},De={class:"py-6"},Ie={class:"sm:flex sm:items-center sm:justify-between"},Oe={class:"w-full items-center sm:flex sm:space-x-5"},Be={class:"flex shrink-0 items-center"},Ne=["src","alt"],Ve={class:"flex w-full flex-col sm:mt-0 sm:pt-1 sm:text-left"},Ee={class:"flex w-full items-end"},ze={class:"inline-block text-4xl font-bold text-gray-900 sm:text-5xl"},He={key:0,class:"mt-2 flex"},Fe={key:1,class:"mt-2 flex gap-2"},Re={class:"inline text-3xl font-medium text-gray-600"},Ye={class:"ml-1 inline-block cursor-pointer self-center text-sm font-medium text-gray-500 transition duration-150 ease-in-out hover:text-gray-600"},Qe={key:0},Ue={key:1},Ge={class:"mt-2 grid grid-cols-1 gap-2 sm:grid-cols-2 xl:grid-cols-7 xl:gap-8"},Pe={class:"col-span-2 grid grid-cols-1 grid-rows-2 gap-2"},We={class:"flex w-full flex-row items-center justify-center"},Ze={class:"flex w-full flex-row items-center justify-center"},qe={class:"flex items-center space-x-2"},Je={class:"col-span-2 grid grid-cols-1 grid-rows-2 gap-2"},Ke={class:"flex w-full flex-row items-center justify-center"},Xe={class:"flex w-full flex-row items-center justify-center"},_e={key:0},et={key:0,class:"mt-8 text-xs leading-6 font-semibold text-gray-500 uppercase"},tt={class:"mt-4"},st={key:1,class:"mt-8 text-xs leading-6 font-semibold text-gray-500 uppercase"},ot={key:2},lt={key:0},it={class:"rounded-xl py-20 text-white dark:bg-slate-900"},nt={class:"md:text-center"},at={class:"mt-4 text-lg text-slate-500"},rt={class:"text-xl font-medium text-blue-600"},dt={key:1,class:"rounded-xl py-20 text-white dark:bg-slate-900"},mt={class:"md:text-center"},ct={key:0},ut={class:"mt-8 rounded-xl py-20 text-white sm:pt-12 sm:pb-32 dark:bg-slate-900"},ft={class:"md:text-center"},gt={class:"mt-4 text-lg text-slate-500"},vt={class:"text-xl font-medium text-blue-600"},xt={class:"mt-8 flow-root"},bt={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},pt={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8"},ht={class:"min-w-full divide-y divide-gray-300"},yt={scope:"col",class:"mb-2 min-w-36 px-3 pt-3.5 text-base font-semibold text-gray-900"},wt={class:"flex h-16 w-full flex-col"},kt={class:"text-xs text-gray-500"},Ct=["href"],$t={class:"divide-y divide-gray-200"},At={class:"sticky left-0 z-10 min-w-64 bg-white py-4 pr-3 pl-4 whitespace-nowrap sm:pl-0"},jt={class:"flex items-center gap-x-4"},Mt=["src"],St={class:"py-4 text-sm font-medium whitespace-nowrap text-gray-900"},Lt={class:"md:grid md:grid-cols-1 md:gap-6"},Tt={class:"flex items-center justify-end rounded-br-xl rounded-bl-xl bg-slate-100 px-4 py-3 text-right sm:px-6"},Dt={class:"grid grid-cols-2 gap-4 lg:grid-cols-4"},It={class:"mt-1"},Ot=["disabled"],Bt={key:0,class:"mt-1 text-left text-sm font-medium text-red-600"},Nt={class:"col-span-6 items-center px-4 py-3 sm:px-6"},Vt={class:"mt-1 grid grid-cols-5 items-center justify-center gap-4"},Et=["src","onClick"],zt={class:"col-span-6"},Ht={class:"float-left mt-1 text-4xl"},Ft={key:0},Rt={key:1,class:"mx-auto"},Yt={class:"col-span-6"},Qt={class:"mt-4 flex gap-4"},Ut={key:0},Gt={key:1},Pt={key:0},Wt={key:1},Zt={key:0},qt={key:1},Jt={key:0},Kt={key:1},Xt={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100"},_t={class:"mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-teal-100"},es={class:"grid grid-cols-1 gap-4"},ts={class:"mt-1"},ss={key:0,class:"mt-1 text-left text-sm font-medium text-red-600"},os={class:"mt-1 grid grid-cols-5 items-center justify-center gap-4"},ls=["src","onClick"],is={class:"text-6xl font-bold lg:text-8xl"},ns={class:"text-base leading-7 font-semibold text-indigo-600"},as={class:"mx-auto mt-8 max-w-7xl px-6 lg:px-8"},wo={__name:"Owner",props:{team:Object,students:Array,assignments:Object,scheduledAssignments:Array,classes:Array,errors:Object,grammarActivities:Array,vocabActivities:Array,works:Array,books:Array,videos:Array,tab:[Array,Object]},setup(s){const u=s,G=[{name:"Classes",href:"/classes",current:!1},{name:u.team.name,href:"#",current:!0}],H=x(null);let F=x(u.assignments||{data:[],next_page_url:null});const{items:L}=be(F.value,H),b=W({name:u.team.name,slug:u.team.slug,icon:u.team.photo_url,default_time:u.team.default_time?V.fromISO(u.team.default_time):new Date});let T=x(!1),R=x(!1),y=x(!1),I=x(!1);const X=()=>T.value=!1,_=function(){B.put("/api/archive-classroom",{slug:u.team.slug},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{setTimeout(()=>{T.value=!1},400)}})},ee=function(){B.delete(u.team.slug)},te=()=>{I.value=!1},se=()=>{R.value=!1},w=W({});function P(){B.put("/classes/update/",{form:b},{preserveState:!0,preserveScroll:!0,errorBag:"updateTeam",onSuccess:()=>{R.value=!1,Object.keys(w).forEach(g=>{w[g]=null})},onError:g=>{Object.keys(g).forEach(t=>{w[t]=g[t]})}})}Z(()=>u.team.name,g=>{Object.assign(G,[{name:"Classrooms",href:"/classrooms",current:!1},{name:g,href:"#",current:!0}])}),Z(()=>u.assignments,g=>{F.value=g||{data:[],next_page_url:null},L.value=F.value.data},{deep:!0});let k=x(!1),C=x(!1),$=x(!1),A=x(!1),d=x(!1),j=x(!1);const oe=(g,t)=>{if(g.length===0)return"bg-gray-300";const E=g[0].completed_at;return E?le(E,t)?"bg-green-500":"bg-amber-500":"bg-sky-500"},le=(g,t)=>V.fromISO(g).ts<=V.fromISO(t).ts,O=x(!1),ie=()=>{O.value=!0,B.put("/api/cycle-enrollment-code",{slug:u.team.slug},{preserveState:!0,preserveScroll:!0,onSuccess:()=>{B.reload({only:["team"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{O.value=!1}})}})};return(g,t)=>{const E=q("Head"),ne=q("Link"),ae=de("tippy");return l(),D(ue,null,{default:i(()=>[a(E,null,{default:i(()=>[e("title",null,v(s.team.name),1)]),_:1}),e("div",{class:f(["relative isolate bg-white px-6 lg:px-8",{grayscale:s.team.archived==1}])},[e("div",Le,[e("div",Te,[a(fe,{class:"lg:col-span-9 xl:grid-cols-10",pages:G})])]),e("section",null,[e("div",De,[e("div",Ie,[e("div",Oe,[e("div",Be,[e("img",{class:"mx-auto h-24 w-24 rounded-full",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/${s.team.photo_url}`,alt:s.team.name},null,8,Ne)]),e("div",Ve,[e("div",Ee,[e("h5",ze,v(s.team.name),1)]),s.team.archived==1?(l(),n("div",He,t[19]||(t[19]=[e("span",{class:"inline-block text-3xl font-medium text-gray-500"}," Archived ",-1)]))):(l(),n("div",Fe,[e("div",Re,v(s.team.enrollment_code),1),e("span",Ye,[a(m(Se),{class:"h-5 w-5",onClick:t[0]||(t[0]=o=>c(y)?y.value=!0:y=!0)})]),s.team.subscription.is_subscribed?(l(),n("span",{key:0,class:f(["ml-4 self-center rounded-full px-4 py-1 text-center text-sm font-bold",s.team.subscription.tier==="single-class"?"bg-blue-100 text-blue-600":"bg-slate-900 text-white"])},[s.team.subscription.tier==="single-class"?(l(),n("span",Qe,"Class access until ")):(l(),n("span",Ue,"Full access until ")),r(" "+v(m(V).fromSQL(s.team.subscription.ends_at).toFormat("MMMM dd, yyyy")),1)],2)):p("",!0)]))])])])])]),e("section",null,[a(ge,{"current-url":`/classes/${s.team.slug}`,"current-tab":s.tab.tab},{tab1:i(()=>t[20]||(t[20]=[r(" Assignments ")])),content1:i(()=>[t[33]||(t[33]=e("div",{class:"text-xs leading-6 font-semibold text-gray-500 uppercase"}," Quick Assignments ",-1)),e("div",Ge,[e("div",{class:f(["col-span-3 flex w-full flex-col items-center justify-center rounded-lg border-2 border-gray-300 py-4 shadow-sm transition duration-150",{"cursor-pointer hover:border-gray-400 hover:bg-gray-50":!s.team.archived}]),onClick:t[1]||(t[1]=o=>s.team.subscription.is_subscribed?s.team.archived!=1?c(A)?A.value=!0:A=!0:null:c(d)?d.value=!0:d=!0)},t[21]||(t[21]=[e("div",{class:"flex justify-center -space-x-2"},[e("img",{class:"inline-block h-12 w-12 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/aeneid.png",alt:""}),e("img",{class:"inline-block h-12 w-12 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/metamorphoses.png",alt:""}),e("img",{class:"inline-block h-12 w-12 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/sulpicia.png",alt:""}),e("img",{class:"inline-block h-12 w-12 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/catullus.png",alt:""}),e("img",{class:"inline-block h-12 w-12 rounded-full ring-2 ring-white",src:"https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/circle/caesar-gallic-war.png",alt:""})],-1),e("p",{class:"mt-4 text-center text-lg font-medium"}," Read and translate Latin texts. ",-1)]),2),e("div",Pe,[e("div",{class:f(["flex w-full items-center justify-between rounded-lg border-2 border-indigo-300 bg-indigo-50 py-2 shadow-sm transition duration-150",{"cursor-pointer hover:border-indigo-400 hover:bg-indigo-100":!s.team.archived}]),onClick:t[2]||(t[2]=o=>s.team.subscription.is_subscribed?s.team.archived!=1?c(k)?k.value=!0:k=!0:null:c(d)?d.value=!0:d=!0)},[e("div",We,[a(ye,{class:"inline w-12"}),t[22]||(t[22]=e("span",{class:"ml-2 text-2xl font-semibold tracking-tight text-indigo-500"}," Infinitas ",-1))])],2),e("div",{class:f(["flex w-full items-center justify-between rounded-lg border-2 border-orange-300 bg-orange-50 py-2 shadow-sm transition duration-150",{"group cursor-pointer hover:border-orange-400 hover:bg-orange-100":!s.team.archived}]),onClick:t[3]||(t[3]=o=>s.team.subscription.is_subscribed?s.team.archived!=1?c(C)?C.value=!0:C=!0:null:c(d)?d.value=!0:d=!0)},[e("div",Ze,[e("div",qe,[a(m(we),{class:"h-8 w-8 text-orange-600"}),t[23]||(t[23]=e("span",{class:"text-2xl font-semibold tracking-tight text-orange-600"}," Grammar ",-1))])])],2)]),e("div",Je,[e("div",{class:f(["flex w-full content-between rounded-lg border-2 border-emerald-300 bg-emerald-50 py-2 shadow-sm transition duration-150",{"group cursor-pointer hover:border-emerald-400 hover:bg-emerald-100":!s.team.archived}]),onClick:t[4]||(t[4]=o=>s.team.subscription.is_subscribed?s.team.archived!=1?c($)?$.value=!0:$=!0:null:c(d)?d.value=!0:d=!0)},[e("div",Ke,[a(m(ke),{class:"h-8 w-8 text-emerald-600"}),t[24]||(t[24]=e("span",{class:"ml-2 self-center text-2xl font-semibold tracking-tight text-emerald-600"}," Vocab ",-1))])],2),e("div",{class:f(["flex w-full content-between rounded-lg border-2 border-slate-800 bg-slate-600 py-2 shadow-sm transition duration-150",{"group cursor-pointer hover:border-slate-900 hover:bg-slate-700":!s.team.archived}]),onClick:t[5]||(t[5]=o=>s.team.subscription.is_subscribed?s.team.archived!=1?c(j)?j.value=!0:j=!0:null:c(d)?d.value=!0:d=!0)},[e("div",Xe,[a(m(Ce),{class:"h-8 w-8 text-white"}),t[25]||(t[25]=e("span",{class:"ml-2 self-center text-2xl font-semibold tracking-tight text-white"}," Videos ",-1))])],2)])]),s.team.subscription.is_subscribed?(l(),n("div",_e,[s.scheduledAssignments.length>0?(l(),n("div",et," Scheduled Assignments ")):p("",!0),e("div",tt,[(l(!0),n(M,null,S(s.scheduledAssignments,(o,h)=>(l(),D(K,{assignment:o,team:s.team,students:s.students,key:h,"due-date":!!o.due_at,scheduled:!0,works:s.works,books:s.books,"grammar-activities":s.grammarActivities,"vocab-activities":s.vocabActivities,videos:s.videos,"can-edit":!0},null,8,["assignment","team","students","due-date","works","books","grammar-activities","vocab-activities","videos"]))),128))]),m(L).length>0?(l(),n("div",st," Active Assignments ")):p("",!0),e("div",null,[(l(!0),n(M,null,S(m(L),(o,h)=>(l(),D(K,{assignment:o,team:s.team,students:s.students,key:h,"due-date":!!o.due_at,works:s.works,books:s.books,"grammar-activities":s.grammarActivities,"vocab-activities":s.vocabActivities,videos:s.videos,"can-edit":!0},null,8,["assignment","team","students","due-date","works","books","grammar-activities","vocab-activities","videos"]))),128))]),s.scheduledAssignments.length==0&&m(L).length==0&&s.team.student_count>0?(l(),n("div",ot,t[26]||(t[26]=[e("div",{class:"rounded-xl py-20 text-white dark:bg-slate-900"},[e("div",{class:"md:text-center"},[e("h2",{class:"font-display text-3xl tracking-tight text-slate-900 sm:text-4xl dark:text-white"}," No Assignments Yet "),e("p",{class:"mt-4 text-lg text-slate-500"}," Assign work to your students to get started via the Quick Assignments above. ")])],-1)]))):p("",!0)])):p("",!0),e("div",{class:f(["grid gap-4",s.team.student_count==0&&!s.team.subscription.is_subscribed?"grid-cols-2":"grid-cols-1"])},[s.team.student_count==0?(l(),n("div",lt,[e("div",it,[e("div",nt,[t[29]||(t[29]=e("h2",{class:"font-display text-3xl tracking-tight text-slate-900 sm:text-4xl dark:text-white"}," No Students Yet ",-1)),e("p",at,[t[27]||(t[27]=r(" Students can join your class via the ")),e("span",rt,v(s.team.enrollment_code),1),t[28]||(t[28]=r(" enrollment code. "))])])])])):p("",!0),s.team.subscription.is_subscribed?p("",!0):(l(),n("div",dt,[e("div",mt,[t[31]||(t[31]=e("h2",{class:"font-display text-3xl tracking-tight text-slate-900 sm:text-4xl dark:text-white"}," Activate Classroom Access ",-1)),t[32]||(t[32]=e("p",{class:"mt-4 text-lg text-slate-500"},[r(" Supercharge the LatinTutorial experience for you and your students with either a "),e("span",{class:"font-bold"},"monthly"),r(" or "),e("span",{class:"font-bold"},"annual"),r(" plan for this class. ")],-1)),a(Q,{class:"mt-8",onClick:t[6]||(t[6]=o=>c(d)?d.value=!0:d=!0),color:"white",size:"md"},{default:i(()=>t[30]||(t[30]=[r("Learn More")])),_:1})])]))],2),e("div",{ref_key:"landmark",ref:H},null,512)]),tab2:i(()=>t[34]||(t[34]=[r(" Students ")])),content2:i(()=>[s.team.student_count==0?(l(),n("div",ct,[e("div",ut,[e("div",ft,[t[37]||(t[37]=e("h2",{class:"font-display text-3xl tracking-tight text-slate-900 sm:text-4xl dark:text-white"}," No Students Yet ",-1)),e("p",gt,[t[35]||(t[35]=r(" Students can join your class via the ")),e("span",vt,v(s.team.enrollment_code),1),t[36]||(t[36]=r(" enrollment code. "))])])])])):p("",!0),a(ve,{students:s.students,team:s.team.slug,archived:s.team.archived==1},null,8,["students","team","archived"])]),tab3:i(()=>t[38]||(t[38]=[r(" Results ")])),content3:i(()=>[e("div",xt,[e("div",bt,[e("div",pt,[e("table",ht,[e("thead",null,[e("tr",null,[t[39]||(t[39]=e("th",{scope:"col",class:"sticky left-0 z-10 min-w-64 bg-white py-4 pr-3 pl-4 whitespace-nowrap sm:pl-0"}," Name ",-1)),(l(!0),n(M,null,S(m(L),o=>(l(),n("th",yt,[e("div",wt,[e("p",kt,v(m(V).fromISO(o.due_at).toFormat("LLL d, yyyy")),1),Y((l(),n("a",{class:"mt-0.5 line-clamp-2 text-center leading-5 text-blue-600 transition duration-150 ease-in-out hover:text-blue-700 hover:underline",href:`/classes/${s.team.slug}/assignments/${o.id}`},[r(v(o.title),1)],8,Ct)),[[ae,{content:o.title,placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]])])]))),256))])]),e("tbody",$t,[(l(!0),n(M,null,S(s.students,o=>(l(),n("tr",null,[e("td",At,[e("div",jt,[e("img",{class:"h-8 w-8 rounded-full",src:o.photo_url,alt:""},null,8,Mt),a(ne,{href:`/classes/${s.team.slug}/students/${o.slug}`,class:"truncate text-base leading-6 font-medium text-gray-800 hover:text-blue-700 hover:underline"},{default:i(()=>[r(v(o.name),1)]),_:2},1032,["href"])])]),(l(!0),n(M,null,S(m(L),h=>(l(),n("td",St,[e("span",{class:f(["top-0 right-0 mx-auto block h-3 w-3 rounded-full",oe(h.completion.filter(re=>re.user_id==o.id),h.due_at)])},null,2)]))),256))]))),256))])])])]),t[40]||(t[40]=e("div",{class:"my-12 grid grid-cols-2 gap-4 text-sm font-medium text-gray-600 sm:grid-cols-4"},[e("div",{class:"flex w-full flex-row items-center justify-center"},[e("div",{class:"h-4 w-4 rounded-full bg-green-500"}),e("div",{class:"ml-2"},"Completed")]),e("div",{class:"flex w-full flex-row items-center justify-center"},[e("div",{class:"h-4 w-4 rounded-full bg-amber-500"}),e("div",{class:"ml-2"},"Completed Late")]),e("div",{class:"flex w-full flex-row items-center justify-center"},[e("div",{class:"h-4 w-4 rounded-full bg-sky-500"}),e("div",{class:"ml-2"},"In Progress")]),e("div",{class:"flex w-full flex-row items-center justify-center"},[e("div",{class:"h-4 w-4 rounded-full bg-gray-300"}),e("div",{class:"ml-2"},"Not Yet Started")])],-1))])]),tab4:i(()=>t[41]||(t[41]=[r(" Settings ")])),content4:i(()=>[e("div",Lt,[a(U,null,{title:i(()=>t[42]||(t[42]=[r(" Class Name and Icon")])),description:i(()=>t[43]||(t[43]=[r(" Change your classroom's name and icon. ")])),form:i(()=>[e("div",Tt,[e("div",Dt,[e("div",It,[t[44]||(t[44]=e("label",{for:"classroom_name",class:"block text-left text-sm font-bold text-gray-700"}," Name ",-1)),Y(e("input",{id:"classroom_name","onUpdate:modelValue":t[7]||(t[7]=o=>b.name=o),type:"text",name:"classroom_name",class:"mt-1 block rounded-md border-gray-300 bg-white shadow-xs focus:border-indigo-500 focus:ring-indigo-500",disabled:s.team.archived==1},null,8,Ot),[[J,b.name]])]),w["form.name"]?(l(),n("div",Bt,v(w["form.name"][0]),1)):p("",!0)])]),e("div",Nt,[t[45]||(t[45]=e("label",{for:"classroom_icon",class:"mb-2 block text-left text-sm font-bold text-gray-700"}," Icon ",-1)),e("div",Vt,[(l(),n(M,null,S(20,o=>e("div",{key:o,class:"mx-auto"},[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`,class:f(["h-16 w-16 rounded-full",{"ring-3 ring-teal-500 ring-offset-2":`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`===b.icon,"cursor-not-allowed opacity-25":s.team.archived==1,"cursor-pointer":s.team.archived!=1}]),onClick:h=>b.icon=`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`},null,10,Et)])),64))])])]),actions:i(()=>[a(Q,{size:"sm",color:"black",class:f([{"opacity-25":b.processing},"w-24"]),disabled:b.processing||s.team.archived==1,onClick:P},{default:i(()=>t[46]||(t[46]=[r(" Save ")])),_:1},8,["class","disabled"])]),_:1}),a(U,null,{title:i(()=>t[47]||(t[47]=[r(" Enrollment Code ")])),description:i(()=>t[48]||(t[48]=[r(" Change your classroom's enrollment code. ")])),form:i(()=>[e("div",zt,[e("div",Ht,v(s.team.enrollment_code),1)])]),actions:i(()=>[a(Q,{size:"sm",class:f(["w-56",{"opacity-25":O.value}]),color:"black",onClick:ie,disabled:O.value||s.team.archived==1},{default:i(()=>[a(me,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:i(()=>[O.value?(l(),n("span",Rt,t[49]||(t[49]=[e("svg",{class:"mr-2 inline h-3 w-3 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),r("Loading ...")]))):(l(),n("span",Ft,"Change Enrollment Code"))]),_:1})]),_:1},8,["class","disabled"])]),_:1}),a(U,null,{title:i(()=>t[50]||(t[50]=[r(" Archive or Delete Classroom ")])),description:i(()=>t[51]||(t[51]=[r(" Permanently delete this classroom. ")])),form:i(()=>[e("div",Yt,[t[52]||(t[52]=e("div",{class:"mt-1 text-sm text-gray-700"}," Archiving a classroom will remove it from your dashboard and make it inaccessible to students. You can restore it at any time. Deleting a classroom will permanently remove it and all of its content. This action cannot be reversed. ",-1)),e("div",Qt,[e("button",{type:"button",class:"inline-flex w-full cursor-pointer justify-center rounded-md border border-gray-300 bg-white px-2 py-1 text-base font-medium text-gray-700 shadow-xs hover:bg-gray-100 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm",onClick:t[8]||(t[8]=o=>c(T)?T.value=!0:T=!0)},[s.team.archived!=1?(l(),n("span",Ut,"Archive Class")):(l(),n("span",Gt,"Restore Class"))]),e("button",{type:"button",class:"inline-flex w-full cursor-pointer justify-center rounded-md border border-transparent bg-red-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm",onClick:t[9]||(t[9]=o=>c(I)?I.value=!0:I=!0)}," Delete Class ")])])]),_:1})])]),_:1},8,["current-url","current-tab"]),e("div",{ref_key:"landmark",ref:H},null,512)]),(l(),D(ce,{to:"body"},[a(N,{open:m(T),onCloseModal:X},{title:i(()=>[s.team.archived?(l(),n("span",Pt,"Restore")):(l(),n("span",Wt,"Archive")),t[53]||(t[53]=r(" this Classroom "))]),icon:i(()=>[e("div",{class:f(["mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-amber-100",s.team.archived==1?"bg-emerald-100":"bg-amber-100"])},[s.team.archived?(l(),D(m(je),{key:1,class:"h-6 w-6 text-emerald-600","aria-hidden":"true"})):(l(),D(m(Me),{key:0,class:"h-6 w-6 text-amber-600","aria-hidden":"true"}))],2)]),main:i(()=>[t[54]||(t[54]=r(" Are you sure you wish to ")),s.team.archived==1?(l(),n("span",Zt,"restore")):(l(),n("span",qt,"archive")),t[55]||(t[55]=r(" this classroom? "))]),actionButton:i(()=>[e("button",{type:"button",class:f(["inline-flex w-full justify-center self-center rounded-md border border-transparent px-2 py-1 text-base font-medium text-white shadow-xs focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm",s.team.archived==1?"bg-emerald-500 hover:bg-emerald-600":"bg-amber-600 hover:bg-amber-700"]),onClick:_},[s.team.archived==1?(l(),n("span",Jt,"Restore")):(l(),n("span",Kt,"Archive"))],2)]),_:1},8,["open"]),a(N,{open:m(I),onCloseModal:te},{title:i(()=>t[56]||(t[56]=[r(" Delete this Classroom ")])),icon:i(()=>[e("div",Xt,[a(m($e),{class:"h-6 w-6 text-red-600","aria-hidden":"true"})])]),main:i(()=>t[57]||(t[57]=[r(" Are you sure you wish to delete this classroom? This action cannot be reversed. ")])),actionButton:i(()=>[e("button",{type:"button",class:"inline-flex w-full justify-center self-center rounded-md border border-transparent bg-red-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm",onClick:ee}," Delete ")]),_:1},8,["open"]),a(N,{open:m(R),onCloseModal:se,onSubmitForm:P},{title:i(()=>t[58]||(t[58]=[r("Edit this Classroom")])),icon:i(()=>[e("div",_t,[a(m(Ae),{class:"h-6 w-6 text-teal-600","aria-hidden":"true"})])]),main:i(()=>[e("div",es,[e("div",null,[t[59]||(t[59]=e("label",{for:"classroom_name",class:"block text-left text-sm font-medium text-gray-700"}," Change your Classroom's Name ",-1)),e("div",ts,[Y(e("input",{id:"classroom_name","onUpdate:modelValue":t[10]||(t[10]=o=>b.name=o),type:"text",name:"classroom_name",class:"sm:text-md block w-full rounded-md border-gray-300 shadow-xs focus:border-teal-400 focus:ring-teal-400"},null,512),[[J,b.name]])]),w["form.name"]?(l(),n("div",ss,v(w["form.name"][0]),1)):p("",!0)]),t[60]||(t[60]=e("label",{for:"classroom_icon",class:"block text-left text-sm font-medium text-gray-700"}," Change your Classroom's Icon ",-1)),e("div",os,[(l(),n(M,null,S(20,o=>e("div",{key:o,class:"mx-auto"},[e("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`,class:f(["h-16 w-16 cursor-pointer rounded-full",{"ring-3 ring-teal-500 ring-offset-2":`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`==b.icon}]),onClick:h=>b.icon=`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/classroom-photos/bg-${o}.png`},null,10,ls)])),64))])])]),actionButton:i(()=>t[61]||(t[61]=[e("button",{type:"submit",class:"inline-flex w-full justify-center self-center rounded-md border border-transparent bg-indigo-600 px-2 py-1 text-base font-medium text-white shadow-xs hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-hidden sm:text-sm"}," Update ",-1)])),_:1},8,["open"]),a(N,{open:m(y),"close-button-text":"Close",onCloseModal:t[11]||(t[11]=o=>c(y)?y.value=!1:y=!1)},{title:i(()=>t[62]||(t[62]=[r(" Enrollment Code ")])),main:i(()=>[e("h1",is,v(s.team.enrollment_code),1)]),_:1},8,["open"]),a(N,{open:m(d),onCloseModal:t[13]||(t[13]=o=>c(d)?d.value=!1:d=!1),"modal-size":"xl","show-close":!1},{title:i(()=>[e("h1",ns," Activate Access for "+v(s.team.name),1)]),main:i(()=>[e("div",as,[t[63]||(t[63]=e("div",{class:"mx-auto max-w-4xl text-center"},[e("p",{class:"mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl"}," Give access to your students ")],-1)),t[64]||(t[64]=e("p",{class:"mx-auto mt-6 max-w-2xl text-center text-lg leading-6 text-gray-600"}," Choose a plan to supercharge how you and your students use LatinTutorial. ",-1)),a(pe,{team:s.team,"onUpdate:close":t[12]||(t[12]=o=>c(d)?d.value=!1:d=!1)},null,8,["team"])])]),_:1},8,["open"]),a(xe,{open:m(A),onCloseModal:t[14]||(t[14]=o=>c(A)?A.value=!1:A=!1),team:s.team,works:s.works,books:s.books},null,8,["open","team","works","books"]),a(z,{open:m(k),onCloseModal:t[15]||(t[15]=o=>c(k)?k.value=!1:k=!1),team:s.team,color:"indigo",name:"infinitas"},null,8,["open","team"]),a(z,{open:m(C),onCloseModal:t[16]||(t[16]=o=>c(C)?C.value=!1:C=!1),activities:s.grammarActivities,team:s.team,color:"orange",name:"grammar"},null,8,["open","activities","team"]),a(z,{open:m($),onCloseModal:t[17]||(t[17]=o=>c($)?$.value=!1:$=!1),activities:s.vocabActivities,team:s.team,color:"emerald",name:"vocabulary"},null,8,["open","activities","team"]),a(z,{open:m(j),onCloseModal:t[18]||(t[18]=o=>c(j)?j.value=!1:j=!1),activities:s.videos,team:s.team,color:"slate",name:"video"},null,8,["open","activities","team"])]))],2),a(he)]),_:1})}}};export{wo as default};
