class C{constructor(t){this._canceled=!1,this.data=t}get type(){return this.constructor.type}get cancelable(){return this.constructor.cancelable}cancel(){this._canceled=!0}canceled(){return this._canceled}clone(t){return new this.constructor({...this.data,...t})}}C.type="event";C.cancelable=!1;class L{constructor(t){this.draggable=t}attach(){throw new Error("Not Implemented")}detach(){throw new Error("Not Implemented")}}const F={mouse:0,drag:0,touch:100};class Ht{constructor(t=[],e={}){this.containers=[...t],this.options={...e},this.dragging=!1,this.currentContainer=null,this.originalSource=null,this.startEvent=null,this.delay=we(e.delay)}attach(){return this}detach(){return this}addContainer(...t){this.containers=[...this.containers,...t]}removeContainer(...t){this.containers=this.containers.filter(e=>!t.includes(e))}trigger(t,e){const n=document.createEvent("Event");return n.detail=e,n.initEvent(e.type,!0,!0),t.dispatchEvent(n),this.lastEvent=e,e}}function we(r){const t={};if(r===void 0)return{...F};if(typeof r=="number"){for(const e in F)Object.prototype.hasOwnProperty.call(F,e)&&(t[e]=r);return t}for(const e in F)Object.prototype.hasOwnProperty.call(F,e)&&(r[e]===void 0?t[e]=F[e]:t[e]=r[e]);return t}function E(r,t){if(r==null)return null;function e(i){return i==null||t==null?!1:xe(t)?Element.prototype.matches.call(i,t):De(t)?[...t].includes(i):Me(t)?t===i:Oe(t)?t(i):!1}let n=r;do{if(n=n.correspondingUseElement||n.correspondingElement||n,e(n))return n;n=(n==null?void 0:n.parentNode)||null}while(n!=null&&n!==document.body&&n!==document);return null}function xe(r){return typeof r=="string"}function De(r){return r instanceof NodeList||r instanceof Array}function Me(r){return r instanceof Node}function Oe(r){return typeof r=="function"}function Vt(r,t,e,n){return Math.sqrt((e-r)**2+(n-t)**2)}class ft extends C{get originalEvent(){return this.data.originalEvent}get clientX(){return this.data.clientX}get clientY(){return this.data.clientY}get target(){return this.data.target}get container(){return this.data.container}get originalSource(){return this.data.originalSource}get pressure(){return this.data.pressure}}class Mt extends ft{}Mt.type="drag:start";class Ot extends ft{}Ot.type="drag:move";class Lt extends ft{}Lt.type="drag:stop";class Le extends ft{}Le.type="drag:pressure";const $=Symbol("onContextMenuWhileDragging"),z=Symbol("onMouseDown"),k=Symbol("onMouseMove"),q=Symbol("onMouseUp"),lt=Symbol("startDrag"),M=Symbol("onDistanceChange");class Te extends Ht{constructor(t=[],e={}){super(t,e),this.mouseDownTimeout=null,this.pageX=null,this.pageY=null,this[$]=this[$].bind(this),this[z]=this[z].bind(this),this[k]=this[k].bind(this),this[q]=this[q].bind(this),this[lt]=this[lt].bind(this),this[M]=this[M].bind(this)}attach(){document.addEventListener("mousedown",this[z],!0)}detach(){document.removeEventListener("mousedown",this[z],!0)}[z](t){if(t.button!==0||t.ctrlKey||t.metaKey)return;const e=E(t.target,this.containers);if(!e||this.options.handle&&t.target&&!E(t.target,this.options.handle))return;const n=E(t.target,this.options.draggable);if(!n)return;const{delay:i}=this,{pageX:s,pageY:o}=t;Object.assign(this,{pageX:s,pageY:o}),this.onMouseDownAt=Date.now(),this.startEvent=t,this.currentContainer=e,this.originalSource=n,document.addEventListener("mouseup",this[q]),document.addEventListener("dragstart",It),document.addEventListener("mousemove",this[M]),this.mouseDownTimeout=window.setTimeout(()=>{this[M]({pageX:this.pageX,pageY:this.pageY})},i.mouse)}[lt](){const t=this.startEvent,e=this.currentContainer,n=this.originalSource,i=new Mt({clientX:t.clientX,clientY:t.clientY,target:t.target,container:e,originalSource:n,originalEvent:t});this.trigger(this.currentContainer,i),this.dragging=!i.canceled(),this.dragging&&(document.addEventListener("contextmenu",this[$],!0),document.addEventListener("mousemove",this[k]))}[M](t){const{pageX:e,pageY:n}=t,{distance:i}=this.options,{startEvent:s,delay:o}=this;if(Object.assign(this,{pageX:e,pageY:n}),!this.currentContainer)return;const a=Date.now()-this.onMouseDownAt,l=Vt(s.pageX,s.pageY,e,n)||0;clearTimeout(this.mouseDownTimeout),a<o.mouse?document.removeEventListener("mousemove",this[M]):l>=i&&(document.removeEventListener("mousemove",this[M]),this[lt]())}[k](t){if(!this.dragging)return;const e=document.elementFromPoint(t.clientX,t.clientY),n=new Ot({clientX:t.clientX,clientY:t.clientY,target:e,container:this.currentContainer,originalEvent:t});this.trigger(this.currentContainer,n)}[q](t){if(clearTimeout(this.mouseDownTimeout),t.button!==0||(document.removeEventListener("mouseup",this[q]),document.removeEventListener("dragstart",It),document.removeEventListener("mousemove",this[M]),!this.dragging))return;const e=document.elementFromPoint(t.clientX,t.clientY),n=new Lt({clientX:t.clientX,clientY:t.clientY,target:e,container:this.currentContainer,originalEvent:t});this.trigger(this.currentContainer,n),document.removeEventListener("contextmenu",this[$],!0),document.removeEventListener("mousemove",this[k]),this.currentContainer=null,this.dragging=!1,this.startEvent=null}[$](t){t.preventDefault()}}function It(r){r.preventDefault()}function X(r){const{touches:t,changedTouches:e}=r;return t&&t[0]||e&&e[0]}const R=Symbol("onTouchStart"),A=Symbol("onTouchEnd"),j=Symbol("onTouchMove"),ct=Symbol("startDrag"),O=Symbol("onDistanceChange");let gt=!1;window.addEventListener("touchmove",r=>{gt&&r.preventDefault()},{passive:!1});class Ae extends Ht{constructor(t=[],e={}){super(t,e),this.currentScrollableParent=null,this.tapTimeout=null,this.touchMoved=!1,this.pageX=null,this.pageY=null,this[R]=this[R].bind(this),this[A]=this[A].bind(this),this[j]=this[j].bind(this),this[ct]=this[ct].bind(this),this[O]=this[O].bind(this)}attach(){document.addEventListener("touchstart",this[R])}detach(){document.removeEventListener("touchstart",this[R])}[R](t){const e=E(t.target,this.containers);if(!e||this.options.handle&&t.target&&!E(t.target,this.options.handle))return;const n=E(t.target,this.options.draggable);if(!n)return;const{distance:i=0}=this.options,{delay:s}=this,{pageX:o,pageY:a}=X(t);Object.assign(this,{pageX:o,pageY:a}),this.onTouchStartAt=Date.now(),this.startEvent=t,this.currentContainer=e,this.originalSource=n,document.addEventListener("touchend",this[A]),document.addEventListener("touchcancel",this[A]),document.addEventListener("touchmove",this[O]),e.addEventListener("contextmenu",$t),i&&(gt=!0),this.tapTimeout=window.setTimeout(()=>{this[O]({touches:[{pageX:this.pageX,pageY:this.pageY}]})},s.touch)}[ct](){const t=this.startEvent,e=this.currentContainer,n=X(t),i=this.originalSource,s=new Mt({clientX:n.pageX,clientY:n.pageY,target:t.target,container:e,originalSource:i,originalEvent:t});this.trigger(this.currentContainer,s),this.dragging=!s.canceled(),this.dragging&&document.addEventListener("touchmove",this[j]),gt=this.dragging}[O](t){const{distance:e}=this.options,{startEvent:n,delay:i}=this,s=X(n),o=X(t),a=Date.now()-this.onTouchStartAt,l=Vt(s.pageX,s.pageY,o.pageX,o.pageY);Object.assign(this,o),clearTimeout(this.tapTimeout),a<i.touch?document.removeEventListener("touchmove",this[O]):l>=e&&(document.removeEventListener("touchmove",this[O]),this[ct]())}[j](t){if(!this.dragging)return;const{pageX:e,pageY:n}=X(t),i=document.elementFromPoint(e-window.scrollX,n-window.scrollY),s=new Ot({clientX:e,clientY:n,target:i,container:this.currentContainer,originalEvent:t});this.trigger(this.currentContainer,s)}[A](t){if(clearTimeout(this.tapTimeout),gt=!1,document.removeEventListener("touchend",this[A]),document.removeEventListener("touchcancel",this[A]),document.removeEventListener("touchmove",this[O]),this.currentContainer&&this.currentContainer.removeEventListener("contextmenu",$t),!this.dragging)return;document.removeEventListener("touchmove",this[j]);const{pageX:e,pageY:n}=X(t),i=document.elementFromPoint(e-window.scrollX,n-window.scrollY);t.preventDefault();const s=new Lt({clientX:e,clientY:n,target:i,container:this.currentContainer,originalEvent:t});this.trigger(this.currentContainer,s),this.currentContainer=null,this.dragging=!1,this.startEvent=null}}function $t(r){r.preventDefault(),r.stopPropagation()}class Tt extends C{constructor(t){super(t),this.data=t}get dragEvent(){return this.data.dragEvent}}Tt.type="collidable";class Fe extends Tt{get collidingElement(){return this.data.collidingElement}}Fe.type="collidable:in";class Xe extends Tt{get collidingElement(){return this.data.collidingElement}}Xe.type="collidable:out";function Wt(r,t){return function(e){Pe(t,"addInitializer"),mt(e,"An initializer"),r.push(e)}}function St(r,t){if(!r(t))throw new TypeError("Attempted to access private element on non-instance")}function Ye(r,t,e,n,i,s,o,a,l,d,m){var u;switch(s){case 1:u="accessor";break;case 2:u="method";break;case 3:u="getter";break;case 4:u="setter";break;default:u="field"}var c,h,g={kind:u,name:a?"#"+e:e,static:o,private:a,metadata:m},v={v:!1};if(s!==0&&(g.addInitializer=Wt(i,v)),a||s!==0&&s!==2)if(s===2)c=function(p){return St(d,p),n.value};else{var b=s===0||s===1;(b||s===3)&&(c=a?function(p){return St(d,p),n.get.call(p)}:function(p){return n.get.call(p)}),(b||s===4)&&(h=a?function(p,y){St(d,p),n.set.call(p,y)}:function(p,y){n.set.call(p,y)})}else c=function(p){return p[e]},s===0&&(h=function(p,y){p[e]=y});var f=a?d.bind():function(p){return e in p};g.access=c&&h?{get:c,set:h,has:f}:c?{get:c,has:f}:{set:h,has:f};try{return r.call(t,l,g)}finally{v.v=!0}}function Pe(r,t){if(r.v)throw new Error("attempted to call "+t+" after decoration was finished")}function mt(r,t){if(typeof r!="function")throw new TypeError(t+" must be a function")}function _t(r,t){var e=typeof t;if(r===1){if(e!=="object"||t===null)throw new TypeError("accessor decorators must return an object with get, set, or init properties or void 0");t.get!==void 0&&mt(t.get,"accessor.get"),t.set!==void 0&&mt(t.set,"accessor.set"),t.init!==void 0&&mt(t.init,"accessor.init")}else if(e!=="function"){var n;throw n=r===0?"field":r===5?"class":"method",new TypeError(n+" decorators must return a function or void 0")}}function Ne(r){return function(){return r(this)}}function Ie(r){return function(t){r(this,t)}}function $e(r,t,e,n,i,s,o,a,l,d,m){var u,c,h,g,v,b,f=e[0];n||Array.isArray(f)||(f=[f]),a?u=s===0||s===1?{get:Ne(e[3]),set:Ie(e[4])}:s===3?{get:e[3]}:s===4?{set:e[3]}:{value:e[3]}:s!==0&&(u=Object.getOwnPropertyDescriptor(t,i)),s===1?h={get:u.get,set:u.set}:s===2?h=u.value:s===3?h=u.get:s===4&&(h=u.set);for(var p=n?2:1,y=f.length-1;y>=0;y-=p){var D;(g=Ye(f[y],n?f[y-1]:void 0,i,u,l,s,o,a,h,d,m))!==void 0&&(_t(s,g),s===0?D=g:s===1?(D=g.init,v=g.get||h.get,b=g.set||h.set,h={get:v,set:b}):h=g,D!==void 0&&(c===void 0?c=D:typeof c=="function"?c=[c,D]:c.push(D)))}if(s===0||s===1){if(c===void 0)c=function(w,S){return S};else if(typeof c!="function"){var T=c;c=function(w,S){for(var yt=S,Et=T.length-1;Et>=0;Et--)yt=T[Et].call(w,yt);return yt}}else{var bt=c;c=function(w,S){return bt.call(w,S)}}r.push(c)}s!==0&&(s===1?(u.get=h.get,u.set=h.set):s===2?u.value=h:s===3?u.get=h:s===4&&(u.set=h),a?s===1?(r.push(function(w,S){return h.get.call(w,S)}),r.push(function(w,S){return h.set.call(w,S)})):s===2?r.push(h):r.push(function(w,S){return h.call(w,S)}):Object.defineProperty(t,i,u))}function ze(r,t,e,n){for(var i,s,o,a=[],l=new Map,d=new Map,m=0;m<t.length;m++){var u=t[m];if(Array.isArray(u)){var c,h,g=u[1],v=u[2],b=u.length>3,f=16&g,p=!!(8&g),y=e;if(g&=7,p?(c=r,g!==0&&(h=s=s||[]),b&&!o&&(o=function(bt){return qe(bt)===r}),y=o):(c=r.prototype,g!==0&&(h=i=i||[])),g!==0&&!b){var D=p?d:l,T=D.get(v)||0;if(T===!0||T===3&&g!==4||T===4&&g!==3)throw new Error("Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: "+v);D.set(v,!(!T&&g>2)||g)}$e(a,c,u,f,v,g,p,b,h,y,n)}}return zt(a,i),zt(a,s),a}function zt(r,t){t&&r.push(function(e){for(var n=0;n<t.length;n++)t[n].call(e);return e})}function ke(r,t,e,n){if(t.length){for(var i=[],s=r,o=r.name,a=e?2:1,l=t.length-1;l>=0;l-=a){var d={v:!1};try{var m=t[l].call(e?t[l-1]:void 0,s,{kind:"class",name:o,addInitializer:Wt(i,d),metadata:n})}finally{d.v=!0}m!==void 0&&(_t(5,m),s=m)}return[Ut(s,n),function(){for(var u=0;u<i.length;u++)i[u].call(s)}]}}function Ut(r,t){return Object.defineProperty(r,Symbol.metadata||Symbol.for("Symbol.metadata"),{configurable:!0,enumerable:!0,value:t})}function Kt(r,t,e,n,i,s){if(arguments.length>=6)var o=s[Symbol.metadata||Symbol.for("Symbol.metadata")];var a=Object.create(o===void 0?null:o),l=ze(r,t,i,a);return e.length||Ut(r,a),{e:l,get c(){return ke(r,e,n,a)}}}function qe(r){if(Object(r)!==r)throw TypeError("right-hand side of 'in' should be an object, got "+(r!==null?typeof r:"null"));return r}function pt(r,{name:t,addInitializer:e}){e(function(){this[t]=r.bind(this)})}function Re(r){return requestAnimationFrame(()=>{requestAnimationFrame(r)})}class x extends C{constructor(t){super(t),this.data=t}get source(){return this.data.source}get originalSource(){return this.data.originalSource}get mirror(){return this.data.mirror}get sourceContainer(){return this.data.sourceContainer}get sensorEvent(){return this.data.sensorEvent}get originalEvent(){return this.sensorEvent?this.sensorEvent.originalEvent:null}}x.type="drag";class At extends x{}At.type="drag:start";At.cancelable=!0;class Gt extends x{}Gt.type="drag:move";class vt extends x{get overContainer(){return this.data.overContainer}get over(){return this.data.over}}vt.type="drag:over";vt.cancelable=!0;function je(r){return r.type===vt.type}class Jt extends x{get overContainer(){return this.data.overContainer}get over(){return this.data.over}}Jt.type="drag:out";class Qt extends x{get overContainer(){return this.data.overContainer}}Qt.type="drag:over:container";class Zt extends x{get overContainer(){return this.data.overContainer}}Zt.type="drag:out:container";class te extends x{get pressure(){return this.data.pressure}}te.type="drag:pressure";class Ft extends x{}Ft.type="drag:stop";Ft.cancelable=!0;class ee extends x{}ee.type="drag:stopped";var re,ne;class Be extends L{constructor(t){re(super(t)),this.lastWidth=0,this.lastHeight=0,this.mirror=null}attach(){this.draggable.on("mirror:created",this.onMirrorCreated).on("drag:over",this.onDragOver).on("drag:over:container",this.onDragOver)}detach(){this.draggable.off("mirror:created",this.onMirrorCreated).off("mirror:destroy",this.onMirrorDestroy).off("drag:over",this.onDragOver).off("drag:over:container",this.onDragOver)}getOptions(){return this.draggable.options.resizeMirror||{}}onMirrorCreated({mirror:t}){this.mirror=t}onMirrorDestroy(){this.mirror=null}onDragOver(t){this.resize(t)}resize(t){requestAnimationFrame(()=>{let e=null;const{overContainer:n}=t;if(this.mirror==null||this.mirror.parentNode==null)return;this.mirror.parentNode!==n&&n.appendChild(this.mirror),je(t)&&(e=t.over);const i=e||this.draggable.getDraggableElementsForContainer(n)[0];i&&Re(()=>{const s=i.getBoundingClientRect();this.mirror==null||this.lastHeight===s.height&&this.lastWidth===s.width||(this.mirror.style.width=`${s.width}px`,this.mirror.style.height=`${s.height}px`,this.lastWidth=s.width,this.lastHeight=s.height)})})}}ne=Be;[re]=Kt(ne,[[pt,2,"onMirrorCreated"],[pt,2,"onMirrorDestroy"],[pt,2,"onDragOver"]],[],0,void 0,L).e;class Xt extends C{get dragEvent(){return this.data.dragEvent}get snappable(){return this.data.snappable}}Xt.type="snap";class se extends Xt{}se.type="snap:in";se.cancelable=!0;class ie extends Xt{}ie.type="snap:out";ie.cancelable=!0;var oe,ae;const He={duration:150,easingFunction:"ease-in-out",horizontal:!1};class Ve extends L{constructor(t){oe(super(t)),this.options={...He,...this.getOptions()},this.lastAnimationFrame=null}attach(){this.draggable.on("sortable:sorted",this.onSortableSorted)}detach(){this.draggable.off("sortable:sorted",this.onSortableSorted)}getOptions(){return this.draggable.options.swapAnimation||{}}onSortableSorted({oldIndex:t,newIndex:e,dragEvent:n}){const{source:i,over:s}=n;this.lastAnimationFrame&&cancelAnimationFrame(this.lastAnimationFrame),this.lastAnimationFrame=requestAnimationFrame(()=>{t>=e?kt(i,s,this.options):kt(s,i,this.options)})}}ae=Ve;[oe]=Kt(ae,[[pt,2,"onSortableSorted"]],[],0,void 0,L).e;function kt(r,t,{duration:e,easingFunction:n,horizontal:i}){for(const s of[r,t])s.style.pointerEvents="none";if(i){const s=r.offsetWidth;r.style.transform=`translate3d(${s}px, 0, 0)`,t.style.transform=`translate3d(-${s}px, 0, 0)`}else{const s=r.offsetHeight;r.style.transform=`translate3d(0, ${s}px, 0)`,t.style.transform=`translate3d(0, -${s}px, 0)`}requestAnimationFrame(()=>{for(const s of[r,t])s.addEventListener("transitionend",le),s.style.transition=`transform ${e}ms ${n}`,s.style.transform=""})}function le(r){r.target==null||!We(r.target)||(r.target.style.transition="",r.target.style.pointerEvents="",r.target.removeEventListener("transitionend",le))}function We(r){return"style"in r}const ut=Symbol("onInitialize"),ht=Symbol("onDestroy"),qt=Symbol("announceEvent"),Ct=Symbol("announceMessage"),_e="aria-relevant",Ue="aria-atomic",Ke="aria-live",Ge="role",Je={expire:7e3};class Qe extends L{constructor(t){super(t),this.options={...Je,...this.getOptions()},this.originalTriggerMethod=this.draggable.trigger,this[ut]=this[ut].bind(this),this[ht]=this[ht].bind(this)}attach(){this.draggable.on("draggable:initialize",this[ut])}detach(){this.draggable.off("draggable:destroy",this[ht])}getOptions(){return this.draggable.options.announcements||{}}[qt](t){const e=this.options[t.type];e&&typeof e=="string"&&this[Ct](e),e&&typeof e=="function"&&this[Ct](e(t))}[Ct](t){Ze(t,{expire:this.options.expire})}[ut](){this.draggable.trigger=t=>{try{this[qt](t)}finally{this.originalTriggerMethod.call(this.draggable,t)}}}[ht](){this.draggable.trigger=this.originalTriggerMethod}}const wt=tr();function Ze(r,{expire:t}){const e=document.createElement("div");return e.textContent=r,wt.appendChild(e),setTimeout(()=>{wt.removeChild(e)},t)}function tr(){const r=document.createElement("div");return r.setAttribute("id","draggable-live-region"),r.setAttribute(_e,"additions"),r.setAttribute(Ue,"true"),r.setAttribute(Ke,"assertive"),r.setAttribute(Ge,"log"),r.style.position="fixed",r.style.width="1px",r.style.height="1px",r.style.top="-1px",r.style.overflow="hidden",r}document.addEventListener("DOMContentLoaded",()=>{document.body.appendChild(wt)});const B=Symbol("onInitialize"),Y=Symbol("onDestroy"),er={};class rr extends L{constructor(t){super(t),this.options={...er,...this.getOptions()},this[B]=this[B].bind(this),this[Y]=this[Y].bind(this)}attach(){this.draggable.on("draggable:initialize",this[B]).on("draggable:destroy",this[Y])}detach(){this.draggable.off("draggable:initialize",this[B]).off("draggable:destroy",this[Y]),this[Y]()}getOptions(){return this.draggable.options.focusable||{}}getElements(){return[...this.draggable.containers,...this.draggable.getDraggableElements()]}[B](){requestAnimationFrame(()=>{this.getElements().forEach(t=>nr(t))})}[Y](){requestAnimationFrame(()=>{this.getElements().forEach(t=>sr(t))})}}const xt=[];function nr(r){!r.getAttribute("tabindex")&&r.tabIndex===-1&&(xt.push(r),r.tabIndex=0)}function sr(r){const t=xt.indexOf(r);t!==-1&&(r.tabIndex=-1,xt.splice(t,1))}class N extends C{constructor(t){super(t),this.data=t}get source(){return this.data.source}get originalSource(){return this.data.originalSource}get sourceContainer(){return this.data.sourceContainer}get sensorEvent(){return this.data.sensorEvent}get dragEvent(){return this.data.dragEvent}get originalEvent(){return this.sensorEvent?this.sensorEvent.originalEvent:null}}class ce extends N{}ce.type="mirror:create";class ue extends N{get mirror(){return this.data.mirror}}ue.type="mirror:created";class he extends N{get mirror(){return this.data.mirror}}he.type="mirror:attached";class Yt extends N{get mirror(){return this.data.mirror}get passedThreshX(){return this.data.passedThreshX}get passedThreshY(){return this.data.passedThreshY}}Yt.type="mirror:move";Yt.cancelable=!0;class de extends N{get mirror(){return this.data.mirror}get passedThreshX(){return this.data.passedThreshX}get passedThreshY(){return this.data.passedThreshY}}de.type="mirror:moved";class Pt extends N{get mirror(){return this.data.mirror}}Pt.type="mirror:destroy";Pt.cancelable=!0;const H=Symbol("onDragStart"),V=Symbol("onDragMove"),W=Symbol("onDragStop"),_=Symbol("onMirrorCreated"),U=Symbol("onMirrorMove"),K=Symbol("onScroll"),Rt=Symbol("getAppendableContainer"),ir={constrainDimensions:!1,xAxis:!0,yAxis:!0,cursorOffsetX:null,cursorOffsetY:null,thresholdX:null,thresholdY:null};class or extends L{constructor(t){super(t),this.options={...ir,...this.getOptions()},this.scrollOffset={x:0,y:0},this.initialScrollOffset={x:window.scrollX,y:window.scrollY},this[H]=this[H].bind(this),this[V]=this[V].bind(this),this[W]=this[W].bind(this),this[_]=this[_].bind(this),this[U]=this[U].bind(this),this[K]=this[K].bind(this)}attach(){this.draggable.on("drag:start",this[H]).on("drag:move",this[V]).on("drag:stop",this[W]).on("mirror:created",this[_]).on("mirror:move",this[U])}detach(){this.draggable.off("drag:start",this[H]).off("drag:move",this[V]).off("drag:stop",this[W]).off("mirror:created",this[_]).off("mirror:move",this[U])}getOptions(){return this.draggable.options.mirror||{}}[H](t){if(t.canceled())return;"ontouchstart"in window&&document.addEventListener("scroll",this[K],!0),this.initialScrollOffset={x:window.scrollX,y:window.scrollY};const{source:e,originalSource:n,sourceContainer:i,sensorEvent:s}=t;this.lastMirrorMovedClient={x:s.clientX,y:s.clientY};const o=new ce({source:e,originalSource:n,sourceContainer:i,sensorEvent:s,dragEvent:t});if(this.draggable.trigger(o),dr(s)||o.canceled())return;const a=this[Rt](e)||i;this.mirror=e.cloneNode(!0);const l=new ue({source:e,originalSource:n,sourceContainer:i,sensorEvent:s,dragEvent:t,mirror:this.mirror}),d=new he({source:e,originalSource:n,sourceContainer:i,sensorEvent:s,dragEvent:t,mirror:this.mirror});this.draggable.trigger(l),a.appendChild(this.mirror),this.draggable.trigger(d)}[V](t){if(!this.mirror||t.canceled())return;const{source:e,originalSource:n,sourceContainer:i,sensorEvent:s}=t;let o=!0,a=!0;if(this.options.thresholdX||this.options.thresholdY){const{x:d,y:m}=this.lastMirrorMovedClient;if(Math.abs(d-s.clientX)<this.options.thresholdX?o=!1:this.lastMirrorMovedClient.x=s.clientX,Math.abs(m-s.clientY)<this.options.thresholdY?a=!1:this.lastMirrorMovedClient.y=s.clientY,!o&&!a)return}const l=new Yt({source:e,originalSource:n,sourceContainer:i,sensorEvent:s,dragEvent:t,mirror:this.mirror,passedThreshX:o,passedThreshY:a});this.draggable.trigger(l)}[W](t){if("ontouchstart"in window&&document.removeEventListener("scroll",this[K],!0),this.initialScrollOffset={x:0,y:0},this.scrollOffset={x:0,y:0},!this.mirror)return;const{source:e,sourceContainer:n,sensorEvent:i}=t,s=new Pt({source:e,mirror:this.mirror,sourceContainer:n,sensorEvent:i,dragEvent:t});this.draggable.trigger(s),s.canceled()||this.mirror.remove()}[K](){this.scrollOffset={x:window.scrollX-this.initialScrollOffset.x,y:window.scrollY-this.initialScrollOffset.y}}[_]({mirror:t,source:e,sensorEvent:n}){const i=this.draggable.getClassNamesFor("mirror"),s=({mirrorOffset:a,initialX:l,initialY:d,...m})=>(this.mirrorOffset=a,this.initialX=l,this.initialY=d,this.lastMovedX=l,this.lastMovedY=d,{mirrorOffset:a,initialX:l,initialY:d,...m});t.style.display="none";const o={mirror:t,source:e,sensorEvent:n,mirrorClasses:i,scrollOffset:this.scrollOffset,options:this.options,passedThreshX:!0,passedThreshY:!0};return Promise.resolve(o).then(ar).then(lr).then(cr).then(ur).then(jt({initial:!0})).then(hr).then(s)}[U](t){if(t.canceled())return null;const e=({lastMovedX:s,lastMovedY:o,...a})=>(this.lastMovedX=s,this.lastMovedY=o,{lastMovedX:s,lastMovedY:o,...a}),n=s=>{const o=new de({source:t.source,originalSource:t.originalSource,sourceContainer:t.sourceContainer,sensorEvent:t.sensorEvent,dragEvent:t.dragEvent,mirror:this.mirror,passedThreshX:t.passedThreshX,passedThreshY:t.passedThreshY});return this.draggable.trigger(o),s},i={mirror:t.mirror,sensorEvent:t.sensorEvent,mirrorOffset:this.mirrorOffset,options:this.options,initialX:this.initialX,initialY:this.initialY,scrollOffset:this.scrollOffset,passedThreshX:t.passedThreshX,passedThreshY:t.passedThreshY,lastMovedX:this.lastMovedX,lastMovedY:this.lastMovedY};return Promise.resolve(i).then(jt({raf:!0})).then(e).then(n)}[Rt](t){const e=this.options.appendTo;return typeof e=="string"?document.querySelector(e):e instanceof HTMLElement?e:typeof e=="function"?e(t):t.parentNode}}function ar({source:r,...t}){return I(e=>{const n=r.getBoundingClientRect();e({source:r,sourceRect:n,...t})})}function lr({sensorEvent:r,sourceRect:t,options:e,...n}){return I(i=>{const s=e.cursorOffsetY===null?r.clientY-t.top:e.cursorOffsetY,o=e.cursorOffsetX===null?r.clientX-t.left:e.cursorOffsetX;i({sensorEvent:r,sourceRect:t,mirrorOffset:{top:s,left:o},options:e,...n})})}function cr({mirror:r,source:t,options:e,...n}){return I(i=>{let s,o;if(e.constrainDimensions){const a=getComputedStyle(t);s=a.getPropertyValue("height"),o=a.getPropertyValue("width")}r.style.display=null,r.style.position="fixed",r.style.pointerEvents="none",r.style.top=0,r.style.left=0,r.style.margin=0,e.constrainDimensions&&(r.style.height=s,r.style.width=o),i({mirror:r,source:t,options:e,...n})})}function ur({mirror:r,mirrorClasses:t,...e}){return I(n=>{r.classList.add(...t),n({mirror:r,mirrorClasses:t,...e})})}function hr({mirror:r,...t}){return I(e=>{r.removeAttribute("id"),delete r.id,e({mirror:r,...t})})}function jt({withFrame:r=!1,initial:t=!1}={}){return({mirror:e,sensorEvent:n,mirrorOffset:i,initialY:s,initialX:o,scrollOffset:a,options:l,passedThreshX:d,passedThreshY:m,lastMovedX:u,lastMovedY:c,...h})=>I(g=>{const v={mirror:e,sensorEvent:n,mirrorOffset:i,options:l,...h};if(i){const b=d?Math.round((n.clientX-i.left-a.x)/(l.thresholdX||1))*(l.thresholdX||1):Math.round(u),f=m?Math.round((n.clientY-i.top-a.y)/(l.thresholdY||1))*(l.thresholdY||1):Math.round(c);l.xAxis&&l.yAxis||t?e.style.transform=`translate3d(${b}px, ${f}px, 0)`:l.xAxis&&!l.yAxis?e.style.transform=`translate3d(${b}px, ${s}px, 0)`:l.yAxis&&!l.xAxis&&(e.style.transform=`translate3d(${o}px, ${f}px, 0)`),t&&(v.initialX=b,v.initialY=f),v.lastMovedX=b,v.lastMovedY=f}g(v)},{frame:r})}function I(r,{raf:t=!1}={}){return new Promise((e,n)=>{t?requestAnimationFrame(()=>{r(e,n)}):r(e,n)})}function dr(r){return/^drag/.test(r.originalEvent.type)}const G=Symbol("onDragStart"),J=Symbol("onDragMove"),Q=Symbol("onDragStop"),Z=Symbol("scroll"),gr={speed:6,sensitivity:50,scrollableElements:[]};class mr extends L{constructor(t){super(t),this.options={...gr,...this.getOptions()},this.currentMousePosition=null,this.scrollAnimationFrame=null,this.scrollableElement=null,this.findScrollableElementFrame=null,this[G]=this[G].bind(this),this[J]=this[J].bind(this),this[Q]=this[Q].bind(this),this[Z]=this[Z].bind(this)}attach(){this.draggable.on("drag:start",this[G]).on("drag:move",this[J]).on("drag:stop",this[Q])}detach(){this.draggable.off("drag:start",this[G]).off("drag:move",this[J]).off("drag:stop",this[Q])}getOptions(){return this.draggable.options.scrollable||{}}getScrollableElement(t){return this.hasDefinedScrollableElements()?E(t,this.options.scrollableElements)||document.documentElement:vr(t)}hasDefinedScrollableElements(){return this.options.scrollableElements.length!==0}[G](t){this.findScrollableElementFrame=requestAnimationFrame(()=>{this.scrollableElement=this.getScrollableElement(t.source)})}[J](t){if(this.findScrollableElementFrame=requestAnimationFrame(()=>{this.scrollableElement=this.getScrollableElement(t.sensorEvent.target)}),!this.scrollableElement)return;const e=t.sensorEvent,n={x:0,y:0};"ontouchstart"in window&&(n.y=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0,n.x=window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0),this.currentMousePosition={clientX:e.clientX-n.x,clientY:e.clientY-n.y},this.scrollAnimationFrame=requestAnimationFrame(this[Z])}[Q](){cancelAnimationFrame(this.scrollAnimationFrame),cancelAnimationFrame(this.findScrollableElementFrame),this.scrollableElement=null,this.scrollAnimationFrame=null,this.findScrollableElementFrame=null,this.currentMousePosition=null}[Z](){if(!this.scrollableElement||!this.currentMousePosition)return;cancelAnimationFrame(this.scrollAnimationFrame);const{speed:t,sensitivity:e}=this.options,n=this.scrollableElement.getBoundingClientRect(),i=n.bottom>window.innerHeight,o=n.top<0||i,a=Dt(),l=this.scrollableElement,d=this.currentMousePosition.clientX,m=this.currentMousePosition.clientY;if(l!==document.body&&l!==document.documentElement&&!o){const{offsetHeight:u,offsetWidth:c}=l;n.top+u-m<e?l.scrollTop+=t:m-n.top<e&&(l.scrollTop-=t),n.left+c-d<e?l.scrollLeft+=t:d-n.left<e&&(l.scrollLeft-=t)}else{const{innerHeight:u,innerWidth:c}=window;m<e?a.scrollTop-=t:u-m<e&&(a.scrollTop+=t),d<e?a.scrollLeft-=t:c-d<e&&(a.scrollLeft+=t)}this.scrollAnimationFrame=requestAnimationFrame(this[Z])}}function pr(r){const t=/(auto|scroll)/,e=getComputedStyle(r,null),n=e.getPropertyValue("overflow")+e.getPropertyValue("overflow-y")+e.getPropertyValue("overflow-x");return t.test(n)}function fr(r){return getComputedStyle(r).getPropertyValue("position")==="static"}function vr(r){if(!r)return Dt();const t=getComputedStyle(r).getPropertyValue("position"),e=t==="absolute",n=E(r,i=>e&&fr(i)?!1:pr(i));return t==="fixed"||!n?Dt():n}function Dt(){return document.scrollingElement||document.documentElement}class br{constructor(){this.callbacks={}}on(t,...e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(...e),this}off(t,e){if(!this.callbacks[t])return null;const n=this.callbacks[t].slice(0);for(let i=0;i<n.length;i++)e===n[i]&&this.callbacks[t].splice(i,1);return this}trigger(t){if(!this.callbacks[t.type])return null;const e=[...this.callbacks[t.type]],n=[];for(let i=e.length-1;i>=0;i--){const s=e[i];try{s(t)}catch(o){n.push(o)}}return n.length&&console.error(`Draggable caught errors while triggering '${t.type}'`,n),this}}class Nt extends C{get draggable(){return this.data.draggable}}Nt.type="draggable";class ge extends Nt{}ge.type="draggable:initialize";class me extends Nt{}me.type="draggable:destroy";const tt=Symbol("onDragStart"),P=Symbol("onDragMove"),et=Symbol("onDragStop"),rt=Symbol("onDragPressure"),nt=Symbol("dragStop"),yr={"drag:start":r=>`Picked up ${r.source.textContent.trim()||r.source.id||"draggable element"}`,"drag:stop":r=>`Released ${r.source.textContent.trim()||r.source.id||"draggable element"}`},Er={"container:dragging":"draggable-container--is-dragging","source:dragging":"draggable-source--is-dragging","source:placed":"draggable-source--placed","container:placed":"draggable-container--placed","body:dragging":"draggable--is-dragging","draggable:over":"draggable--over","container:over":"draggable-container--over","source:original":"draggable--original",mirror:"draggable-mirror"},Sr={draggable:".draggable-source",handle:null,delay:{},distance:0,placedTimeout:800,plugins:[],sensors:[],exclude:{plugins:[],sensors:[]}};class st{constructor(t=[document.body],e={}){if(t instanceof NodeList||t instanceof Array)this.containers=[...t];else if(t instanceof HTMLElement)this.containers=[t];else throw new Error("Draggable containers are expected to be of type `NodeList`, `HTMLElement[]` or `HTMLElement`");this.options={...Sr,...e,classes:{...Er,...e.classes||{}},announcements:{...yr,...e.announcements||{}},exclude:{plugins:e.exclude&&e.exclude.plugins||[],sensors:e.exclude&&e.exclude.sensors||[]}},this.emitter=new br,this.dragging=!1,this.plugins=[],this.sensors=[],this[tt]=this[tt].bind(this),this[P]=this[P].bind(this),this[et]=this[et].bind(this),this[rt]=this[rt].bind(this),this[nt]=this[nt].bind(this),document.addEventListener("drag:start",this[tt],!0),document.addEventListener("drag:move",this[P],!0),document.addEventListener("drag:stop",this[et],!0),document.addEventListener("drag:pressure",this[rt],!0);const n=Object.values(st.Plugins).filter(o=>!this.options.exclude.plugins.includes(o)),i=Object.values(st.Sensors).filter(o=>!this.options.exclude.sensors.includes(o));this.addPlugin(...n,...this.options.plugins),this.addSensor(...i,...this.options.sensors);const s=new ge({draggable:this});this.on("mirror:created",({mirror:o})=>this.mirror=o),this.on("mirror:destroy",()=>this.mirror=null),this.trigger(s)}destroy(){document.removeEventListener("drag:start",this[tt],!0),document.removeEventListener("drag:move",this[P],!0),document.removeEventListener("drag:stop",this[et],!0),document.removeEventListener("drag:pressure",this[rt],!0);const t=new me({draggable:this});this.trigger(t),this.removePlugin(...this.plugins.map(e=>e.constructor)),this.removeSensor(...this.sensors.map(e=>e.constructor))}addPlugin(...t){const e=t.map(n=>new n(this));return e.forEach(n=>n.attach()),this.plugins=[...this.plugins,...e],this}removePlugin(...t){return this.plugins.filter(n=>t.includes(n.constructor)).forEach(n=>n.detach()),this.plugins=this.plugins.filter(n=>!t.includes(n.constructor)),this}addSensor(...t){const e=t.map(n=>new n(this.containers,this.options));return e.forEach(n=>n.attach()),this.sensors=[...this.sensors,...e],this}removeSensor(...t){return this.sensors.filter(n=>t.includes(n.constructor)).forEach(n=>n.detach()),this.sensors=this.sensors.filter(n=>!t.includes(n.constructor)),this}addContainer(...t){return this.containers=[...this.containers,...t],this.sensors.forEach(e=>e.addContainer(...t)),this}removeContainer(...t){return this.containers=this.containers.filter(e=>!t.includes(e)),this.sensors.forEach(e=>e.removeContainer(...t)),this}on(t,...e){return this.emitter.on(t,...e),this}off(t,e){return this.emitter.off(t,e),this}trigger(t){return this.emitter.trigger(t),this}getClassNameFor(t){return this.getClassNamesFor(t)[0]}getClassNamesFor(t){const e=this.options.classes[t];return e instanceof Array?e:typeof e=="string"||e instanceof String?[e]:[]}isDragging(){return!!this.dragging}getDraggableElements(){return this.containers.reduce((t,e)=>[...t,...this.getDraggableElementsForContainer(e)],[])}getDraggableElementsForContainer(t){return[...t.querySelectorAll(this.options.draggable)].filter(n=>n!==this.originalSource&&n!==this.mirror)}cancel(){this[nt]()}[tt](t){const e=dt(t),{target:n,container:i,originalSource:s}=e;if(!this.containers.includes(i))return;if(this.options.handle&&n&&!E(n,this.options.handle)){e.cancel();return}this.originalSource=s,this.sourceContainer=i,this.lastPlacedSource&&this.lastPlacedContainer&&(clearTimeout(this.placedTimeoutID),this.lastPlacedSource.classList.remove(...this.getClassNamesFor("source:placed")),this.lastPlacedContainer.classList.remove(...this.getClassNamesFor("container:placed"))),this.source=this.originalSource.cloneNode(!0),this.originalSource.parentNode.insertBefore(this.source,this.originalSource),this.originalSource.style.display="none";const o=new At({source:this.source,originalSource:this.originalSource,sourceContainer:i,sensorEvent:e});if(this.trigger(o),this.dragging=!o.canceled(),o.canceled()){this.source.remove(),this.originalSource.style.display=null;return}this.originalSource.classList.add(...this.getClassNamesFor("source:original")),this.source.classList.add(...this.getClassNamesFor("source:dragging")),this.sourceContainer.classList.add(...this.getClassNamesFor("container:dragging")),document.body.classList.add(...this.getClassNamesFor("body:dragging")),Bt(document.body,"none"),requestAnimationFrame(()=>{const l=dt(t).clone({target:this.source});this[P]({...t,detail:l})})}[P](t){if(!this.dragging)return;const e=dt(t),{container:n}=e;let i=e.target;const s=new Gt({source:this.source,originalSource:this.originalSource,sourceContainer:n,sensorEvent:e});this.trigger(s),s.canceled()&&e.cancel(),i=E(i,this.options.draggable);const o=E(e.target,this.containers),a=e.overContainer||o,l=this.currentOverContainer&&a!==this.currentOverContainer,d=this.currentOver&&i!==this.currentOver,m=a&&this.currentOverContainer!==a,u=o&&i&&this.currentOver!==i;if(d){const c=new Jt({source:this.source,originalSource:this.originalSource,sourceContainer:n,sensorEvent:e,over:this.currentOver,overContainer:this.currentOverContainer});this.currentOver.classList.remove(...this.getClassNamesFor("draggable:over")),this.currentOver=null,this.trigger(c)}if(l){const c=new Zt({source:this.source,originalSource:this.originalSource,sourceContainer:n,sensorEvent:e,overContainer:this.currentOverContainer});this.currentOverContainer.classList.remove(...this.getClassNamesFor("container:over")),this.currentOverContainer=null,this.trigger(c)}if(m){a.classList.add(...this.getClassNamesFor("container:over"));const c=new Qt({source:this.source,originalSource:this.originalSource,sourceContainer:n,sensorEvent:e,overContainer:a});this.currentOverContainer=a,this.trigger(c)}if(u){i.classList.add(...this.getClassNamesFor("draggable:over"));const c=new vt({source:this.source,originalSource:this.originalSource,sourceContainer:n,sensorEvent:e,overContainer:a,over:i});this.currentOver=i,this.trigger(c)}}[nt](t){if(!this.dragging)return;this.dragging=!1;const e=new Ft({source:this.source,originalSource:this.originalSource,sensorEvent:t?t.sensorEvent:null,sourceContainer:this.sourceContainer});this.trigger(e),e.canceled()||this.source.parentNode.insertBefore(this.originalSource,this.source),this.source.remove(),this.originalSource.style.display="",this.source.classList.remove(...this.getClassNamesFor("source:dragging")),this.originalSource.classList.remove(...this.getClassNamesFor("source:original")),this.originalSource.classList.add(...this.getClassNamesFor("source:placed")),this.sourceContainer.classList.add(...this.getClassNamesFor("container:placed")),this.sourceContainer.classList.remove(...this.getClassNamesFor("container:dragging")),document.body.classList.remove(...this.getClassNamesFor("body:dragging")),Bt(document.body,""),this.currentOver&&this.currentOver.classList.remove(...this.getClassNamesFor("draggable:over")),this.currentOverContainer&&this.currentOverContainer.classList.remove(...this.getClassNamesFor("container:over")),this.lastPlacedSource=this.originalSource,this.lastPlacedContainer=this.sourceContainer,this.placedTimeoutID=setTimeout(()=>{this.lastPlacedSource&&this.lastPlacedSource.classList.remove(...this.getClassNamesFor("source:placed")),this.lastPlacedContainer&&this.lastPlacedContainer.classList.remove(...this.getClassNamesFor("container:placed")),this.lastPlacedSource=null,this.lastPlacedContainer=null},this.options.placedTimeout);const n=new ee({source:this.source,originalSource:this.originalSource,sensorEvent:t?t.sensorEvent:null,sourceContainer:this.sourceContainer});this.trigger(n),this.source=null,this.originalSource=null,this.currentOverContainer=null,this.currentOver=null,this.sourceContainer=null}[et](t){this[nt](t)}[rt](t){if(!this.dragging)return;const e=dt(t),n=this.source||E(e.originalEvent.target,this.options.draggable),i=new te({sensorEvent:e,source:n,pressure:e.pressure});this.trigger(i)}}st.Plugins={Announcement:Qe,Focusable:rr,Mirror:or,Scrollable:mr};st.Sensors={MouseSensor:Te,TouchSensor:Ae};function dt(r){return r.detail}function Bt(r,t){r.style.webkitUserSelect=t,r.style.mozUserSelect=t,r.style.msUserSelect=t,r.style.oUserSelect=t,r.style.userSelect=t}class it extends C{constructor(t){super(t),this.data=t}get dragEvent(){return this.data.dragEvent}}it.type="droppable";class pe extends it{get dropzone(){return this.data.dropzone}}pe.type="droppable:start";pe.cancelable=!0;class fe extends it{get dropzone(){return this.data.dropzone}}fe.type="droppable:dropped";fe.cancelable=!0;class ve extends it{get dropzone(){return this.data.dropzone}}ve.type="droppable:returned";ve.cancelable=!0;class be extends it{get dropzone(){return this.data.dropzone}}be.type="droppable:stop";be.cancelable=!0;class ot extends C{constructor(t){super(t),this.data=t}get dragEvent(){return this.data.dragEvent}}ot.type="swappable";class ye extends ot{}ye.type="swappable:start";ye.cancelable=!0;class Ee extends ot{get over(){return this.data.over}get overContainer(){return this.data.overContainer}}Ee.type="swappable:swap";Ee.cancelable=!0;class Cr extends ot{get swappedElement(){return this.data.swappedElement}}Cr.type="swappable:swapped";class wr extends ot{}wr.type="swappable:stop";class at extends C{constructor(t){super(t),this.data=t}get dragEvent(){return this.data.dragEvent}}at.type="sortable";class Se extends at{get startIndex(){return this.data.startIndex}get startContainer(){return this.data.startContainer}}Se.type="sortable:start";Se.cancelable=!0;class Ce extends at{get currentIndex(){return this.data.currentIndex}get over(){return this.data.over}get overContainer(){return this.data.dragEvent.overContainer}}Ce.type="sortable:sort";Ce.cancelable=!0;class xr extends at{get oldIndex(){return this.data.oldIndex}get newIndex(){return this.data.newIndex}get oldContainer(){return this.data.oldContainer}get newContainer(){return this.data.newContainer}}xr.type="sortable:sorted";class Dr extends at{get oldIndex(){return this.data.oldIndex}get newIndex(){return this.data.newIndex}get oldContainer(){return this.data.oldContainer}get newContainer(){return this.data.newContainer}}Dr.type="sortable:stop";export{st as D,Se as S,pe as a,be as b,E as c,fe as d,ve as e,Ce as f,xr as g,Dr as h};
