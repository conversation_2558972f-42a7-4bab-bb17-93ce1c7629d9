import{e as f,o as n,c as g,w as l,u as i,a as e,d as x,h as v,b as d,n as p,g as y,t as b,F as h}from"./app-f0078ddb.js";import{k as w,O as _,h as k}from"./radio-group-97521e36.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";const V={class:"grid grid-cols-1 gap-4 md:grid-cols-9 md:gap-8 2xl:grid-cols-2"},B={class:"mx-auto flex h-10 w-full space-x-1 self-center rounded-lg bg-gray-200 p-0.5 md:col-span-5 2xl:col-span-1"},$={__name:"OptionsDifficulty",emits:["update:difficulty"],setup(C,{emit:c}){const m=c,o=[{name:"positive",level:0},{name:"comparative",level:1},{name:"superlative",level:2}],r=f(o[0]);return(O,s)=>(n(),g(i(k),{modelValue:r.value,"onUpdate:modelValue":s[0]||(s[0]=t=>r.value=t)},{default:l(()=>[e("div",V,[s[1]||(s[1]=e("div",{class:"flex items-center justify-between md:col-span-4 2xl:col-span-1"},[e("span",{class:"flex grow flex-col"},[e("span",{as:"span",class:"text-sm leading-6 font-semibold text-gray-900",passive:""},"Difficulty"),e("span",{as:"span",class:"text-sm text-gray-500"},"Increase the challenge with these words.")])],-1)),e("div",B,[(n(),x(h,null,v(o,t=>d(i(_),{key:t.id,as:"template",value:t,onClick:a=>m("update:difficulty",t.level)},{default:l(({checked:a,active:u})=>[e("div",{class:p(["w-full cursor-pointer rounded-md py-2 text-center text-sm leading-5 font-medium font-semibold","ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden",a?"bg-white text-amber-600 shadow-sm":"text-gray-700"])},[d(i(w),{as:"span",class:"capitalize"},{default:l(()=>[y(b(t.name),1)]),_:2},1024),e("div",{class:p([u?"border":"border-2",a?"border-teal-500":"border-transparent","pointer-events-none absolute -inset-px rounded-lg"]),"aria-hidden":"true"},null,2)],2)]),_:2},1032,["value","onClick"])),64))])])]),_:1},8,["modelValue"]))}};export{$ as default};
