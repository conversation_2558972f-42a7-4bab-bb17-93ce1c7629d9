import{c as He}from"./env-c107754a.js";import{J as j,e as g,H as F,l as f,A as S,B as P,L as b,F as Ne,p as I,U as Be,I as U,K as C,Q as xe,V as We,X as je,z as Ie}from"./app-f0078ddb.js";import{a as Ue,t as Ve,w as Ye}from"./use-outside-click-484df218.js";import{f as Z,u as ee,o as qe}from"./keyboard-982fc047.js";import{A as k,o as w,u as _,i as oe,N as de}from"./render-c34c346a.js";import{i as V,S as A,P as K,N as D,T as Ge}from"./focus-management-8406d052.js";import{t as pe}from"./micro-task-89dcd6af.js";import{o as me}from"./disposables-4ddc41dd.js";import{l as ze,i as G}from"./open-closed-7f51e238.js";import{k as Ke}from"./description-cd3ec634.js";function _e(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let O=[];_e(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&O[0]!==t.target&&(O.unshift(t.target),O=O.filter(l=>l!=null&&l.isConnected),O.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function ge(e,t,l,n){He.isServer||j(a=>{e=e??window,e.addEventListener(t,l,n),a(()=>e.removeEventListener(t,l,n))})}var W=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(W||{});function Qe(){let e=g(0);return Ue("keydown",t=>{t.key==="Tab"&&(e.value=t.shiftKey?1:0)}),e}function he(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let l of e.value){let n=w(l);n instanceof HTMLElement&&t.add(n)}return t}var we=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(we||{});let B=Object.assign(F({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:g(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:l,expose:n}){let a=g(null);n({el:a,$el:a});let o=f(()=>V(a)),u=g(!1);S(()=>u.value=!0),P(()=>u.value=!1),Xe({ownerDocument:o},f(()=>u.value&&!!(e.features&16)));let r=Ze({ownerDocument:o,container:a,initialFocus:f(()=>e.initialFocus)},f(()=>u.value&&!!(e.features&2)));et({ownerDocument:o,container:a,containers:e.containers,previousActiveElement:r},f(()=>u.value&&!!(e.features&8)));let i=Qe();function d(m){let p=w(a);p&&(y=>y())(()=>{_(i.value,{[W.Forwards]:()=>{K(p,D.First,{skipElements:[m.relatedTarget]})},[W.Backwards]:()=>{K(p,D.Last,{skipElements:[m.relatedTarget]})}})})}let s=g(!1);function E(m){m.key==="Tab"&&(s.value=!0,requestAnimationFrame(()=>{s.value=!1}))}function v(m){if(!u.value)return;let p=he(e.containers);w(a)instanceof HTMLElement&&p.add(w(a));let y=m.relatedTarget;y instanceof HTMLElement&&y.dataset.headlessuiFocusGuard!=="true"&&(ye(p,y)||(s.value?K(w(a),_(i.value,{[W.Forwards]:()=>D.Next,[W.Backwards]:()=>D.Previous})|D.WrapAround,{relativeTo:m.target}):m.target instanceof HTMLElement&&A(m.target)))}return()=>{let m={},p={ref:a,onKeydown:E,onFocusout:v},{features:y,initialFocus:T,containers:H,...M}=e;return b(Ne,[!!(y&4)&&b(Z,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:ee.Focusable}),k({ourProps:p,theirProps:{...t,...M},slot:m,attrs:t,slots:l,name:"FocusTrap"}),!!(y&4)&&b(Z,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:d,features:ee.Focusable})])}}}),{features:we});function Je(e){let t=g(O.slice());return I([e],([l],[n])=>{n===!0&&l===!1?pe(()=>{t.value.splice(0)}):n===!1&&l===!0&&(t.value=O.slice())},{flush:"post"}),()=>{var l;return(l=t.value.find(n=>n!=null&&n.isConnected))!=null?l:null}}function Xe({ownerDocument:e},t){let l=Je(t);S(()=>{j(()=>{var n,a;t.value||((n=e.value)==null?void 0:n.activeElement)===((a=e.value)==null?void 0:a.body)&&A(l())},{flush:"post"})}),P(()=>{t.value&&A(l())})}function Ze({ownerDocument:e,container:t,initialFocus:l},n){let a=g(null),o=g(!1);return S(()=>o.value=!0),P(()=>o.value=!1),S(()=>{I([t,l,n],(u,r)=>{if(u.every((d,s)=>(r==null?void 0:r[s])===d)||!n.value)return;let i=w(t);i&&pe(()=>{var d,s;if(!o.value)return;let E=w(l),v=(d=e.value)==null?void 0:d.activeElement;if(E){if(E===v){a.value=v;return}}else if(i.contains(v)){a.value=v;return}E?A(E):K(i,D.First|D.NoScroll)===Ge.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),a.value=(s=e.value)==null?void 0:s.activeElement})},{immediate:!0,flush:"post"})}),a}function et({ownerDocument:e,container:t,containers:l,previousActiveElement:n},a){var o;ge((o=e.value)==null?void 0:o.defaultView,"focus",u=>{if(!a.value)return;let r=he(l);w(t)instanceof HTMLElement&&r.add(w(t));let i=n.value;if(!i)return;let d=u.target;d&&d instanceof HTMLElement?ye(r,d)?(n.value=d,A(d)):(u.preventDefault(),u.stopPropagation(),A(i)):A(n.value)},!0)}function ye(e,t){for(let l of e)if(l.contains(t))return!0;return!1}function tt(e){let t=Be(e.getSnapshot());return P(e.subscribe(()=>{t.value=e.getSnapshot()})),t}function lt(e,t){let l=e(),n=new Set;return{getSnapshot(){return l},subscribe(a){return n.add(a),()=>n.delete(a)},dispatch(a,...o){let u=t[a].call(l,...o);u&&(l=u,n.forEach(r=>r()))}}}function nt(){let e;return{before({doc:t}){var l;let n=t.documentElement;e=((l=t.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:t,d:l}){let n=t.documentElement,a=n.clientWidth-n.offsetWidth,o=e-a;l.style(n,"paddingRight",`${o}px`)}}}function at(){return Ve()?{before({doc:e,d:t,meta:l}){function n(a){return l.containers.flatMap(o=>o()).some(o=>o.contains(a))}t.microTask(()=>{var a;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let r=me();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let o=(a=window.scrollY)!=null?a:window.pageYOffset,u=null;t.addEventListener(e,"click",r=>{if(r.target instanceof HTMLElement)try{let i=r.target.closest("a");if(!i)return;let{hash:d}=new URL(i.href),s=e.querySelector(d);s&&!n(s)&&(u=s)}catch{}},!0),t.addEventListener(e,"touchstart",r=>{if(r.target instanceof HTMLElement)if(n(r.target)){let i=r.target;for(;i.parentElement&&n(i.parentElement);)i=i.parentElement;t.style(i,"overscrollBehavior","contain")}else t.style(r.target,"touchAction","none")}),t.addEventListener(e,"touchmove",r=>{if(r.target instanceof HTMLElement){if(r.target.tagName==="INPUT")return;if(n(r.target)){let i=r.target;for(;i.parentElement&&i.dataset.headlessuiPortal!==""&&!(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth);)i=i.parentElement;i.dataset.headlessuiPortal===""&&r.preventDefault()}else r.preventDefault()}},{passive:!1}),t.add(()=>{var r;let i=(r=window.scrollY)!=null?r:window.pageYOffset;o!==i&&window.scrollTo(0,o),u&&u.isConnected&&(u.scrollIntoView({block:"nearest"}),u=null)})})}}:{}}function ot(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function rt(e){let t={};for(let l of e)Object.assign(t,l(t));return t}let R=lt(()=>new Map,{PUSH(e,t){var l;let n=(l=this.get(e))!=null?l:{doc:e,count:0,d:me(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let l=this.get(e);return l&&(l.count--,l.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:l}){let n={doc:e,d:t,meta:rt(l)},a=[at(),nt(),ot()];a.forEach(({before:o})=>o==null?void 0:o(n)),a.forEach(({after:o})=>o==null?void 0:o(n))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});R.subscribe(()=>{let e=R.getSnapshot(),t=new Map;for(let[l]of e)t.set(l,l.documentElement.style.overflow);for(let l of e.values()){let n=t.get(l.doc)==="hidden",a=l.count!==0;(a&&!n||!a&&n)&&R.dispatch(l.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",l),l.count===0&&R.dispatch("TEARDOWN",l)}});function ut(e,t,l){let n=tt(R),a=f(()=>{let o=e.value?n.value.get(e.value):void 0;return o?o.count>0:!1});return I([e,t],([o,u],[r],i)=>{if(!o||!u)return;R.dispatch("PUSH",o,l);let d=!1;i(()=>{d||(R.dispatch("POP",r??o,l),d=!0)})},{immediate:!0}),a}let X=new Map,x=new Map;function ce(e,t=g(!0)){j(l=>{var n;if(!t.value)return;let a=w(e);if(!a)return;l(function(){var u;if(!a)return;let r=(u=x.get(a))!=null?u:1;if(r===1?x.delete(a):x.set(a,r-1),r!==1)return;let i=X.get(a);i&&(i["aria-hidden"]===null?a.removeAttribute("aria-hidden"):a.setAttribute("aria-hidden",i["aria-hidden"]),a.inert=i.inert,X.delete(a))});let o=(n=x.get(a))!=null?n:0;x.set(a,o+1),o===0&&(X.set(a,{"aria-hidden":a.getAttribute("aria-hidden"),inert:a.inert}),a.setAttribute("aria-hidden","true"),a.inert=!0)})}function it({defaultContainers:e=[],portals:t,mainTreeNodeRef:l}={}){let n=g(null),a=V(n);function o(){var u,r,i;let d=[];for(let s of e)s!==null&&(s instanceof HTMLElement?d.push(s):"value"in s&&s.value instanceof HTMLElement&&d.push(s.value));if(t!=null&&t.value)for(let s of t.value)d.push(s);for(let s of(u=a==null?void 0:a.querySelectorAll("html > *, body > *"))!=null?u:[])s!==document.body&&s!==document.head&&s instanceof HTMLElement&&s.id!=="headlessui-portal-root"&&(s.contains(w(n))||s.contains((i=(r=w(n))==null?void 0:r.getRootNode())==null?void 0:i.host)||d.some(E=>s.contains(E))||d.push(s));return d}return{resolveContainers:o,contains(u){return o().some(r=>r.contains(u))},mainTreeNodeRef:n,MainTreeNode(){return l!=null?null:b(Z,{features:ee.Hidden,ref:n})}}}let Ee=Symbol("ForcePortalRootContext");function st(){return C(Ee,!1)}let fe=F({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:t,attrs:l}){return U(Ee,e.force),()=>{let{force:n,...a}=e;return k({theirProps:a,ourProps:{},slot:{},slots:t,attrs:l,name:"ForcePortalRoot"})}}}),be=Symbol("StackContext");var te=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(te||{});function dt(){return C(be,()=>{})}function ct({type:e,enabled:t,element:l,onUpdate:n}){let a=dt();function o(...u){n==null||n(...u),a(...u)}S(()=>{I(t,(u,r)=>{u?o(0,e,l):r===!0&&o(1,e,l)},{immediate:!0,flush:"sync"})}),P(()=>{t.value&&o(1,e,l)}),U(be,o)}function ft(e){let t=V(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let l=t.getElementById("headlessui-portal-root");if(l)return l;let n=t.createElement("div");return n.setAttribute("id","headlessui-portal-root"),t.body.appendChild(n)}const le=new WeakMap;function vt(e){var t;return(t=le.get(e))!=null?t:0}function ve(e,t){let l=t(vt(e));return l<=0?le.delete(e):le.set(e,l),l}let pt=F({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:l}){let n=g(null),a=f(()=>V(n)),o=st(),u=C(Te,null),r=g(o===!0||u==null?ft(n.value):u.resolveTarget());r.value&&ve(r.value,v=>v+1);let i=g(!1);S(()=>{i.value=!0}),j(()=>{o||u!=null&&(r.value=u.resolveTarget())});let d=C(ne,null),s=!1,E=je();return I(n,()=>{if(s||!d)return;let v=w(n);v&&(P(d.register(v),E),s=!0)}),P(()=>{var v,m;let p=(v=a.value)==null?void 0:v.getElementById("headlessui-portal-root");!p||r.value!==p||ve(r.value,y=>y-1)||r.value.children.length>0||(m=r.value.parentElement)==null||m.removeChild(r.value)}),()=>{if(!i.value||r.value===null)return null;let v={ref:n,"data-headlessui-portal":""};return b(xe,{to:r.value},k({ourProps:v,theirProps:e,slot:{},attrs:l,slots:t,name:"Portal"}))}}}),ne=Symbol("PortalParentContext");function mt(){let e=C(ne,null),t=g([]);function l(o){return t.value.push(o),e&&e.register(o),()=>n(o)}function n(o){let u=t.value.indexOf(o);u!==-1&&t.value.splice(u,1),e&&e.unregister(o)}let a={register:l,unregister:n,portals:t};return[t,F({name:"PortalWrapper",setup(o,{slots:u}){return U(ne,a),()=>{var r;return(r=u.default)==null?void 0:r.call(u)}}})]}let Te=Symbol("PortalGroupContext"),gt=F({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:l}){let n=We({resolveTarget(){return e.target}});return U(Te,n),()=>{let{target:a,...o}=e;return k({theirProps:o,ourProps:{},slot:{},attrs:t,slots:l,name:"PortalGroup"})}}});var ht=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ht||{});let ae=Symbol("DialogContext");function re(e){let t=C(ae,null);if(t===null){let l=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,re),l}return t}let z="DC8F892D-2EBD-447C-A4C8-A03058436FF4",Dt=F({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:z},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:l,slots:n,expose:a}){var o,u;let r=(o=e.id)!=null?o:`headlessui-dialog-${oe()}`,i=g(!1);S(()=>{i.value=!0});let d=!1,s=f(()=>e.role==="dialog"||e.role==="alertdialog"?e.role:(d||(d=!0,console.warn(`Invalid role [${s}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),E=g(0),v=ze(),m=f(()=>e.open===z&&v!==null?(v.value&G.Open)===G.Open:e.open),p=g(null),y=f(()=>V(p));if(a({el:p,$el:p}),!(e.open!==z||v!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof m.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${m.value===z?void 0:e.open}`);let T=f(()=>i.value&&m.value?0:1),H=f(()=>T.value===0),M=f(()=>E.value>1),ue=C(ae,null)!==null,[$e,Le]=mt(),{resolveContainers:Q,mainTreeNodeRef:ie,MainTreeNode:Se}=it({portals:$e,defaultContainers:[f(()=>{var c;return(c=N.panelRef.value)!=null?c:p.value})]}),Pe=f(()=>M.value?"parent":"leaf"),se=f(()=>v!==null?(v.value&G.Closing)===G.Closing:!1),Fe=f(()=>ue||se.value?!1:H.value),De=f(()=>{var c,h,$;return($=Array.from((h=(c=y.value)==null?void 0:c.querySelectorAll("body > *"))!=null?h:[]).find(L=>L.id==="headlessui-portal-root"?!1:L.contains(w(ie))&&L instanceof HTMLElement))!=null?$:null});ce(De,Fe);let Oe=f(()=>M.value?!0:H.value),Re=f(()=>{var c,h,$;return($=Array.from((h=(c=y.value)==null?void 0:c.querySelectorAll("[data-headlessui-portal]"))!=null?h:[]).find(L=>L.contains(w(ie))&&L instanceof HTMLElement))!=null?$:null});ce(Re,Oe),ct({type:"Dialog",enabled:f(()=>T.value===0),element:p,onUpdate:(c,h)=>{if(h==="Dialog")return _(c,{[te.Add]:()=>E.value+=1,[te.Remove]:()=>E.value-=1})}});let Ae=Ke({name:"DialogDescription",slot:f(()=>({open:m.value}))}),Y=g(null),N={titleId:Y,panelRef:g(null),dialogState:T,setTitleId(c){Y.value!==c&&(Y.value=c)},close(){t("close",!1)}};U(ae,N);let Ce=f(()=>!(!H.value||M.value));Ye(Q,(c,h)=>{c.preventDefault(),N.close(),Ie(()=>h==null?void 0:h.focus())},Ce);let ke=f(()=>!(M.value||T.value!==0));ge((u=y.value)==null?void 0:u.defaultView,"keydown",c=>{ke.value&&(c.defaultPrevented||c.key===qe.Escape&&(c.preventDefault(),c.stopPropagation(),N.close()))});let Me=f(()=>!(se.value||T.value!==0||ue));return ut(y,Me,c=>{var h;return{containers:[...(h=c.containers)!=null?h:[],Q]}}),j(c=>{if(T.value!==0)return;let h=w(p);if(!h)return;let $=new ResizeObserver(L=>{for(let J of L){let q=J.target.getBoundingClientRect();q.x===0&&q.y===0&&q.width===0&&q.height===0&&N.close()}});$.observe(h),c(()=>$.disconnect())}),()=>{let{open:c,initialFocus:h,...$}=e,L={...l,ref:p,id:r,role:s.value,"aria-modal":T.value===0?!0:void 0,"aria-labelledby":Y.value,"aria-describedby":Ae.value},J={open:T.value===0};return b(fe,{force:!0},()=>[b(pt,()=>b(gt,{target:p.value},()=>b(fe,{force:!1},()=>b(B,{initialFocus:h,containers:Q,features:H.value?_(Pe.value,{parent:B.features.RestoreFocus,leaf:B.features.All&~B.features.FocusLock}):B.features.None},()=>b(Le,{},()=>k({ourProps:L,theirProps:{...$,...l},slot:J,attrs:l,slots:n,visible:T.value===0,features:de.RenderStrategy|de.Static,name:"Dialog"})))))),b(Se)])}}}),Ot=F({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l,expose:n}){var a;let o=(a=e.id)!=null?a:`headlessui-dialog-panel-${oe()}`,u=re("DialogPanel");n({el:u.panelRef,$el:u.panelRef});function r(i){i.stopPropagation()}return()=>{let{...i}=e,d={id:o,ref:u.panelRef,onClick:r};return k({ourProps:d,theirProps:i,slot:{open:u.dialogState.value===0},attrs:t,slots:l,name:"DialogPanel"})}}}),Rt=F({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:l}){var n;let a=(n=e.id)!=null?n:`headlessui-dialog-title-${oe()}`,o=re("DialogTitle");return S(()=>{o.setTitleId(a),P(()=>o.setTitleId(null))}),()=>{let{...u}=e;return k({ourProps:{id:a},theirProps:u,slot:{open:o.dialogState.value===0},attrs:t,slots:l,name:"DialogTitle"})}}});export{ge as E,Ot as G,it as N,Rt as V,Dt as Y,W as d,Qe as n,mt as q,O as t};
