import{C as l,L as p,a as u,b as m,P as f,c as h}from"./chart-183f17ad.js";import{e as o,p as y,A as b,o as x,d as C,a as g}from"./app-f0078ddb.js";/* empty css            */const E={__name:"ExperienceChart",props:{labels:{type:Array,required:!0},data:{type:Array,required:!0}},setup(i){const a=i,r=o(null);let t;const d=Math.max(...a.data)<=10?10:void 0,e={labels:a.labels,datasets:[{label:"experience",data:a.data,backgroundColor:"#34d399",borderColor:"#34d399",borderWidth:1,pointRadius:3}]},s=o({plugins:{title:{display:!1}},responsive:!0,maintainAspectRatio:!1,scales:{x:{display:!1},y:{type:"linear",position:"left",ticks:{beginAtZero:!0},min:0,max:d,grace:"5% 5%"}}}),n=()=>{t&&t.destroy(),t=new l(r.value,{type:"line",data:e,options:s.value})};return y(()=>[a.labels,a.data],c=>{e.labels=a.labels,e.datasets[0].data=a.data,a.data2&&(e.datasets[1].data=a.data2),s.value.scales.y.max=Math.max(...a.data)<=10?10:void 0,n()},{deep:!0}),b(()=>{n()}),l.register(p,u,m,f,h),(c,_)=>(x(),C("div",null,[g("canvas",{ref_key:"chartRef",ref:r,id:"my-chart-id",height:"160"},null,512)]))}};export{E as default};
