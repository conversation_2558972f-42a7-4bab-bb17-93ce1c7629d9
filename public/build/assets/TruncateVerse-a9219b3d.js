import{r as x}from"./removePunctuation-702d8a66.js";import{e as i,p as f,o,d as u,h,n as y,u as c,f as k,a as w,t as C,g as D,F as V}from"./app-f0078ddb.js";const _={key:0,class:"text-gray-400",textContent:"… "},b=["textContent"],B={key:1,class:"text-gray-400",textContent:"…"},N={__name:"TruncateVerse",props:{verse:{type:String,required:!0},token:{type:String,required:!0},isDark:{type:Boolean,default:!1}},setup(r){const n=r;let e=i([]),p=i(!1),d=i(!1),l=i(parseInt(n.token.split(":")[1],10));const g=()=>{e.value=n.verse.split(" ").map((t,a)=>({token:t,index:a})),p.value=!1,d.value=!1,l.value>10&&(p.value=!0,e.value=e.value.slice(l.value-10)),e.value.length>20&&(d.value=!0,e.value=e.value.slice(0,20))};g(),f(()=>n.verse,()=>{g()}),f(()=>n.token,t=>{l.value=parseInt(t.split(":")[1],10),g()});const v=(t,a)=>x(t)===n.token.split(":")[0]&&a===l.value;return(t,a)=>(o(!0),u(V,null,h(c(e),(s,m)=>(o(),u("span",{key:s.index,class:y(["inline-block bg-transparent font-intro font-medium",r.isDark?"text-white":"text-gray-800"])},[c(p)&&m==0?(o(),u("span",_)):k("",!0),w("span",{class:y({"z-0 rounded-sm bg-yellow-100 ring-4 ring-yellow-100":!r.isDark&&v(s.token,s.index),"z-0 rounded-sm bg-yellow-600 ring-4 ring-yellow-600":r.isDark&&v(s.token,s.index)}),textContent:C(s.token)},null,10,b),a[0]||(a[0]=D(" ")),c(d)&&c(e).length-1===m?(o(),u("span",B)):k("",!0)],2))),128))}};export{N as _};
