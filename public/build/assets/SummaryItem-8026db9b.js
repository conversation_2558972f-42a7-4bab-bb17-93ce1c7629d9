import{_ as p}from"./TruncateVerse-a9219b3d.js";import{r as g}from"./CheckCircleIcon-d86d1232.js";import{r as f}from"./XCircleIcon-63af2b2a.js";import{r as b}from"./ArrowLongRightIcon-d06130d7.js";import{l as _,o as t,d as e,a as r,b as u,u as h,f as o,t as a,F as x,h as y,n as c}from"./app-f0078ddb.js";import"./removePunctuation-702d8a66.js";/* empty css            */const k={class:"flex w-full flex-row items-center py-8"},C={class:"flex grow flex-row"},L={class:"flex grow flex-col items-center sm:items-start"},A={class:"mb-2 inline sm:mr-2 sm:mb-0 sm:hidden"},j={key:0,class:"text-blue-600"},M={key:1,class:"text-pink-600"},H={key:0,class:"flex flex-col items-center sm:flex-row"},T={class:"text-center text-xl font-semibold text-gray-900 sm:text-left"},$=["innerHTML"],B=["innerHTML"],N={class:"mt-2 mb-2 text-center text-sm font-medium text-gray-600 sm:mt-1 sm:text-left"},U={key:0,class:"flex flex-wrap text-lg"},V={key:1},Y={key:1,class:"mt-2 grid gap-2 text-xs sm:grid-cols-4"},E={class:"mx-auto w-full"},F={key:0,class:"mb-2 text-center text-xs font-medium text-gray-500"},I={class:"flex items-center py-1 text-sm"},S={class:"px-4 text-left font-medium"},W={class:"inline-flex h-6 items-center rounded-full border-2 border-teal-400 bg-teal-100 px-3 text-xs font-medium text-gray-800 shadow-lg select-none"},z={key:0},D={class:"flex items-center py-1 text-sm"},O={class:"w-2/5 px-4 text-right font-medium"},R={key:0},q={key:1},G={key:3,class:"mt-2"},J=["id"],K={class:"mx-auto self-center"},P={key:0},Q={key:1},X={class:"mx-auto w-full"},Z={key:0,class:"mb-2 text-center text-xs font-medium text-gray-500"},tt={class:"mx-auto flex w-full items-center justify-center"},et={class:"flex py-1"},st={key:0,class:"mx-px rounded-l-full border-2 border-gray-400 bg-white py-1 pr-1 pl-2 text-sm font-medium text-gray-800 shadow-lg select-none"},rt={key:1,class:"mx-px rounded-r-full border-2 border-teal-400 bg-teal-100 py-1 pr-2 pl-1 text-sm font-medium text-gray-800 shadow-lg select-none"},it={key:0},lt={class:"mx-auto flex w-full items-center justify-center"},nt={class:"flex py-1"},ot={class:"mx-px rounded-l-full border-2 border-gray-400 bg-white py-1 pr-1 pl-2 text-sm font-medium text-gray-800 shadow-lg select-none"},at={class:"mx-px rounded-r-full border-2 border-red-400 bg-red-100 py-1 pr-2 pl-1 text-sm font-medium text-gray-800 shadow-lg select-none"},mt={class:"mx-auto w-full"},dt={key:0,class:"mb-2 text-center text-xs font-medium text-gray-500"},ct={class:"mx-auto flex w-full items-center justify-center"},ut={class:"flex py-1"},xt={class:"mx-px rounded-lg border-2 border-teal-400 bg-teal-100 px-2 py-1 text-sm font-medium text-gray-800 shadow-lg select-none"},yt={key:0},ht={class:"mx-auto flex w-full items-center justify-center"},gt={class:"flex py-1"},ft={class:"ml-4 flex hidden items-center sm:inline-block"},bt={key:0,class:"text-blue-600"},wt={key:1,class:"text-pink-600"},jt={__name:"SummaryItem",props:{item:Object},setup(s){const l=s,w=m=>!l.item.attempt||!l.item.attempt.split("")[m]?!1:l.item.attempt.split("")[m].toUpperCase()==l.item.answer.split("")[m].toUpperCase(),v=(m,d)=>m==l.item.options[d];return _(()=>{const m=l.item.attempt?l.item.attempt.split(""):[],d=l.item.answer?l.item.answer.split(""):[];for(;m.length<d.length;)m.push("");return m}),(m,d)=>(t(),e("div",k,[r("div",C,[r("div",L,[r("div",A,[s.item.correct==1?(t(),e("div",j,[u(h(g),{class:"h-8 w-8"})])):(t(),e("div",M,[u(h(f),{class:"h-8 w-8"})]))]),s.item.type!="match"?(t(),e("div",H,[r("h4",T,[s.item.activity_type=="custom"?(t(),e("span",{key:0,innerHTML:s.item.instructions.replace("_____","<span class=text-blue-600>"+s.item.stem.split(":")[0]+"</span>")},null,8,$)):(t(),e("span",{key:1,innerHTML:s.item.stem},null,8,B))])])):o("",!0),r("div",null,[r("h5",N,[s.item.activity_type=="custom"?(t(),e("span",U,[u(p,{token:s.item.stem,verse:s.item.verse},null,8,["token","verse"])])):(t(),e("span",V,a(s.item.instructions),1))])]),s.item.type=="mc"?(t(),e("div",Y,[(t(!0),e(x,null,y(s.item.options,(i,n)=>(t(),e("div",{class:c(["rounded-lg border-2 px-2 py-1 text-center font-semibold",[{"border-lime-400 bg-lime-100 text-gray-900":n===0&&i==l.item.answer},{"border-teal-400 bg-teal-100 text-gray-900":n===1&&i==l.item.answer},{"border-purple-400 bg-purple-100 text-gray-900":n===2&&i==l.item.answer},{"border-sky-400 bg-sky-100 text-gray-900":n===3&&i==l.item.answer},{"border-gray-400 bg-gray-50 text-gray-500":l.item.attempt!=i},{"border-gray-500 bg-gray-300 text-gray-600":l.item.attempt==i&&l.item.attempt!=l.item.answer}]])},a(i),3))),256))])):o("",!0),s.item.type=="match"?(t(),e("div",{key:2,class:c(["mt-2 grid",s.item.correct==0?"grid-cols-2":"grid-cols-1"])},[r("div",E,[s.item.correct==0?(t(),e("h5",F," Answer ")):o("",!0),(t(!0),e(x,null,y(s.item.options,i=>(t(),e("div",I,[r("div",S,a(i.split(":")[0]),1),u(h(b),{class:"mr-4 inline h-5 w-5 text-gray-500"}),r("div",W,a(i.split(":")[1]),1)]))),256))]),s.item.correct==0?(t(),e("div",z,[d[0]||(d[0]=r("h5",{class:"mb-2 text-center text-xs font-medium text-gray-500"}," Your Attempt ",-1)),(t(!0),e(x,null,y(s.item.attempt.split("|"),(i,n)=>(t(),e("div",D,[r("div",O,a(i.split(":")[0]),1),u(h(b),{class:"mr-4 inline h-5 w-5 text-gray-500"}),r("div",{class:c(["inline-flex h-6 items-center rounded-full border-2 px-3 text-xs font-medium text-gray-800 shadow-lg select-none",v(i,n)?"border-teal-400 bg-teal-100":"border-gray-500 bg-gray-300 text-gray-600"])},[i.split(":")[1].length>0?(t(),e("span",R,a(i.split(":")[1]),1)):(t(),e("span",q,"no answer"))],2)]))),256))])):o("",!0)],2)):o("",!0),s.item.type=="type"?(t(),e("div",G,[(t(!0),e(x,null,y(s.item.answer.split(""),(i,n)=>(t(),e("div",{key:n,class:c(["inline-block",n==0?"mr-1 ml-0":"mx-1"])},[r("div",{id:"input"+n,ref_for:!0,ref:"input",type:"text",maxlength:"1",class:c(["items-auto flex h-12 border-t-0 border-r-0 border-l-0 p-1 text-center uppercase transition duration-150 ease-in-out focus:border-teal-500 focus:ring-white focus:outline-hidden",[s.item.answer.length<9?"w-10 text-3xl":"w-8 text-2xl",w(n)?"rounded-lg border-b-0 bg-teal-500 text-white":"rounded-lg border-b-0 bg-red-500 text-white"]])},[r("p",K,[i?(t(),e("span",P,a(i.toUpperCase()),1)):(t(),e("span",Q," "))])],10,J)],2))),128))])):o("",!0),s.item.type=="drag"?(t(),e("div",{key:4,class:c(["mt-2 grid",s.item.correct==0?"sm:grid-cols-2":"grid-cols-1"])},[r("div",X,[s.item.correct==0?(t(),e("h5",Z," Answer ")):o("",!0),r("div",tt,[(t(!0),e(x,null,y(s.item.answer.split("|"),(i,n)=>(t(),e("div",et,[n==0?(t(),e("div",st,a(i),1)):o("",!0),n>0?(t(),e("div",rt,a(i),1)):o("",!0)]))),256))])]),s.item.correct==0?(t(),e("div",it,[d[1]||(d[1]=r("h5",{class:"mb-2 text-center text-xs font-medium text-gray-500"}," Your Attempt ",-1)),r("div",lt,[r("div",nt,[r("div",ot,a(s.item.answer.split("|")[0]),1),r("div",at,a(s.item.attempt.replace(new RegExp(`^${s.item.answer.split("|")[0]}`),"").toLowerCase()),1)])])])):o("",!0)],2)):o("",!0),s.item.type=="dragWithWords"?(t(),e("div",{key:5,class:c(["mt-2 grid",s.item.correct==0?"grid-cols-2":"grid-cols-1"])},[r("div",mt,[s.item.correct==0?(t(),e("h5",dt," Answer ")):o("",!0),r("div",ct,[(t(!0),e(x,null,y(s.item.answer.split(" "),(i,n)=>(t(),e("div",ut,[r("div",xt,a(i),1)]))),256))])]),s.item.correct==0?(t(),e("div",yt,[d[2]||(d[2]=r("h5",{class:"mb-2 text-center text-xs font-medium text-gray-500"}," Your Attempt ",-1)),r("div",ht,[(t(!0),e(x,null,y(s.item.attempt.split(" "),(i,n)=>(t(),e("div",gt,[r("div",{class:c(["mx-px rounded-lg border-2 px-2 py-1 text-sm font-medium text-gray-800 shadow-lg select-none",[i.toLowerCase()==s.item.answer.split(" ")[n]?"border-teal-400 bg-teal-100":"border-red-400 bg-red-100"]])},a(i),3)]))),256))])])):o("",!0)],2)):o("",!0)]),r("div",ft,[s.item.correct==1?(t(),e("div",bt,[u(h(g),{class:"h-8 w-8"})])):(t(),e("div",wt,[u(h(f),{class:"h-8 w-8"})]))])])]))}};export{jt as default};
