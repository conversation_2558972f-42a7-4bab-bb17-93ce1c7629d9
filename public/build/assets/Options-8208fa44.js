import b from"./OptionsMode-109debf5.js";import w from"./OptionsWords-ece543ea.js";import{_ as p}from"./ToggleItem-94c3ab1e.js";import y from"./OptionsDifficulty-1a9565c6.js";import{V as k,l as c,o as N,d as L,a as l,t as i,b as d,u as _}from"./app-f0078ddb.js";import"./radio-group-97521e36.js";import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";import"./use-resolve-button-type-24d8b5c5.js";/* empty css            */const x={class:"flex-1 py-2"},v={class:"mt-4 text-base font-normal text-gray-600"},W={class:"mt-8 rounded-lg bg-gray-100 p-6"},U={class:"mt-12 grid grid-cols-1 gap-12 border-t border-gray-300 pb-12 pt-12 sm:grid-cols-2 sm:pb-6 sm:pt-8"},z={__name:"Options",props:{words:Object,learned:Array,names:Number},emits:["update:options","update:totalWords"],setup(f,{emit:u}){const r=f;let o=k({mode:"unlimited",words:"all",time:0,difficulty:0,toggleLearned:!1,toggleNames:!1});const n=u,m=c(()=>{let t=r.words.total;switch(o.words){case"all":o.toggleLearned&&(t=t-r.learned.length),o.toggleNames&&(t=t-r.names);break;case"core":t=r.words.core,o.toggleLearned&&(t=t-r.learned.filter(e=>e.core==1).length);break;case"not core":t=r.words.total-r.words.core,o.toggleLearned&&(t=t-r.learned.filter(e=>e.core==0).length),o.toggleNames&&(t=t-r.names);break}return n("update:totalWords",t),t}),g=c(()=>{let t=r.words.core;switch(o.words){case"not core":t=0;break;default:o.toggleLearned&&(t=t-r.learned.filter(e=>e.core==1).length);break}return t});function a(t,e){switch(t){case"words":o.words=e;break;case"time":o.time=e;break;case"difficulty":o.difficulty=e;break;case"toggleLearned":o.toggleLearned=e;break;case"toggleNames":o.toggleNames=e;break}n("update:options",o)}return(t,e)=>(N(),L("div",x,[e[6]||(e[6]=l("h1",{class:"text-4xl font-bold text-gray-900"},"Options",-1)),l("p",v,i(m.value)+" Total Words | "+i(g.value)+" Core | "+i(m.value-g.value)+" Not Core ",1),l("div",W,[d(b,{"current-mode":_(o).mode,"onUpdate:mode":e[0]||(e[0]=s=>a("time",s))},null,8,["current-mode"]),d(w,{class:"mt-12","onUpdate:words":e[1]||(e[1]=s=>a("words",s))}),d(y,{class:"mt-12","onUpdate:difficulty":e[2]||(e[2]=s=>a("difficulty",s))}),l("div",U,[e[5]||(e[5]=l("div",{class:"hidden bg-amber-600 bg-blue-700 bg-green-700"},null,-1)),d(p,{order:"left",class:"self-top",label:"Ignore Learned Words",description:"Practice only with words not marked as learned.","button-color":"amber","onUpdate:toggle":e[3]||(e[3]=s=>a("toggleLearned",s))}),d(p,{order:"left",class:"self-top",label:"Ignore Names",description:"Remove proper nouns from the vocabulary list.","onUpdate:toggle":e[4]||(e[4]=s=>a("toggleNames",s))})])])]))}};export{z as default};
