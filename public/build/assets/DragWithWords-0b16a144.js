import"./SortableEvent-286d5fef.js";import{S as L}from"./Sortable-ff8f985a.js";import{_ as f}from"./ButtonItem-718c0517.js";import{_ as D}from"./_plugin-vue_export-helper-c27b6911.js";import{e as h,A as S,o as u,d as m,a as s,F as T,h as F,u as A,b,w as p,g as v}from"./app-f0078ddb.js";/* empty css            */const M={class:"flex-1"},W=["innerHTML"],H=["innerHTML"],I={class:"relative mt-12 grid w-full grid-cols-1 justify-items-center sm:px-16"},q={class:"h-24 w-full"},N={id:"workBench",class:"flex h-full w-full items-start justify-center",sortableContainer:""},$=["id","onDblclick","innerHTML"],j={class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8 xl:px-10"},V={__name:"DragWithWords",props:{options:Array,stem:String,instructions:String},emits:["submit"],setup(d,{emit:x}){const w=d;let g=h([]);var l=h([]),n="";S(()=>{g.value=B(w.options),C()});const y=x;function _(){y("submit",{answer:l.value});var r=document.querySelectorAll("div#answerBench [sortable]");r.forEach(t=>{t.remove()});var e=document.querySelectorAll("div#workBench [sortable]");e.forEach(t=>{t.remove()})}function E(r){var e=document.getElementById(r.id);e.parentNode.id=="workBench"?document.getElementById("answerBench").appendChild(e):document.getElementById("workBench").appendChild(e);let t=n.containers.length,o=0;for(l.value=[];o<=t-1;)o==t-1?n.getDraggableElementsForContainer(n.containers[o]).forEach(a=>{c(a)}):n.getDraggableElementsForContainer(n.containers[o]).forEach(a=>{i(a),l.value.push(a.innerText)}),o++}function C(){n&&n.destroy(),n=new L(document.querySelectorAll("[sortableContainer]"),{draggable:"[sortable]",plugins:[],mirror:{constrainDimensions:!1},classes:{mirror:["bg-indigo-200"]}});let r=n.containers.length;n.on("sortable:sorted",()=>{let e=0;l.value=[],setTimeout(()=>{for(;e<=r-1;)e==r-1?n.getDraggableElementsForContainer(n.containers[e]).forEach(t=>{c(t)}):n.getDraggableElementsForContainer(n.containers[e]).forEach(t=>{i(t)}),e++},0)}).on("mirror:attached",function(e){e.mirror.style.position="fixed"}).on("drag:stop",()=>{let e=0;l.value=[],setTimeout(()=>{for(;e<=r-1;)e==r-1?n.getDraggableElementsForContainer(n.containers[e]).forEach(t=>{c(t)}):n.getDraggableElementsForContainer(n.containers[e]).forEach(t=>{i(t),l.value.push(t.innerText)}),e++},0)}).on("hook:destroyed",()=>{n.destroy()})}function k(){var r=document.querySelectorAll("div#answerBench [sortable]");r.forEach(e=>{i(e),document.getElementById("workBench").appendChild(e)})}function B(r){const e=[];for(let t=0;t<r.length;t++)e.push({id:t,data:r[t]});return e}function c(r){r.classList.add("mx-1"),r.classList.remove("mx-px")}function i(r){r.classList.contains("mx-1")&&(r.classList.remove("mx-1"),r.classList.add("mx-px"))}return(r,e)=>(u(),m("div",M,[s("h1",{class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900",innerHTML:d.stem},null,8,W),s("h3",{class:"text-center text-lg font-medium text-gray-600",innerHTML:d.instructions},null,8,H),s("div",I,[e[2]||(e[2]=s("div",{class:"h-24 w-full"},[s("div",{id:"answerBench",class:"flex h-full w-full items-center justify-center rounded-2xl border-2 border-dashed border-gray-400 py-2",sortableContainer:""})],-1)),e[3]||(e[3]=s("div",{class:"my-8 w-full border border-gray-400"},null,-1)),s("div",q,[s("div",N,[(u(!0),m(T,null,F(A(g),(t,o)=>(u(),m("div",{id:t.id,key:o,class:"mx-1 inline-flex cursor-move items-center rounded-lg border-2 border-gray-500 bg-gray-100 px-4 py-1 py-2 text-2xl font-medium text-gray-800 shadow-sm transition-colors transition-shadow duration-150 select-none hover:bg-gray-200 hover:shadow-lg",draggable:"",sortable:"",onDblclick:a=>E(t),innerHTML:t.data},null,40,$))),128))])])]),s("div",j,[b(f,{size:"lg",class:"w-full",color:"white",onClick:e[0]||(e[0]=t=>k())},{default:p(()=>e[4]||(e[4]=[v("Clear")])),_:1}),b(f,{size:"lg",class:"w-full",color:"pink",onClick:e[1]||(e[1]=t=>_())},{default:p(()=>e[5]||(e[5]=[v("Submit")])),_:1})])]))}},Q=D(V,[["__scopeId","data-v-db19b6ce"]]);export{Q as default};
