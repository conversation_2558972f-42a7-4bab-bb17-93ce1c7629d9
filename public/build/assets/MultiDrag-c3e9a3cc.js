import{_ as M}from"./AppLayout-33f062bc.js";import{_ as F}from"./Breadcrumbs-c96e9207.js";import{D as T,c as _,a as $,b as q,d as I,e as j}from"./SortableEvent-286d5fef.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";import{r as K}from"./ArrowLongRightIcon-d06130d7.js";import{e as x,V as O,A as R,z as P,bb as V,i as G,o as m,c as U,w,b as D,a as n,d as v,h as C,t as W,u as Z,F as E}from"./app-f0078ddb.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./ButtonItem-718c0517.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";/* empty css            */const g=Symbol("onDragStart"),f=Symbol("onDragMove"),h=Symbol("onDragStop"),S=Symbol("dropInDropZone"),k=Symbol("returnToOriginalDropzone"),B=Symbol("closestDropzone"),L=Symbol("getDropzones");function J({dragEvent:u,dropzone:e}){const t=u.source.textContent.trim()||u.source.id||"draggable element",s=e.textContent.trim()||e.id||"droppable element";return`Dropped ${t} into ${s}`}function Q({dragEvent:u,dropzone:e}){const t=u.source.textContent.trim()||u.source.id||"draggable element",s=e.textContent.trim()||e.id||"droppable element";return`Returned ${t} from ${s}`}const X={"droppable:dropped":J,"droppable:returned":Q},Y={"droppable:active":"draggable-dropzone--active","droppable:occupied":"draggable-dropzone--occupied"},ee={dropzone:".draggable-droppable"};class te extends T{constructor(e=[],t={}){super(e,{...ee,...t,classes:{...Y,...t.classes||{}},announcements:{...X,...t.announcements||{}}}),this.dropzones=null,this.lastDropzone=null,this.initialDropzone=null,this[g]=this[g].bind(this),this[f]=this[f].bind(this),this[h]=this[h].bind(this),this.on("drag:start",this[g]).on("drag:move",this[f]).on("drag:stop",this[h])}destroy(){super.destroy(),this.off("drag:start",this[g]).off("drag:move",this[f]).off("drag:stop",this[h])}[g](e){if(e.canceled())return;this.dropzones=[...this[L]()];const t=_(e.sensorEvent.target,this.options.dropzone);if(!t){e.cancel();return}const s=new $({dragEvent:e,dropzone:t});if(this.trigger(s),s.canceled()){e.cancel();return}this.initialDropzone=t;for(const a of this.dropzones)a.classList.contains(this.getClassNameFor("droppable:occupied"))||a.classList.add(...this.getClassNamesFor("droppable:active"))}[f](e){if(e.canceled())return;const t=this[B](e.sensorEvent.target);t&&!t.classList.contains(this.getClassNameFor("droppable:occupied"))&&this[S](e,t)?this.lastDropzone=t:(!t||t===this.initialDropzone)&&this.lastDropzone&&(this[k](e),this.lastDropzone=null)}[h](e){const t=new q({dragEvent:e,dropzone:this.lastDropzone||this.initialDropzone});this.trigger(t);const s=this.getClassNamesFor("droppable:occupied");for(const a of this.dropzones)a.classList.remove(...this.getClassNamesFor("droppable:active"));this.lastDropzone&&this.lastDropzone!==this.initialDropzone&&this.initialDropzone.classList.remove(...s),this.dropzones=null,this.lastDropzone=null,this.initialDropzone=null}[S](e,t){const s=new I({dragEvent:e,dropzone:t});if(this.trigger(s),s.canceled())return!1;const a=this.getClassNamesFor("droppable:occupied");return this.lastDropzone&&this.lastDropzone.classList.remove(...a),t.appendChild(e.source),t.classList.add(...a),!0}[k](e){const t=new j({dragEvent:e,dropzone:this.lastDropzone});this.trigger(t),!t.canceled()&&(this.initialDropzone.appendChild(e.source),this.lastDropzone.classList.remove(...this.getClassNamesFor("droppable:occupied")))}[B](e){return this.dropzones?_(e,this.dropzones):null}[L](){const e=this.options.dropzone;return typeof e=="string"?document.querySelectorAll(e):e instanceof NodeList||e instanceof Array?e:typeof e=="function"?e():[]}}const oe={class:"2xl:pr-[32rem] lg:pr-96"},re={class:"p-8"},ne={class:"mt-8"},se={class:"flex flex-col items-center justify-center space-y-4"},ie={class:"flex flex-col justify-center space-y-4"},ae=["id","data-stem"],le={class:"py-2 px-4 text-xl font-semibold w-1/3 rounded-sm text-right"},de=["id","data-node"],pe={class:"h-10 w-full"},ce={class:"flex h-full w-full items-start justify-center",droppableContainer:""},ue={class:"flex",id:"workBench",dropzone:""},me=["data-value","innerHTML","id"],ge={__name:"MultiDrag",props:{stems:{type:Array,required:!0},keys:{type:Array,required:!0}},setup(u){const e=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:"Attempt",href:"#",current:!0}],t=x([{stem:"agricola"},{stem:"agricolae"},{stem:"agricolam"}]),s=x(["nominative","genitive","accusative"]),a=O({}),A=(z,o)=>{for(const i in o)if(o.hasOwnProperty(i)&&o[i].key===z)return i;return-1};let b;return R(()=>{const z=document.querySelectorAll("[droppableContainer]");b=new te(z,{draggable:"[draggable]",dropzone:"[dropzone]",mirror:{constrainDimensions:!1}});let o,i,l,r;b.on("mirror:attached",function(d){d.mirror.style.position="fixed"}).on("drag:over",function(d){if(i=d.data.overContainer,r=i.children[0],r.id.includes("workBench-")&&(r=null),o=d.data.source,l=d.data.source.parentElement,i.id.includes("dropzone-")&&r.id!=o.id){i.prepend(o),r.remove(),l.prepend(r);const p=r;r=o,o=p}}).on("drag:out",function(d){i.id.includes("dropzone")&&r.id!=o.id&&r&&(r.remove(),i.appendChild(r),l.appendChild(o)),r=null}).on("droppable:dropped",function(d){const p=d.data.dropzone;let c=document.getElementById("workBench");p.id.startsWith("workBench-")&&(p.id==="workBench-start"&&(c.prepend(o),p.remove(),c.parentElement.prepend(p)),p.id==="workBench-end"&&(c.appendChild(o),p.remove(),c.parentElement.appendChild(p)),l.classList.remove("draggable-dropzone--occupied"),d.cancel())}).on("droppable:stop",function(d){l.classList.remove("draggable-droppable--occupied"),P(()=>{document.querySelectorAll("[keyContainer]").forEach(c=>{if(c.children.length>0){const y=c.children[0].dataset.value,N={stem:c.parentNode.dataset.stem,key:y};delete a[A(y,a)],a[c.id.replace("dropzone-","")]=N}})})})}),V(()=>{b&&b.destroy()}),(z,o)=>{const i=G("Head");return m(),U(M,null,{default:w(()=>[D(i,null,{default:w(()=>o[0]||(o[0]=[n("title",null,"Practice",-1)])),_:1}),n("main",oe,[n("div",re,[D(F,{class:"lg:col-span-9 xl:grid-cols-10",pages:e}),n("div",ne,[n("div",se,[o[3]||(o[3]=n("h2",{class:"text-xl font-semibold mb-5"}," Match Latin value with grammar ",-1)),n("div",ie,[(m(!0),v(E,null,C(t.value,(l,r)=>(m(),v("div",{key:r,class:"flex flex-row items-center gap-4",id:r,"data-stem":l.stem},[n("div",le,W(l.stem),1),n("div",null,[D(Z(K),{class:"w-12 text-gray-500"})]),n("div",{droppableContainer:"",dropzone:"",keyContainer:"",id:"dropzone-"+r,"data-node":r,class:"grow flex items-center justify-center px-4 border-2 border-dashed border-gray-300 rounded-xl cursor-move bg-white w-36 h-10 text-left"},null,8,de)],8,ae))),128))]),o[4]||(o[4]=n("div",{class:"my-8 w-full border border-gray-400"},null,-1)),n("div",pe,[n("div",ce,[o[1]||(o[1]=n("div",{class:"bg-white grow h-full w-auto",id:"workBench-start",dropzone:""},null,-1)),n("div",ue,[(m(!0),v(E,null,C(s.value,(l,r)=>(m(),v("div",{key:"unassigned-"+r,class:"h-10 inline-flex cursor-move select-none items-center rounded-l-full rounded-r-full bg-emerald-100 hover:bg-emerald-200 transition-colors transition-shadow duration-150 px-3 text-lg font-medium shadow-lg border-2 border-emerald-400 text-gray-800",draggable:"","data-value":l,innerHTML:l,id:`item-${r}`},null,8,me))),128))]),o[2]||(o[2]=n("div",{id:"workBench-end",class:"bg-white grow h-full w-auto",dropzone:""},null,-1))])])])])])]),o[5]||(o[5]=n("aside",{class:"bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},null,-1))]),_:1})}}},Je=H(ge,[["__scopeId","data-v-8ece80bd"]]);export{Je as default};
