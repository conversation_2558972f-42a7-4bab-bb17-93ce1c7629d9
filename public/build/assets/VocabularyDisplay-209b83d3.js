import{_ as A}from"./ButtonItem-718c0517.js";import{_ as N}from"./_plugin-vue_export-helper-c27b6911.js";import{r as $}from"./StarIcon-155a2a28.js";import{r as j}from"./CheckCircleIcon-d86d1232.js";import{r as H}from"./ArrowRightOnRectangleIcon-b404433d.js";import{r as Z}from"./CheckIcon-6a201aa1.js";import{o as s,d as c,a as n,i,a1 as G,b as r,w as a,g,t as l,f as h,q as f,c as p,n as T}from"./app-f0078ddb.js";import{S as W,h as q}from"./transition-a0923044.js";import{Y as P,G as F,V as J}from"./dialog-86f7bd91.js";/* empty css            */import"./render-c34c346a.js";import"./open-closed-7f51e238.js";import"./env-c107754a.js";import"./disposables-4ddc41dd.js";import"./micro-task-89dcd6af.js";import"./use-outside-click-484df218.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";function K(e,o){return s(),c("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"})])}const Q={components:{TransitionRoot:W,StarIcon:$,CheckCircleIcon:j,ButtonItem:A,ArrowRightOnRectangleIcon:H,Dialog:P,DialogPanel:F,DialogTitle:J,TransitionChild:q,CheckIcon:Z,EllipsisHorizontalIcon:K},props:{word:{type:Object,default:()=>({})},toggle:Boolean,displaySyntax:Boolean,isLearned:Boolean,isStarred:Boolean,verifiedSyntax:Boolean},data(){return{open:!1,isDragging:!1,favoriteWord:!1,learnWord:!1,positions:{clientX:void 0,clientY:void 0,movementX:0,movementY:0}}},watch:{toggle(){this.open=this.toggle}},methods:{openModal(){this.open=!0},closeModal(){this.open=!1,this.$emit("closeModal")},dragMouseDown:function(e){e.preventDefault(),this.isDragging=!0,this.positions.clientX=e.clientX,this.positions.clientY=e.clientY,document.onmousemove=this.elementDrag,document.onmouseup=this.closeDragElement},elementDrag:function(e){e.preventDefault(),this.positions.movementX=this.positions.clientX-e.clientX,this.positions.movementY=this.positions.clientY-e.clientY,this.positions.clientX=e.clientX,this.positions.clientY=e.clientY,this.$refs.draggableContainer.style.top=this.$refs.draggableContainer.offsetTop-this.positions.movementY+"px",this.$refs.draggableContainer.style.left=this.$refs.draggableContainer.offsetLeft-this.positions.movementX+"px"},closeDragElement(){document.onmouseup=null,document.onmousemove=null,this.isDragging=!1},touchStart:function(e){e.target===this.$refs.ellipsisIcon&&(this.positions.clientX=e.touches[0].clientX,this.positions.clientY=e.touches[0].clientY,document.ontouchmove=this.touchMove,document.ontouchend=this.closeTouchElement,this.isDragging=!0)},touchMove:function(e){if(this.isDragging){var o=e.touches[0].clientX,t=e.touches[0].clientY;this.positions.movementX=this.positions.clientX-o,this.positions.movementY=this.positions.clientY-t,this.positions.clientX=o,this.positions.clientY=t,this.$refs.draggableContainer.style.top=this.$refs.draggableContainer.offsetTop-this.positions.movementY+"px",this.$refs.draggableContainer.style.left=this.$refs.draggableContainer.offsetLeft-this.positions.movementX+"px"}},closeTouchElement(){this.isDragging=!1,document.ontouchend=null,document.ontouchmove=null}},mounted(){this.$refs.draggableContainer.ontouchstart=this.touchStart},destroyed(){document.onmousemove=null,document.onmouseup=null,document.ontouchmove=null,document.ontouchend=null}},U={class:"z-50 lg:fixed lg:right-64 lg:bottom-24 lg:left-0 lg:flex lg:translate-x-1/2 lg:transform lg:items-center lg:justify-center lg:px-4 lg:pb-4"},tt={id:"draggable-container",ref:"draggableContainer",class:"absolute hidden lg:inline-block"},et={class:"block w-full transform overflow-hidden rounded-xl bg-white px-4 pb-4 text-left align-bottom shadow-xl transition-all sm:mb-8 sm:max-w-sm sm:px-6 sm:pb-6 sm:align-middle md:w-screen md:max-w-md"},ot={class:"mt-6"},nt={class:"text-center"},st={class:"text-lg leading-6 font-semibold text-gray-900"},it={key:0},rt={class:"mt-1 text-sm text-gray-600"},at={key:0,class:"mr-2 text-xs uppercase"},lt={key:1},ct={class:"mt-2 text-base text-gray-900"},dt={class:"mt-5 grid grid-cols-1 gap-2 sm:mt-6 sm:grid-cols-4"};function mt(e,o,t,ut,R,m){const E=i("EllipsisHorizontalIcon"),V=i("CheckCircleIcon"),d=i("ButtonItem"),O=i("StarIcon"),L=i("ArrowRightOnRectangleIcon"),z=i("TransitionRoot"),u=G("tippy");return s(),c("div",U,[n("div",tt,[r(z,{show:R.open,as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-24 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-24 sm:scale-95"},{default:a(()=>{var w,_,x,v,y,b,k,C,D,I,Y,S,X,M;return[n("div",et,[r(E,{onMousedown:m.dragMouseDown,onTouchstart:m.touchStart,ref:"ellipsisIcon",class:"absolute top-0 left-0 mt-2 ml-2 h-6 w-6 cursor-move text-slate-500"},null,8,["onMousedown","onTouchstart"]),n("div",ot,[n("div",nt,[n("h3",st,[g(l(((_=(w=t.word)==null?void 0:w.word)==null?void 0:_.display_word)||"Loading...")+" ",1),(v=(x=t.word)==null?void 0:x.word)!=null&&v.gender?(s(),c("span",it,", "+l(t.word.word.gender),1)):h("",!0)]),n("p",rt,[((b=(y=t.word)==null?void 0:y.word)==null?void 0:b.core)==1?(s(),c("span",at,"core")):h("",!0),g(" "+l(((C=(k=t.word)==null?void 0:k.word)==null?void 0:C.pos)||"")+" ",1),((I=(D=t.word)==null?void 0:D.grammar)==null?void 0:I.length)>0&&t.displaySyntax&&t.verifiedSyntax?(s(),c("span",lt," | "+l(t.word.grammar),1)):h("",!0)]),n("p",ct,l(((S=(Y=t.word)==null?void 0:Y.word)==null?void 0:S.short_def)||"Definition unavailable"),1)])]),n("div",dt,[f((s(),p(d,{type:"button",color:"white",onClick:o[0]||(o[0]=B=>e.$emit("update:learned-words",t.word.word_id))},{default:a(()=>[r(V,{class:T(["mx-auto h-5 w-5 stroke-current stroke-2 transition duration-250",[t.isLearned?"text-blue-600":"text-gray-400"]])},null,8,["class"])]),_:1})),[[u,{content:t.isLearned?"Mark as not learned":"Mark as learned",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),f((s(),p(d,{type:"button",color:"white",onClick:o[1]||(o[1]=B=>e.$emit("update:starred-words",t.word.word_id))},{default:a(()=>[r(O,{class:T(["mx-auto h-5 w-5 stroke-current stroke-2 transition duration-250",[t.isStarred?"text-green-600":"stroke-current text-gray-400"]])},null,8,["class"])]),_:1})),[[u,{content:t.isStarred?"Remove from favorites":"Add to favorites",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),f((s(),p(d,{link:(M=(X=t.word)==null?void 0:X.word)!=null&&M.id?`/words/w/${t.word.word.id}`:"#",color:"white"},{default:a(()=>[r(L,{class:"mx-auto h-5 w-5 stroke-current text-gray-600 transition duration-250"})]),_:1},8,["link"])),[[u,{content:"View word details",placement:"bottom",trigger:"mouseenter",hideOnClick:!0}]]),r(d,{type:"button",color:"indigo",onClick:o[2]||(o[2]=B=>m.closeModal())},{default:a(()=>o[3]||(o[3]=[g(" Close ")])),_:1})])])]}),_:1},8,["show"])],512)])}const Tt=N(Q,[["render",mt]]);export{Tt as default};
