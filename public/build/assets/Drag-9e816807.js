import{e as A,p as N,A as U,B as I,o as v,d as g,a,r as V,b as f,w,u as m,T as O,n as $,t as j,g as B}from"./app-f0078ddb.js";import{d as T}from"./vuedraggable.umd-aab17b5c.js";import{_}from"./ButtonItem-718c0517.js";import M from"./NextButtonSmall-9e6ffefc.js";import{r as b}from"./replaceMacra-3b9666ed.js";import{_ as Q}from"./_plugin-vue_export-helper-c27b6911.js";import{r as R}from"./CheckCircleIcon-d86d1232.js";import{r as W}from"./XCircleIcon-63af2b2a.js";/* empty css            */const F={class:"flex-1"},G={class:"mt-4 mb-4 text-center text-3xl font-semibold text-gray-900"},H={class:"text-center text-lg font-medium text-gray-600"},J={class:"relative mt-12 grid w-full touch-manipulation grid-cols-1 justify-items-center sm:px-16"},P={class:"h-24 w-full"},X=["onClick"],Y={class:"mt-6 flex justify-center space-x-4"},Z=["onClick"],ee={class:"mt-8 text-center"},se={key:0},te={key:0,class:"flex items-center justify-center text-blue-500"},ne={key:1,class:"flex items-center justify-center text-red-600"},oe={key:1,class:"grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-8 xl:px-10"},le={__name:"Drag",props:{options:Array,syllabalized:String,isAnswer:Boolean,isCorrect:Boolean,disabled:Boolean,isVocabulary:{type:Boolean,default:!1},questionKey:String},emits:["submit","next-question","i-know-this"],setup(o,{emit:S}){const d=o,u=A([]),l=A([]),h=S;N(()=>d.options,t=>{u.value=t.map((e,s)=>({id:s,data:e}))},{immediate:!0});const y=A([]);function C(){document.removeEventListener("keydown",x);const t=l.value.map(r=>r.data);y.value=[...t];const e=[...d.options],s=d.syllabalized.split("|"),n=[],p=[];s.forEach((r,c)=>{t[c]===r?n.push({id:c,data:r}):(n.push({id:c,data:r}),t[c]&&p.push({id:c+1e3,data:t[c]}))});const i=new Set(n.map(r=>b(r.data))),k=e.filter(r=>!i.has(b(r)));l.value=n,u.value=k.map((r,c)=>({id:c+1e3,data:r})),n.map(r=>r.data).join("").toUpperCase(),h("submit",{answer:t.join(""),key:d.questionKey,isArray:!1})}U(()=>{document.addEventListener("keydown",x)}),I(()=>{document.removeEventListener("keydown",x)});function x(t){t.preventDefault(),t.which==13&&C()}const z=()=>{h("next-question")},D=()=>{h("i-know-this")};function E(t,e){const n=d.syllabalized.split("|").length,p=d.isAnswer?n:l.value.length;let i=[];if(e===0&&p===1?i.push("rounded-full px-4"):e===0?i.push("rounded-l-full pl-4 rounded-r-none pr-2"):e===p-1?i.push("rounded-r-full pr-4 rounded-l-none pl-2"):i.push("rounded-l-none pl-2","rounded-r-none pr-2"),!d.isAnswer)i.push("bg-purple-100 border-purple-400");else if(d.isCorrect)i.push("bg-teal-100 border-teal-400");else{const k=y.value[e];t===k?i.push("bg-teal-100 border-teal-400"):i.push("bg-red-100 border-red-400")}return i.join(" ")}function K(t){let e=[];if(l.value.includes(t)){const s=l.value.indexOf(t);s===0?e.push("rounded-l-full pl-4"):e.push("rounded-l-none pl-2"),s!==l.value.length-1?e.push("rounded-r-none pr-2"):e.push("rounded-r-full pr-4")}else e.push("rounded-r-full rounded-l-full pr-4 pl-4");return d.isAnswer&&y.value.some(n=>n===t)&&e.push("bg-red-100 border-red-400"),e.join(" ")}function q(){l.value=[],u.value=d.options.map((t,e)=>({id:e,data:t}))}function L(t){const e=l.value.find(n=>n.id===t.id),s=u.value.find(n=>n.id===t.id);e?(l.value=l.value.filter(n=>n.id!==t.id),u.value.push(e)):s&&(u.value=u.value.filter(n=>n.id!==t.id),l.value.push(s))}return(t,e)=>(v(),g("div",F,[a("h1",G,[V(t.$slots,"stem",{},void 0,!0)]),a("h3",H,[V(t.$slots,"instructions",{},void 0,!0)]),a("div",J,[a("div",P,[f(m(T),{modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=s=>l.value=s),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"relative flex h-24 w-full items-center justify-center rounded-2xl border-2 border-dashed border-gray-400 py-2","ghost-class":"ghost",animation:200,disabled:o.isAnswer},{item:w(({element:s,index:n})=>[a("div",{class:$(["mx-px inline-flex items-center border-2 py-2 text-2xl font-medium shadow-lg select-none",[E(m(b)(s.data),n),{"cursor-move hover:bg-purple-200":!o.isAnswer,"pointer-events-none":o.isAnswer}]]),onClick:p=>L(s)},j(m(b)(s.data)),11,X)]),_:1},8,["modelValue","disabled"])]),e[6]||(e[6]=a("div",{class:"my-8 w-full border border-gray-400"},null,-1)),a("div",Y,[f(m(T),{modelValue:u.value,"onUpdate:modelValue":e[1]||(e[1]=s=>u.value=s),group:{name:"words",pull:!0,put:!0},"item-key":"id",class:"flex h-24 w-full items-start justify-center","ghost-class":"ghost",animation:200,disabled:o.isAnswer},{item:w(({element:s})=>[a("div",{class:$(["mx-px inline-flex items-center border-2 border-purple-400 bg-purple-100 py-2 text-2xl font-medium shadow-lg select-none",[K(s.data),{"cursor-move hover:bg-purple-200":!o.isAnswer,"pointer-events-none":o.isAnswer}]]),onClick:n=>L(s)},j(s.data),11,Z)]),_:1},8,["modelValue","disabled"])])]),a("div",ee,[f(O,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95 ","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:w(()=>[o.isAnswer?(v(),g("div",se,[o.isCorrect?(v(),g("div",te,[f(m(R),{class:"mr-2 inline-block h-10 w-10"}),e[7]||(e[7]=a("p",{class:"inline-block text-2xl font-semibold"},"correct",-1))])):(v(),g("div",ne,[f(m(W),{class:"mr-2 inline-block h-10 w-10"}),e[8]||(e[8]=a("p",{class:"inline-block text-2xl font-semibold"},"incorrect",-1))])),f(M,{"is-answer":o.isAnswer,"is-correct":o.isCorrect,disabled:o.disabled,"is-vocabulary":o.isVocabulary,onIKnowThis:e[2]||(e[2]=s=>D()),onNextQuestion:e[3]||(e[3]=s=>z())},null,8,["is-answer","is-correct","disabled","is-vocabulary"])])):(v(),g("div",oe,[f(_,{size:"lg",class:"w-full",color:"white",onClick:e[4]||(e[4]=s=>q())},{default:w(()=>e[9]||(e[9]=[B("Clear")])),_:1}),f(_,{size:"lg",class:"w-full",color:"pink",onClick:e[5]||(e[5]=s=>C())},{default:w(()=>e[10]||(e[10]=[B("Submit")])),_:1})]))]),_:1})])]))}},ve=Q(le,[["__scopeId","data-v-868ccd74"]]);export{ve as default};
