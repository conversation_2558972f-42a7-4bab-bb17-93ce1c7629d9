import{o as r,d as i,a as s,e as p,A as h,B as k,s as u,b as a,u as l,f as y,n as w,w as C,T as B,z as j}from"./app-f0078ddb.js";import{r as q}from"./CheckCircleIcon-d86d1232.js";/* empty css            */function g(t,c){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5"})])}const A={class:"flex items-center justify-center"},K={key:1,class:"xl:px-10"},T=["disabled"],V={key:0,class:"flex items-center justify-center"},z={__name:"NextButtonSmall",props:{isCorrect:{type:Boolean,required:!0},isAnswer:{type:Boolean,required:!0},disabled:{type:Boolean,required:!0,default:!0},isVocabulary:{type:Boolean,required:!1,default:!1}},emits:["next-question","i-know-this"],setup(t,{emit:c}){const n=t,f=c;let m=p(!0);function b(o){o.ctrlKey||o.metaKey||o.altKey||(o.preventDefault(),(o.which==13||o.which==32)&&!n.disabled&&n.isAnswer&&j(()=>{d()}))}const d=()=>{!n.disabled&&n.isAnswer&&f("next-question")},v=()=>{!n.disabled&&n.isAnswer&&f("i-know-this")};return h(()=>{document.addEventListener("keydown",b),n.isCorrect||setTimeout(()=>{m.value=!1},25)}),k(()=>{document.removeEventListener("keydown",b)}),(o,e)=>t.isCorrect?(r(),i("div",{key:0,class:w(["mt-4 grid xl:px-10",t.isVocabulary?"grid-cols-1 gap-4 sm:grid-cols-2":"grid-cols-1"])},[t.isVocabulary?(r(),i("button",{key:0,class:"group flex h-10 w-full transform items-center justify-center rounded-lg border border-gray-300 px-3 py-1 text-base font-bold leading-4 text-gray-600 shadow-xs duration-150 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:e[0]||(e[0]=u(x=>v(),["prevent"]))},[e[3]||(e[3]=s("div",null,"I Know This Word",-1)),s("div",null,[a(l(q),{class:"-mr-1 ml-2 inline h-5 w-5 stroke-2 text-gray-400 transition duration-150 group-hover:text-blue-600"})])])):y("",!0),s("button",{class:"flex h-10 w-full transform items-center justify-center rounded-lg border border-transparent bg-blue-500 px-3 py-1 text-base font-bold leading-4 text-white shadow-xs duration-150 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:e[1]||(e[1]=u(x=>d(),["prevent"]))},[s("div",A,[e[4]||(e[4]=s("div",null,"Continue",-1)),a(l(g),{class:"-mr-1 ml-2 inline h-4 w-4"})])])],2)):(r(),i("div",K,[s("button",{class:w(["button-disabled mt-4 flex h-10 w-full transform items-center justify-center rounded-lg border border-transparent px-3 py-1 text-base font-bold leading-4 text-white shadow-xs duration-150 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{"button-animation":!l(m)||t.isCorrect}]),disabled:t.disabled,onClick:e[2]||(e[2]=u(x=>d(),["prevent"]))},[a(B,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95 ","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition duration-0","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:C(()=>[t.disabled?y("",!0):(r(),i("div",V,[e[5]||(e[5]=s("div",null,"Continue",-1)),a(l(g),{class:"-mr-1 ml-2 inline h-4 w-4"})]))]),_:1})],10,T)]))}};export{z as default};
