import{Y as $,K as k,Z as S,L as E,F as N}from"./app-f0078ddb.js";var j;let T=Symbol("headlessui.useid"),A=0;const U=(j=$)!=null?j:function(){return k(T,()=>`${++A}`)()};function W(e){var t;if(e==null||e.value==null)return null;let n=(t=e.value.$el)!=null?t:e.value;return n instanceof Node?n:null}function g(e,t,...n){if(e in t){let r=t[e];return typeof r=="function"?r(...n):r}let l=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,g),l}var P=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(P||{}),H=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(H||{});function Y({visible:e=!0,features:t=0,ourProps:n,theirProps:l,...r}){var s;let i=O(l,n),u=Object.assign(r,{props:i});if(e||t&2&&i.static)return b(u);if(t&1){let p=(s=i.unmount)==null||s?0:1;return g(p,{0(){return null},1(){return b({...r,props:{...i,hidden:!0,style:{display:"none"}}})}})}return b(u)}function b({props:e,attrs:t,slots:n,slot:l,name:r}){var s,i;let{as:u,...p}=R(e,["unmount","static"]),a=(s=n.default)==null?void 0:s.call(n,l),m={};if(l){let c=!1,h=[];for(let[d,f]of Object.entries(l))typeof f=="boolean"&&(c=!0),f===!0&&h.push(d);c&&(m["data-headlessui-state"]=h.join(" "))}if(u==="template"){if(a=v(a??[]),Object.keys(p).length>0||Object.keys(t).length>0){let[c,...h]=a??[];if(!x(c)||h.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${r} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(p).concat(Object.keys(t)).map(o=>o.trim()).filter((o,y,w)=>w.indexOf(o)===y).sort((o,y)=>o.localeCompare(y)).map(o=>`  - ${o}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(o=>`  - ${o}`).join(`
`)].join(`
`));let d=O((i=c.props)!=null?i:{},p,m),f=S(c,d,!0);for(let o in d)o.startsWith("on")&&(f.props||(f.props={}),f.props[o]=d[o]);return f}return Array.isArray(a)&&a.length===1?a[0]:a}return E(u,Object.assign({},p,m),{default:()=>a})}function v(e){return e.flatMap(t=>t.type===N?v(t.children):[t])}function O(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let l of e)for(let r in l)r.startsWith("on")&&typeof l[r]=="function"?(n[r]!=null||(n[r]=[]),n[r].push(l[r])):t[r]=l[r];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(l=>[l,void 0])));for(let l in n)Object.assign(t,{[l](r,...s){let i=n[l];for(let u of i){if(r instanceof Event&&r.defaultPrevented)return;u(r,...s)}}});return t}function C(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function R(e,t=[]){let n=Object.assign({},e);for(let l of t)l in n&&delete n[l];return n}function x(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}export{Y as A,C as E,P as N,H as S,R as T,U as i,W as o,g as u};
