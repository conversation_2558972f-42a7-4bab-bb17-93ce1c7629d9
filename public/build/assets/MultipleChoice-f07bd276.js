import{A as g,B as y,e as v,o as l,d as r,a as s,r as a,F as k,h as _,n as u,t as p,u as w,f as x}from"./app-f0078ddb.js";/* empty css            */const K={class:"flex-1"},C={class:"mt-4 mb-4 text-center text-3xl font-semibold whitespace-pre-line text-gray-900"},D={class:"text-center text-lg font-medium text-gray-600"},E={class:"relative my-6 grid w-full touch-manipulation grid-cols-1 justify-items-center select-none xl:px-16"},L={class:"grid w-full grid-cols-1 gap-4 sm:grid-cols-4"},$=["onClick"],B={class:"line-clamp-2"},A={key:0,class:"absolute top-1 right-2"},V={__name:"MultipleChoice",props:{options:Array},emits:["submit"],setup(f,{emit:h}){const o=f,b=h,n=t=>{b("submit",{answer:t})};g(()=>{document.addEventListener("keydown",m),document.addEventListener("keyup",d)}),y(()=>{document.removeEventListener("keydown",m),document.addEventListener("keyup",d)});let i=v(!1);function d(t){(t.which===17||t.which===18||t.which===224)&&(i.value=!1)}function m(t){switch((t.ctrlKey||t.metaKey||t.altKey)&&(i.value=!0),t.which){case 49:t.preventDefault(),n(o.options[0]);break;case 50:t.preventDefault(),n(o.options[1]);break;case 51:t.preventDefault(),n(o.options[2]);break;case 52:t.preventDefault(),n(o.options[3]);break}}return(t,F)=>(l(),r("div",K,[s("h1",C,[a(t.$slots,"stem")]),s("h3",D,[a(t.$slots,"instructions")]),a(t.$slots,"latin"),a(t.$slots,"english"),s("div",E,[s("div",L,[(l(!0),r(k,null,_(o.options,(c,e)=>(l(),r("div",{key:c,class:u(["col-span-2 mt-2",{"col-start-2":e===2&&o.options.length===3}])},[s("button",{class:u(["h-16 w-full transform items-center rounded-lg border-2 px-6 py-4 text-xl font-semibold text-black shadow-md duration-150 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-hidden md:h-24",[{"border-lime-400 bg-lime-100":e===0},{"border-teal-400 bg-teal-100":e===1},{"border-purple-400 bg-purple-100":e===2},{"border-sky-400 bg-sky-100":e===3}]]),onClick:M=>n(c)},[s("p",B,p(c),1),w(i)?(l(),r("div",A,[s("span",{class:u(["text-2xl font-bold",[{"text-lime-400":e===0},{"text-teal-400":e===1},{"text-purple-400":e===2},{"text-sky-400":e===3}]])},p(e+1),3)])):x("",!0)],10,$)],2))),128))])])]))}};export{V as default};
