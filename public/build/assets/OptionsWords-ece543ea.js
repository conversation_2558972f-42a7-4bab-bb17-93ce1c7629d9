import{e as f,o as i,c as g,w as r,u as o,a as e,d as x,h as b,b as d,n as m,g as w,t as v,F as h}from"./app-f0078ddb.js";import{k as _,O as y,h as k}from"./radio-group-97521e36.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";const V={class:"grid grid-cols-1 gap-4 md:grid-cols-9 md:gap-8 2xl:grid-cols-2"},C={class:"mx-auto flex h-10 w-full space-x-1 self-center rounded-lg bg-gray-200 p-0.5 md:col-span-5 2xl:col-span-1"},$={__name:"OptionsWords",emits:["update:words"],setup(B,{emit:p}){const c=p,l=[{name:"all"},{name:"core"},{name:"not core"}],n=f(l[0]);return(O,t)=>(i(),g(o(k),{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=s=>n.value=s)},{default:r(()=>[e("div",V,[t[1]||(t[1]=e("div",{class:"flex items-center justify-between md:col-span-4 2xl:col-span-1"},[e("span",{class:"flex grow flex-col"},[e("span",{as:"span",class:"text-sm leading-6 font-semibold text-gray-900",passive:""},"Words"),e("span",{as:"span",class:"text-sm text-gray-500"},"Customize further by working with all or a subset of this list.")])],-1)),e("div",C,[(i(),x(h,null,b(l,s=>d(o(y),{key:s.name,as:"template",value:s,onClick:a=>c("update:words",s.name)},{default:r(({checked:a,active:u})=>[e("div",{class:m(["w-full cursor-pointer rounded-md py-2 text-center text-sm leading-5 font-semibold","ring-white/60 ring-offset-2 ring-offset-blue-400 focus:ring-2 focus:outline-hidden",a?"bg-white text-amber-600 shadow-sm":"text-gray-700"])},[d(o(_),{as:"span",class:"capitalize"},{default:r(()=>[w(v(s.name),1)]),_:2},1024),e("div",{class:m([u?"border":"border-2",a?"border-teal-500":"border-transparent","pointer-events-none absolute -inset-px rounded-lg"]),"aria-hidden":"true"},null,2)],2)]),_:2},1032,["value","onClick"])),64))])])]),_:1},8,["modelValue"]))}};export{$ as default};
