import{_ as o}from"./_plugin-vue_export-helper-c27b6911.js";import{o as r,d as e,y as s}from"./app-f0078ddb.js";const c={},a={xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 0 54 50"};function n(i,t){return r(),e("svg",a,t[0]||(t[0]=[s('<defs><linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%" gradientUnits="userSpaceOnUse"><stop offset="0%" style="stop-color:var(--color-purple-500);stop-opacity:1;"></stop><stop offset="100%" style="stop-color:var(--color-indigo-500);stop-opacity:1;"></stop></linearGradient></defs><path fill="url(#gradient)" stroke="url(#gradient)" d="M37.156,37.969c-7.816,0-11.399-6.687-14.56-12.585c-3.127-5.837-5.311-9.382-9.253-9.382c-5.343,0-9.372,3.881-9.372,9.027c0,1.104-0.896,2-2,2s-2-0.896-2-2c0-7.305,5.874-13.027,13.372-13.027c6.621,0,9.751,5.842,12.779,11.493c2.887,5.387,5.612,10.475,11.034,10.475c4.733,0,8.885-4.178,8.885-8.94c0-1.104,0.896-2,2-2s2,0.896,2,2C50.041,32.043,44.141,37.969,37.156,37.969z"></path><path fill="url(#gradient)" stroke="url(#gradient)" d="M21.054 26.739c-1.971 4.331-3.799 7.341-7.835 7.341-5.382 0-9.288-3.807-9.288-9.052v-1H.012v1C.012 32.424 5.689 38 13.219 38c5.415 0 8.18-3.449 10.172-7.144C22.541 29.485 21.775 28.083 21.054 26.739zM36.875 12c-5.491 0-8.484 3.673-10.609 7.596.563.984 1.099 1.981 1.621 2.957.234.437.461.854.685 1.264 1.98-4.329 3.986-7.897 8.304-7.897 5.335 0 9.207 3.831 9.207 9.109v1H50v-1C50 17.723 44.234 12 36.875 12z"></path>',3)]))}const d=o(c,[["render",n]]);export{d as I};
