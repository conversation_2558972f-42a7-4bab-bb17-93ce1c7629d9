import{l as b,o,d as l,a as e,t as r,g as m,f as g,F as c,h as u}from"./app-f0078ddb.js";/* empty css            */const h={class:"declension-table"},v={class:"mb-4 text-center"},x={class:"text-xl font-bold"},_={class:"text-sm text-gray-600"},f={key:0},p={class:"grid grid-cols-1 gap-8 md:grid-cols-2"},w={class:"w-full border-collapse"},y={class:"border border-gray-300 p-2 font-medium"},k={class:"border border-gray-300 p-2 text-center"},C={class:"w-full border-collapse"},A={class:"border border-gray-300 p-2 font-medium"},B={class:"border border-gray-300 p-2 text-center"},F={__name:"Table",props:{word:{type:Object,required:!0},cases:{type:Array,default:()=>["nominative","genitive","dative","accusative","ablative","vocative"]}},setup(s){const n=s,i=b(()=>n.cases.filter(a=>n.word.declensions.singular[a]||n.word.declensions.plural[a]));return(a,d)=>(o(),l("div",h,[e("div",v,[e("h2",x,r(s.word.lemma),1),e("p",_,[m(r(s.word.gender)+", "+r(s.word.declension)+" declension ",1),s.word.isGreek?(o(),l("span",f,"(Greek)")):g("",!0)])]),e("div",p,[e("div",null,[d[0]||(d[0]=e("h3",{class:"mb-4 text-center text-lg font-semibold"},"Singular",-1)),e("table",w,[e("tbody",null,[(o(!0),l(c,null,u(i.value,t=>(o(),l("tr",{key:`singular-${t}`},[e("td",y,r(t.charAt(0).toUpperCase()+t.slice(1)),1),e("td",k,r(s.word.declensions.singular[t]),1)]))),128))])])]),e("div",null,[d[1]||(d[1]=e("h3",{class:"mb-4 text-center text-lg font-semibold"},"Plural",-1)),e("table",C,[e("tbody",null,[(o(!0),l(c,null,u(i.value,t=>(o(),l("tr",{key:`plural-${t}`},[e("td",A,r(t.charAt(0).toUpperCase()+t.slice(1)),1),e("td",B,r(s.word.declensions.plural[t]),1)]))),128))])])])])]))}};export{F as default};
