import{o as r,d as i,a as o,n as a,u as b,r as s,f as u,W as g,k as v}from"./app-f0078ddb.js";/* empty css            */const f={class:"block w-full"},h={class:"w-full"},y={class:"focus:outline-hidden"},p={class:"focus:outline-hidden"},x={class:"focus:outline-hidden"},T={class:"mx-auto mt-8"},k={key:0,class:"focus:outline-hidden"},$={key:1,class:"focus:outline-hidden"},S={key:2,class:"focus:outline-hidden"},C={key:3,class:"focus:outline-hidden"},U={__name:"TabComponent",props:{currentUrl:{type:String,required:!0},currentTab:{type:[Object,String],required:!1},authenticated:{type:Boolean,required:!0}},emits:["update:currentTab"],setup(e,{emit:w}){const d=e,m=()=>{g.get(d.currentUrl,{},{onSuccess:()=>{},preserveScroll:!0,preserveState:!0})},l=t=>{g.get(d.currentUrl,{tab:t},{only:[t,"tab"],onSuccess:()=>{},preserveScroll:!0,preserveState:!0})};return(t,n)=>(r(),i("div",f,[o("div",h,[o("div",{class:a(["grid gap-2 text-center sm:gap-8",e.authenticated?(b(v)().props.user.id==1,"grid-cols-3"):"grid-cols-2"])},[o("div",y,[o("button",{onClick:n[0]||(n[0]=c=>m()),class:a(["duration-250 w-full cursor-pointer rounded-md px-3 py-2 text-sm font-bold transition ease-in-out",e.currentTab==null?"bg-indigo-100 text-indigo-700":"text-gray-500 hover:bg-gray-50 hover:text-gray-700"])},[s(t.$slots,"tab1")],2)]),o("div",p,[o("button",{onClick:n[1]||(n[1]=c=>l("vocab")),class:a(["duration-250 w-full cursor-pointer rounded-md px-3 py-2 text-sm font-bold transition ease-in-out",e.currentTab=="vocab"?"bg-indigo-100 text-indigo-700":"text-gray-500 hover:bg-gray-50 hover:text-gray-700"])},[s(t.$slots,"tab2")],2)]),o("div",x,[o("button",{onClick:n[2]||(n[2]=c=>l("notes")),class:a(["duration-250 w-full cursor-pointer rounded-md px-3 py-2 text-sm font-bold transition ease-in-out",e.currentTab=="notes"?"bg-indigo-100 text-indigo-700":"text-gray-500 hover:bg-gray-50 hover:text-gray-700"])},[s(t.$slots,"tab3")],2)])],2),o("div",T,[e.currentTab==null?(r(),i("div",k,[s(t.$slots,"content1")])):u("",!0),e.currentTab==="vocab"?(r(),i("div",$,[s(t.$slots,"content2")])):u("",!0),e.currentTab==="notes"?(r(),i("div",S,[s(t.$slots,"content3")])):u("",!0),e.currentTab==="translation"&&b(v)().props.user.id==1?(r(),i("div",C,[s(t.$slots,"content4")])):u("",!0)])])]))}};export{U as default};
