import{D as o}from"./datetime-8ddd27a0.js";import{e as u,l as y,i as f,o as i,c as p,w as v,a as t,t as r,d as n,f as l}from"./app-f0078ddb.js";/* empty css            */const x={class:"flex w-full cursor-pointer flex-col items-start justify-between gap-4 py-5 sm:flex-row sm:items-center sm:px-2"},_={class:"flex w-auto flex-col overflow-hidden sm:grow"},h={class:"font-intro text-lg font-bold text-gray-900 sm:truncate"},w={class:"mt-1 text-sm font-medium text-gray-600 sm:truncate"},g={class:"align-center flex w-full justify-between sm:w-auto sm:justify-end sm:py-2"},b={class:"inline flex grow sm:pr-4"},D={key:0,class:"grow-0 rounded-full bg-emerald-500 px-2 py-1 text-xs font-bold text-white"},k={key:1,class:"grow-0 rounded-full bg-sky-500 px-2 py-1 text-xs font-bold text-white"},I={class:"items-end sm:w-16"},S={class:"py-1 text-right text-xs text-gray-600"},j={__name:"ActivityItem",props:["activity"],setup(s){const e=s;let a=u(o.fromISO(e.activity.updated_at).toLocal());const d=y(()=>{switch(a.value.toISODate()){case o.now().toLocal().toISODate():return"Today";case o.now().minus({days:1}).toLocal().toISODate():return"Yesterday";default:return a.value.toISODate()<o.now().minus({years:1}).toLocal().toISODate()?a.value.toFormat("MMM d, yyyy"):a.value.toFormat("MMM d")}});return(c,L)=>{const m=f("Link");return i(),p(m,{href:e.activity.type=="vocab"?c.route("practice.vocabulary.attempt.id",s.activity.id):c.route("practice.grammar.attempt.id",s.activity.id),class:"duration-250 bg-white transition hover:bg-gray-50"},{default:v(()=>[t("div",x,[t("div",_,[t("h3",h,r(e.activity.title),1),t("h5",w,r(e.activity.description),1)]),t("div",g,[t("div",b,[e.activity.type=="vocab"?(i(),n("div",D," vocabulary ")):l("",!0),e.activity.type=="grammar"?(i(),n("div",k," grammar ")):l("",!0)]),t("div",I,[t("p",S,r(d.value),1)])])])]),_:1},8,["href"])}}};export{j as default};
