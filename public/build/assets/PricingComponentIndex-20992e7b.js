import{e as h,V as te,l as N,C as se,k as E,o as l,d as a,a as t,b as r,w as p,u as o,F as S,h as T,f as m,T as M,q as J,x as Y,j as I,t as i,g as f,n as v,s as oe}from"./app-f0078ddb.js";import{D as le}from"./index-b0adb136.js";import{D as V}from"./datetime-8ddd27a0.js";import{_ as j}from"./ButtonItem-718c0517.js";import{r as ae}from"./CheckIcon-4bbdc2ab.js";import{r as O}from"./ChevronLeftIcon-2a41c533.js";import{r as L}from"./ChevronRightIcon-0e7ec64c.js";import{r as ne}from"./CheckCircleIcon-d86d1232.js";import{O as re,h as ie}from"./radio-group-97521e36.js";/* empty css            */import"./form-cb36670c.js";import"./render-c34c346a.js";import"./use-tree-walker-100527b8.js";import"./focus-management-8406d052.js";import"./env-c107754a.js";import"./keyboard-982fc047.js";import"./description-cd3ec634.js";import"./label-6c8c1cbc.js";const ue={class:"flex flex-cols transform duration-350 transition"},de={key:0},ce={class:"flex justify-center mt-4"},me={"aria-label":"Payment frequency"},fe={class:"isolate mx-auto mt-4 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-2"},ge=["id"],ve={class:"mt-6 flex items-baseline gap-x-1"},pe=["href","onClick","aria-describedby"],xe={class:"w-full mt-8"},ye={key:1,class:"w-full flex flex-col"},be={class:"flex"},he={class:"px-2 w-full flex-1"},we={class:"grow grid grid-cols-2 gap-2"},_e={class:"w-full"},ke=["onClick"],Ce={class:"mr-4"},De=["src"],$e={class:"grow flex flex-col text-left"},Se={class:"text-base font-medium"},Te={class:"w-full pt-2"},Me={key:1,class:"w-10"},Ve={key:2,class:"w-full flex flex-col"},je={class:"mt-8 flex items-center justify-center flex-col grow"},qe={class:"mt-1 relative flex grow items-stretch focus-within:z-10"},Fe=["disabled"],Ae={key:0,class:"mt-1 text-left text-sm font-medium text-red-600"},Ne={key:3,class:"w-full flex flex-col"},Oe={class:"mt-8 flex items-center justify-center flex-col grow"},Pe={class:"text-gray-900 text-lg font-semibold leading-8 mb-4"},ze={key:0},Ue={class:"flex"},Be={class:"grow"},Ee={key:1,class:"w-10"},Je={key:4,class:"w-full flex flex-col"},Ye={class:"mt-8 flex items-center justify-center flex-col grow"},Ie={class:"flex w-full"},Le={class:"grow pr-10"},Re={class:"text-gray-900 text-lg font-semibold leading-6"},We={key:0},He={key:0},Ge={key:1},Ke={key:0},Qe={key:0,class:"text-gray-600"},Xe={class:"py-8"},Ze={class:"text-8xl font-bold text-gray-900"},et={class:"mt-4"},tt=["href"],st={key:0},ot={key:1},lt={key:5,class:"w-full flex flex-col"},at={class:"mt-8 flex items-center justify-center flex-col grow"},nt={key:0},rt={key:0,class:"text-xs text-red-500 mt-2"},it={class:"mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3"},ut={key:1},dt={class:"flex flex-col items-center"},Mt={__name:"PricingComponentIndex",props:{teams:{type:Object,required:!0},errors:{type:Object,required:!0}},emits:["update:close"],setup(q,{emit:R}){const W=q;let x=h(null);const w=h(0);let u=te({name:"",slug:""});const H=R;let k=h("");const C=new Date,F=new Date(C);F.setDate(F.getDate()+1);const D=new Date(C);D.setFullYear(C.getFullYear()+1);let d=h();function c(n,e){d.value=e,n===0&&(x.value=null,u.name="",u.slug=""),n===3&&y.value.value=="annually"&&(x.value=D),w.value=n}let P=h(W.teams);const G=n=>{if(n.slug===u.slug){u.name="",u.slug="";return}u.name=n.name,u.slug=n.slug},g=N(()=>{if(x.value){const n=V.fromJSDate(new Date),s=V.fromJSDate(new Date(x.value)).diff(n,"months").months;return s<1?1:Math.ceil(s)}return 1}),z=new Date(C),U=N(()=>(z.setMonth(C.getMonth()+g.value),g.value>8?V.fromJSDate(D).toFormat("MMMM dd, yyyy"):V.fromJSDate(z).toFormat("MMMM dd, yyyy"))),K=N(()=>g.value>7&&g.value<=12?d.value=="single-classroom"?80:200:d.value=="single-classroom"?g.value*10:g.value*25),Q=n=>{$.value?n.preventDefault():$.value=!0},_=se({user_id:E().props.user?E().props.user.id:null,location:window.location.href,team:u.name+" ("+u.slug+")",message:null}),X=()=>{A.value=!0,setTimeout(()=>{_.message=null,H("update:close"),setTimeout(()=>{A.value=!1},300)},3e3)};let $=h(!1),A=h(!1);const B=[{value:"monthly",label:"Monthly",priceSuffix:"per month"},{value:"annually",label:"Annually",priceSuffix:"for the year"}],Z=[{name:"Single Classroom",id:"single-classroom",price:{monthly:"$10",annually:"$80"},description:"Full access for you and up to 25 students",features:["Assign activites","View student progress ","Enhance your curriculum"],featured:!1,cta:"Choose or make a class",nextTab:{monthly:1,annually:1}},{name:"All Classrooms",id:"full-classroom",price:{monthly:"$25",annually:"$200"},description:"Unlimited classes for you and your students",features:["Create unlimited classes","Assign activites","View student progress ","Enhance your curriculum"],featured:!0,cta:"Buy plan",nextTab:{monthly:2,annually:3}}],y=h(B[0]),ee=async()=>{try{const n=await axios.post("/classes",{classroomName:k.value}),{team:e,slug:s,photo_url:b}=n.data;u.name=e,u.slug=s,P.value.push({name:e,slug:s,photo_url:b}),c(2,d.value)}catch(n){n.response&&n.response.data.errors?console.error(n.response.data.errors):console.error("An error occurred:",n.message)}};return(n,e)=>(l(),a("div",ue,[w.value===0?(l(),a("div",de,[t("div",ce,[t("fieldset",me,[r(o(ie),{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=s=>y.value=s),class:"grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-xs font-semibold leading-5 ring-1 ring-inset ring-gray-200"},{default:p(()=>[(l(),a(S,null,T(B,s=>r(o(re),{as:"template",key:s.value,value:s},{default:p(({checked:b})=>[t("div",{class:v([b?"bg-indigo-600 text-white":"text-gray-500","cursor-pointer rounded-full px-2.5 py-1"])},i(s.label),3)]),_:2},1032,["value"])),64))]),_:1},8,["modelValue"])])]),t("div",fe,[(l(),a(S,null,T(Z,s=>t("div",{key:s.id,class:v([s.featured?"bg-gray-900 ring-gray-900":"ring-gray-200","rounded-3xl p-8 ring-1 xl:p-10"])},[t("h3",{id:s.id,class:v([s.featured?"text-white":"text-gray-900","text-lg font-semibold leading-8"])},i(s.name),11,ge),t("p",{class:v([s.featured?"text-gray-300":"text-gray-600","mt-4 text-sm leading-6"])},i(s.description),3),t("p",ve,[t("span",{class:v([s.featured?"text-white":"text-gray-900","text-4xl font-bold tracking-tight"])},i(typeof s.price=="string"?s.price:s.price[y.value.value]),3),typeof s.price!="string"?(l(),a("span",{key:0,class:v([s.featured?"text-gray-300":"text-gray-600","text-sm font-semibold leading-6"])},i(y.value.priceSuffix),3)):m("",!0)]),t("a",{href:s.href,onClick:b=>y.value.value=="monthly"?c(s.nextTab.monthly,s.id):c(s.nextTab.annually,s.id),"aria-describedby":s.id,class:v([s.featured?"bg-white/10 text-white hover:bg-white/20 focus-visible:outline-white":"bg-indigo-600 text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-indigo-600","mt-6 block rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 cursor-pointer"])},i(s.cta),11,pe),t("ul",{role:"list",class:v([s.featured?"text-gray-300":"text-gray-600","mt-8 space-y-3 text-sm leading-6 xl:mt-10"])},[(l(!0),a(S,null,T(s.features,b=>(l(),a("li",{key:b,class:"flex gap-x-3"},[r(o(ae),{class:v([s.featured?"text-white":"text-indigo-600","h-6 w-5 flex-none"]),"aria-hidden":"true"},null,8,["class"]),f(" "+i(b),1)]))),128))],2)],2)),64))]),t("div",xe,[e[15]||(e[15]=t("h4",{class:"text-gray-600"}," Have a question, want a quote, or wish to pay by invoice? ",-1)),r(j,{onClick:e[1]||(e[1]=s=>c(5)),class:"w-full mt-4",color:"gray"},{default:p(()=>e[14]||(e[14]=[f("Reach Out to LatinTutorial")])),_:1})])])):m("",!0),w.value===1?(l(),a("div",ye,[e[18]||(e[18]=t("div",{class:"mt-8 flex items-center justify-center flex-col grow"},[t("h2",{class:"text-gray-900 text-lg font-semibold leading-8 mb-4"}," Choose the class you'd like to purchase access for. ")],-1)),t("div",be,[t("div",{class:"flex items-center rounded-lg group hover:bg-slate-50 cursor-pointer transition duration-150 ease-in-out w-10",onClick:e[2]||(e[2]=s=>c(0))},[r(o(O),{class:"h-10 w-10 text-gray-500"})]),t("div",he,[t("div",we,[(l(!0),a(S,null,T(o(P),s=>(l(),a("div",_e,[t("div",{onClick:b=>G(s),class:v(["flex items-center px-4 py-2 w-full rounded-xl transition duration-150 ease-in-out cursor-pointer hover:bg-blue-100",o(u).slug===s.slug?"bg-blue-100":"bg-white"])},[t("div",Ce,[t("img",{src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/${s.photo_url}`,alt:"class",class:"w-8 h-8 rounded-lg"},null,8,De)]),t("div",$e,[t("h3",Se,i(s.name),1)])],10,ke)]))),256))]),e[17]||(e[17]=t("div",{class:"py-2 text-sm uppercase text-gray-500 font-medium"},"Or",-1)),t("div",Te,[r(j,{onClick:e[3]||(e[3]=s=>c(4,o(d))),class:"w-full",color:"white",size:"sm"},{default:p(()=>e[16]||(e[16]=[f("Create a new class")])),_:1})])]),r(M,{mode:"out-in","enter-active-class":"transition ease-out duration-300","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-200","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:p(()=>[o(u).name?(l(),a("div",{key:0,class:"flex items-center rounded-lg group cursor-pointer w-10 group bg-indigo-50 hover:bg-indigo-100",onClick:e[4]||(e[4]=s=>y.value.value=="annually"?c(3,o(d)):c(2,o(d)))},[r(o(L),{class:"h-10 w-10 text-indigo-500 group-hover:text-indigo-600"})])):(l(),a("div",Me))]),_:1})])])):m("",!0),w.value===4?(l(),a("div",Ve,[t("div",je,[e[19]||(e[19]=t("h2",{class:"text-gray-900 text-lg font-semibold leading-8 mb-4"}," Enter a name for your new classroom. ",-1)),t("div",qe,[J(t("input",{id:"classroom_name","onUpdate:modelValue":e[5]||(e[5]=s=>I(k)?k.value=s:k=s),type:"text",name:"classroom_name",class:"sm:text-md block w-full rounded-l-md border-gray-300 shadow-xs focus:border-teal-400 focus:ring-teal-400"},null,512),[[Y,o(k)]]),t("button",{onClick:e[6]||(e[6]=s=>ee()),disabled:o(k).length<1,type:"button",class:"relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md shadow-xs px-3 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed disabled:bg-gray-100 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"}," Create ",8,Fe)]),q.errors.createClass?(l(),a("div",Ae,i(q.errors.createClass.classroomName),1)):m("",!0)])])):m("",!0),w.value===2?(l(),a("div",Ne,[t("div",Oe,[t("h2",Pe,[e[20]||(e[20]=f(" Select the date for the end of your subscription ")),o(d)=="single-classroom"?(l(),a("span",ze,"for "+i(o(u).name),1)):m("",!0),e[21]||(e[21]=f(". "))])]),t("div",Ue,[t("div",{class:"flex items-center rounded-lg group hover:bg-slate-50 cursor-pointer transition duration-150 ease-in-out w-10",onClick:e[7]||(e[7]=s=>o(d)=="single-classroom"?c(1):c(0))},[r(o(O),{class:"h-10 w-10 text-gray-500"})]),t("div",Be,[r(o(le),{modelValue:o(x),"onUpdate:modelValue":e[8]||(e[8]=s=>I(x)?x.value=s:x=s),mode:"date","min-date":o(F),"max-date":o(D)},null,8,["modelValue","min-date","max-date"])]),r(M,{mode:"out-in","enter-active-class":"transition ease-out duration-300","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-200","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:p(()=>[o(x)?(l(),a("div",{key:0,class:"flex items-center rounded-lg group cursor-pointer w-10 group bg-indigo-50 hover:bg-indigo-100",onClick:e[9]||(e[9]=s=>c(3,o(d)))},[r(o(L),{class:"h-10 w-10 text-indigo-500 group-hover:text-indigo-600"})])):(l(),a("div",Ee))]),_:1})]),e[22]||(e[22]=t("div",{class:"flex justify-center mt-4"},[t("h4",{class:"text-gray-600"}," We will automatically calculate the best option for your subscription. ")],-1))])):m("",!0),w.value===3?(l(),a("div",Je,[t("div",Ye,[t("div",Ie,[t("div",{class:"flex items-center rounded-lg group hover:bg-slate-50 cursor-pointer transition duration-150 ease-in-out w-10",onClick:e[10]||(e[10]=s=>y.value.value=="monthly"?c(2,o(d)):o(d)=="single-classroom"?c(1):c(0))},[r(o(O),{class:"h-10 w-10 text-gray-500"})]),t("div",Le,[t("div",null,[t("h2",Re,[g.value>8?(l(),a("span",We,[e[23]||(e[23]=f("1 year subscription ")),o(d)=="single-classroom"?(l(),a("span",He,"for "+i(o(u).name),1)):m("",!0),f(" expires on "+i(U.value),1)])):(l(),a("span",Ge,[f(i(g.value)+" month subscription ",1),o(d)=="single-classroom"?(l(),a("span",Ke,"for "+i(o(u).name),1)):m("",!0),f(" expires on "+i(U.value),1)]))]),g.value>8?(l(),a("p",Qe," A year subscription is cheaper than a monthly subscription over "+i(g.value)+" months. ",1)):m("",!0)]),t("div",Xe,[t("h1",Ze,[f(" $"+i(K.value),1),e[24]||(e[24]=t("span",{class:"text-xl font-normal text-gray-700"}," billed once",-1))])]),t("div",et,[t("a",{as:"button",href:`/classes/${o(u).slug}/subscribe?plan=${y.value.value}&tier=${o(d)}&length=${g.value}`,onClick:Q,class:v(["bg-indigo-600 mx-8 text-white shadow-xs hover:bg-indigo-500 mt-6 block rounded-md px-3 py-2 text-center text-sm font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",{"bg-indigo-400 cursor-not-allowed":o($)}])},[r(M,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:p(()=>[o($)?(l(),a("span",st,"Processing ...")):(l(),a("span",ot,"Purchase Subscription"))]),_:1})],10,tt)])])])])])):m("",!0),w.value===5?(l(),a("div",lt,[t("div",at,[e[31]||(e[31]=t("h2",{class:"text-gray-900 text-lg font-semibold leading-8 mb-4"},null,-1)),r(M,{as:"template",mode:"out-in",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:p(()=>[o(A)?(l(),a("div",ut,[t("div",dt,[r(o(ne),{class:"h-24 w-24 text-emerald-500"}),e[29]||(e[29]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900"}," Thanks for your interest! ",-1)),e[30]||(e[30]=t("div",{class:"mt-2 text-sm text-gray-500"},[t("p",null," We'll take a look and get back to you as soon as possible. ")],-1))])])):(l(),a("div",nt,[t("form",{onSubmit:e[13]||(e[13]=oe(s=>{o(_).post("/submit-feedback",{preserveScroll:!0,onSuccess:()=>{X()}})},["prevent"]))},[t("div",null,[e[25]||(e[25]=t("label",{for:"comment",class:"sr-only"},"Comment",-1)),t("div",null,[J(t("textarea",{"onUpdate:modelValue":e[11]||(e[11]=s=>o(_).message=s),rows:"5",name:"message",id:"message",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Add your message..."},null,512),[[Y,o(_).message]]),o(_).errors.message?(l(),a("div",rt,i(o(_).errors.message),1)):m("",!0)])]),e[28]||(e[28]=t("div",{class:"mt-2"},[t("p",{class:"text-xs text-gray-500"}," Your user information, including name, email, and the name of this class, will be sent with your message. ")],-1)),t("div",it,[r(j,{size:"sm",color:"white",onClick:e[12]||(e[12]=s=>c(0))},{default:p(()=>e[26]||(e[26]=[f(" Cancel ")])),_:1}),r(j,{size:"sm",color:"indigo",type:"submit",disabled:o(_).processing},{default:p(()=>e[27]||(e[27]=[f(" Send ")])),_:1},8,["disabled"])])],32)]))]),_:1})])])):m("",!0)]))}};export{Mt as default};
