import{_ as B}from"./AppLayout-33f062bc.js";import{_ as z}from"./Breadcrumbs-c96e9207.js";import{p as u}from"./pluralize-d25a928b.js";import{u as E}from"./useInfiniteScroll-1e8e8e17.js";import{_ as H}from"./ButtonItem-718c0517.js";import{D as _}from"./datetime-8ddd27a0.js";import{_ as I}from"./Footer-0988dcd8.js";import{r as h}from"./ChevronRightIcon-a926c707.js";import{e as p,i as k,o as a,c as w,w as d,b as l,a as t,t as s,u as n,d as c,h as b,F as y,f as v,g as P}from"./app-f0078ddb.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./useIntersect-6e15125e.js";/* empty css            */const R={class:"2xl:pr-[32rem] lg:pr-96 pb-16"},T={class:"p-8"},q={class:"mt-4 w-full py-6"},F={"aria-labelledby":"Description"},M=["src"],U={class:"w-full py-6"},A={class:"flex flex-row font-intro"},G={class:"grow self-center"},J={class:"text-4xl font-bold text-gray-900 font-intro"},K={class:"text-xl font-bold text-gray-900 font-intro mt-2"},Q={class:"text-base font-medium text-gray-600 font-intro mt-2"},W={class:"mt-4 grid grid-cols-2 text-gray-600 border-b border-gray-300 pb-4 font-semibold"},X={class:"text-left"},Y={class:"text-right"},Z={class:"mt-8"},tt={class:"divide-y"},et={class:"flex flex-col font-intro mr-2"},ot={class:"flex flex-row"},rt=["textContent"],st={class:"text-sm font-medium leading-5 text-gray-500 text-right grow"},nt={class:"mt-2 line-clamp-2 flex items-center text-base font-semibold leading-5 text-gray-500"},it={class:"self-center group-hover:bg-sky-100 transition duration-150 ease-in-out rounded-full p-1"},at={class:"bg-slate-50 lg:fixed lg:bottom-0 lg:right-0 lg:top-16 lg:w-96 2xl:w-[32rem] lg:overflow-y-auto lg:border-l lg:border-slate-300 p-8"},lt={class:"flex flex-col justify-between border-b border-gray-300 pb-8"},ct={class:"text-sm font-bold uppercase text-gray-500"},dt={key:0},ut={key:1},mt={class:"hover:bg-slate-100 mt-2 rounded-xl transition duration-150 ease-in-out px-2 py-4"},ft={class:"flex grow flex-col font-intro"},ht=["textContent"],gt={class:"mt-2 line-clamp-2 flex items-center text-base font-semibold leading-5 text-gray-600"},xt={class:"self-center group-hover:bg-sky-100 transition duration-150 ease-in-out rounded-full p-1"},_t={key:0,class:"mt-2 text-sm font-bold text-sky-600"},pt={"aria-labelledby":"Collections"},kt={class:"text-sm mt-8 font-bold uppercase text-gray-500"},wt={class:"mt-4 flex flex-col divide-y items-center"},bt={class:"flex grow flex-col"},yt={class:"text-lg font-bold text-gray-900 font-intro leading-5"},vt={class:"text-xs font-medium text-gray-700 line-clamp-2 mt-2"},Ct={class:"self-center group-hover:bg-sky-100 transition duration-150 ease-in-out rounded-full p-1"},St={key:0,class:"mt-2 font-sans text-sm font-medium text-center text-gray-600"},Lt={class:"capitalize"},de={__name:"Index",props:{work:Object,collections:Object,verseCount:Number,recentSection:Object,firstSection:Object},setup(e){const r=e,C=[{name:"Read",href:"/read",current:!1},{name:r.work.name,href:`/read/${r.work.name}`,current:!1},{name:"Collections",href:"#",current:!0}],g=p(null);let S=p(r.collections);const{items:L}=E(S.value,g),$=o=>"/read/"+r.work.author.url+"/"+r.work.url+"/collections/"+o.url,D=o=>o.book==0?"Preface":r.work.l1+" "+o.book+" - "+o.subtitle;let m=r.recentSection?r.recentSection:r.firstSection;const N=o=>o.chapter?route("read.section-chapter",{author:r.work.author.url,work:r.work.url,book:r.work.l1.toLowerCase()+"-"+o.book,chapter:r.work.l2.toLowerCase()+"-"+o.chapter,line_start:o.line_start,line_end:o.line_end}):route("read.section",{author:r.work.author.url,work:r.work.url,book:r.work.l1.toLowerCase()+"-"+o.book,line_start:o.line_start,line_end:o.line_end}),j=o=>o.chapter?r.book.work_l2+" "+o.chapter:"Lines "+o.line_start+"-"+o.line_end;return(o,x)=>{const O=k("Head"),f=k("Link");return a(),w(B,null,{default:d(()=>[l(O,null,{default:d(()=>[t("title",null,s(e.work.name)+" Collections",1)]),_:1}),t("main",R,[t("div",T,[l(z,{class:"lg:col-span-9 xl:grid-cols-10",pages:C}),t("div",q,[t("section",F,[t("img",{class:"overflow-hidden rounded-2xl bg-white",src:`https://latintutorial-spaces.nyc3.cdn.digitaloceanspaces.com/works/${e.work.image_name}`,alt:""},null,8,M)]),t("section",U,[t("div",A,[t("div",G,[t("h1",J,s(e.work.name),1),t("h1",K,s(e.work.author.name),1),t("p",Q,s(e.work.description),1)])])]),t("section",W,[t("div",X,s(`${e.collections.data.length} ${n(u)("collection",e.collections.length).toLowerCase()}`),1),t("div",Y,s(e.verseCount.toLocaleString()+" "+n(u)(e.work.l4,e.verseCount).toLocaleLowerCase()),1)]),t("section",Z,[t("div",tt,[(a(!0),c(y,null,b(n(L),i=>(a(),c("div",{key:i.id,class:"py-3 hover:bg-gray-50 transition duration-150 ease-in-out px-2"},[l(f,{href:$(i),class:"duration-250 flex flex-row items-center rounded-lg py-4 transition ease-in-out group cursor-pointer align-center"},{default:d(()=>[t("div",et,[t("div",ot,[t("h3",{class:"text-lg font-bold leading-5 text-gray-900 line-clamp-1",textContent:s(i.name)},null,8,rt),t("h5",st,s(i.reference),1)]),t("p",nt,s(i.description),1)]),t("div",it,[l(n(h),{class:"h-8 w-8 text-gray-400 stroke-2 group-hover:text-sky-700 transition duration-150 ease-in-out"})])]),_:2},1032,["href"])]))),128))]),t("div",{ref_key:"landmark",ref:g},null,512)])])]),l(I)]),t("aside",at,[t("div",lt,[t("h5",ct,[e.recentSection?(a(),c("span",dt,"Most Recent Section")):(a(),c("span",ut,"Start Reading"))]),t("div",mt,[l(f,{class:"duration-250 flex flex-row items-center py-4 transition ease-in-out group cursor-pointer",href:N(n(m))},{default:d(()=>[t("div",ft,[t("h3",{class:"text-lg font-bold leading-5 text-gray-900 line-clamp-1",textContent:s(j(n(m)))},null,8,ht),t("p",gt,s(n(m).description),1)]),t("div",xt,[l(n(h),{class:"h-8 w-8 text-gray-400 stroke-2 group-hover:text-sky-700 transition duration-150 ease-in-out"})])]),_:1},8,["href"])]),e.recentSection?(a(),c("p",_t," Last visited on "+s(n(_).fromISO(e.recentSection.last_accessed).toLocaleString(n(_).DATE_MED)),1)):v("",!0)]),t("section",pt,[t("h5",kt,s(n(u)(e.work.l1,e.work.books.length))+" for this work ",1),t("div",wt,[(a(!0),c(y,null,b(e.work.books.slice(0,5),(i,V)=>(a(),w(f,{key:V,href:`/read/${e.work.author.url}/${e.work.url}/${e.work.l1.toLowerCase()}-${i.book}`,class:"w-full py-4 px-2 flex items-center flex-row group cursor-pointer hover:bg-gray-100 transition duration-150 ease-in-out"},{default:d(()=>[t("div",bt,[t("h4",yt,s(D(i)),1),t("p",vt," Donec bibendum lacinia augue. Praesent aliquet arcu at commodo bibendum. Pellentesque mattis ullamcorper orci non suscipit. Donec eget urna vulputate, dapibus magna nec, pulvinar libero. Nullam ipsum ante, fermentum sit amet facilisis sed, sodales ut. "+s(i.description),1)]),t("div",Ct,[l(n(h),{class:"h-8 w-8 text-gray-400 stroke-2 group-hover:text-sky-700 transition duration-150 ease-in-out"})])]),_:2},1032,["href"]))),128))]),e.work.books.length>5?(a(),c("div",St," ... and "+s(e.work.books.length-5)+" more ",1)):v("",!0),l(H,{class:"mt-4 w-full",size:"sm",color:"white",link:`/read/${e.work.author.url}/${e.work.url}`},{default:d(()=>[x[0]||(x[0]=P("View all ")),t("span",Lt,s(n(u)(e.work.l1,e.work.books.length)),1)]),_:1},8,["link"])])])]),_:1})}}};export{de as default};
