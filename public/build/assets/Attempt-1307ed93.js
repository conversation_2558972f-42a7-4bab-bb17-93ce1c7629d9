import{o,d as l,a as s,V as R,e as u,k as h,A as We,p as Ue,l as X,c as $,w,b as g,u as t,m as Be,j as ve,t as x,f as m,T as G,g as A,F as T,h as J,q as fe,x as ge,n as q,a9 as xe,W as D,a3 as oe}from"./app-f0078ddb.js";import{_ as Fe}from"./AppLayout-33f062bc.js";import{_ as He}from"./Breadcrumbs-c96e9207.js";import{_ as he}from"./ProgressBar-b7203293.js";import{_ as j}from"./ButtonItem-718c0517.js";import{T as le}from"./easytimer-3d932146.js";import Re from"./Options-d704621e.js";import{P as qe}from"./Promotion-3eee0057.js";import{_ as ye}from"./AssignmentModule-fc2620bb.js";import{r as De}from"./InformationCircleIcon-5139ba20.js";import{r as Oe}from"./PlusIcon-fbc40698.js";import{r as Ke}from"./PlayIcon-01d0f73f.js";import{a as E}from"./animate.es-f1432f5f.js";/* empty css            */import"./_plugin-vue_export-helper-c27b6911.js";import"./PlayCircleIcon-8bd12a30.js";import"./BookOpenIcon-1746d343.js";import"./RectangleGroupIcon-04390470.js";import"./QueueListIcon-824b634b.js";import"./dialog-86f7bd91.js";import"./env-c107754a.js";import"./use-outside-click-484df218.js";import"./render-c34c346a.js";import"./focus-management-8406d052.js";import"./keyboard-982fc047.js";import"./micro-task-89dcd6af.js";import"./disposables-4ddc41dd.js";import"./open-closed-7f51e238.js";import"./description-cd3ec634.js";import"./transition-a0923044.js";import"./XMarkIcon-9bc7c0bd.js";import"./ChevronDownIcon-660c32b0.js";import"./use-resolve-button-type-24d8b5c5.js";import"./use-text-value-2c18b2b1.js";import"./calculate-active-index-51ff911b.js";import"./use-tree-walker-100527b8.js";import"./combobox-4401f443.js";import"./form-cb36670c.js";import"./Colosseum-0e8d62a4.js";import"./CheckCircleIcon-d86d1232.js";import"./ChevronRightIcon-a926c707.js";import"./ToggleItem-94c3ab1e.js";import"./label-6c8c1cbc.js";import"./OptionsMode-26ccd316.js";import"./radio-group-97521e36.js";import"./datetime-8ddd27a0.js";import"./CheckIcon-6a201aa1.js";import"./ArrowPathIcon-f74cb8d6.js";function Ne(y,i){return o(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 5.25v13.5m-7.5-13.5v13.5"})])}const Xe={class:"pb-16 lg:pr-96 2xl:pr-[32rem]"},Ge={class:"px-4 py-8 sm:px-8"},Te={class:"flex flex-row items-center justify-between"},Je={class:"mt-8"},Qe={key:0},Ye={key:1},Ze={key:0,class:"mb-8 border-b border-gray-300 pb-16 lg:hidden"},et={class:"mt-2 mb-8"},tt={class:"text-center text-xs font-bold text-gray-500 uppercase"},st={class:"mt-8"},at={key:0,class:"rounded-lg bg-white p-6 shadow"},rt={class:"mb-4 flex items-center justify-between"},ot={class:"text-xl text-gray-900"},lt={class:"font-bold"},nt={class:"grid grid-cols-1 gap-8 md:grid-cols-2"},it={class:"space-y-4"},ut=["for"],ct={class:"relative w-2/3"},dt=["id","onUpdate:modelValue","disabled","onInput","onKeydown"],mt=["onClick"],pt={class:"space-y-4"},vt=["for"],ft={class:"relative w-2/3"},gt=["id","onUpdate:modelValue","disabled","onInput","onKeydown"],xt=["onClick"],ht={class:"mt-6 flex justify-end"},yt={class:"hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"},wt={key:0,class:"mb-8"},bt={key:1,class:"mt-2"},_t={class:"text-center text-xs font-bold text-gray-500 uppercase"},kt={key:2,class:"mt-8"},Ct={key:3},Vt={key:0},At={class:"mt-2 text-left text-sm text-gray-800"},$t={key:0},Mt={class:"mx-auto mt-8 text-center"},Lt={key:0,class:"mx-auto"},Pt={key:1,class:"mx-auto"},St={key:0},zt={key:0,class:"mx-auto"},jt={key:1,class:"mx-auto"},Et={key:4,class:"mt-8 grid grid-cols-1 gap-8"},It={key:0,class:"text-left"},Wt={class:"text-sm font-bold text-gray-500 uppercase"},Ut={key:0},Bt={key:1},Ft={key:0,class:"mt-2 flex flex-row items-center"},Ht={"aria-labelledby":"userStats"},Rt={class:"grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"},qt={class:"text-sm leading-6 font-medium text-gray-500"},Dt={class:"w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"},Ot={key:0},Kt={key:0,class:"ml-1 text-sm font-medium text-gray-500"},Nt={key:1},Xt={class:"mt-4 grid grid-cols-10 gap-2 opacity-75"},Gt={key:1,class:"mt-8 w-full"},Tt={key:0,class:"mx-auto"},Jt={key:1,class:"mx-auto"},Ks={__name:"Attempt",props:{session:{type:Object,required:!1},word:{type:Object,required:!1},assignment:{type:Object,required:!1},initialWords:{type:Array,required:!1}},setup(y){const i=y,we=[{name:"Practice",href:"/practice",current:!1},{name:"Grammar",href:"/practice/grammar",current:!1},{name:"Declension Practice",href:"#",current:!0}];let v=R({declensions:[],cases:[],difficulty:0,time:0,requireMacra:!1,zenMode:!1});const be={id:0,lemma:"puella",gender:"feminine",declension:"1st",declensions:{singular:{nominative:"puella",genitive:"puellae",dative:"puellae",accusative:"puellam",ablative:"puellā",vocative:"puella",locative:""},plural:{nominative:"puellae",genitive:"puellārum",dative:"puellīs",accusative:"puellās",ablative:"puellīs",vocative:"puellae",locative:""}}};let M=u(h().props.authenticated?h().props.user:null),ne=u(h().props.authenticated?M.value.xp:0),O=h().props.authenticated?u(M.value.xp):0,L=h().props.authenticated?u(M.value.level):u(0);u(h().props.authenticated?M.value.level.next_level_max:null);let _e=u(!1),Q=u(!1),I=u(!1),n=R({correct:0,incorrect:0}),f=u(),P=u(),Y=u(!0),Z=u(!1),ee=u(!1),W=u(0),te=h().props.authenticated?u(M.value.level.max-M.value.xp+1):u(0),se=u(0),k=u(),ae=R([]),b=u(0),p=new le,_=new le,C=R({singular:{nominative:"",genitive:"",dative:"",accusative:"",ablative:"",vocative:"",locative:""},plural:{nominative:"",genitive:"",dative:"",accusative:"",ablative:"",vocative:"",locative:""}}),V=R({singular:{nominative:!1,genitive:!1,dative:!1,accusative:!1,ablative:!1,vocative:!1,locative:!1},plural:{nominative:!1,genitive:!1,dative:!1,accusative:!1,ablative:!1,vocative:!1,locative:!1}}),U=u(0),S=u(!1);u(!0);let B=u(!1);We(()=>{i.initialWord&&(f.value=i.initialWord),i.session&&(I.value=!0,n.correct=i.session.correct,n.incorrect=i.session.attempts-i.session.correct,b.value=i.session.streak,U.value=i.session.xp_earned,W.value=i.session.correct?Math.round(i.session.correct/i.session.attempts*100):0,_.start()),i.assignment&&(F.value=i.assignment.completed==0)}),Ue(()=>i.word,r=>{r&&(P.value=r,Y.value=!0,f.value||(f.value=r))},{immediate:!0});const ie=X(()=>L.value?(O.value-L.value.min)/(L.value.max-L.value.min):0),ue=X(()=>n.correct+n.incorrect==0?-1:n.correct+n.incorrect<10?n.correct/10:Math.round(100*n.correct/(n.correct+n.incorrect))),ce=()=>{h().props.authenticated?(Q.value=!0,Ce()):D.get("/login?redirect=/practice/grammar/declension")},ke=X(()=>[{name:"Correct",value:n.correct,unit:"out of "+(n.correct+n.incorrect)},{name:"Current Streak",value:b.value,unit:"in a row"},{name:"XP Earned",value:U.value},{name:"Accuracy",value:W.value,unit:"%"}]),Ce=(r=!1)=>{D.reload({only:["session"],data:{reattempt:!!r,declensions:v.declensions,cases:v.cases,difficulty:v.difficulty},preserveState:!0,preserveScroll:!0,onSuccess:()=>{n.correct=i.session.correct,n.incorrect=i.session.attempts-i.session.correct,b.value=i.session.streak,U.value=i.session.xp_earned,W.value=i.session.correct?Math.round(i.session.correct/i.session.attempts*100):0,oe.post("/api/practice/grammar/declension/update-session-options",{session:i.session.id,settings:v}).then(()=>{setTimeout(()=>{_.start({countdown:!1,startValues:{seconds:0},precision:"secondTenths"}),v.time>0?(p.start({countdown:!0,startValues:{seconds:v.time}}),k.value=z(p.getTimeValues()),p.addEventListener("secondsUpdated",function(){k.value=z(p.getTimeValues())}),p.addEventListener("targetAchieved",function(){N()})):(k.value=z(_.getTimeValues()),_.addEventListener("secondsUpdated",function(){k.value=z(_.getTimeValues())})),D.reload({only:["initialWords"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{f.value=i.initialWords[0],P.value=i.initialWords[1],Q.value=!1,I.value=!0}})},Ve(200,500)),f.value||(f.value=be)})}})},Ve=(r,e)=>Math.floor(Math.random()*(e-r+1))+r,K=(r,e,a)=>{if(C[r][e]===f.value.declensions[r][e]){V[r][e]=!0,Ae(r,e,C[r][e]);const c=[...document.querySelectorAll(".singular-input"),...document.querySelectorAll(".plural-input")],d=c.indexOf(a.target);d!==-1&&d+1<c.length&&c[d+1].focus();let H=!0;for(const Ee of["singular","plural"]){for(const Ie of v.cases)if(!V[Ee][Ie]){H=!1;break}if(!H)break}H&&$e()}},Ae=async(r,e,a)=>{try{await oe.post("/api/practice/grammar/declension/log-field",{session:i.session.id,word_id:f.value.id,number:r,case:e,input:a,correct:!0,used_hint:re.value,time:_.getTimeValues().secondTenths})}catch(c){console.error("Failed to log field:",c)}re.value=!1},$e=()=>{if(ee.value=!0,b.value<1)E(d=>{b.value=Math.round(d)},{duration:.3,easing:"ease-out"});else{let d=b.value;E(H=>{b.value=Math.round(H+d)},{duration:.3,easing:"ease-out"})}let r=n.correct;E(d=>{n.correct=Math.round(d+r)},{duration:.3,easing:"ease-out"});let e=ne.value;E(d=>{ne.value=Math.round(e+d)},{duration:.3,easing:"ease-out"});let a=U.value;E(d=>{U.value=Math.round(a+d*me())},{duration:.3,easing:"ease-out"});let c=W.value;E(d=>{W.value=c+Math.round((Math.round(n.correct/(n.correct+n.incorrect)*100)-c)*d)},{duration:.3,easing:"ease-out"}),se.value=_.getTotalTimeValues().seconds,oe.post("/api/practice/grammar/declension/add-attempt",{session:i.session.id,word_id:f.value.id,correct:ee.value,xp:me(),streak:b.value,time:_.getTotalTimeValues().secondTenths}).then(d=>{O.value=d.data.xp,L.value=d.data.level,te.value=d.data.next_level_xp,setTimeout(()=>{de()},1500)})},de=()=>{P.value?(Me(),f.value=P.value,P.value=null,Y.value=!0,_e.value=!1,D.reload({only:["word"],preserveState:!0,preserveScroll:!0,replace:!0,onSuccess:()=>{P.value=i.word,Y.value=!1}})):N()},Me=()=>{for(let r in C)for(let e in C[r])C[r][e]="",V[r][e]=!1},me=()=>{if(ee.value)switch(v.difficulty){case 1:return 3;case 2:return 5;default:return 2}return 0},N=()=>{Z.value=!0,D.post("/api/practice/grammar/declension/finish",{session:i.session.id})},Le=()=>{n.incorrect++,b.value=0,ae.push({word:f.value.lemma,correct:!1}),de()},re=u(!1),pe=(r,e)=>{re.value=!0,C[r][e]=f.value.declensions[r][e],V[r][e]=!0,b.value>0&&b.value--};let F=u(!1);const z=r=>r.days>1?"more than "+r.days+" days":r.days>0?"more than "+r.days+" day":r.hours>1?"more than "+r.hours+" hours":r.hours>0?"more than "+r.hours+" hour":r.seconds<=9?r.minutes+":0"+r.seconds:r.minutes+":"+r.seconds,Pe=()=>{v.time>0?p.isRunning()?(p.pause(),S.value=!0):(p.start(),S.value=!1):_.isRunning()?(_.pause(),S.value=!0):(_.start(),S.value=!1)},Se=()=>{let r=p.getTimeValues();r.minutes+=1,p.removeAllEventListeners("targetAchieved"),p=new le,p.start({countdown:!0,startValues:{minutes:r.minutes,seconds:r.seconds}}),S.value&&p.pause(),k.value=z(p.getTimeValues()),p.addEventListener("secondsUpdated",function(){k.value=z(p.getTimeValues())}),p.addEventListener("targetAchieved",function(){N()})},ze=[{value:1,color:"bg-red-500"},{value:2,color:"bg-orange-600"},{value:3,color:"bg-amber-500"},{value:4,color:"bg-yellow-600"},{value:5,color:"bg-lime-600"},{value:6,color:"bg-green-600"},{value:7,color:"bg-teal-600"},{value:8,color:"bg-blue-600"},{value:9,color:"bg-indigo-600"},{value:10,color:"bg-purple-600"}],je=X(()=>n.correct+n.incorrect==0?-1:n.correct+n.incorrect<10?n.correct:Math.round(10*n.correct/(n.correct+n.incorrect)));return(r,e)=>(o(),$(Fe,null,{default:w(()=>[g(t(Be),null,{default:w(()=>e[10]||(e[10]=[s("title",null,"Declension Practice",-1)])),_:1}),s("main",Xe,[s("div",Ge,[s("div",Te,[g(He,{class:"lg:col-span-9 xl:grid-cols-10",pages:we}),g(t(De),{class:"mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden",onClick:e[0]||(e[0]=a=>ve(B)?B.value=!t(B):B=!t(B))})]),s("div",Je,[g(G,{mode:"out-in","enter-active-class":"transition ease-out duration-150","enter-from-class":"transform opacity-0 scale-95","enter-to-class":"transform opacity-100 scale-100","leave-active-class":"transition ease-in duration-150","leave-from-class":"transform opacity-100 scale-100","leave-to-class":"transform opacity-0 scale-95"},{default:w(()=>[t(I)?(o(),l("div",Ye,[t(h)().props.authenticated?(o(),l("div",Ze,[g(he,{class:"",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+t(L).level,"post-text":t(O)+" XP",progress:ie.value},null,8,["pre-text","post-text","progress"]),s("div",et,[s("p",tt,x(t(te))+" xp to the next level ",1)])])):m("",!0),y.assignment?(o(),$(ye,{key:1,assignment:y.assignment,"words-seen":t(ae).length,"words-correct":t(n).correct,time:t(se),accuracy:ue.value,onCompleted:e[2]||(e[2]=a=>r.completeAssignment())},null,8,["assignment","words-seen","words-correct","time","accuracy"])):m("",!0)])):(o(),l("div",Qe,[g(Re,{"onUpdate:options":e[1]||(e[1]=a=>ve(v)?v.value=a:v=a)})]))]),_:1})])]),s("div",st,[t(f)?(o(),l("div",at,[s("div",rt,[s("h2",ot,[s("span",lt,x(t(f).word),1),A(", "+x(t(f).gender)+", "+x(t(f).definition),1)])]),s("div",nt,[s("div",null,[e[11]||(e[11]=s("h3",{class:"mb-4 text-center text-lg font-semibold"},"Singular",-1)),s("div",it,[(o(!0),l(T,null,J(t(v).cases,a=>(o(),l("div",{key:`singular-${a}`,class:"flex items-center"},[s("label",{for:`singular-${a}`,class:"w-1/3 font-medium text-gray-700"},x(a.charAt(0).toUpperCase()+a.slice(1)),9,ut),s("div",ct,[fe(s("input",{id:`singular-${a}`,"onUpdate:modelValue":c=>t(C).singular[a]=c,type:"text",class:q(["singular-input w-full rounded-md border border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none",{"border-green-500 bg-green-50":t(V).singular[a]}]),disabled:t(V).singular[a],onInput:c=>K("singular",a,c),onKeydown:xe(c=>K("singular",a,c),["tab"])},null,42,dt),[[ge,t(C).singular[a]]]),t(V).singular[a]?m("",!0):(o(),l("button",{key:0,onClick:c=>pe("singular",a),class:"absolute top-2 right-2 text-xs text-gray-400 hover:text-gray-600"}," Hint ",8,mt))])]))),128))])]),s("div",null,[e[12]||(e[12]=s("h3",{class:"mb-4 text-center text-lg font-semibold"},"Plural",-1)),s("div",pt,[(o(!0),l(T,null,J(t(v).cases,a=>(o(),l("div",{key:`plural-${a}`,class:"flex items-center"},[s("label",{for:`plural-${a}`,class:"w-1/3 font-medium text-gray-700"},x(a.charAt(0).toUpperCase()+a.slice(1)),9,vt),s("div",ft,[fe(s("input",{id:`plural-${a}`,"onUpdate:modelValue":c=>t(C).plural[a]=c,type:"text",class:q(["plural-input w-full rounded-md border border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none",{"border-green-500 bg-green-50":t(V).plural[a]}]),disabled:t(V).plural[a],onInput:c=>K("plural",a,c),onKeydown:xe(c=>K("plural",a,c),["tab"])},null,42,gt),[[ge,t(C).plural[a]]]),t(V).plural[a]?m("",!0):(o(),l("button",{key:0,onClick:c=>pe("plural",a),class:"absolute top-2 right-2 text-xs text-gray-400 hover:text-gray-600"}," Hint ",8,xt))])]))),128))])])]),s("div",ht,[g(j,{color:"gray",size:"md",onClick:Le,class:"px-16"},{default:w(()=>e[13]||(e[13]=[A(" Get a New Word ")])),_:1})])])):m("",!0)])]),s("aside",yt,[t(h)().props.authenticated?m("",!0):(o(),l("section",wt,[g(qe)])),s("div",null,[t(h)().props.authenticated?(o(),$(he,{key:0,class:"mt-2",height:"h-3",size:"sm","display-progress":!1,"display-total":!0,"pre-text":"LEVEL "+t(L).level,"post-text":t(O)+" XP",progress:ie.value},null,8,["pre-text","post-text","progress"])):m("",!0),t(h)().props.authenticated?(o(),l("div",bt,[s("p",_t,x(t(te))+" xp to the next level ",1)])):m("",!0),e[23]||(e[23]=s("h2",{class:"mt-8 font-intro text-2xl font-bold"},"Declension Practice",-1)),e[24]||(e[24]=s("h4",{class:"mt-2 font-sans text-sm font-medium text-gray-600"}," Practice Latin declensions to improve your vocabulary and grammar skills. ",-1)),y.assignment?(o(),l("section",kt,[g(ye,{assignment:y.assignment,"words-seen":t(ae).length,"words-correct":t(n).correct,time:t(se),accuracy:ue.value,onCompleted:e[3]||(e[3]=a=>r.completeAssignment())},null,8,["assignment","words-seen","words-correct","time","accuracy"])])):m("",!0),t(I)?(o(),l("div",Et,[t(k)?(o(),l("section",It,[s("h5",Wt,[e[20]||(e[20]=A(" Time ")),t(v).time>0?(o(),l("span",Ut,"Remaining")):(o(),l("span",Bt,"Elapsed"))]),t(k)?(o(),l("div",Ft,[s("div",{class:q(["grow text-left font-medium text-gray-900",t(k).length>10?"text-2xl":"text-4xl"])},x(t(k)),3),t(v).time>0?(o(),l("div",{key:0,class:"group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:e[7]||(e[7]=a=>Se())},[g(t(Oe),{class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})])):m("",!0),s("div",{class:"group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300",onClick:e[8]||(e[8]=a=>Pe())},[t(S)?(o(),$(t(Ke),{key:0,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"})):(o(),$(t(Ne),{key:1,class:"mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"}))])])):m("",!0)])):m("",!0),s("section",Ht,[s("div",null,[s("dl",Rt,[(o(!0),l(T,null,J(ke.value,a=>(o(),l("div",{key:a.name,class:"flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"},[s("dt",qt,x(a.name),1),s("dd",Dt,[a.value?(o(),l("span",Ot,[A(x(a.value)+" ",1),a.unit?(o(),l("span",Kt,x(a.unit),1)):m("",!0)])):(o(),l("span",Nt,"–"))])]))),128))])])]),s("section",null,[e[21]||(e[21]=s("h5",{class:"text-sm font-bold text-gray-500 uppercase"}," Performance ",-1)),s("div",Xt,[(o(),l(T,null,J(ze,a=>s("div",{class:q(["h-4 w-full rounded-sm shadow-sm",je.value>=a.value?a.color:"bg-gray-300"]),key:a.value},null,2)),64))])]),t(I)?(o(),l("div",Gt,[g(j,{class:"inline-flex w-full disabled:bg-blue-400",size:"lg",color:"indigo",disabled:t(Z),onClick:e[9]||(e[9]=a=>N())},{default:w(()=>[g(G,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:w(()=>[t(Z)?(o(),l("span",Tt,e[22]||(e[22]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Finishing ...",-1)]))):(o(),l("span",Jt,"Finish"))]),_:1})]),_:1},8,["disabled"])])):m("",!0)])):(o(),l("div",Ct,[t(F)&&y.assignment.attempt?(o(),l("div",Vt,[e[14]||(e[14]=s("h4",{class:"mt-8 text-left text-sm font-bold text-gray-500 uppercase"}," Assignment in Progress ",-1)),s("p",At,[A(x(y.assignment.attempt.correct)+" out of "+x(y.assignment.attempt.attempts)+" correct ",1),y.assignment.attempt.attempts>0?(o(),l("span",$t,"("+x(Math.round(100*y.assignment.attempt.correct)/y.assignment.attempt.attempts)+"% accuracy)",1)):m("",!0),A(" | "+x(r.durationOfAttempt(y.assignment.attempt.time)),1)])])):m("",!0),s("div",Mt,[t(h)().props.authenticated&&t(M).membership.subscribed?(o(),l("div",{key:0,class:q(["grid",t(F)?"grid-cols-2 gap-4":"grid-cols-1"])},[g(j,{color:"blue",size:"md",class:"inline-flex w-full disabled:bg-blue-400",onClick:e[4]||(e[4]=a=>ce())},{default:w(()=>[g(G,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:w(()=>[t(Q)?(o(),l("span",Lt,e[15]||(e[15]=[s("svg",{class:"inline h-5 w-5 animate-spin text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),l("span",Pt,[e[16]||(e[16]=A("Continue")),t(F)?(o(),l("span",St," Attempt")):m("",!0)]))]),_:1})]),_:1}),t(F)?(o(),$(j,{key:0,color:"white",size:"md",class:"inline-flex w-full disabled:bg-gray-200",onClick:e[5]||(e[5]=a=>r.handleReattemptClick())},{default:w(()=>[g(G,{mode:"out-in","enter-active-class":"transition ease-out duration-100","enter-from-class":"transform opacity-0","enter-to-class":"transform opacity-100","leave-active-class":"transition ease-in duration-100","leave-from-class":"transform opacity-100","leave-to-class":"transform opacity-0"},{default:w(()=>[r.isLoadingReattempt?(o(),l("span",zt,e[17]||(e[17]=[s("svg",{class:"inline h-5 w-5 animate-spin text-gray-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),s("span",{class:"ml-2"},"Loading ...",-1)]))):(o(),l("span",jt,"Restart"))]),_:1})]),_:1})):m("",!0)],2)):t(h)().props.authenticated?(o(),$(j,{key:1,color:"indigo",size:"md",class:"flex w-full items-center justify-center",link:"/subscribe"},{default:w(()=>e[18]||(e[18]=[A("Join LatinTutorial Pro to Attempt")])),_:1})):(o(),$(j,{key:2,color:"indigo",size:"md",class:"flex w-full items-center justify-center",onClick:e[6]||(e[6]=a=>ce())},{default:w(()=>e[19]||(e[19]=[A("Log in to Continue")])),_:1}))])]))])])]),_:1}))}};export{Ks as default};
