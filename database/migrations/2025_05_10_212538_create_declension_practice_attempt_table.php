<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('declension_practice_attempts', function (Blueprint $table) {
      $table->id();
      $table->uuid('session_id');
      $table
        ->foreign('session_id')
        ->references('id')
        ->on('declension_sessions');
      $table->bigInteger('user_id');
      $table->foreign('user_id')->references('id')->on('users');
      $table->text('attempt')->nullable();
      $table->unsignedBigInteger('word_id');
      $table->foreign('word_id')->references('id')->on('words');
      $table->boolean('correct');
      $table->bigInteger('time');
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('declension_practice_attempts');
  }
};
