<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('infinitas_work', function (Blueprint $table) {
      $table->id();
      $table->bigInteger('user_id');
      $table->foreign('user_id')->references('id')->on('users');
      $table->string('source');
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('infinitas_work');
  }
};
