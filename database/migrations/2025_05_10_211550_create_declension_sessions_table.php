<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('declension_sessions', function (Blueprint $table) {
      $table->uuid('id')->primary();
      $table->bigInteger('user_id');
      $table->foreign('user_id')->references('id')->on('users');
      $table->json('options')->nullable();
      $table->boolean('completed')->default(false);
      $table->unsignedInteger('attempts')->default(0);
      $table->unsignedInteger('correct')->default(0);
      $table->unsignedInteger('streak')->default(0);
      $table->bigInteger('time')->default(0);
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('declension_sessions');
  }
};
