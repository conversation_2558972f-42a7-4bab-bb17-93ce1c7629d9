<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('work_word', function (Blueprint $table) {
      $table->id();
      $table->integer('work_id')->unsigned();
      $table->foreign('work_id')->references('id')->on('works');
      $table->bigInteger('word_id')->unsigned();
      $table->foreign('word_id')->references('id')->on('words');
      $table->bigInteger('count')->default(0);
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('work_word');
  }
};
