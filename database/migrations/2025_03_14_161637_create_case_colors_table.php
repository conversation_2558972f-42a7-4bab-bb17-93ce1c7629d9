<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
  /**
   * Run the migrations.
   */
  public function up(): void
  {
    Schema::create('case_colors', function (Blueprint $table) {
      $table->id();
      $table->bigInteger('user_id')->constrained()->onDelete('cascade');
      $table->integer('case_id');
      $table->string('color');
      $table->timestamps();
    });
  }

  /**
   * Reverse the migrations.
   */
  public function down(): void
  {
    Schema::dropIfExists('case_colors');
  }
};
