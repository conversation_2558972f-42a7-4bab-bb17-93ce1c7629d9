<?php

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\WordController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\ReadController;
use App\Http\Controllers\VideoFavoriteController;
use App\Http\Controllers\PracticeController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\SectionController;
use App\Http\Controllers\LearnedWordController;
use App\Http\Controllers\StarredWordController;
use App\Http\Controllers\VocabPracticeController;
use App\Http\Controllers\GrammarPracticeController;
use App\Http\Controllers\VocabPracticeAttemptController;
use App\Http\Controllers\GrammarPracticeAttemptController;
use App\Http\Controllers\AchievementController;
use App\Http\Controllers\ActivityController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\SectionNoteController;
use App\Http\Controllers\NoteController;
use App\Http\Controllers\DevController;
use App\Http\Controllers\WorkController;
use App\Http\Controllers\AuthOAuthController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\InfiniteVocabController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\CaseColorController;
use App\Http\Controllers\DeclensionController;
use App\Http\Controllers\DeclensionPracticeController;
use Illuminate\Http\Request;
use Stripe\Stripe;
use App\Models\User;
use App\Models\Video;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Laravel\Socialite\Facades\Socialite;
use App\Http\Controllers\TranslationController;
use OpenAI\Laravel\Facades\OpenAI;
use App\Http\Controllers\VideoTrackingController;
use App\Models\Puzzle;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware('web')->group(function () {
  Route::get('/health', function () {
    return response()->json(['status' => 'OK'], 200);
  });

  // About and Contact Pages
  Route::get('/about', function () {
    $stats = [
      'video_minutes' => round(
        Video::where('published_at', '<', Carbon\Carbon::now())->sum('length') /
          60
      ),
      'video_views' => Video::where(
        'published_at',
        '<',
        Carbon\Carbon::now()
      )->sum('views'),
      'words_practiced' => \App\Models\VocabPracticeAttempt::count(),
      'langauge_practice' => \App\Models\GrammarPracticeAttempt::count(),
    ];
    return Inertia::render('About', [
      'stats' => $stats,
    ]);
  })->name('about');
  Route::get('/contact', function () {
    return Inertia::render('Contact');
  })->name('contact');
  Route::get('/faq', function () {
    return Inertia::render('Faq');
  })->name('faq');
  Route::post('/send-contact-email', [
    DevController::class,
    'contactEmail',
  ])->name('send-contact-email');

  // Read
  Route::get('/read', [ReadController::class, 'index'])->name('read');
  Route::get('/read/{author}/{work}', [ReadController::class, 'show'])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
    ])
    ->name('read.show');
  Route::get('/read/{author}/{work}/{book}', [BookController::class, 'show'])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
      'book' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('read.book.show');
  Route::get('/read/{author}/{work}/{book}/{line_start}-{line_end}', [
    SectionController::class,
    'show',
  ])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
      'book' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
      'line_start' => '[0-9]+', // Only numbers
      'line_end' => '[0-9]+', // Only numbers
    ])
    ->name('read.section');

  Route::get('/read/{author}/{work}/{book}/{chapter}/{line_start}-{line_end}', [
    SectionController::class,
    'showChapter',
  ])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
      'book' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
      'chapter' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
      'line_start' => '[0-9]+', // Only numbers
      'line_end' => '[0-9]+', // Only numbers
    ])
    ->name('read.section-chapter');

  // Videos
  Route::get('/watch', [VideoController::class, 'index'])->name('watch');
  Route::get('/watch/{id}', [VideoController::class, 'show'])
    ->where([
      'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('watch.show');
  Route::post('api/watch/download', [VideoController::class, 'download'])->name(
    'watch.download'
  );
  // Route::redirect("/videos/{id}", "/watch/{id}", 301);

  // Words
  Route::get('/words', [WordController::class, 'index'])->name('words');
  Route::get('/words/custom', [WordController::class, 'custom'])->name(
    'words.custom'
  );
  Route::get('/words/c/list', [WordController::class, 'list'])->name(
    'words.list'
  );
  Route::get('/words/w/{id}', [WordController::class, 'show'])
    ->where([
      'id' => '[0-9]+', // Only numbers
    ])
    ->name('words.show');

  Route::get('/', function () {
    if (Auth::check()) {
      return Redirect::route('dashboard');
    }
    return Inertia::render('Welcome', [
      'canLogin' => Route::has('login'),
      'canRegister' => true,
      'video' => Video::where('published_at', '<=', now())
        ->where('youtube_id', '!=', null)
        ->orderBy('published_at', 'desc')
        ->first(),
    ]);
  })->name('home');

  Route::get('/style', function () {
    return Inertia::render('Style');
  })->name('style');
  Route::get('/practice', [PracticeController::class, 'index'])->name(
    'practice.index'
  );
  Route::get('/practice/grammar', [
    GrammarPracticeController::class,
    'index',
  ])->name('practice.grammar');
  Route::get('/practice/vocabulary', [
    VocabPracticeController::class,
    'index',
  ])->name('practice.vocabulary');
  Route::get('/practice/grammar/a/{id}', [
    GrammarPracticeAttemptController::class,
    'index',
  ])
    ->where([
      'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('practice.grammar.attempt');
  Route::get('practice/vocabulary/attempt', [
    VocabPracticeAttemptController::class,
    'index',
  ])->name('practice.vocabulary.attempt');
  Route::get('infinitas', [InfiniteVocabController::class, 'index'])->name(
    'infinitas'
  );
  // Route::get('infinitas/how-it-works', [
  //   InfiniteVocabController::class,
  //   'howItWorks',
  // ])->name('infinitas.how-it-works');
  Route::post('/api/infinitas/add-attempt', [
    InfiniteVocabController::class,
    'store',
  ]);
  Route::post('/api/infinitas/set-source', [
    InfiniteVocabController::class,
    'setSource',
  ]);

  // Subscriptions
  Route::get('/subscribe', [SubscriptionController::class, 'index'])->name(
    'subscribe'
  );
  Route::get('/subscribe/create', [SubscriptionController::class, 'store']);
  Route::get('/subscribe/edit', [SubscriptionController::class, 'edit'])->name(
    'edit-subscription'
  );
  Route::get('/classes/{class}/subscribe', [
    SubscriptionController::class,
    'storeClassroom',
  ])
    ->where([
      'class' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('subscribe.class');
  Route::get('/classes/{class}/subscribe/success', [
    SubscriptionController::class,
    'checkoutSuccess',
  ])
    ->where([
      'class' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('checkout-success');
  Route::get('/classes/{class}/subscribe/cancel', [
    SubscriptionController::class,
    'checkoutCancel',
  ])
    ->where([
      'class' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('checkout-cancel');
  // Route for redirecting to Google
  Route::get('auth/google', [
    AuthOAuthController::class,
    'redirectToGoogle',
  ])->name('auth.google');

  // Google callback route
  Route::get('auth/google/callback', [
    AuthOAuthController::class,
    'handleGoogleCallback',
  ]);

  // Classes
  Route::post('api/join-classroom', [TeamController::class, 'joinTeam']);
});
Route::middleware(['auth:sanctum', 'verified'])->group(function () {
  Route::resource('/classes', TeamController::class)->names([
    'index' => 'classes.index',
    'create' => 'classes.create',
    'store' => 'classes.store',
    'show' => 'classes.show',
    'edit' => 'classes.edit',
    'update' => 'classes.update',
    'destroy' => 'classes.destroy',
  ]);
  Route::put('api/cycle-enrollment-code', [
    TeamController::class,
    'cycleEnrollmentCode',
  ]);
});

Route::middleware([
  'auth:sanctum',
  // config("jetstream.auth_session"),
  'verified',
  'subscribed',
  // "track-last-active",
])->group(function () {
  // Assignments
  Route::resource('/assignments', AssignmentController::class)->names([
    'index' => 'assignments.index',
    'create' => 'assignments.create',
    'store' => 'assignments.store',
    'show' => 'assignments.show',
    'edit' => 'assignments.edit',
    'update' => 'assignments.update',
    'destroy' => 'assignments.destroy',
  ]);

  // Dashboard
  Route::get('/dashboard', [DashboardController::class, 'index'])->name(
    'dashboard'
  );

  // Achievements
  Route::get('/achievements', [AchievementController::class, 'index'])->name(
    'achievements.index'
  );

  // Practice
  Route::get('practice/activities', [ActivityController::class, 'index'])->name(
    'practice.activities.index'
  );

  // Vocabulary
  Route::post('/practice/vocabulary/create', [
    VocabPracticeController::class,
    'create',
  ]);
  Route::get('practice/vocabulary/attempt/{id}', [
    VocabPracticeAttemptController::class,
    'show',
  ])
    ->where([
      'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('practice.vocabulary.attempt.id');
  Route::get('infinitas/attempt', [
    InfiniteVocabController::class,
    'attempt',
  ])->name('infinitas.attempt');

  // Grammar
  Route::post('/practice/grammar/create', [
    GrammarPracticeController::class,
    'create',
  ])->name('practice.grammar.create');
  Route::get('/practice/grammar/c/attempt', [
    GrammarPracticeController::class,
    'custom',
  ])->name('practice.grammar.custom');

  Route::get('practice/grammar/activity/{activity}', [
    GrammarPracticeController::class,
    'show',
  ])
    ->where([
      'activity' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('practice.grammar.activity');
  Route::get('practice/grammar/attempt/{id}', [
    GrammarPracticeAttemptController::class,
    'show',
  ])
    ->where([
      'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('practice.grammar.attempt.id');

  // Read
  Route::get('/read/{author}/{work}/collections', [
    CollectionController::class,
    'index',
  ])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
    ])
    ->name('read.collection.index');
  Route::get('/read/{author}/{work}/collections/{collection}', [
    CollectionController::class,
    'show',
  ])
    ->where([
      'author' => '[a-zA-Z\-]+', // Letters and hyphens
      'work' => '[a-zA-Z\-]+', // Letters and hyphens
      'collections' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('read.collection.show');

  // Teams
  Route::get('/classes/{team}/assignments/{assignment}', [
    AssignmentController::class,
    'show',
  ])->where([
    'team' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    'assignment' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);
  Route::get('/classes/{team}/students/{student}', [
    TeamController::class,
    'student',
  ])
    ->where([
      'team' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
      'student' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
    ])
    ->name('classes.student');
  Route::post('api/remove-student', [
    TeamController::class,
    'removeStudent',
  ])->name('remove-student');
  Route::post('api/leave-class', [TeamController::class, 'leaveTeam'])->name(
    'leave-class'
  );
  Route::put('api/archive-classroom', [TeamController::class, 'archive']);
  Route::post('api/classes/sort', [TeamController::class, 'sort']);

  // Words
  Route::post('/words/{word}/learn', [
    LearnedWordController::class,
    'store',
  ])->where([
    'word' => '[0-9]+', // Numbers only
  ]);
  Route::post('/words/{word}/star', [
    StarredWordController::class,
    'store',
  ])->where([
    'word' => '[0-9]+', // Numbers only
  ]);

  // Watch
  Route::post('api/toggle-video-favorite', [
    VideoFavoriteController::class,
    'toggle',
  ])->name('video-favorite.toggle');

  // APIs
  Route::post('/api/add-word-user', [ReadController::class, 'addUserWord']);
  Route::post('/api/practice/vocabulary/get-books', [
    VocabPracticeController::class,
    'getBooks',
  ]);
  Route::post('/api/practice/vocabulary/update-session-options', [
    VocabPracticeAttemptController::class,
    'update',
  ])->name('practice.vocabulary.attempt.update');
  Route::post('/api/practice/vocabulary/add-attempt', [
    VocabPracticeAttemptController::class,
    'store',
  ]);
  Route::post('/api/practice/vocabulary/add-known-word', [
    VocabPracticeAttemptController::class,
    'addKnownWord',
  ]);
  Route::post('/api/practice/vocabulary/finish', [
    VocabPracticeAttemptController::class,
    'finish',
  ]);
  Route::post('/api/practice/grammar/update-session-options', [
    GrammarPracticeAttemptController::class,
    'update',
  ])->name('practice.grammar.attempt.update');
  Route::post('/api/practice/grammar/add-attempt', [
    GrammarPracticeAttemptController::class,
    'store',
  ]);
  Route::post('/api/practice/grammar/finish', [
    GrammarPracticeAttemptController::class,
    'finish',
  ]);
  Route::get('/api/practice/vocabulary/check-attempts/{id}', [
    VocabPracticeAttemptController::class,
    'checkAttempts',
  ])->where([
    'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);
  Route::get('/api/practice/grammar/check-attempts/{id}', [
    GrammarPracticeAttemptController::class,
    'checkAttempts',
  ])->where([
    'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);
  Route::post('/api/mark-all-as-learned', [
    LearnedWordController::class,
    'markAllAsLearned',
  ]);
  Route::post('/api/submit-description', [SectionController::class, 'store']);
  Route::delete('/api/delete-description/{id}', [
    SectionController::class,
    'destroy',
  ])->where([
    'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);
  Route::post('/api/submit-sectionNote', [
    SectionNoteController::class,
    'store',
  ]);
  Route::delete('/api/delete-sectionNote/{id}', [
    SectionNoteController::class,
    'destroy',
  ])->where([
    'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);
  Route::post('/api/submit-note', [NoteController::class, 'store']);
  Route::delete('/api/delete-note/{id}', [
    NoteController::class,
    'destroy',
  ])->where([
    'id' => '[a-zA-Z0-9\-]+', // Letters, numbers, and hyphens
  ]);

  Route::post('/api/toggle-work-pinned', [
    WorkController::class,
    'togglePinned',
  ]);

  Route::post('api/mark-assignment-completed', [
    AssignmentController::class,
    'markCompleted',
  ]);
  // Search
  Route::post('/search', [SearchController::class, 'search'])->name('search');

  // Feedback
  Route::post('/submit-feedback', [FeedbackController::class, 'store'])->name(
    'feedback'
  );
  Route::post('/track-video', [VideoTrackingController::class, 'track']);

  Route::post('/api/case-colors/color/{id}', [
    CaseColorController::class,
    'update',
  ]);
  Route::post('/api/case-colors/color/{id}/reset', [
    CaseColorController::class,
    'reset',
  ]);
  Route::post('/api/case-colors/reset', [
    CaseColorController::class,
    'destroy',
  ]);
});

Route::middleware('dev')->group(function () {
  // Declension Practice Routes
  Route::get('/practice/grammar/declension', [
    DeclensionPracticeController::class,
    'index',
  ])->name('practice.grammar.declension');

  Route::post('/api/practice/grammar/declension/create-session', [
    DeclensionPracticeController::class,
    'createSession',
  ]);

  Route::post('/api/practice/grammar/declension/add-attempt', [
    DeclensionPracticeController::class,
    'addAttempt',
  ]);
  Route::post('/api/practice/grammar/declension/log-field', [
    DeclensionPracticeController::class,
    'logField',
  ]);

  Route::post('/api/practice/grammar/declension/update-session-options', [
    DeclensionPracticeController::class,
    'updateSessionOptions',
  ]);

  Route::post('/api/practice/grammar/declension/finish', [
    DeclensionPracticeController::class,
    'finish',
  ]);

  Route::get('/practice/grammar/declension/summary/{id}', [
    DeclensionPracticeController::class,
    'summary',
  ])->name('practice.grammar.declension.summary');

  // Route::get('/test', [TranslationController::class, 'translateWord']);
  // Route::get('/decline/{id}', [DeclensionController::class, 'decline']);
  // Route::get('/decline', function () {
  //   $service = new App\Services\LatinDeclensionService();

  //   $word = App\Models\Word::where('core', 1)->where('pos', 'noun')->first();

  //   $form = $service->decline($word)->get(); // returns both singular and plural ablative forms

  //   dd($form);
  // })->name('decline');
  Route::get('/classroom-dashboard', [
    TeamController::class,
    'dashboard',
  ])->name('classroom-dashboard');
  Route::post('/classroom-dashboard/refresh-quote', [
    TeamController::class,
    'refreshQuote',
  ])->name('dashboard.refreshQuote');
  Route::post('/classroom-dashboard/refresh-grammar', [
    TeamController::class,
    'refreshGrammar',
  ])->name('dashboard.refreshGrammar');

  Route::get(
    '/read/{author}/{work}/{book}/{chapter}/{line_start}-{line_end}/syntax',
    [ReadController::class, 'syntaxChapter']
  )->name('syntax-chapter');
  Route::get('/read/{author}/{work}/{book}/{line_start}-{line_end}/syntax', [
    ReadController::class,
    'syntax',
  ])->name('syntax');

  Route::post('api/edit-syntax', [ReadController::class, 'editSyntax']);
  Route::post('api/verify-section-syntax', [
    ReadController::class,
    'verifySectionSyntax',
  ]);
  Route::post('api/update-lexicon-id', [
    WordController::class,
    'updateLexicon',
  ]);

  Route::get('/dev', [DevController::class, 'index'])->name('dev');
  // Add Collections
  Route::get('/dev/collections', [
    CollectionController::class,
    'developerIndex',
  ])->name('dev.collections.index');
  Route::post('/dev/collections', [CollectionController::class, 'store'])->name(
    'dev.collection.store'
  );
  Route::post('/dev/collections/{collection}/tag', [
    CollectionController::class,
    'tagVerses',
  ])->name('dev.collection.tag');
  Route::get('/dev/add-text', [WorkController::class, 'addWorkIndex'])->name(
    'dev.text'
  );
  Route::get('/dev/update-morphology', [
    WorkController::class,
    'updateMorphology',
  ]);

  Route::post('api/tokenize', [WorkController::class, 'tokenize']);
  Route::post('api/get-progress-tokenize', [
    WorkController::class,
    'getProgressTokenize',
  ]);
  Route::post('api/create-books', [WorkController::class, 'createBooks']);
  Route::post('api/create-work', [WorkController::class, 'createWork']);
  Route::post('api/create-author', [WorkController::class, 'createAuthor']);
  Route::post('api/create-category', [WorkController::class, 'createCategory']);
  Route::post('api/create-sections', [WorkController::class, 'createSections']);
  Route::post('api/get-progress-section', [
    WorkController::class,
    'getProgressSection',
  ]);
  Route::post('api/create-sections-by-hand', [
    WorkController::class,
    'createSectionsByHand',
  ]);
  Route::post('api/add-sections-by-hand', [
    WorkController::class,
    'addSectionsByHand',
  ]);
  Route::post('api/update-verse-indent', [
    WorkController::class,
    'updateVerseIndent',
  ]);
  Route::post('api/update-verse-stanza', [
    WorkController::class,
    'updateVerseStanza',
  ]);
  Route::post('api/submit-collatinus', [
    WorkController::class,
    'addCollatinus',
  ]);
  Route::post('api/download-book-text', [
    WorkController::class,
    'downloadBookText',
  ]);
  Route::post('api/submit-work', [WorkController::class, 'uploadWork']);

  Route::get('get-blank-words', [WordController::class, 'getBlankWords'])->name(
    'get-blank-words'
  );
  Route::post('fill-blank-words', [WordController::class, 'fillBlankWords']);
  Route::post('api/translation', [
    TranslationController::class,
    'checkTranslation',
  ]);
});

// For Billing through Stripe
Route::post(
  'stripe/webhook',
  '\Laravel\Cashier\Http\Controllers\WebhookController@handleWebhook'
)->name('cashier.webhook');

// Route::get('chat', function () {
//   $response = OpenAI::chat()->create([
//     'model' => 'gpt-4o-mini',
//     'messages' => [
//       [
//         'role' => 'user',
//         'content' =>
//           'What is the quality of this translation for "Laocoon ardens summa decurrit ab arce, et procul: “O miseri, quae tanta insania, cives? Creditis avectos hostes? Aut ulla putatis"? Verified syntax for this section is: Laocoon: nominative singular; Creditis: present indicative active 2nd plural; ardens: masculine nominative singular; avectos: participle perfect passive masculine accusative plural; summa: feminine ablative singular; hostes: accusative plural; decurrit: present indicative active 3rd singular; arce: ablative singular; miseri: masculine vocative plural; ulla: neuter accusative plural; putatis: present indicative active 2nd plural; quae: feminine nominative singular; tanta: feminine nominative singular; insania: nominative singular; cives: vocative plural. The translation is "Glowing Laocoon runs down to the highest citadel. And says: \"Oh miserable citizens, whom of such a great frenzy? Do yall believe they have been taken away the enemies? Or do you believe they are without any gift of the Greeks by dowry?\"". Please provide both a score and a justification. Use this rubric when giving a score: 5: Your translation shows a strong understanding of the Latin text. A few mistakes, typical in learning Latin, are perfectly fine. 4: While your translation has errors that show that you don\'t have quite as strong an understanding of the Latin text, the translation is still done at a good level. 3: Your translation doesn\'t shows some important concepts and has some fundamental flaws in understanding of the Latin text. 2: Your translation is either not complete or shows significant and important flaws in understanding. 1: Your translation is very poor and does not reflect an understanding or reading of the Latin text.',
//       ],
//     ],
//     'tools' => [
//       [
//         'type' => 'function',
//         'function' => [
//           'name' => 'get_translation_quality',
//           'description' =>
//             'Review this translation. Provide feedback as a Latin teacher. Be nice and supportive. Do not give the correct translation directly but point out specific issues in the translation. Identify individual grammar, vocabulary, or syntax errors and explain them clearly so the student understands why they are incorrect. Be detailed and precise in your response, as if helping the student learn to correct their own mistakes. Identify specific issues in the translation. For example, if a verb has the wrong tense, explain which word is incorrect and why. For example: The verb cano is 1st person singular, meaning \'I sing.\' The subject of the verb is implicit in the verb itself. The word virumque (\'and the man\') is accusative, so it cannot be the subject. Avoid general comments like \'check the grammar\' or \'improve clarity.\' Be specific about the issue and reference the Latin word or phrase causing the problem. Write as if you are guiding a student through their mistakes in a supportive and educational way.',
//           'parameters' => [
//             'type' => 'object',
//             'properties' => [
//               'score' => [
//                 'type' => 'integer',
//                 'description' =>
//                   'The score of the translation on a scale from 1 to 5 as an integer',
//               ],
//               'improvements' => [
//                 'type' => 'array',
//                 'items' => [
//                   'type' => 'string',
//                   'maxLength' => 50,
//                 ],
//                 'description' =>
//                   'Provide specific, actionable tips to improve the translation. Identify grammatical issues with explicit references to individual words or phrases, explaining why they are incorrect and what should be improved, without giving the corrected translation. Focus on helping the student understand the grammar, syntax, or vocabulary errors they made. Avoid vague suggestions like "improve clarity" or general advice about style. Be precise, as if correcting a student\'s mistake in a classroom. Don\'t provide more than 5 improvements, but don\'t worry if there aren\'t 5 improvements to give, you can give fewer than 5, and don\'t be wordy at all. Don\'t worry about issues of tone or formality of language (so yall is fine for you plural), just focus on the content. Focus most importantly on subject verb agreemnent, verb tense, noun cases, and word choice.',
//               ],
//             ],
//             'required' => ['score'],
//           ],
//         ],
//       ],
//     ],
//   ]);

//   // Iterate through the choices
//   foreach ($response->choices as $choice) {
//     // Access toolCalls
//     foreach ($choice->message->toolCalls as $toolCall) {
//       $function = $toolCall->function;

//       // Decode the arguments JSON
//       $arguments = json_decode($function->arguments, true);

//       // Access and display the score and improvements
//       if (isset($arguments['score'])) {
//         echo 'Score: ' . $arguments['score'] . "\n";
//       }
//       if (
//         isset($arguments['improvements']) &&
//         is_array($arguments['improvements'])
//       ) {
//         echo "Improvements:\n";
//         foreach ($arguments['improvements'] as $improvement) {
//           echo "- $improvement\n";
//         }
//       }
//     }
//   }
// })->name('chat');
