<?php

return [
  'declensions' => [
    'first' => [
      'base' => [
        'singular' => [
          'nominative' => null,
          'genitive' => 'ae',
          'dative' => 'ae',
          'accusative' => 'am',
          'ablative' => 'ā',
          'vocative' => 'a',
          'locative' => 'ae',
        ],
        'plural' => [
          'nominative' => 'ae',
          'genitive' => 'ārum',
          'dative' => 'īs',
          'accusative' => 'ās',
          'ablative' => 'īs',
          'vocative' => 'ae',
          'locative' => 'īs',
        ],
      ],

      'subtypes' => [
        'Greek' => [
          'singular' => [
            'nominative' => null,
            'genitive' => 'ēs',
            'accusative' => 'ēn',
            'ablative' => 'ē',
            'vocative' => 'ē',
          ],
        ],
        'Greek.Ma' => [
          'singular' => [
            'nominative' => null,
            'accusative' => ['ān', 'am'],
            'ablative' => 'ā',
          ],
        ],
        'Greek.Me' => [
          'singular' => [
            'nominative' => null,
            'accusative' => ['ēn', 'em'],
            'ablative' => ['ē', 'ā'],
            'vocative' => 'ē',
          ],
        ],
        'am' => [
          'singular' => [
            'nominative' => null,
            'accusative' => 'ām',
            'vocative' => 'ām',
          ],
        ],
        'abus' => [
          'plural' => [
            'dative' => 'ābus',
            'ablative' => 'ābus',
          ],
        ],
        'loc' => [
          // if explicitly tagged
          'singular' => [
            'locative' => 'ae',
          ],
          'plural' => [
            'locative' => 'īs',
          ],
        ],
      ],
    ],
    'second' => [
      'base' => [
        'singular' => [
          'nominative' => null,
          'genitive' => 'ī',
          'dative' => 'ō',
          'accusative' => 'um',
          'ablative' => 'ō',
          'vocative' => 'e',
        ],
        'plural' => [
          'nominative' => 'ī',
          'genitive' => 'ōrum',
          'dative' => 'īs',
          'accusative' => 'ōs',
          'ablative' => 'īs',
          'vocative' => 'ī',
        ],
      ],

      'subtypes' => [
        // Neuter overrides nominative, accusative, and vocative
        'N' => [
          'singular' => [
            'nominative' => null,
            'accusative' => null,
            'vocative' => null,
          ],
          'plural' => [
            'nominative' => 'a',
            'accusative' => 'a',
            'vocative' => 'a',
          ],
        ],

        // R-stem omits changes in vocative and nominative
        'r-stem' => [
          'singular' => [
            'nominative' => null,
            'vocative' => null,
          ],
        ],

        // ius nouns with alternate genitive and vocative
        'ius' => [
          'singular' => [
            'genitive' => [
              ['ending' => 'ī', 'trim' => 1],
              ['ending' => 'iī', 'trim' => 1],
            ],
            'vocative' => 'e',
          ],
        ],
        'voci' => [
          'singular' => [
            'vocative' => [['ending' => 'ī', 'trim' => 1]],
          ],
        ],

        // ium neuter nouns (like imperium)
        'ium' => [
          'singular' => [
            'nominative' => null,
            'genitive' => [
              ['ending' => 'ī', 'trim' => 1],
              ['ending' => 'iī', 'trim' => 1],
            ],
            'vocative' => null,
          ],
        ],
        '-ius' => [
          'singular' => [
            'genitive' => 'ī',
            'vocative' => 'e',
          ],
        ],
        '-ium' => [
          'singular' => [
            'genitive' => 'ī',
          ],
        ],

        // Greek masculine nouns
        'Greek' => [
          'singular' => [
            'nominative' => null,
            'accusative' => 'on',
          ],
        ],

        // Greek neuter nouns
        'Greek.N' => [
          'singular' => [
            'nominative' => null,
            'accusative' => 'on',
            'vocative' => 'on',
          ],
          'plural' => [
            'nominative' => 'a',
            'accusative' => 'a',
            'vocative' => 'a',
          ],
        ],

        // Vos variant
        'vos' => [
          'singular' => [
            'nominative' => null,
          ],
        ],

        // Vom variant
        'vom' => [
          'singular' => [
            'nominative' => null,
            'accusative' => 'vom',
            'vocative' => 'vom',
          ],
        ],
        'loc' => [
          'singular' => [
            'vocative' => null,
            'genitive' => 'ī',
            'locative' => 'ī',
          ],
          'plural' => [
            'locative' => 'īs',
          ],
        ],
        // Plural-only second declension nouns
        'pl' => [
          'singular' => [],
        ],
      ],
    ],
    'third' => [
      'base' => [
        'singular' => [
          'nominative' => null,
          'genitive' => 'is',
          'dative' => 'ī',
          'accusative' => 'em',
          'ablative' => 'e',
          'vocative' => null,
        ],
        'plural' => [
          'nominative' => 'ēs',
          'genitive' => 'um',
          'dative' => 'ibus',
          'accusative' => 'ēs',
          'ablative' => 'ibus',
          'vocative' => 'ēs',
        ],
      ],
      'subtypes' => [
        'N' => [
          'singular' => [
            'nominative' => null,
            'accusative' => null,
            'vocative' => null,
          ],
          'plural' => [
            'nominative' => 'a',
            'accusative' => 'a',
            'vocative' => 'a',
          ],
        ],
        'M' => [
          'singular' => [
            'accusative' => 'em',
          ],
          'plural' => [
            'nominative' => 'ēs',
            'accusative' => 'ēs',
            'vocative' => 'ēs',
          ],
        ],

        'I' => [
          'plural' => [
            'genitive' => 'ium',
          ],
        ],
        '-I' => [
          'plural' => [
            'genitive' => 'um',
          ],
        ],
        'pure' => [
          'singular' => [
            'ablative' => 'ī',
          ],
          'plural' => [
            'nominative' => 'ia',
            'accusative' => 'ia',
            'vocative' => 'ia',
          ],
        ],
        'navis' => [
          'singular' => [
            'accusative' => ['em', 'im'],
          ],
          'plural' => [
            'accusative' => 'īs',
          ],
        ],
        'ignis' => [
          'singular' => [
            'accusative' => ['em', 'im'],
            'ablative' => ['e', 'ī'],
          ],
          'plural' => [
            'accusative' => 'īs',
          ],
        ],
        'Greek' => [
          'singular' => [
            'genitive' => ['os', 'is'],
            'accusative' => ['a', 'em'],
            'vocative' => '',
          ],
          'plural' => [
            'nominative' => 'es',
            'accusative' => 'as',
            'vocative' => 'es',
          ],
        ],
        'acc-im' => [
          'singular' => [
            'accusative' => 'im',
          ],
        ],
        'acc-im-in' => [
          'singular' => [
            'accusative' => ['im', 'in'],
          ],
        ],
        'acc-im-in-em' => [
          'singular' => [
            'accusative' => ['im', 'in', 'em'],
          ],
        ],
        'acc-im-em' => [
          'singular' => [
            'accusative' => ['im', 'em'],
          ],
        ],
        'acc-im-occ-em' => [
          'singular' => [
            'accusative' => ['im', 'em'],
          ],
        ],
        'acc-em-im' => [
          'singular' => [
            'accusative' => ['em', 'im'],
          ],
        ],
        'abl-i' => [
          'singular' => [
            'ablative' => 'ī',
          ],
        ],
        'abl-i-e' => [
          'singular' => [
            'ablative' => ['ī', 'e'],
          ],
        ],
        'abl-e-i' => [
          'singular' => [
            'ablative' => ['e', 'ī'],
          ],
        ],
        'abl-e-occ-i' => [
          'singular' => [
            'ablative' => ['e', 'ī'],
          ],
        ],
        'loc' => [
          'singular' => [
            'locative' => 'ī',
          ],
          'plural' => [
            'locative' => 'ibus',
          ],
        ],
      ],
    ],
    'fourth' => [
      'base' => [
        'singular' => [
          'nominative' => null,
          'genitive' => 'ūs',
          'dative' => 'uī',
          'accusative' => 'um',
          'ablative' => 'ū',
          'vocative' => null,
        ],
        'plural' => [
          'nominative' => 'ūs',
          'genitive' => 'uum',
          'dative' => 'ibus',
          'accusative' => 'ūs',
          'ablative' => 'ibus',
          'vocative' => 'ūs',
        ],
      ],
      'subtypes' => [
        'N' => [
          'singular' => [
            'nominative' => null,
            'genitive' => 'ūs',
            'dative' => null,
            'accusative' => null,
            'ablative' => null,
            'vocative' => null,
          ],
          'plural' => [
            'nominative' => 'ua',
            'accusative' => 'ua',
            'vocative' => 'ua',
          ],
        ],
        'ubus' => [
          'plural' => [
            'dative' => 'ubus',
            'ablative' => 'ubus',
          ],
        ],
        'o' => [
          'singular' => [
            'nominative' => null,
            'genitive' => 'ūs',
            'dative' => null,
            'accusative' => null,
            'ablative' => null,
            'vocative' => null,
          ],
        ],
      ],
    ],
    'fifth' => [
      'base' => [
        'singular' => [
          'nominative' => null,
          'genitive' => 'eī',
          'dative' => 'eī',
          'accusative' => 'em',
          'ablative' => 'ē',
          'vocative' => null,
        ],
        'plural' => [
          'nominative' => 'ēs',
          'genitive' => 'ērum',
          'dative' => 'ēbus',
          'accusative' => 'ēs',
          'ablative' => 'ēbus',
          'vocative' => 'ēs',
        ],
      ],
      'subtypes' => [
        'ies' => [
          'singular' => [
            'genitive' => 'ēī',
            'dative' => 'ēī',
          ],
        ],
        'loc' => [
          'singular' => [
            'locative' => 'ē',
          ],
          'plural' => [
            'locative' => 'ēbus',
          ],
        ],
      ],
    ],
  ],

  // Add special patterns for irregular nouns
  'irregular' => [
    'bos' => [
      'singular' => [
        'nominative' => 'bos',
        'genitive' => 'bōvis',
        'dative' => 'bovī',
        'accusative' => 'bovem',
        'ablative' => 'bove',
        'vocative' => 'bos',
      ],
      'plural' => [
        'nominative' => 'bovēs',
        'genitive' => 'boum',
        'dative' => ['bōbis', 'būbus'],
        'accusative' => 'bovēs',
        'ablative' => ['bōbis', 'būbus'],
        'vocative' => 'bovēs',
      ],
    ],
    'senex' => [
      'singular' => [
        'nominative' => 'senex',
        'genitive' => 'senis',
        'dative' => 'senī',
        'accusative' => 'senem',
        'ablative' => 'sene',
        'vocative' => 'senex',
      ],
      'plural' => [
        'nominative' => 'senēs',
        'genitive' => 'senum',
        'dative' => 'senibus',
        'accusative' => 'senēs',
        'ablative' => 'senibus',
        'vocative' => 'senēs',
      ],
    ],
    'carō' => [
      'singular' => [
        'nominative' => 'carō',
        'genitive' => 'carnis',
        'dative' => 'carnī',
        'accusative' => 'carnem',
        'ablative' => 'carne',
        'vocative' => 'carō',
      ],
      'plural' => [
        'nominative' => 'carnēs',
        'genitive' => 'carnium',
        'dative' => 'carnibus',
        'accusative' => 'carnēs',
        'ablative' => 'carnibus',
        'vocative' => 'carnēs',
      ],
    ],
    'os' => [
      'singular' => [
        'nominative' => 'os',
        'genitive' => 'ossis',
        'dative' => 'ōssī',
        'accusative' => 'os',
        'ablative' => 'osse',
        'vocative' => 'os',
      ],
      'plural' => [
        'nominative' => 'ossa',
        'genitive' => 'ossium',
        'dative' => 'ossibus',
        'accusative' => 'ossa',
        'ablative' => 'ossibus',
        'vocative' => 'ossa',
      ],
    ],
    'vīs' => [
      'singular' => [
        'nominative' => 'vīs',
        'genitive' => 'vīs',
        'dative' => 'vī',
        'accusative' => 'vim',
        'ablative' => 'vī',
        'vocative' => 'vīs',
      ],
      'plural' => [
        'nominative' => 'vīrēs',
        'genitive' => 'vīrium',
        'dative' => 'vīribus',
        'accusative' => ['vīrīs', 'vīrēs'],
        'ablative' => 'vīribus',
        'vocative' => 'vīrēs',
      ],
    ],
    'sūs' => [
      'singular' => [
        'nominative' => 'sūs',
        'genitive' => 'suis',
        'dative' => 'suī',
        'accusative' => 'suem',
        'ablative' => 'sue',
        'vocative' => 'sūs',
      ],
      'plural' => [
        'nominative' => 'suēs',
        'genitive' => 'suum',
        'dative' => ['sūbus', 'subus', 'suibus'],
        'accusative' => 'suēs',
        'ablative' => ['sūbus', 'subus', 'suibus'],
        'vocative' => 'suēs',
      ],
    ],
    'deus' => [
      'singular' => [
        'nominative' => 'deus',
        'genitive' => 'deī',
        'dative' => 'deō',
        'accusative' => 'deum',
        'ablative' => 'deō',
        'vocative' => 'deus',
      ],
      'plural' => [
        'nominative' => ['dī', 'diī', 'deī'],
        'genitive' => ['deōrum', 'deum'],
        'dative' => ['dīs', 'diīs', 'deīs'],
        'accusative' => 'deōs',
        'ablative' => ['dīs', 'diīs', 'deīs'],
        'vocative' => ['dī', 'diī', 'deī'],
      ],
    ],
    'Deus' => [
      'singular' => [
        'nominative' => 'Deus',
        'genitive' => 'Deī',
        'dative' => 'Deō',
        'accusative' => 'Deum',
        'ablative' => 'Deō',
        'vocative' => ['Deus', 'Dee'],
      ],
    ],
    'domus' => [
      'singular' => [
        'nominative' => 'domus',
        'genitive' => 'domūs',
        'dative' => ['domuī', 'domō'],
        'accusative' => 'domum',
        'ablative' => ['domō', 'domū'],
        'vocative' => 'domus',
        'locative' => 'domī',
      ],
      'plural' => [
        'nominative' => 'domūs',
        'genitive' => ['domuum', 'domōrum'],
        'dative' => 'domibus',
        'accusative' => ['domōs', 'domūs'],
        'ablative' => 'domibus',
        'vocative' => 'domūs',
      ],
    ],
  ],
];
