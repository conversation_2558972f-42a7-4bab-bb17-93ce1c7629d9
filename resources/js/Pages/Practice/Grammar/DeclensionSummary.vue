<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import Breadcrumbs from '@/Shared/Breadcrumbs.vue';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/solid';
import { ref, computed } from 'vue';
import { Link } from '@inertiajs/vue3';
import ButtonItem from '@/Shared/ButtonItem.vue';

const props = defineProps({
  session: Object,
  attempts: Array,
});

const breadcrumbs = [
  { name: 'Practice', href: '/practice', current: false },
  { name: 'Grammar', href: '/practice/grammar', current: false },
  {
    name: 'Declension Practice',
    href: '/practice/grammar/declension',
    current: false,
  },
  { name: 'Summary', href: '#', current: true },
];

const accuracy = computed(() => {
  if (props.session.attempts === 0) return 0;
  return Math.round((props.session.correct / props.session.attempts) * 100);
});

const correctAttempts = computed(() => {
  return props.attempts.filter((attempt) => attempt.correct).length;
});

const incorrectAttempts = computed(() => {
  return props.attempts.filter((attempt) => !attempt.correct).length;
});

const averageTime = computed(() => {
  if (props.attempts.length === 0) return 0;
  const totalTime = props.attempts.reduce(
    (sum, attempt) => sum + attempt.time,
    0,
  );
  return Math.round(totalTime / props.attempts.length / 10); // Convert to seconds
});
</script>

<template>
  <AppLayout>
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <Breadcrumbs :pages="breadcrumbs" />

      <div class="mt-8">
        <h1 class="text-3xl font-bold text-gray-900">Practice Summary</h1>

        <!-- Stats Cards -->
        <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div
            class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
          >
            <dt class="truncate text-sm font-medium text-gray-500">
              Total Words
            </dt>
            <dd
              class="mt-1 text-3xl font-semibold tracking-tight text-gray-900"
            >
              {{ session.attempts }}
            </dd>
          </div>

          <div
            class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
          >
            <dt class="truncate text-sm font-medium text-gray-500">Correct</dt>
            <dd
              class="mt-1 text-3xl font-semibold tracking-tight text-green-600"
            >
              {{ session.correct }}
            </dd>
          </div>

          <div
            class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
          >
            <dt class="truncate text-sm font-medium text-gray-500">Accuracy</dt>
            <dd
              class="mt-1 text-3xl font-semibold tracking-tight text-blue-600"
            >
              {{ accuracy }}%
            </dd>
          </div>

          <div
            class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
          >
            <dt class="truncate text-sm font-medium text-gray-500">
              XP Earned
            </dt>
            <dd
              class="mt-1 text-3xl font-semibold tracking-tight text-indigo-600"
            >
              {{ session.xp_earned }}
            </dd>
          </div>
        </div>

        <!-- Attempt History -->
        <div class="mt-8 overflow-hidden rounded-lg bg-white shadow">
          <div class="px-4 py-5 sm:px-6">
            <h2 class="text-lg leading-6 font-medium text-gray-900">
              Attempt History
            </h2>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              Your most recent declension practice attempts.
            </p>
          </div>

          <div class="border-t border-gray-200">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    >
                      Word
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    >
                      Result
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    >
                      Time (seconds)
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    >
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-for="attempt in attempts" :key="attempt.id">
                    <td
                      class="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900"
                    >
                      {{ attempt.word }}
                    </td>
                    <td
                      class="px-6 py-4 text-sm whitespace-nowrap text-gray-500"
                    >
                      <span
                        v-if="attempt.correct"
                        class="flex items-center text-green-600"
                      >
                        <CheckCircleIcon class="mr-1 h-5 w-5" />
                        Correct
                      </span>
                      <span v-else class="flex items-center text-red-600">
                        <XCircleIcon class="mr-1 h-5 w-5" />
                        Incorrect
                      </span>
                    </td>
                    <td
                      class="px-6 py-4 text-sm whitespace-nowrap text-gray-500"
                    >
                      {{ Math.round(attempt.time / 10) }}
                    </td>
                    <td
                      class="px-6 py-4 text-sm whitespace-nowrap text-gray-500"
                    >
                      {{ new Date(attempt.created_at).toLocaleString() }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex justify-between">
          <Link :href="`/practice/grammar`">
            <ButtonItem color="gray" size="md">
              Back to Grammar Practice
            </ButtonItem>
          </Link>

          <Link :href="`/practice/grammar/declension`">
            <ButtonItem color="indigo" size="md"> Practice Again </ButtonItem>
          </Link>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
