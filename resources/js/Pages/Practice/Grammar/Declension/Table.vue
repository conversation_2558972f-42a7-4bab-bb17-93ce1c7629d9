<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  word: {
    type: Object,
    required: true,
  },
  cases: {
    type: Array,
    default: () => [
      'nominative',
      'genitive',
      'dative',
      'accusative',
      'ablative',
      'vocative',
    ],
  },
});

const displayCases = computed(() => {
  return props.cases.filter(
    (caseType) =>
      props.word.declensions.singular[caseType] ||
      props.word.declensions.plural[caseType],
  );
});
</script>

<template>
  <div class="declension-table">
    <div class="mb-4 text-center">
      <h2 class="text-xl font-bold">{{ word.lemma }}</h2>
      <p class="text-sm text-gray-600">
        {{ word.gender }}, {{ word.declension }} declension
        <span v-if="word.isGreek">(Greek)</span>
      </p>
    </div>

    <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
      <!-- Singular Column -->
      <div>
        <h3 class="mb-4 text-center text-lg font-semibold">Singular</h3>
        <table class="w-full border-collapse">
          <tbody>
            <tr v-for="caseType in displayCases" :key="`singular-${caseType}`">
              <td class="border border-gray-300 p-2 font-medium">
                {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
              </td>
              <td class="border border-gray-300 p-2 text-center">
                {{ word.declensions.singular[caseType] }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Plural Column -->
      <div>
        <h3 class="mb-4 text-center text-lg font-semibold">Plural</h3>
        <table class="w-full border-collapse">
          <tbody>
            <tr v-for="caseType in displayCases" :key="`plural-${caseType}`">
              <td class="border border-gray-300 p-2 font-medium">
                {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
              </td>
              <td class="border border-gray-300 p-2 text-center">
                {{ word.declensions.plural[caseType] }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
