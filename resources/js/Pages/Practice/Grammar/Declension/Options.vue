<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import ToggleItem from '@/Shared/ToggleItem.vue';
import ButtonItem from '@/Shared/ButtonItem.vue';
import Difficulty from '@/Pages/Practice/Grammar/Components/OptionsDifficulty.vue';
import Modes from '@/Pages/Practice/Grammar/Components/OptionsMode.vue';

const emit = defineEmits(['update:options']);

const declensionOptions = [
  { id: 1, name: '1st Declension', value: 'first' },
  { id: 2, name: '2nd Declension (Masculine)', value: 'second_masc' },
  { id: 3, name: '2nd Declension (Neuter)', value: 'second_neut' },
  { id: 4, name: '3rd Declension (Consonant)', value: 'third_cons' },
  { id: 5, name: '3rd Declension (i-stem)', value: 'third_i' },
  { id: 6, name: '3rd Declension (Neuter)', value: 'third_neut' },
  { id: 7, name: '4th Declension', value: 'fourth' },
  { id: 8, name: '5th Declension', value: 'fifth' },
];

const caseOptions = [
  { id: 1, name: 'Nominative', value: 'nominative' },
  { id: 2, name: 'Genitive', value: 'genitive' },
  { id: 3, name: 'Dative', value: 'dative' },
  { id: 4, name: 'Accusative', value: 'accusative' },
  { id: 5, name: 'Ablative', value: 'ablative' },
  { id: 6, name: 'Vocative', value: 'vocative' },
];

const difficultyOptions = [
  { id: 0, name: 'Easy', description: 'Basic forms only' },
  { id: 1, name: 'Medium', description: 'All common forms' },
  { id: 2, name: 'Hard', description: 'All forms including rare cases' },
];

const selectedDeclensions = ref([]);
const selectedCases = ref([
  'nominative',
  'genitive',
  'dative',
  'accusative',
  'ablative',
]);
const selectedDifficulty = ref(0);
const requireMacra = ref(false);
const zenMode = ref(false);

const isValid = computed(() => {
  return selectedDeclensions.value.length > 0 && selectedCases.value.length > 0;
});

const updateOptions = () => {
  if (isValid.value) {
    emit('update:options', {
      declensions: selectedDeclensions.value,
      cases: selectedCases.value,
      difficulty: selectedDifficulty.value,
      time: settings.time,
      requireMacra: requireMacra.value,
      zenMode: zenMode.value,
    });
  }
};

let settings = reactive({
  time: 0,
  difficulty: 0,
});

// Watch for changes and update options
watch([selectedDeclensions, selectedCases, selectedDifficulty], () => {
  updateOptions();
});

// Initialize options
onMounted(() => {
  updateOptions();
});
</script>

<template>
  <div class="flex-1 py-2">
    <h1 class="text-4xl font-bold text-gray-900">
      Declension Practice Options
    </h1>
    <p class="mt-1 text-sm text-gray-500">
      Select which declensions and cases you want to practice.
    </p>

    <div class="mt-8 gap-8 rounded-lg bg-gray-100 p-6">
      <!-- Declension Selection -->
      <div class="grid grid-cols-2 gap-8">
        <div>
          <h3 class="text-base font-medium text-gray-900">Declensions</h3>
          <div class="mt-4 ml-4 grid grid-cols-1 gap-2">
            <div
              v-for="option in declensionOptions"
              :key="option.id"
              class="flex items-center space-x-2"
            >
              <input
                type="checkbox"
                :id="`declension-${option.id}`"
                :value="option.value"
                v-model="selectedDeclensions"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600"
              />
              <label
                :for="`declension-${option.id}`"
                class="text-sm text-gray-700"
              >
                {{ option.name }}
              </label>
            </div>
          </div>
        </div>

        <!-- Case Selection -->
        <div>
          <h3 class="text-base font-medium text-gray-900">Cases</h3>
          <div class="mt-4 ml-4 grid grid-cols-1 gap-2">
            <div
              v-for="option in caseOptions"
              :key="option.id"
              class="flex items-center space-x-2"
            >
              <input
                type="checkbox"
                :id="`case-${option.id}`"
                :value="option.value"
                v-model="selectedCases"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600"
              />
              <label :for="`case-${option.id}`" class="text-sm text-gray-700">
                {{ option.name }}
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-12 rounded-lg bg-gray-100 p-6">
        <Modes
          :current-mode="settings.time"
          @update:mode="updateSettings('time', $event)"
        />
        <div
          class="mt-12 grid grid-cols-1 gap-12 border-t border-gray-300 pt-12 pb-12 sm:grid-cols-2 sm:pt-8 sm:pb-6"
        >
          <ToggleItem
            order="left"
            class="self-top"
            label="Require Long Marks"
            description="Require correct placement of macrons."
            button-color="amber"
            v-model="requireMacra"
          />
          <ToggleItem
            order="left"
            class="self-top"
            label="Zen Mode"
            description="Automatically move to the next word."
            button-color="amber"
            v-model="zenMode"
          />
        </div>
      </div>
    </div>
  </div>
</template>
