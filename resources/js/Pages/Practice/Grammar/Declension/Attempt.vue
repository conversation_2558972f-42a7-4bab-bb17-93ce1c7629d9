<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import Breadcrumbs from '@/Shared/Breadcrumbs.vue';
import { InformationCircleIcon } from '@heroicons/vue/24/solid';
import { ref, reactive, computed, watch, onMounted, Transition } from 'vue';
import { usePage, router, Head } from '@inertiajs/vue3';
import ProgressBar from '@/Shared/ProgressBar.vue';
import ButtonItem from '@/Shared/ButtonItem.vue';
import Timer from 'easytimer.js';
import { animate } from 'motion';
import axios from 'axios';
import DeclensionOptions from './Options.vue';
import Promotion from '@/Shared/Promotion.vue';
import AssignmentModule from '@/Shared/AssignmentModule.vue';
import { PlusIcon, PlayIcon, PauseIcon } from '@heroicons/vue/24/outline';

const props = defineProps({
  session: { type: Object, required: false },
  word: { type: Object, required: false },
  assignment: { type: Object, required: false },
  initialWords: { type: Array, required: false },
});

const breadcrumbs = [
  { name: 'Practice', href: '/practice', current: false },
  { name: 'Grammar', href: '/practice/grammar', current: false },
  { name: 'Declension Practice', href: '#', current: true },
];

let settings = reactive({
  declensions: [],
  cases: [],
  difficulty: 0,
  time: 0,
  requireMacra: false,
  zenMode: false,
});
const defaultWord = {
  id: 0,
  lemma: 'puella',
  gender: 'feminine',
  declension: '1st',
  declensions: {
    singular: {
      nominative: 'puella',
      genitive: 'puellae',
      dative: 'puellae',
      accusative: 'puellam',
      ablative: 'puellā',
      vocative: 'puella',
      locative: '',
    },
    plural: {
      nominative: 'puellae',
      genitive: 'puellārum',
      dative: 'puellīs',
      accusative: 'puellās',
      ablative: 'puellīs',
      vocative: 'puellae',
      locative: '',
    },
  },
};

let user = ref(usePage().props.authenticated ? usePage().props.user : null);
let xp = ref(usePage().props.authenticated ? user.value.xp : 0);
let xpProgress = usePage().props.authenticated ? ref(user.value.xp) : 0;
let level = usePage().props.authenticated ? ref(user.value.level) : ref(0);
let nextLevel = ref(
  usePage().props.authenticated ? user.value.level.next_level_max : null,
);
let isAnswer = ref(false);
let isLoading = ref(false);
let attempt = ref(false);
let stats = reactive({
  correct: 0,
  incorrect: 0,
});
let activeWord = ref();
let nextWord = ref();
let wordChanged = ref(true);
let isFinishing = ref(false);
let isCorrect = ref(false);
let accuracy = ref(0);
let nextLevelXp = usePage().props.authenticated
  ? ref(user.value.level.max - user.value.xp + 1)
  : ref(0);
let activeTime = ref(0);
let time = ref();
let summaryList = reactive([]);

let currentStreak = ref(0);
let timer = new Timer();
let runningTime = new Timer();
let userInputs = reactive({
  singular: {
    nominative: '',
    genitive: '',
    dative: '',
    accusative: '',
    ablative: '',
    vocative: '',
    locative: '',
  },
  plural: {
    nominative: '',
    genitive: '',
    dative: '',
    accusative: '',
    ablative: '',
    vocative: '',
    locative: '',
  },
});
let isCorrectInput = reactive({
  singular: {
    nominative: false,
    genitive: false,
    dative: false,
    accusative: false,
    ablative: false,
    vocative: false,
    locative: false,
  },
  plural: {
    nominative: false,
    genitive: false,
    dative: false,
    accusative: false,
    ablative: false,
    vocative: false,
    locative: false,
  },
});
let xp_earned = ref(0);
let timerIsPaused = ref(false);
let nextItemButtonDisabled = ref(true);
let showInfo = ref(false);

// Initialize with props if available
onMounted(() => {
  if (props.initialWord) {
    activeWord.value = props.initialWord;
  }
  if (props.session) {
    attempt.value = true;
    stats.correct = props.session.correct;
    stats.incorrect = props.session.attempts - props.session.correct;
    currentStreak.value = props.session.streak;
    xp_earned.value = props.session.xp_earned;
    accuracy.value = props.session.correct
      ? Math.round((props.session.correct / props.session.attempts) * 100)
      : 0;

    // Start the timer
    runningTime.start();
  }
  if (props.assignment) {
    assignmentInProgress.value = props.assignment.completed == 0;
  }
});

// Watch for new words
watch(
  () => props.word,
  (newWord) => {
    if (newWord) {
      nextWord.value = newWord;
      wordChanged.value = true;

      if (!activeWord.value) {
        activeWord.value = newWord;
      }
    }
  },
  { immediate: true },
);

const getProgress = computed(() => {
  if (!level.value) return 0;
  return (
    (xpProgress.value - level.value.min) / (level.value.max - level.value.min)
  );
});

const accuracyMeter = computed(() => {
  if (stats.correct + stats.incorrect == 0) {
    return -1;
  }
  if (stats.correct + stats.incorrect < 10) {
    return stats.correct / 10;
  }
  return Math.round((100 * stats.correct) / (stats.correct + stats.incorrect));
});

const handleContinueClick = () => {
  if (usePage().props.authenticated) {
    isLoading.value = true;
    setOptions();
  } else {
    router.get(`/login?redirect=/practice/grammar/declension`);
  }
};

const userStats = computed(() => {
  return [
    {
      name: 'Correct',
      value: stats.correct,
      unit: 'out of ' + (stats.correct + stats.incorrect),
    },
    {
      name: 'Current Streak',
      value: currentStreak.value,
      unit: 'in a row',
    },
    {
      name: 'XP Earned',
      value: xp_earned.value,
    },
    {
      name: 'Accuracy',
      value: accuracy.value,
      unit: '%',
    },
  ];
});

const setOptions = (reattempt = false) => {
  router.reload({
    only: ['session'],
    data: {
      reattempt: Boolean(reattempt),
      declensions: settings.declensions,
      cases: settings.cases,
      difficulty: settings.difficulty,
    },
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      stats.correct = props.session.correct;
      stats.incorrect = props.session.attempts - props.session.correct;
      currentStreak.value = props.session.streak;
      xp_earned.value = props.session.xp_earned;
      accuracy.value = props.session.correct
        ? Math.round((props.session.correct / props.session.attempts) * 100)
        : 0;

      axios
        .post('/api/practice/grammar/declension/update-session-options', {
          session: props.session.id,
          settings: settings,
        })
        .then(() => {
          setTimeout(
            () => {
              runningTime.start({
                countdown: false,
                startValues: { seconds: 0 },
                precision: 'secondTenths',
              });

              // Determine if mode is timed or untimed
              if (settings.time > 0) {
                timer.start({
                  countdown: true,
                  startValues: { seconds: settings.time },
                });
                time.value = getDisplayTime(timer.getTimeValues());
                timer.addEventListener('secondsUpdated', function () {
                  time.value = getDisplayTime(timer.getTimeValues());
                });
                timer.addEventListener('targetAchieved', function () {
                  finishAttempt();
                });
              } else {
                time.value = getDisplayTime(runningTime.getTimeValues());
                runningTime.addEventListener('secondsUpdated', function () {
                  time.value = getDisplayTime(runningTime.getTimeValues());
                });
              }
              router.reload({
                only: ['initialWords'],
                preserveState: true,
                preserveScroll: true,
                replace: true,
                onSuccess: () => {
                  activeWord.value = props.initialWords[0];
                  nextWord.value = props.initialWords[1];
                  isLoading.value = false;
                  attempt.value = true;
                },
              });
            },
            randomInteger(200, 500),
          );
          // 🔽 Insert this to initialize with default word if none is active
          if (!activeWord.value) {
            activeWord.value = defaultWord;
          }
        });
    },
  });
};

const randomInteger = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const checkAnswer = (number, caseType, event) => {
  if (
    userInputs[number][caseType] ===
    activeWord.value.declensions[number][caseType]
  ) {
    isCorrectInput[number][caseType] = true;
    logFieldAttempt(number, caseType, userInputs[number][caseType]);

    // Find the next input to focus
    const allInputs = [
      ...document.querySelectorAll('.singular-input'),
      ...document.querySelectorAll('.plural-input'),
    ];

    const currentIndex = allInputs.indexOf(event.target);
    if (currentIndex !== -1 && currentIndex + 1 < allInputs.length) {
      allInputs[currentIndex + 1].focus();
    }

    // Check if all inputs are correct
    let allCorrect = true;
    for (const num of ['singular', 'plural']) {
      for (const cs of settings.cases) {
        if (!isCorrectInput[num][cs]) {
          allCorrect = false;
          break;
        }
      }
      if (!allCorrect) break;
    }

    if (allCorrect) {
      submitCompletedWord();
    }
  }
};

const logFieldAttempt = async (number, caseType, input) => {
  try {
    await axios.post('/api/practice/grammar/declension/log-field', {
      session: props.session.id,
      word_id: activeWord.value.id,
      number: number, // 'singular' or 'plural'
      case: caseType,
      input: input,
      correct: true,
      used_hint: usedHint.value,
      time: runningTime.getTimeValues().secondTenths,
    });
  } catch (error) {
    console.error('Failed to log field:', error);
  }
  usedHint.value = false;
};

const submitCompletedWord = () => {
  isCorrect.value = true;

  // Update stats
  if (currentStreak.value < 1) {
    animate(
      (progress) => {
        currentStreak.value = Math.round(progress);
      },
      { duration: 0.3, easing: 'ease-out' },
    );
  } else {
    let streak_start = currentStreak.value;
    animate(
      (progress) => {
        currentStreak.value = Math.round(progress + streak_start);
      },
      { duration: 0.3, easing: 'ease-out' },
    );
  }

  let correct_start = stats.correct;
  animate(
    (progress) => {
      stats.correct = Math.round(progress + correct_start);
    },
    { duration: 0.3, easing: 'ease-out' },
  );

  let xp_start = xp.value;
  animate(
    (progress) => {
      xp.value = Math.round(xp_start + progress);
    },
    { duration: 0.3, easing: 'ease-out' },
  );

  let xp_earned_start = xp_earned.value;
  animate(
    (progress) => {
      xp_earned.value = Math.round(xp_earned_start + progress * xp_increment());
    },
    { duration: 0.3, easing: 'ease-out' },
  );

  // Update accuracy
  let accuracy_start = accuracy.value;
  animate(
    (progress) => {
      accuracy.value =
        accuracy_start +
        Math.round(
          (Math.round(
            (stats.correct / (stats.correct + stats.incorrect)) * 100,
          ) -
            accuracy_start) *
            progress,
        );
    },
    { duration: 0.3, easing: 'ease-out' },
  );

  // Submit to server
  activeTime.value = runningTime.getTotalTimeValues().seconds;

  axios
    .post('/api/practice/grammar/declension/add-attempt', {
      session: props.session.id,
      word_id: activeWord.value.id,
      correct: isCorrect.value,
      xp: xp_increment(),
      streak: currentStreak.value,
      time: runningTime.getTotalTimeValues().secondTenths,
    })
    .then((response) => {
      xpProgress.value = response.data.xp;
      level.value = response.data.level;
      nextLevelXp.value = response.data.next_level_xp;

      // Load next word after a short delay
      setTimeout(() => {
        nextQuestion();
      }, 1500);
    });
};

const nextQuestion = () => {
  if (nextWord.value) {
    // Reset inputs and correctness
    resetInputs();

    // Set the next word as active
    activeWord.value = nextWord.value;
    nextWord.value = null;
    wordChanged.value = true;
    isAnswer.value = false;

    // Request a new word
    router.reload({
      only: ['word'],
      preserveState: true,
      preserveScroll: true,
      replace: true,
      onSuccess: () => {
        nextWord.value = props.word;
        wordChanged.value = false;
      },
    });
  } else {
    // No more words, finish the session
    finishAttempt();
  }
};

const resetInputs = () => {
  for (let num in userInputs) {
    for (let caseType in userInputs[num]) {
      userInputs[num][caseType] = '';
      isCorrectInput[num][caseType] = false;
    }
  }
};

const xp_increment = () => {
  if (isCorrect.value) {
    switch (settings.difficulty) {
      case 1:
        return 3;
      case 2:
        return 5;
      default:
        return 2;
    }
  }
  return 0;
};

const finishAttempt = () => {
  isFinishing.value = true;
  router.post('/api/practice/grammar/declension/finish', {
    session: props.session.id,
  });
};

const skipWord = () => {
  // Mark as incorrect
  stats.incorrect++;
  currentStreak.value = 0;

  // Add to summary list
  summaryList.push({
    word: activeWord.value.lemma,
    correct: false,
  });

  // Move to next word
  nextQuestion();
};
const usedHint = ref(false);
const showHint = (number, caseType) => {
  usedHint.value = true;
  // Fill in the correct answer for this field
  userInputs[number][caseType] = activeWord.value.declensions[number][caseType];
  isCorrectInput[number][caseType] = true;

  // Penalty for using hint
  if (currentStreak.value > 0) {
    currentStreak.value--;
  }
};

let assignmentInProgress = ref(false);

const getDisplayTime = (time) => {
  if (time.days > 1) {
    return 'more than ' + time.days + ' days';
  }
  if (time.days > 0) {
    return 'more than ' + time.days + ' day';
  }
  if (time.hours > 1) {
    return 'more than ' + time.hours + ' hours';
  }
  if (time.hours > 0) {
    return 'more than ' + time.hours + ' hour';
  }
  if (time.seconds <= 9) {
    return time.minutes + ':0' + time.seconds;
  }
  return time.minutes + ':' + time.seconds;
};

const pauseTimer = () => {
  if (settings.time > 0) {
    if (timer.isRunning()) {
      timer.pause();
      timerIsPaused.value = true;
    } else {
      timer.start();
      timerIsPaused.value = false;
    }
  } else {
    if (runningTime.isRunning()) {
      runningTime.pause();
      timerIsPaused.value = true;
    } else {
      runningTime.start();
      timerIsPaused.value = false;
    }
  }
};

const addTime = () => {
  let previousTimerValue = timer.getTimeValues();
  previousTimerValue.minutes += 1;
  timer.removeAllEventListeners('targetAchieved');
  timer = new Timer();
  timer.start({
    countdown: true,
    startValues: {
      minutes: previousTimerValue.minutes,
      seconds: previousTimerValue.seconds,
    },
  });
  if (timerIsPaused.value) {
    timer.pause();
  }
  time.value = getDisplayTime(timer.getTimeValues());
  timer.addEventListener('secondsUpdated', function () {
    time.value = getDisplayTime(timer.getTimeValues());
  });
  timer.addEventListener('targetAchieved', function () {
    finishAttempt();
  });
};

const performance = [
  {
    value: 1,
    color: 'bg-red-500',
  },
  {
    value: 2,
    color: 'bg-orange-600',
  },
  {
    value: 3,
    color: 'bg-amber-500',
  },
  {
    value: 4,
    color: 'bg-yellow-600',
  },
  {
    value: 5,
    color: 'bg-lime-600',
  },
  {
    value: 6,
    color: 'bg-green-600',
  },
  {
    value: 7,
    color: 'bg-teal-600',
  },
  {
    value: 8,
    color: 'bg-blue-600',
  },
  {
    value: 9,
    color: 'bg-indigo-600',
  },
  {
    value: 10,
    color: 'bg-purple-600',
  },
];
const performanceMeter = computed(() => {
  if (stats.correct + stats.incorrect == 0) {
    return -1;
  }
  if (stats.correct + stats.incorrect < 10) {
    return stats.correct;
  } else {
    return Math.round((10 * stats.correct) / (stats.correct + stats.incorrect));
  }
});
</script>

<template>
  <AppLayout>
    <Head>
      <title>Declension Practice</title>
    </Head>
    <main class="pb-16 lg:pr-96 2xl:pr-[32rem]">
      <div class="px-4 py-8 sm:px-8">
        <div class="flex flex-row items-center justify-between">
          <Breadcrumbs
            class="lg:col-span-9 xl:grid-cols-10"
            :pages="breadcrumbs"
          />
          <InformationCircleIcon
            class="mt-1 ml-2 h-6 w-6 cursor-pointer stroke-current stroke-2 text-slate-400 transition duration-150 ease-in-out hover:text-slate-500 lg:hidden"
            @click="showInfo = !showInfo"
          />
        </div>
        <div class="mt-8">
          <Transition
            mode="out-in"
            enter-active-class="transition ease-out duration-150"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div v-if="!attempt">
              <DeclensionOptions @update:options="settings = $event" />
            </div>
            <div v-else>
              <div
                class="mb-8 border-b border-gray-300 pb-16 lg:hidden"
                v-if="usePage().props.authenticated"
              >
                <ProgressBar
                  class=""
                  height="h-3"
                  size="sm"
                  :display-progress="false"
                  :display-total="true"
                  :pre-text="'LEVEL ' + level.level"
                  :post-text="xpProgress + ' XP'"
                  :progress="getProgress"
                />
                <div class="mt-2 mb-8">
                  <p
                    class="text-center text-xs font-bold text-gray-500 uppercase"
                  >
                    {{ nextLevelXp }}
                    xp to the next level
                  </p>
                </div>
              </div>
              <AssignmentModule
                v-if="assignment"
                :assignment="assignment"
                :words-seen="summaryList.length"
                :words-correct="stats.correct"
                :time="activeTime"
                :accuracy="accuracyMeter"
                @completed="completeAssignment()"
              />
              <!-- <DeclensionWord
                :word="activeWord"
                :show-info="showInfo"
                @update:showInfo="showInfo = $event"
              /> -->
            </div>
          </Transition>
        </div>
      </div>

      <div class="mt-8">
        <!-- Current Word -->
        <div v-if="activeWord" class="rounded-lg bg-white p-6 shadow">
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-xl text-gray-900">
              <span class="font-bold">{{ activeWord.word }}</span
              >, {{ activeWord.gender }},
              {{ activeWord.definition }}
            </h2>
          </div>

          <!-- Declension Input Form -->
          <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
            <!-- Singular Column -->
            <div>
              <h3 class="mb-4 text-center text-lg font-semibold">Singular</h3>
              <div class="space-y-4">
                <div
                  v-for="caseType in settings.cases"
                  :key="`singular-${caseType}`"
                  class="flex items-center"
                >
                  <label
                    :for="`singular-${caseType}`"
                    class="w-1/3 font-medium text-gray-700"
                  >
                    {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
                  </label>
                  <div class="relative w-2/3">
                    <input
                      :id="`singular-${caseType}`"
                      v-model="userInputs.singular[caseType]"
                      type="text"
                      class="singular-input w-full rounded-md border border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none"
                      :class="{
                        'border-green-500 bg-green-50':
                          isCorrectInput.singular[caseType],
                      }"
                      :disabled="isCorrectInput.singular[caseType]"
                      @input="checkAnswer('singular', caseType, $event)"
                      @keydown.tab="checkAnswer('singular', caseType, $event)"
                    />
                    <button
                      v-if="!isCorrectInput.singular[caseType]"
                      @click="showHint('singular', caseType)"
                      class="absolute top-2 right-2 text-xs text-gray-400 hover:text-gray-600"
                    >
                      Hint
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Plural Column -->
            <div>
              <h3 class="mb-4 text-center text-lg font-semibold">Plural</h3>
              <div class="space-y-4">
                <div
                  v-for="caseType in settings.cases"
                  :key="`plural-${caseType}`"
                  class="flex items-center"
                >
                  <label
                    :for="`plural-${caseType}`"
                    class="w-1/3 font-medium text-gray-700"
                  >
                    {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
                  </label>
                  <div class="relative w-2/3">
                    <input
                      :id="`plural-${caseType}`"
                      v-model="userInputs.plural[caseType]"
                      type="text"
                      class="plural-input w-full rounded-md border border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none"
                      :class="{
                        'border-green-500 bg-green-50':
                          isCorrectInput.plural[caseType],
                      }"
                      :disabled="isCorrectInput.plural[caseType]"
                      @input="checkAnswer('plural', caseType, $event)"
                      @keydown.tab="checkAnswer('plural', caseType, $event)"
                    />
                    <button
                      v-if="!isCorrectInput.plural[caseType]"
                      @click="showHint('plural', caseType)"
                      class="absolute top-2 right-2 text-xs text-gray-400 hover:text-gray-600"
                    >
                      Hint
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end">
            <ButtonItem color="gray" size="md" @click="skipWord" class="px-16">
              Get a New Word
            </ButtonItem>
          </div>
        </div>
      </div>
    </main>
    <aside
      class="hidden bg-slate-50 p-8 lg:fixed lg:top-16 lg:right-0 lg:bottom-0 lg:inline-block lg:w-96 lg:overflow-y-auto lg:border-l lg:border-slate-300 2xl:w-[32rem]"
    >
      <section v-if="!usePage().props.authenticated" class="mb-8">
        <Promotion />
      </section>

      <div>
        <ProgressBar
          v-if="usePage().props.authenticated"
          class="mt-2"
          height="h-3"
          size="sm"
          :display-progress="false"
          :display-total="true"
          :pre-text="'LEVEL ' + level.level"
          :post-text="xpProgress + ' XP'"
          :progress="getProgress"
        />
        <div class="mt-2" v-if="usePage().props.authenticated">
          <p class="text-center text-xs font-bold text-gray-500 uppercase">
            {{ nextLevelXp }}
            xp to the next level
          </p>
        </div>

        <h2 class="mt-8 font-intro text-2xl font-bold">Declension Practice</h2>
        <h4 class="mt-2 font-sans text-sm font-medium text-gray-600">
          Practice Latin declensions to improve your vocabulary and grammar
          skills.
        </h4>
        <section v-if="assignment" class="mt-8">
          <AssignmentModule
            :assignment="assignment"
            :words-seen="summaryList.length"
            :words-correct="stats.correct"
            :time="activeTime"
            :accuracy="accuracyMeter"
            @completed="completeAssignment()"
          />
        </section>
        <div v-if="!attempt">
          <div v-if="assignmentInProgress && assignment.attempt">
            <h4
              class="mt-8 text-left text-sm font-bold text-gray-500 uppercase"
            >
              Assignment in Progress
            </h4>
            <p class="mt-2 text-left text-sm text-gray-800">
              {{ assignment.attempt.correct }} out of
              {{ assignment.attempt.attempts }} correct
              <span v-if="assignment.attempt.attempts > 0"
                >({{
                  Math.round(100 * assignment.attempt.correct) /
                  assignment.attempt.attempts
                }}% accuracy)</span
              >
              | {{ durationOfAttempt(assignment.attempt.time) }}
            </p>
          </div>

          <div class="mx-auto mt-8 text-center">
            <div
              v-if="usePage().props.authenticated && user.membership.subscribed"
              class="grid"
              :class="
                assignmentInProgress ? 'grid-cols-2 gap-4' : 'grid-cols-1'
              "
            >
              <ButtonItem
                color="blue"
                size="md"
                class="inline-flex w-full disabled:bg-blue-400"
                @click="handleContinueClick()"
              >
                <Transition
                  mode="out-in"
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0"
                  enter-to-class="transform opacity-100"
                  leave-active-class="transition ease-in duration-100"
                  leave-from-class="transform opacity-100"
                  leave-to-class="transform opacity-0"
                >
                  <span v-if="isLoading" class="mx-auto">
                    <svg
                      class="inline h-5 w-5 animate-spin text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span class="ml-2">Loading ...</span>
                  </span>
                  <span v-else class="mx-auto"
                    >Continue<span v-if="assignmentInProgress">
                      Attempt</span
                    ></span
                  >
                </Transition>
              </ButtonItem>
              <ButtonItem
                v-if="assignmentInProgress"
                color="white"
                size="md"
                class="inline-flex w-full disabled:bg-gray-200"
                @click="handleReattemptClick()"
                ><Transition
                  mode="out-in"
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0"
                  enter-to-class="transform opacity-100"
                  leave-active-class="transition ease-in duration-100"
                  leave-from-class="transform opacity-100"
                  leave-to-class="transform opacity-0"
                >
                  <span v-if="isLoadingReattempt" class="mx-auto">
                    <svg
                      class="inline h-5 w-5 animate-spin text-gray-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span class="ml-2">Loading ...</span>
                  </span>
                  <span v-else class="mx-auto">Restart</span>
                </Transition></ButtonItem
              >
            </div>
            <ButtonItem
              v-else-if="usePage().props.authenticated"
              color="indigo"
              size="md"
              class="flex w-full items-center justify-center"
              link="/subscribe"
              >Join LatinTutorial Pro to Attempt</ButtonItem
            >
            <ButtonItem
              v-else
              color="indigo"
              size="md"
              class="flex w-full items-center justify-center"
              @click="handleContinueClick()"
              >Log in to Continue</ButtonItem
            >
          </div>
        </div>

        <div v-else class="mt-8 grid grid-cols-1 gap-8">
          <section v-if="time" class="text-left">
            <h5 class="text-sm font-bold text-gray-500 uppercase">
              Time <span v-if="settings.time > 0">Remaining</span
              ><span v-else>Elapsed</span>
            </h5>
            <div class="mt-2 flex flex-row items-center" v-if="time">
              <div
                class="grow text-left font-medium text-gray-900"
                :class="time.length > 10 ? 'text-2xl' : 'text-4xl'"
              >
                {{ time }}
              </div>
              <div
                v-if="settings.time > 0"
                class="group mr-4 flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300"
                @click="addTime()"
              >
                <PlusIcon
                  class="mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"
                />
              </div>
              <div
                class="group flex h-10 w-10 cursor-pointer items-center justify-center self-center rounded-lg bg-slate-200 shadow-sm hover:bg-slate-300"
                @click="pauseTimer()"
              >
                <PlayIcon
                  v-if="timerIsPaused"
                  class="mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"
                />
                <PauseIcon
                  v-else
                  class="mx-auto h-5 w-5 stroke-2 text-slate-500 group-hover:text-slate-600"
                />
              </div>
            </div>
          </section>
          <section aria-labelledby="userStats">
            <div>
              <dl
                class="grid grid-cols-1 gap-px overflow-hidden rounded-lg bg-gray-900/5 shadow-sm sm:grid-cols-2 lg:grid-cols-2"
              >
                <div
                  v-for="stat in userStats"
                  :key="stat.name"
                  class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-4 sm:px-6 xl:px-8"
                >
                  <dt class="text-sm leading-6 font-medium text-gray-500">
                    {{ stat.name }}
                  </dt>
                  <dd
                    class="w-full flex-none text-4xl leading-10 tracking-tight text-gray-900"
                  >
                    <span v-if="stat.value"
                      >{{ stat.value }}
                      <span
                        v-if="stat.unit"
                        class="ml-1 text-sm font-medium text-gray-500"
                        >{{ stat.unit }}</span
                      ></span
                    ><span v-else>–</span>
                  </dd>
                </div>
              </dl>
            </div>
          </section>
          <section>
            <h5 class="text-sm font-bold text-gray-500 uppercase">
              Performance
            </h5>
            <div class="mt-4 grid grid-cols-10 gap-2 opacity-75">
              <div
                class="h-4 w-full rounded-sm shadow-sm"
                :class="
                  performanceMeter >= square.value
                    ? square.color
                    : 'bg-gray-300'
                "
                v-for="square in performance"
                :key="square.value"
              ></div>
            </div>
          </section>
          <div v-if="attempt" class="mt-8 w-full">
            <ButtonItem
              class="inline-flex w-full disabled:bg-blue-400"
              size="lg"
              color="indigo"
              :disabled="isFinishing"
              @click="finishAttempt()"
            >
              <Transition
                mode="out-in"
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0"
                enter-to-class="transform opacity-100"
                leave-active-class="transition ease-in duration-100"
                leave-from-class="transform opacity-100"
                leave-to-class="transform opacity-0"
              >
                <span v-if="isFinishing" class="mx-auto">
                  <svg
                    class="inline h-5 w-5 animate-spin text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <span class="ml-2">Finishing ...</span>
                </span>
                <span v-else class="mx-auto">Finish</span>
              </Transition>
            </ButtonItem>
          </div>
        </div>
      </div>
    </aside>
  </AppLayout>
</template>
