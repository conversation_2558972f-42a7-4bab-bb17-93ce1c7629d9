<script setup>
import { computed, ref, watchEffect } from 'vue';
import ButtonItem from '@/Shared/ButtonItem.vue';
import Modal from '@/Shared/Modal.vue';
import { Colosseum, ThirtyDayStreak } from '@/Shared/Icons/Achievements';

const props = defineProps({
  source: String,
  works: Array,
  //   authors: Array,
});

const sources = computed(() => [
  ...props.works,
  //   ...props.authors,
  {
    title: 'LatinTutorial',
    color: 'indigo',
    description: 'Full Vocabulary by frequency',
    type: 'syllabi',
    value: 'latintutorial',
    image: null,
    icon: Colosseum,
    image_text: null,
    padding: 0.5,
  },
  {
    title: 'AP Latin',
    color: 'blue',
    description: 'Required 1,000 Words',
    type: 'syllabi',
    value: 'ap_syllabus',
    image: null,
    icon: null,
    image_text: 'AP',
    padding: null,
  },
  {
    title: 'Readings',
    color: 'slate',
    description: 'Words from recent reading sections',
    type: 'syllabi',
    value: 'readings',
    image: null,
    icon: ThirtyDayStreak,
    image_text: null,
    padding: 0.5,
  },
]);

let openSourceModal = ref(false);

const currentSource = ref(null);

watchEffect(() => {
  currentSource.value = sources.value.find(
    (source) => source.value === props.source.split(':')[1],
  );
});

const queuedSource = ref(null);
watchEffect(() => {
  queuedSource.value = currentSource.value;
});

const saveSource = () => {
  if (!queuedSource.value) return;
  currentSource.value = queuedSource.value;

  axios.post('/api/infinitas/set-source', {
    source: `${queuedSource.value.type}:${queuedSource.value.value.toLowerCase()}`,
  });

  openSourceModal.value = false;
};

const isSelectedSource = (sourceItem) => {
  return queuedSource.value?.value === sourceItem.value;
};

let sourceTab = ref(props.source.split(':')[0]);

const tabs = [
  { name: 'Syllabi' },
  { name: 'Readings' },
  // { name: 'Authors' },
];
</script>

<template>
  <div class="rounded-xl bg-white p-2 lg:p-4">
    <div class="flex flex-row items-center">
      <h3 class="flex items-center text-2xl font-bold text-gray-900">
        Source
        <span
          class="ml-2 rounded-full bg-gradient-to-r from-purple-500 to-indigo-600 px-2 py-px text-xs font-semibold text-white lowercase"
          >new</span
        >
      </h3>
    </div>
    <p class="mt-2 text-sm text-gray-600">
      Select the primary source for the words you want to practice.
    </p>
    <div
      @click="openSourceModal = true"
      class="group mt-2 flex cursor-pointer items-center gap-3 rounded-xl border-2 border-dashed border-white px-4 py-4 transition duration-200 ease-in-out hover:border-gray-400 hover:bg-gray-50"
    >
      <div
        class="relative flex size-12 items-center overflow-hidden rounded-full bg-white"
        :class="[
          `p-${currentSource.padding}`,
          !currentSource.image
            ? `border-2 border-${currentSource.color}-600`
            : '',
        ]"
      >
        <img
          v-if="currentSource?.image"
          class="w-auto"
          :src="currentSource?.image"
          :alt="currentSource?.value"
        />
        <component
          :is="currentSource?.icon"
          v-else-if="currentSource?.icon"
          :class="`self-center bg-transparent fill-${currentSource?.color}-600`"
        />

        <h1
          v-else
          class="mx-auto font-intro text-2xl font-semibold text-gray-900"
        >
          {{ currentSource?.image_text }}
        </h1>
      </div>
      <div class="flex flex-col">
        <h4 class="text-base font-bold text-gray-900">
          {{ currentSource?.title }}
        </h4>
        <p class="-mt-1 text-sm text-gray-600">
          {{ currentSource?.description }}
        </p>
      </div>
      <div class="grow text-right">
        <span
          class="text-right text-sm text-white transition duration-200 ease-in-out group-hover:text-gray-600"
          >Click to Change</span
        >
      </div>
    </div>
  </div>
  <Teleport to="body">
    <Modal
      :open="openSourceModal"
      @closeModal="openSourceModal = false"
      modalTitle="Select a Source"
    >
      <template #title>Select a Source</template>
      <template #content>
        <div class="mb-8">
          <p class="mt-2 w-full text-sm text-gray-600">
            You will see words from the full vocabulary once you have all words
            from this source added to your queue.
          </p>
          <div class="mt-2">
            <div class="sm:hidden">
              <label for="tabs" class="sr-only">Select a tab</label>
              <select
                id="tabs"
                name="tabs"
                v-model="sourceTab"
                class="block w-full rounded-md border-gray-300 py-2 pr-10 pl-3 text-base focus:border-indigo-500 focus:ring-indigo-500 focus:outline-hidden sm:text-sm"
              >
                <option
                  v-for="tab in tabs"
                  :key="tab.name"
                  :value="tab.name.toLowerCase()"
                >
                  {{ tab.name }}
                </option>
              </select>
            </div>
            <div class="hidden sm:block">
              <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                  <div
                    v-for="tab in tabs"
                    :key="tab.name"
                    @click="sourceTab = tab.name.toLowerCase()"
                    :class="[
                      sourceTab === tab.name.toLowerCase()
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                      'cursor-pointer border-b-2 px-1 py-4 text-sm font-bold whitespace-nowrap',
                    ]"
                    :aria-current="
                      sourceTab === tab.name.toLowerCase() ? 'page' : undefined
                    "
                  >
                    {{ tab.name }}
                  </div>
                </nav>
              </div>
            </div>
          </div>
          <div class="mt-4 flex h-[29vh] flex-col gap-4 overflow-y-auto">
            <div
              v-for="source in sources.filter(
                (source) => source.type === sourceTab,
              )"
              :key="source"
              class="flex cursor-pointer items-center gap-3 rounded-xl border-2 px-2 py-2 transition duration-200 ease-in-out"
              :class="[
                isSelectedSource(source)
                  ? 'border-emerald-400 bg-emerald-50'
                  : 'border-white bg-white hover:border-gray-300',
              ]"
              @click="queuedSource = source"
            >
              <div class="hidden border-slate-600 fill-slate-600"></div>
              <div
                class="relative flex size-12 items-center overflow-hidden rounded-full bg-white"
                :class="[
                  `p-${source.padding}`,
                  !source.image ? `border-2 border-${source.color}-600` : '',
                ]"
              >
                <img
                  v-if="source.image"
                  class="w-auto"
                  :src="source.image"
                  :alt="source.value"
                />
                <component
                  :is="source?.icon"
                  v-else-if="source?.icon"
                  :class="`self-center bg-transparent fill-${source?.color}-600`"
                />

                <h1
                  v-else
                  class="mx-auto font-intro text-2xl font-semibold"
                  :class="`text-${source?.color}-600`"
                >
                  {{ source.image_text }}
                </h1>
              </div>
              <div class="flex flex-col">
                <h4 class="text-base font-bold text-gray-900">
                  {{ source.title }}
                </h4>
                <p class="-mt-1 text-sm text-gray-600">
                  {{ source.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template #actionButton>
        <ButtonItem size="md" color="indigo" @click="saveSource()"
          >Save
        </ButtonItem>
      </template>
    </Modal>
  </Teleport>
</template>
