<script setup>
import SectionTitle from '@/Components/SectionTitle.vue';
import { useForm, router } from '@inertiajs/vue3';
import { directive as vTippy } from 'vue-tippy';
import { createApp } from 'vue'; // ✅ Import createApp()
import ColorPicker from '@/Shared/ColorPicker.vue'; // ✅ Import the ColorPicker component
import ButtonItem from '@/Shared/ButtonItem.vue';
import { ref, nextTick } from 'vue';

const props = defineProps({
  caseColors: Object,
});

// Tailwind color options (600 level)
const tailwindColors = [
  'bg-pink-600',
  'bg-rose-600',
  'bg-orange-600',
  'bg-amber-600',
  'bg-yellow-600',
  'bg-lime-600',
  'bg-green-600',
  'bg-emerald-600',
  'bg-teal-600',
  'bg-cyan-600',
  'bg-sky-600',
  'bg-blue-600',
  'bg-indigo-600',
  'bg-violet-600',
  'bg-purple-600',
  'bg-fuchsia-600',
  'bg-gray-200',
  'bg-gray-300',
  'bg-gray-400',
  'bg-gray-500',
  'bg-gray-600',
  'bg-gray-700',
  'bg-gray-800',
  'bg-gray-900',
];

// Latin grammatical cases
const caseNames = [
  { id: 1, name: 'nom', fullName: 'Nominative', color: 'blue-600' },
  { id: 2, name: 'gen', fullName: 'Genitive', color: 'emerald-600' },
  { id: 3, name: 'dat', fullName: 'Dative', color: 'orange-600' },
  { id: 4, name: 'acc', fullName: 'Accusative', color: 'pink-600' },
  { id: 5, name: 'abl', fullName: 'Ablative', color: 'purple-600' },
  { id: 6, name: 'voc', fullName: 'Vocative', color: 'fuchsia-600' },
  { id: 7, name: 'loc', fullName: 'Locative', color: 'yellow-600' },
  { id: 8, name: 'ver', fullName: 'Verbs', color: 'gray-900' },
];

// Initialize form with Tailwind classes instead of hex colors
const form = useForm(
  caseNames.reduce((acc, caseItem) => {
    // Find the user's custom color by matching case_id
    const userColorEntry = props.caseColors.find(
      (color) => color.case_id === caseItem.id,
    );

    // If found, use user's color; otherwise, use the default caseItem.color
    acc[caseItem.name] = userColorEntry
      ? `bg-${userColorEntry.color}` // ✅ Use the custom color
      : `bg-${caseItem.color}`; // ✅ Fallback to default color

    return acc;
  }, {}),
);
const reset = () => {
  // Reset form to default case colors
  caseNames.forEach((caseItem) => {
    form[caseItem.name] = `bg-${caseItem.color}`; // ✅ Reset to default colors
  });

  // Send request to reset case colors in the database
  router.post('/api/case-colors/reset', {}, { preserveScroll: true });
};
const tooltipRefs = ref([]);

// Update color on selection
const updateColor = (caseName, newColor) => {
  const caseItem = caseNames.find((item) => item.name === caseName);
  const strippedColor = newColor.startsWith('bg-')
    ? newColor.slice(3)
    : newColor;
  form[caseName] = newColor;

  if (strippedColor === caseItem.color) {
    router.post(
      `/api/case-colors/color/${caseItem.id}/reset`,
      {},
      {
        preserveScroll: true,
        onSuccess: () => {
          nextTick(() => {
            tooltipRefs.value.forEach((ref) => ref._tippy.hide());
          });
        },
      },
    );
  } else {
    router.post(
      `/api/case-colors/color/${caseItem.id}`,
      {
        color: strippedColor,
      },
      {
        preserveScroll: true,
        onSuccess: () => {
          nextTick(() => {
            tooltipRefs.value.forEach((ref) => ref._tippy.hide());
          });
        },
      },
    );
  }
};

// ✅ Function to dynamically create and mount the ColorPicker component
const createTooltipContent = (caseName) => {
  if (typeof document !== 'undefined') {
    const container = document.createElement('div');
    const selectedColor = form[caseName]; // ✅ Get the current color

    createApp(ColorPicker, {
      tailwindColors,
      caseName,
      updateColor,
      selectedColor, // ✅ Pass the selected color
    }).mount(container);

    return container;
  }
  return 'Loading...';
};
</script>

<template>
  <div class="md:grid md:grid-cols-3 md:gap-6">
    <SectionTitle>
      <template #title> Case Colors </template>
      <template #description>
        Customize your reading by selecting case colors from our standard
        palette.
      </template>
    </SectionTitle>

    <div class="mt-5 px-4 md:col-span-2 md:mt-0">
      <div class="grid grid-cols-2 gap-4 lg:grid-cols-4">
        <div
          v-for="caseItem in caseNames"
          :key="caseItem.id"
          class="flex items-center gap-2"
        >
          <div>
            <button
              ref="tooltipRefs"
              v-tippy="{
                content: () => createTooltipContent(caseItem.name),
                allowHTML: true,
                interactive: true,
                trigger: 'click',
                theme: 'rounded',
                placement: 'bottom',
              }"
              :class="[
                form[caseItem.name],
                'h-10 w-10 cursor-pointer rounded-full border border-white',
              ]"
            ></button>
          </div>
          <label class="ml-2 font-semibold capitalize">{{
            caseItem.fullName
          }}</label>
        </div>
      </div>
      <ButtonItem
        class="float-right mt-4 mr-2"
        color="white"
        size="sm"
        @click="reset()"
        >Reset</ButtonItem
      >
    </div>
  </div>
</template>

<style>
.tippy-box[data-theme~='rounded'] {
  border-radius: 12px; /* ⬅️ Makes the tooltip rounded */
  padding: 8px; /* ⬅️ Adjust padding */
  background-color: white; /* ⬅️ Ensures visibility */
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1); /* ⬅️ Soft shadow */
}
.tippy-box[data-theme~='rounded'][data-placement^='top']
  > .tippy-arrow::before {
  border-top-color: white !important; /* White arrow for top placement */
}

.tippy-box[data-theme~='rounded'][data-placement^='bottom']
  > .tippy-arrow::before {
  border-bottom-color: white !important; /* White arrow for bottom placement */
}
</style>
