<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  wordId: {
    type: Number,
    required: true,
  },
});

const declensions = ref(null);
const loading = ref(true);
const error = ref(null);

onMounted(async () => {
  try {
    loading.value = true;
    const response = await axios.get(`/api/decline?word_id=${props.wordId}`);
    declensions.value = response.data.forms;
  } catch (err) {
    error.value = 'Failed to load declensions';
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>

<template>
  <div class="declension-table">
    <div v-if="loading" class="py-4 text-center">Loading declensions...</div>

    <div v-else-if="error" class="py-4 text-center text-red-500">
      {{ error }}
    </div>

    <div v-else-if="declensions" class="grid grid-cols-1 gap-8 lg:grid-cols-2">
      <!-- Singular Column -->
      <div>
        <h2 class="mb-4 text-center text-xl font-bold">Singular</h2>
        <table class="w-full border-collapse border border-gray-300">
          <thead>
            <tr>
              <th class="border border-gray-300 p-2">Case</th>
              <th class="border border-gray-300 p-2">Form</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(form, caseType) in declensions.singular"
              :key="caseType"
            >
              <td class="border border-gray-300 p-2 text-center font-bold">
                {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
              </td>
              <td class="border border-gray-300 p-2 text-center">
                {{ form }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Plural Column -->
      <div>
        <h2 class="mb-4 text-center text-xl font-bold">Plural</h2>
        <table class="w-full border-collapse border border-gray-300">
          <thead>
            <tr>
              <th class="border border-gray-300 p-2">Case</th>
              <th class="border border-gray-300 p-2">Form</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(form, caseType) in declensions.plural" :key="caseType">
              <td class="border border-gray-300 p-2 text-center font-bold">
                {{ caseType.charAt(0).toUpperCase() + caseType.slice(1) }}
              </td>
              <td class="border border-gray-300 p-2 text-center">
                {{ form }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-else class="py-4 text-center">No declension data available</div>
  </div>
</template>
