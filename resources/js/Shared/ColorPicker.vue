<script setup>
const props = defineProps({
  tailwindColors: Array,
  caseName: String,
  updateColor: Function,
  selectedColor: String,
});
</script>

<template>
  <div class="w-80 rounded-full p-2">
    <div class="grid grid-cols-8 gap-2">
      <div
        class="hidden text-amber-600 text-blue-600 text-cyan-600 text-emerald-600 text-fuchsia-600 text-gray-200 text-gray-300 text-gray-400 text-gray-500 text-gray-600 text-gray-700 text-gray-800 text-gray-900 text-green-600 text-indigo-600 text-lime-600 text-orange-600 text-pink-600 text-purple-600 text-rose-600 text-sky-600 text-teal-600 text-violet-600 text-yellow-600"
      ></div>
      <button
        v-for="color in tailwindColors"
        :key="color"
        :class="[
          color,
          'h-8 w-8 cursor-pointer rounded-full border border-white',
          selectedColor === color ? 'ring-2 ring-blue-600' : 'ring-white', // ✅ Add ring if selected
        ]"
        @click="updateColor(caseName, color)"
      ></button>
    </div>
  </div>
</template>
