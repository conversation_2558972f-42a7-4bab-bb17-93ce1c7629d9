<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Str;

class DeclensionPractice extends Model
{
  protected $table = 'declension_sessions';

  protected $fillable = [
    'user_id',
    'options',
    'completed',
    'attempts',
    'correct',
    'streak',
    'time',
  ];

  protected $appends = ['type'];

  public function getTypeAttribute()
  {
    return 'declension';
  }

  protected static function boot()
  {
    parent::boot();

    static::creating(function ($post) {
      $post->{$post->getKeyName()} = (string) Str::uuid();
    });
  }

  public function getIncrementing()
  {
    return false;
  }

  public function getKeyType()
  {
    return 'string';
  }

  public function user()
  {
    return $this->belongsTo(User::class);
  }
}
