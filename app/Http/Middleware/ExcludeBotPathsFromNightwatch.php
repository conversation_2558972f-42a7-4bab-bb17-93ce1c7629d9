<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Nightwatch\Nightwatch;

class ExcludeBotPathsFromNightwatch
{
  protected $excludedPatterns = [
    'wp-',
    'phpmyadmin',
    'xmlrpc.php',
    'backup',
    // Add more known exploit paths as needed
  ];

  public function handle(Request $request, Closure $next)
  {
    $path = ltrim($request->getPathInfo(), '/'); // remove leading slash

    foreach ($this->excludedPatterns as $pattern) {
      if (str_starts_with($path, $pattern)) {
        Nightwatch::stop(); // stop tracking for this request
        break;
      }
    }

    return $next($request);
  }
}
