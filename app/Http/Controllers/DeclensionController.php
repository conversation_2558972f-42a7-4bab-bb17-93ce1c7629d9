<?php

namespace App\Http\Controllers;

use App\Models\Word;
use App\Services\LatinDeclensionService;
use Illuminate\Http\Request;

class DeclensionController extends Controller
{
  protected $declensionService;

  public function __construct(LatinDeclensionService $declensionService)
  {
    $this->declensionService = $declensionService;
  }

  public function decline(Request $request, $id)
  {
    $word = Word::findOrFail($id);

    // Get declension forms
    $declensions = $this->declensionService->decline(
      $word->lemma,
      $word->gender,
      substr($word->type, 1)
    );

    return response()->json([
      'word' => $word->lemma,
      'gender' => $word->gender,
      'declension' => $word->declension,
      'forms' => $declensions,
    ]);
  }
}
