<?php

namespace App\Http\Controllers;

use Request;
use Inertia\Inertia;
use App\Models\Word;
use App\Models\DeclensionPractice;
use App\Models\DeclensionPracticeAttempt;
use App\Events\UserEarnedExperience;
use Auth;
use Str;
use Carbon\Carbon;
use App\Models\User;
use App\Models\AssignmentCompletion;
use Illuminate\Support\Facades\Redis;
use App\Services\LatinDeclensionService;

class DeclensionPracticeController extends Controller
{
  protected $declensionService;

  public function __construct(LatinDeclensionService $declensionService)
  {
    $this->declensionService = $declensionService;
  }

  public function index()
  {
    return Inertia::render('Practice/Grammar/Declension/Attempt', [
      'session' => Inertia::lazy(
        fn() => $this->createSession(Request::input('assignment'))
      ),
      'assignment' => null,
      'initialWords' => Inertia::lazy(
        fn() => $this->getFirstWords(
          Redis::get('user:' . Auth::id() . ':declensionPractice:sessionId')
        )
      ),
      'word' => Inertia::lazy(
        fn() => $this->formatWord(
          $this->getNextWord(
            Redis::get('user:' . Auth::id() . ':declensionPractice:sessionId')
          )
        )
      ),
    ]);
  }

  public function createSession($assignment = null)
  {
    $user = Auth::user();

    if ($assignment) {
      $existingAssignment = AssignmentCompletion::where(
        'assignment_id',
        $assignment
      )
        ->where('user_id', $user->id)
        ->first();
      if ($existingAssignment) {
        $reattempt = filter_var(
          Request::input('reattempt'),
          FILTER_VALIDATE_BOOLEAN
        );

        if ($existingAssignment->activity_id && !$reattempt) {
          $session = GrammarPractice::where(
            'id',
            $existingAssignment->activity_id
          )
            ->where('user_id', $user->id)
            ->first();
        } else {
          $session = $this->createDeclensionSession($user);
          $existingAssignment->activity_id = $session->id;
          $existingAssignment->save();
        }
      } else {
        $session = $this->createDeclensionSession($user);
        // If the record doesn't exist, create a new one
        AssignmentCompletion::create([
          'assignment_id' => $assignment,
          'user_id' => $user->id,
          'completed' => 0,
          'activity_id' => $session->id,
        ]);
      }
    } else {
      $session = $this->createDeclensionSession($user);
    }
    Redis::set(
      'user:' . $user->id . ':declensionPractice:sessionId',
      $session->id
    );
    return $this->formatSession($session);
  }

  private function createDeclensionSession(User $user)
  {
    $this->resetRedisData($user->id);

    $session = new DeclensionPractice();
    $session->user_id = $user->id;
    $session->attempts = 0;
    $session->correct = 0;
    $session->streak = 0;
    $session->options = json_encode([
      'declensions' => Request::input('declensions', []),
      'cases' => Request::input('cases', []),
      'difficulty' => Request::input('difficulty', 0),
    ]);
    $session->save();
    return $session;
  }

  public function getFirstWords($session)
  {
    $session = DeclensionPractice::findOrFail($session);
    $options = json_decode($session->options, true);
    $declensions = $options['declensions'] ?? [];

    // Convert declension options to actual declension numbers
    $declensionNumbers = [];
    foreach ($declensions as $declension) {
      switch ($declension) {
        case 'first':
          $declensionNumbers[] = 'n1';
          break;
        case 'second_masc':
          $declensionNumbers[] = 'n2';
          break;
        case 'second_neut':
          $declensionNumbers[] = 'n21';
          break;
        case 'third_cons':
          $declensionNumbers[] = 'n3';
          break;
        case 'third_i':
          $declensionNumbers[] = 'n3i';
          break;
        case 'third_neut':
          $declensionNumbers[] = 'n31';
          break;
        case 'fourth':
          $declensionNumbers[] = 'n4';
          break;
        case 'fifth':
          $declensionNumbers[] = 'n5';
          break;
      }
    }

    // Query for a word
    $query = Word::where('pos', 'noun')
      ->whereIn('type', $declensionNumbers)
      ->where('core', 1);

    // Get a random word
    $firstWord = $query->inRandomOrder()->first();
    $secondWord = $query
      ->where('id', '!=', $firstWord->id)
      ->inRandomOrder()
      ->first();

    return [$this->formatWord($firstWord), $this->formatWord($secondWord)];
  }

  public function getNextWord($session, $currentWordId = null)
  {
    $session = DeclensionPractice::findOrFail($session);
    $options = json_decode($session->options, true);
    $declensions = $options['declensions'] ?? [];

    // Convert declension options to actual declension numbers
    $declensionNumbers = [];
    foreach ($declensions as $declension) {
      switch ($declension) {
        case 'first':
          $declensionNumbers[] = 'n1';
          break;
        case 'second_masc':
          $declensionNumbers[] = 'n2';
          break;
        case 'second_neut':
          $declensionNumbers[] = 'n21';
          break;
        case 'third_cons':
          $declensionNumbers[] = 'n3';
          break;
        case 'third_i':
          $declensionNumbers[] = 'n3i';
          break;
        case 'third_neut':
          $declensionNumbers[] = 'n31';
          break;
        case 'fourth':
          $declensionNumbers[] = 'n4';
          break;
        case 'fifth':
          $declensionNumbers[] = 'n5';
          break;
      }
    }

    // Query for a word
    $query = Word::where('pos', 'noun')
      ->whereIn('type', $declensionNumbers)
      ->where('core', 1);

    // Exclude current word if provided
    if ($currentWordId) {
      $query->where('id', '!=', $currentWordId);
    }

    // Get a random word
    $word = $query->inRandomOrder()->first();

    if (!$word) {
      // If no word found, get any noun
      $word = Word::where('type', 'noun')
        ->whereNotNull('declension')
        ->inRandomOrder()
        ->first();
    }

    return $word;
  }

  public function nextWord(Request $request)
  {
    $session = DeclensionPractice::findOrFail($request->input('session'));
    $currentWordId = $request->input('current_word_id');

    $word = $this->getNextWord($session, $currentWordId);

    return $this->formatWord($word);
  }

  public function addAttempt(Request $request)
  {
    $session = DeclensionPractice::findOrFail($request->input('session'));
    $wordId = $request->input('word_id');
    $correct = $request->input('correct', false);
    $xp = $request->input('xp', 0);
    $streak = $request->input('streak', 0);
    $time = $request->input('time', 0);

    // Update session stats
    $session->attempts += 1;
    if ($correct) {
      $session->correct += 1;
      $session->xp_earned += $xp;
    }
    $session->streak = $streak;
    $session->save();

    // Record the attempt
    $attempt = new DeclensionPracticeAttempt();
    $attempt->user_id = Auth::id();
    $attempt->item_id = $wordId;
    $attempt->type = 'declension';
    $attempt->session_id = $session->id;
    $attempt->xp_earned = $correct ? $xp : 0;
    $attempt->attempt = json_encode(['word_id' => $wordId]);
    $attempt->correct = $correct;
    $attempt->time = $time;
    $attempt->save();

    // Award XP to the user
    if ($correct) {
      event(new UserEarnedExperience(Auth::user(), $xp));
    }

    return [
      'success' => true,
      'xp' => Auth::user()->xp,
      'level' => Auth::user()->level,
      'next_level_xp' => Auth::user()->level->max - Auth::user()->xp + 1,
    ];
  }

  public function updateSessionOptions()
  {
    $session = DeclensionPractice::findOrFail(Request::input('session'));
    $settings = Request::input('settings');

    $session->options = json_encode($settings);
    $session->save();

    return ['success' => true];
  }

  public function finish(Request $request)
  {
    $session = DeclensionPractice::where('id', $request->input('session'))
      ->where('user_id', Auth::id())
      ->first();

    $session->completed = 1;
    $session->save();

    return redirect()->route('practice.grammar.declension.summary', [
      'id' => $session->id,
    ]);
  }

  public function summary($id)
  {
    $session = DeclensionPractice::findOrFail($id);

    return Inertia::render('Practice/Grammar/DeclensionSummary', [
      'session' => $this->formatSession($session),
      'attempts' => $session
        ->attempts()
        ->with('word')
        ->orderBy('created_at', 'desc')
        ->get()
        ->map(function ($attempt) {
          return [
            'id' => $attempt->id,
            'word' => $attempt->word ? $attempt->word->lemma : 'Unknown',
            'correct' => $attempt->correct,
            'time' => $attempt->time,
            'created_at' => $attempt->created_at->format('Y-m-d H:i:s'),
          ];
        }),
    ]);
  }

  private function formatSession($session)
  {
    return [
      'id' => $session->id,
      'attempts' => $session->attempts,
      'correct' => $session->correct,
      'streak' => $session->streak,
      'xp_earned' => $session->xp_earned,
      'options' => json_decode($session->options, true),
      'completed' => $session->completed,
    ];
  }

  private function formatWord($word)
  {
    if (!$word) {
      return null;
    }
    $declensions = $this->declensionService->decline($word)->get();

    return [
      'id' => $word->id,
      'lemma' => preg_replace(
        '/\(.*\)/',
        '',
        strstr($word->display_word, ',', true)
      ),
      'word' => $word->display_word,
      'gender' => $word->gender,
      'definition' => $word->baseMeaning(),
      'declension' => $word->type,
      'declensions' => $declensions,
    ];
  }

  private function resetRedisData(int $userId)
  {
    $redisKey = "user:$userId:declensionPractice";
    Redis::del("$redisKey:sessionId");
  }

  public function logField(Request $request)
  {
    $data = Request::validate([
      'session' => 'required|exists:declension_sessions,id',
      'word_id' => 'required|integer|exists:words,id',
      'number' => 'required|string|in:singular,plural',
      'case' => 'required|string',
      'input' => 'required|string',
      'correct' => 'required|boolean',
      'used_hint' => 'required|boolean',
    ]);

    DeclensionPracticeAttempt::create([
      'session_id' => $data['session'],
      'word_id' => $data['word_id'],
      'number' => $data['number'],
      'case' => $data['case'],
      'attempt' => $data['input'],
      'correct' => $data['correct'],
      'used_hint' => $data['used_hint'],
      'user_id' => Auth::id(),
    ]);

    return response()->noContent();
  }
}
