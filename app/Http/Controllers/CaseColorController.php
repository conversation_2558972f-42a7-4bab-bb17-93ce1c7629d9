<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\CaseColor;
use Cache;

class CaseColorController extends Controller
{
  public function update(Request $request, $id)
  {
    $user = Auth::user();
    $caseColor = CaseColor::firstOrCreate(
      [
        'user_id' => $user->id,
        'case_id' => $id,
      ],
      [
        'color' => $request->color,
      ]
    );
    if ($caseColor->wasRecentlyCreated === false) {
      $caseColor->color = $request->color;
      $caseColor->save();
    }

    $caseColor->save();
    Cache::forget("user_{$user->id}_case_colors");

    return redirect()->back();
  }

  public function reset($id)
  {
    $user = Auth::user();
    $caseColor = CaseColor::where('case_id', $id)
      ->where('user_id', $user->id)
      ->first();

    if ($caseColor) {
      $caseColor->delete();
      // ✅ Clear cache since case color has been reset
      Cache::forget("user_{$user->id}_case_colors");
    }

    return redirect()->back();
  }

  public function destroy()
  {
    // Get all case colors for the authenticated user and delete them.
    $user = Auth::user();
    CaseColor::where('user_id', $user->id)->delete();

    // ✅ Clear cache since all case colors have been deleted
    Cache::forget("user_{$user->id}_case_colors");

    return redirect()->back();
  }
}
