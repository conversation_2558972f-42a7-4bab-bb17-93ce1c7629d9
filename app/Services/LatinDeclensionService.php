<?php

namespace App\Services;
use App\Models\Word;
use App\Services\Support\DeclinedWordResult;

class LatinDeclensionService
{
  protected $declensionRules;

  public function __construct()
  {
    $this->declensionRules = config('declensions.declensions');
  }

  public function decline(Word|string $word, ?string $type = null)
  {
    if ($word instanceof Word) {
      $displayWord = $word->display_word;
      $type = $word->type;
    } else {
      $displayWord = $word;
    }

    $info = $this->parseType($type);
    if ($type === 'irr') {
      [$rawNom] = array_map('trim', explode(',', $displayWord));
      $nom = preg_replace('/\s*\(.*?\)/', '', $rawNom); // Strip parentheticals

      $irregulars = config('declensions.irregular');

      if (isset($irregulars[$nom])) {
        return $irregulars[$nom];
      }

      return []; // Or throw new \Exception("Irregular noun '$nom' not found.");
    }

    [$rawNom] = array_map('trim', explode(',', $displayWord));
    $nom = preg_replace('/\s*\(.*?\)/', '', $rawNom); // removes e.g. (vinclum)

    $declension = (int) $info['declension'] ?? 1;
    $modifiers = $info['modifiers'] ?? [];

    $key = $this->getDeclensionKey($declension);
    if (!isset($this->declensionRules[$key])) {
      return [];
    }

    $base = $this->declensionRules[$key]['base'];
    $key = ucfirst($key);
    $getSubtypeMethod = "get{$key}DeclensionSubtype";
    $subtypes = method_exists($this, $getSubtypeMethod)
      ? $this->$getSubtypeMethod($nom, $info['explicitStem'] ?? null)
      : [];

    $subtypes = array_unique(array_merge($subtypes, $modifiers));

    // ✅ Inject logic here:
    if (
      !in_array('pl', $subtypes) &&
      !in_array('plural_only', $subtypes) &&
      mb_strtoupper(mb_substr($nom, 0, 1)) === mb_substr($nom, 0, 1)
    ) {
      $subtypes[] = 'sg';
    }
    if (in_array('irr', $subtypes)) {
      [$rawNom] = array_map('trim', explode(',', $displayWord));
      $nom = preg_replace('/\s*\(.*?\)/', '', $rawNom);
      $irregulars = config('declensions.irregular');

      if (isset($irregulars[$nom])) {
        return $irregulars[$nom];
      }

      return [];
    }
    $isPluralOnly = in_array('pl', $subtypes);
    $isSingularOnly = in_array('sg', $subtypes);
    $includeLocative = in_array('loc', $subtypes);

    if (!$includeLocative) {
      unset($base['singular']['locative'], $base['plural']['locative']);
    }

    // Merge subtype overrides
    $overrides = ['singular' => [], 'plural' => []];
    $key = lcfirst($key);
    foreach ($subtypes as $sub) {
      $override = $this->declensionRules[$key]['subtypes'][$sub] ?? null;
      if ($override) {
        $overrides['singular'] = array_merge(
          $overrides['singular'],
          $override['singular'] ?? []
        );
        $overrides['plural'] = array_merge(
          $overrides['plural'],
          $override['plural'] ?? []
        );
      }
    }

    $rules = [
      'singular' => array_merge($base['singular'], $overrides['singular']),
      'plural' => array_merge($base['plural'], $overrides['plural']),
    ];

    [$rules['singular'], $rules['plural']] = $this->applyTypeOverrides(
      $rules,
      $info
    );
    $stem =
      $info['explicitStem'] ??
      $this->getStemFromDisplayWord($displayWord, $declension);
    $result = [];

    // print_r($stem);
    foreach (['singular', 'plural'] as $number) {
      if ($isPluralOnly && $number === 'singular') {
        continue;
      }
      if ($isSingularOnly && $number === 'plural') {
        continue;
      }

      $result[$number] = [];
      foreach ($rules[$number] as $case => $ending) {
        $result[$number][$case] = [];

        $useExplicitStem = !(
          $number === 'singular' && in_array($case, ['nominative', 'vocative'])
        );
        $currentStem = $useExplicitStem
          ? $info['explicitStem'] ?? $stem
          : $this->getStemFromDisplayWord($displayWord, $declension);

        $endings =
          is_array($ending) && is_array($ending[0])
            ? $ending
            : array_map(
              fn($e) => ['ending' => $e],
              is_array($ending) ? $ending : [$ending]
            );

        foreach ($endings as $e) {
          // Alternate nominatives (full forms) already merged in `rules` → just push as-is
          if (
            isset($e['ending']) &&
            !isset($e['trim']) &&
            $number === 'singular' &&
            ($case === 'nominative' || $case === 'vocative')
          ) {
            $result[$number][$case][] = $e['ending'];
            continue;
          }

          if ($e['ending'] === null) {
            $result[$number][$case][] = $nom;
            continue;
          }

          $trim = $e['trim'] ?? 0;
          $source = $useExplicitStem
            ? $currentStem
            : $this->getStemFromDisplayWord($displayWord, $declension);
          $form =
            mb_substr($source, 0, mb_strlen($source) - $trim) . $e['ending'];
          $result[$number][$case][] = $form;
        }

        if (count($result[$number][$case]) === 1) {
          $result[$number][$case] = $result[$number][$case][0];
        }
      }
    }

    // return $result;
    return new DeclinedWordResult($result);
  }

  private function getDeclensionKey(int $declension)
  {
    $keys = [
      1 => 'first',
      2 => 'second',
      3 => 'third',
      4 => 'fourth',
      5 => 'fifth',
    ];
    return $keys[$declension] ?? 'first';
  }

  private function getStemFromDisplayWord(string $displayWord, int $declension)
  {
    [$nom, $gen] = array_map('trim', explode(',', $displayWord) + [1 => '']);

    switch ($declension) {
      case 1:
        return $this->get1stDeclensionStem($nom, $declension);

      case 2:
        return $this->get2ndDeclensionStem($nom, $declension);

      case 3:
        return $this->get3rdDeclensionStem($nom, $declension);

      case 4:
        return $this->get4thDeclensionStem($nom, $declension);

      case 5:
        return $this->get5thDeclensionStem($nom, $declension);

      default:
        return $nom;
    }
  }

  private function get1stDeclensionStem(string $nom): string
  {
    return match (true) {
      $this->mb_str_ends_with($nom, 'ām') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ās') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ēs') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ē') => mb_substr($nom, 0, -1),
      $this->mb_str_ends_with($nom, 'ae') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'a') => mb_substr($nom, 0, -1),
      default => $nom,
    };
  }

  private function get2ndDeclensionStem(string $nom): string
  {
    return match (true) {
      $this->mb_str_ends_with($nom, 'us') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'um') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ī') => mb_substr($nom, 0, -1),
      $this->mb_str_ends_with($nom, 'os') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'on') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'a') => mb_substr($nom, 0, -1),
      default => $nom,
    };
  }

  private function get3rdDeclensionStem(string $nom): string
  {
    $lens = [
      'tūdō' => 'tūdin',
      'āns' => 'ant',
      'ēns' => 'ent',
      'ōns' => 'ont',
      'ceps' => 'cipit',
      'us' => 'or',
      'ex' => 'ic',
      'al' => 'āl',
      'ar' => 'ār',
      'men' => 'min',
      'er' => 'r',
      'or' => 'ōr',
      'gō' => 'gin',
      'ō' => 'ōn',
      'ps' => 'p',
      'bs' => 'b',
      'is' => '',
      's' => 't',
      'x' => 'c',
    ];

    foreach ($lens as $end => $stemEnd) {
      if (str_ends_with($nom, $end)) {
        return substr($nom, 0, -strlen($end)) . $stemEnd;
      }
    }

    // No match: stem = nom (after removing final -s if present)
    return str_ends_with($nom, 's') ? substr($nom, 0, -1) : $nom;
  }

  private function get4thDeclensionStem(string $nom): string
  {
    return match (true) {
      $this->mb_str_ends_with($nom, 'ua') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ū') => mb_substr($nom, 0, -1),
      $this->mb_str_ends_with($nom, 'us') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ūs') => mb_substr($nom, 0, -2),
      $this->mb_str_ends_with($nom, 'ō') => mb_substr($nom, 0, -1),
      default => $nom,
    };
  }

  private function get5thDeclensionStem(string $nom): string
  {
    if ($this->mb_str_ends_with($nom, 'ēs')) {
      return mb_substr($nom, 0, -2);
    }

    return $nom;
  }

  private function parseType(string $type): array
  {
    $parts = explode('.', $type);
    $base = $parts[0];

    preg_match('/n(\d)/', $base, $matches);
    $declension = isset($matches[1]) ? (int) $matches[1] : 1;

    $modifiers = [];
    $overrides = ['singular' => [], 'plural' => []];
    $alternates = ['singular' => [], 'plural' => []];
    $explicitStem = null;

    foreach (array_slice($parts, 1) as $part) {
      if (str_starts_with($part, 'stem=')) {
        $explicitStem = substr($part, 5);
      } elseif (
        preg_match(
          '/^\+(nom|gen|dat|acc|abl|voc|loc)\/(sg|pl)=(.+)$/',
          $part,
          $m
        )
      ) {
        [$_, $case, $number, $ending] = $m;
        $map = [
          'nom' => 'nominative',
          'gen' => 'genitive',
          'dat' => 'dative',
          'acc' => 'accusative',
          'abl' => 'ablative',
          'voc' => 'vocative',
          'loc' => 'locative',
        ];
        $alternates[$number === 'sg' ? 'singular' : 'plural'][
          $map[$case]
        ][] = $ending;
      } elseif (
        preg_match('/^(nom|gen|dat|acc|abl|voc|loc)\/(sg|pl)=(.+)$/', $part, $m)
      ) {
        [$_, $case, $number, $ending] = $m;
        $map = [
          'nom' => 'nominative',
          'gen' => 'genitive',
          'dat' => 'dative',
          'acc' => 'accusative',
          'abl' => 'ablative',
          'voc' => 'vocative',
          'loc' => 'locative',
        ];
        $overrides[$number === 'sg' ? 'singular' : 'plural'][
          $map[$case]
        ] = $ending;
      } else {
        $modifiers[] = $part;
      }
    }

    return [
      'declension' => $declension,
      'modifiers' => $modifiers,
      'overrides' => $overrides,
      'alternates' => $alternates,
      'explicitStem' => $explicitStem,
    ];
  }

  private function getFirstDeclensionSubtype(
    string $nom,
    ?string $stem = null
  ): array {
    if ($this->mb_str_ends_with($nom, 'ām')) {
      return ['F', 'am'];
    }

    if ($this->mb_str_ends_with($nom, 'ās')) {
      return ['M', 'Greek.Ma'];
    }

    if ($this->mb_str_ends_with($nom, 'ēs')) {
      return ['M', 'Greek.Me'];
    }

    if ($this->mb_str_ends_with($nom, 'ē')) {
      return ['F', 'Greek'];
    }

    if ($this->mb_str_ends_with($nom, 'ae')) {
      return ['F', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'a')) {
      return ['F', 'regular'];
    }

    return ['F', 'regular'];
  }

  private function getSecondDeclensionSubtype(
    string $nom,
    ?string $stem = null
  ): array {
    $subtypes = [];

    // Base mappings: apply in order with possible overlaps
    if (preg_match('/[A-Z].*ius$/u', $nom)) {
      return ['M', 'ius', 'voci', 'sg'];
    }

    if ($this->mb_str_ends_with($nom, 'ius')) {
      return ['M', 'ius'];
    }

    if ($this->mb_str_ends_with($nom, 'ium')) {
      return ['N', 'ium'];
    }

    if ($this->mb_str_ends_with($nom, 'us')) {
      return ['M'];
    }

    if ($this->mb_str_ends_with($nom, 'vos')) {
      return ['M', 'vos'];
    }

    if ($this->mb_str_ends_with($nom, 'vom')) {
      return ['N', 'vom'];
    }

    if ($this->mb_str_ends_with($nom, 'os')) {
      // Default to masculine Greek unless explicitly neuter
      return ['M', 'Greek'];
    }

    if ($this->mb_str_ends_with($nom, 'on')) {
      return ['N', 'Greek.N'];
    }

    if ($this->mb_str_ends_with($nom, 'iī')) {
      return ['M', 'ius', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'ia')) {
      return ['N', 'ium', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'ī')) {
      return ['M', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'a')) {
      return ['N', 'pl'];
    }

    if (
      $this->mb_str_ends_with($nom, 'er') ||
      $this->mb_str_ends_with($nom, 'ir')
    ) {
      return ['M', 'r-stem'];
    }

    if ($this->mb_str_ends_with($nom, 'um')) {
      return ['N'];
    }

    return ['M'];
  }

  public function getThirdDeclensionSubtype(
    string $nom,
    ?string $stem = null
  ): array {
    $stem ??= $this->get3rdDeclensionStem($nom, 3);
    $subtypes = [];

    $rules = [
      ['end' => 'tūdō', 'stem' => 'tūdin', 'subtypes' => ['F']],
      ['end' => 'tās', 'stem' => 'tāt', 'subtypes' => ['F']],
      ['end' => 'tūs', 'stem' => 'tūt', 'subtypes' => ['F']],
      ['end' => 'tiō', 'stem' => 'tiōn', 'subtypes' => ['F']],
      ['end' => 'siō', 'stem' => 'siōn', 'subtypes' => ['F']],
      ['end' => 'xiō', 'stem' => 'xiōn', 'subtypes' => ['F']],
      ['end' => 'gō', 'stem' => 'gin', 'subtypes' => ['F']],
      ['end' => 'or', 'stem' => 'ōr', 'subtypes' => ['M']],
      ['end' => 'trīx', 'stem' => 'trīc', 'subtypes' => ['F']],
      ['end' => 'trix', 'stem' => 'trīc', 'subtypes' => ['F']],
      ['end' => 'us', 'stem' => 'or', 'subtypes' => ['N']],
      ['end' => 'us', 'stem' => 'er', 'subtypes' => ['N']],
      ['end' => 'ma', 'stem' => 'mat', 'subtypes' => ['N']],
      ['end' => 'men', 'stem' => 'min', 'subtypes' => ['N']],
      ['end' => 'e', 'stem' => '', 'subtypes' => ['N']], // capital E special case handled below
      ['end' => 'e', 'stem' => '', 'subtypes' => ['N', 'I', 'pure']],
      ['end' => 'al', 'stem' => 'āl', 'subtypes' => ['N', 'I', 'pure']],
      ['end' => 'ar', 'stem' => 'ār', 'subtypes' => ['N', 'I', 'pure']],
      ['end' => 'is', 'stem' => '', 'subtypes' => ['I']],
      ['end' => 'ēs', 'stem' => '', 'subtypes' => ['I']],
    ];

    $nomLower = mb_strtolower($nom);
    foreach ($rules as $rule) {
      if (!$this->mb_str_ends_with($nom, $rule['end'])) {
        continue;
      }

      if (
        $rule['stem'] !== '' &&
        !$this->mb_str_ends_with($stem, $rule['stem'])
      ) {
        continue;
      }

      // Shared base logic: ensure stem is plausible morph of nom
      if (!$this->sharesBase($nom, $stem, $rule['end'], $rule['stem'])) {
        continue;
      }

      // Special handling for capitalized neuter -e (e.g. 'Mare') vs lowercase
      if ($rule['end'] === 'e' && $rule['subtypes'] === ['N']) {
        if ($nom[0] !== mb_strtoupper($nom[0])) {
          continue;
        }
      }

      if ($rule['end'] === 'ēs' && $nom[0] !== mb_strtolower($nom[0])) {
        continue; // Only lowercase nom -ēs gets subtype I
      }

      // Return on first matched rule
      return $rule['subtypes'];
    }

    // Fallback
    return ['M'];
  }

  private function getFourthDeclensionSubtype(
    string $nom,
    ?string $stem = null
  ): array {
    if ($this->mb_str_ends_with($nom, 'ua')) {
      return ['N', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'ū')) {
      return ['N'];
    }

    if ($this->mb_str_ends_with($nom, 'ūs')) {
      return ['M', 'pl'];
    }

    if ($this->mb_str_ends_with($nom, 'us')) {
      return ['M'];
    }

    return ['M', 'regular']; // fallback
  }

  private function getFifthDeclensionSubtype(
    string $nom,
    ?string $stem = null
  ): array {
    if ($this->mb_str_ends_with($nom, 'ēs')) {
      if ($this->mb_str_ends_with($nom, 'iēs')) {
        return ['M', 'ies'];
      }
      return ['F'];
    }

    return ['F', 'regular'];
  }

  private function mb_str_ends_with(string $haystack, string $needle): bool
  {
    $length = mb_strlen($needle);
    if ($length === 0) {
      return true;
    }

    return mb_substr($haystack, -$length) === $needle;
  }

  private function sharesBase(
    string $nom,
    string $stem,
    string $nomSuffix,
    string $stemSuffix
  ): bool {
    $nomBase = $nomSuffix ? mb_substr($nom, 0, -mb_strlen($nomSuffix)) : $nom;
    $stemBase = $stemSuffix
      ? mb_substr($stem, 0, -mb_strlen($stemSuffix))
      : $stem;

    return $nomBase === $stemBase;
  }

  private function applyTypeOverrides(array $rules, array $info): array
  {
    // apply overrides
    $rules['singular'] = array_merge(
      $rules['singular'],
      $info['overrides']['singular']
    );
    $rules['plural'] = array_merge(
      $rules['plural'],
      $info['overrides']['plural']
    );
    // apply alternates
    foreach (['singular', 'plural'] as $num) {
      foreach ($info['alternates'][$num] as $case => $alts) {
        $rules[$num][$case] = array_merge(
          (array) ($rules[$num][$case] ?? []),
          $alts
        );
      }
    }
    return [$rules['singular'], $rules['plural']];
  }
}
