<?php

namespace App\Services\Support;

class DeclinedWordResult
{
  protected array $data;
  protected ?string $selectedCase = null;
  protected ?string $selectedNumber = null;

  protected static array $caseMap = [
    'nom' => 'nominative',
    'gen' => 'genitive',
    'dat' => 'dative',
    'acc' => 'accusative',
    'abl' => 'ablative',
    'voc' => 'vocative',
    'loc' => 'locative',
  ];

  protected static array $numberMap = [
    'sg' => 'singular',
    'pl' => 'plural',
  ];

  public function __construct(array $data)
  {
    $this->data = $data;
  }

  public function case(string $case): static
  {
    $normalized = self::$caseMap[$case] ?? $case;
    $this->selectedCase = $normalized;
    return $this;
  }

  public function number(string $number): static
  {
    $normalized = self::$numberMap[$number] ?? $number;
    $this->selectedNumber = $normalized;
    return $this;
  }

  public function get(): string|array|null
  {
    if ($this->selectedCase && $this->selectedNumber) {
      // Specific cell
      return $this->data[$this->selectedNumber][$this->selectedCase] ?? null;
    }

    if ($this->selectedCase) {
      // Both singular and plural forms for this case
      return [
        'singular' => $this->data['singular'][$this->selectedCase] ?? null,
        'plural' => $this->data['plural'][$this->selectedCase] ?? null,
      ];
    }

    if ($this->selectedNumber) {
      // All cases for this number
      return $this->data[$this->selectedNumber] ?? [];
    }

    // Full table
    return $this->data;
  }

  public function all(): array
  {
    return $this->data;
  }
}
