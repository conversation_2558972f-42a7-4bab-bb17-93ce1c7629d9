<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Carbon\Carbon;
use App\Mail\TrialEndingSoonMail;
use Illuminate\Support\Facades\Mail;

class SendTrialEndingSoonEmails extends Command
{
  protected $signature = 'notify:trial-ending';
  protected $description = 'Send an email notification to users whose trial expires tomorrow.';

  public function handle()
  {
    $startOfTomorrow = Carbon::tomorrow()->startOfDay();
    $endOfTomorrow = Carbon::tomorrow()->endOfDay();

    $users = User::whereBetween('trial_ends_at', [
      $startOfTomorrow,
      $endOfTomorrow,
    ])
      ->whereDoesntHave('teams') // Exclude users who are in a team
      ->get();
    dd($users);
    foreach ($users as $user) {
      Mail::to($user->email)->send(new TrialEndingSoonMail($user));
    }

    $this->info('Trial ending soon emails sent successfully.');
  }
}
