<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Work;
use Illuminate\Support\Facades\Log;

class UpdateWordWorkCounts extends Command
{
  protected $signature = 'wordwork:update';
  protected $description = 'Update word counts in the word_work pivot table using Eloquent';

  public function handle()
  {
    ini_set('memory_limit', '-1'); // Unlimited memory

    try {
      $works = Work::where('public', 1)->get();

      foreach ($works as $work) {
        $existingPivots = $work->words()->pluck('word_work.count', 'word_id');

        $wordCounts = $work
          ->tokens()
          ->select('word_id', DB::raw('count(*) as count'))
          ->whereNotNull('word_id')
          ->groupBy('word_id')
          ->pluck('count', 'word_id');

        foreach ($wordCounts as $wordId => $count) {
          // Only update if count is new or changed
          if (
            !isset($existingPivots[$wordId]) ||
            $existingPivots[$wordId] != $count
          ) {
            $work->words()->syncWithoutDetaching([
              $wordId => ['count' => $count],
            ]);
          }
        }
      }

      $this->info('Word-work counts updated successfully using models.');
    } catch (\Throwable $e) {
      Log::error('Command failed: ' . $e->getMessage());
      $this->error('Error: ' . $e->getMessage());
    }
  }
}
